# Enhanced Gmail-like Email MSW Handlers

## Overview

The email handlers have been significantly enhanced with comprehensive Gmail-like functionality including:

### 1. **Thread API Endpoints** (`/api/google/threads`)

**Advanced Search with Gmail-style operators:**
```bash
# Basic search
GET /api/google/threads?q=meeting

# Gmail-style operators
GET /api/google/threads?q=from:<EMAIL> has:attachment is:unread
GET /api/google/threads?q=subject:project after:2024/01/01 before:2024/12/31
GET /api/google/threads?q=label:important category:work

# Type filtering
GET /api/google/threads?type=inbox
GET /api/google/threads?type=sent
GET /api/google/threads?type=spam
GET /api/google/threads?type=trash

# Label filtering
GET /api/google/threads?labelId=INBOX&labelId=IMPORTANT

# Pagination with tokens
GET /api/google/threads?pageToken=eyJwYWdlIjoyLCJmaWx0ZXJzIjp7fX0&maxResults=25
```

**Thread Details:**
```bash
# Get thread with different formats
GET /api/google/threads/{threadId}?format=full
GET /api/google/threads/{threadId}?format=minimal
GET /api/google/threads/{threadId}?format=metadata&metadataHeaders=subject&metadataHeaders=from
```

**Thread Modifications:**
```bash
# Add/remove labels
POST /api/google/threads/{threadId}/modify
{
  "addLabelIds": ["STARRED", "IMPORTANT"],
  "removeLabelIds": ["UNREAD"]
}

# Trash/untrash
POST /api/google/threads/{threadId}/trash
POST /api/google/threads/{threadId}/untrash

# Delete permanently
DELETE /api/google/threads/{threadId}
```

### 2. **Message API Endpoints** (`/api/google/messages`)

**Message Details:**
```bash
# Get message with different formats
GET /api/google/messages/{messageId}?format=full
GET /api/google/messages/{messageId}?format=minimal
GET /api/google/messages/{messageId}?format=metadata
GET /api/google/messages/{messageId}?format=raw
```

**Send Messages:**
```bash
POST /api/google/messages/send
{
  "raw": "base64-encoded-email-content"
}
# OR
{
  "payload": {
    "headers": {
      "subject": "Hello",
      "to": "<EMAIL>"
    },
    "body": {
      "data": "base64-encoded-body"
    }
  },
  "threadId": "optional-thread-id-for-replies"
}
```

**Message Modifications:**
```bash
# Add/remove labels
POST /api/google/messages/{messageId}/modify
{
  "addLabelIds": ["STARRED"],
  "removeLabelIds": ["UNREAD"]
}

# Trash/untrash
POST /api/google/messages/{messageId}/trash
POST /api/google/messages/{messageId}/untrash
```

### 3. **Batch Operations** (`/api/google/threads/batch`)

```bash
POST /api/google/threads/batch
{
  "operation": "archive",
  "threadIds": ["thread1", "thread2", "thread3"]
}

# Supported operations:
# - archive, delete, markRead, markUnread
# - addLabel, removeLabel, trash, untrash
```

### 4. **Real-time Simulation** (`/api/simulate`)

```bash
# Simulate new email arrival
POST /api/simulate/new-email
{
  "category": "Work"  // optional
}

# Simulate reply to existing thread
POST /api/simulate/reply/{threadId}
```

### 5. **Enhanced Search Operators**

The system now supports Gmail-style search operators:

- **from:<EMAIL>** - Emails from specific sender
- **to:<EMAIL>** - Emails to specific recipient  
- **subject:keyword** - Emails with keyword in subject
- **has:attachment** - Emails with attachments
- **is:unread** - Unread emails
- **is:read** - Read emails
- **is:starred** - Starred emails
- **is:important** - Important emails
- **label:LABELNAME** - Emails with specific label
- **category:Work** - Emails in specific category
- **after:2024/01/01** - Emails after date
- **before:2024/12/31** - Emails before date

### 6. **Realistic Mock Data**

**Enhanced Email Templates:**
- More realistic content with emojis and formatting
- Proper HTML versions of emails
- Realistic attachment generation
- AI analysis with sentiment and suggested actions
- Business hours timing for work emails
- Proper email threading and conversation flows

**Email Categories:** Newsletter, Receipt, Marketing, Social, Updates, Personal, Work, Finance, Travel, Security, Notification, Important, Spam, Other

**Attachment Types:** PDF, DOCX, XLSX, JPG, PNG, ZIP with realistic file names and sizes

### 7. **Statistics & Analytics** (`/api/email-stats`)

```json
{
  "total": 150,
  "unread": 23,
  "important": 8,
  "starred": 5,
  "hasAttachments": 45,
  "byCategory": {
    "Work": 45,
    "Personal": 30,
    "Newsletter": 20
  },
  "byTimeframe": {
    "today": 12,
    "yesterday": 8,
    "thisWeek": 35,
    "thisMonth": 87,
    "older": 8
  },
  "avgMessagesPerThread": 1.8,
  "totalAttachments": 67,
  "totalSizeBytes": 15680234
}
```

### 8. **Response Formats**

**Gmail API Compatible:**
All responses follow Gmail API structure with proper JSON formatting, including:
- Base64 encoded message bodies
- Proper header formatting
- Label management
- Thread nesting
- Attachment metadata

**Legacy API Support:**
Maintains backward compatibility with existing `/api/threads` and `/api/messages` endpoints.

## Usage Examples

### Search for work emails with attachments from last week:
```bash
GET /api/google/threads?q=category:work has:attachment after:week
```

### Get unread important emails:
```bash
GET /api/google/threads?q=is:unread is:important
```

### Archive multiple threads:
```bash
POST /api/google/threads/batch
{
  "operation": "archive",
  "threadIds": ["thread-0001", "thread-0002", "thread-0003"]
}
```

### Mark threads as read and add labels:
```bash
POST /api/google/threads/batch
{
  "operation": "addLabel",
  "threadIds": ["thread-0001"],
  "addLabelIds": ["READ", "PROCESSED"]
}
```

This comprehensive implementation provides a realistic Gmail-like email experience for development and testing purposes, with all the advanced features users expect from modern email clients.