# E-Connect 2 - Developer Guide

## 🚀 Getting Started

This comprehensive developer guide will help you understand, extend, and contribute to the E-Connect 2 email management platform. Whether you're setting up a development environment, adding new features, or integrating with the API, this guide has you covered.

## 🏗️ Development Environment Setup

### **Prerequisites**

```bash
# Required software
Node.js >= 18.0.0
npm >= 8.0.0
Git >= 2.30.0

# Recommended tools
VS Code with extensions:
- TypeScript and JavaScript Language Features
- ESLint
- Prettier
- Tailwind CSS IntelliSense
- Auto Rename Tag
```

### **Project Setup**

```bash
# Clone the repository
git clone <repository-url>
cd e-connect-2

# Install dependencies
npm install

# Start development server
npm run dev

# Open development tools
npm run dev:tools  # If available
```

### **Development Server**

```bash
# Main development server (port 5173)
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix
```

### **Environment Configuration**

Create a `.env.local` file for development-specific settings:

```env
# Development settings
VITE_APP_TITLE="E-Connect 2 Dev"
VITE_API_BASE_URL="http://localhost:5173/api"
VITE_MOCK_API_ENABLED=true
VITE_DEBUG_MODE=true

# Feature flags
VITE_FEATURE_MULTI_ACCOUNT=true
VITE_FEATURE_AI_ASSISTANT=true
VITE_FEATURE_ANALYTICS=true
VITE_FEATURE_BULK_OPERATIONS=true
VITE_FEATURE_COLD_EMAIL_BLOCKER=true

# Performance settings
VITE_MAX_EMAILS_PER_PAGE=50
VITE_SYNC_INTERVAL=30000
VITE_SEARCH_DEBOUNCE=300
VITE_VIRTUAL_SCROLL_THRESHOLD=100

# Development tools
VITE_SHOW_DEVTOOLS=true
VITE_LOG_LEVEL="debug"
VITE_ENABLE_MSW_LOGGING=true
```

## 📁 Project Structure Deep Dive

### **Core Directories**

```typescript
src/
├── components/          # React components organized by feature
│   ├── ui/             # Base UI primitives (Button, Input, Card, etc.)
│   ├── email-list/     # Email management components
│   ├── assistant-chat/ # AI assistant interface
│   ├── charts/         # Analytics visualizations
│   ├── bulk-actions/   # Bulk operation components
│   ├── rules/          # Rules management components
│   ├── settings/       # Settings and preferences
│   ├── account-management/  # Multi-account management
│   ├── account-switcher/    # Account switching UI
│   ├── unified-inbox/  # Unified inbox components
│   └── layout/         # Layout components (Navigation, etc.)
├── routes/             # TanStack Router route definitions
├── stores/             # Zustand state management stores
├── mocks/              # MSW API mocking system
├── utils/              # Utility functions and helpers
├── types/              # TypeScript type definitions
├── hooks/              # Custom React hooks
├── lib/                # Core utilities and configurations
└── assets/             # Static assets
```

### **Component Organization Philosophy**

#### **1. Atomic Design Structure**
```typescript
// UI Components (Atoms)
components/ui/Button.tsx
components/ui/Input.tsx
components/ui/Card.tsx

// Feature Components (Molecules)
components/email-list/EmailListItem.tsx
components/rules/RuleCondition.tsx

// Layout Components (Organisms)
components/email-list/EmailList.tsx
components/assistant-chat/Chat.tsx

// Pages (Templates)
routes/mail/index.tsx
routes/assistant/index.tsx
```

#### **2. Feature-Based Organization**
Each feature has its own directory with:
- Components
- Types
- Utilities
- Tests
- Documentation

```typescript
components/email-list/
├── EmailList.tsx           # Main component
├── EmailListItem.tsx       # Item component
├── EmailPanel.tsx          # Detail panel
├── BulkActions.tsx         # Bulk operations
├── ThreadView.tsx          # Thread display
├── README.md               # Feature documentation
├── USAGE.md                # Usage examples
└── types.ts                # Local types
```

## 🧩 Component Development

### **Component Development Patterns**

#### **1. Component Structure Template**

```typescript
// ComponentName.tsx
import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { cn } from '@/lib/utils'
import type { ComponentProps } from './types'

interface ComponentNameProps {
  // Required props
  data: DataType
  onAction: (action: ActionType) => void
  
  // Optional props
  className?: string
  variant?: 'default' | 'compact' | 'detailed'
  isLoading?: boolean
  
  // Children and composition
  children?: React.ReactNode
}

export function ComponentName({
  data,
  onAction,
  className,
  variant = 'default',
  isLoading = false,
  children
}: ComponentNameProps) {
  // Local state
  const [localState, setLocalState] = useState<StateType>()
  
  // Computed values (memoized)
  const computedValue = useMemo(() => {
    return expensiveCalculation(data)
  }, [data])
  
  // Event handlers (memoized)
  const handleAction = useCallback((actionData: ActionData) => {
    onAction({ type: 'action', data: actionData })
  }, [onAction])
  
  // Effects
  useEffect(() => {
    // Side effects
    return () => {
      // Cleanup
    }
  }, [])
  
  // Early returns for loading/error states
  if (isLoading) {
    return <LoadingSpinner />
  }
  
  if (!data) {
    return <EmptyState />
  }
  
  return (
    <div className={cn(
      'base-classes',
      variant === 'compact' && 'compact-classes',
      variant === 'detailed' && 'detailed-classes',
      className
    )}>
      {/* Component content */}
      {children}
    </div>
  )
}

// Export for testing
export type { ComponentNameProps }
```

#### **2. Compound Component Pattern**

```typescript
// EmailList.tsx - Main component
export function EmailList({ children, ...props }: EmailListProps) {
  return (
    <div className="email-list">
      {children}
    </div>
  )
}

// Sub-components
EmailList.Header = function EmailListHeader({ children }: HeaderProps) {
  return <div className="email-list-header">{children}</div>
}

EmailList.SearchBox = function EmailListSearchBox(props: SearchProps) {
  return <input className="search-box" {...props} />
}

EmailList.Body = function EmailListBody({ children }: BodyProps) {
  return <div className="email-list-body">{children}</div>
}

EmailList.Item = function EmailListItem(props: ItemProps) {
  return <div className="email-list-item">{/* Item content */}</div>
}

// Usage
<EmailList>
  <EmailList.Header>
    <EmailList.SearchBox onSearch={handleSearch} />
  </EmailList.Header>
  <EmailList.Body>
    {emails.map(email => (
      <EmailList.Item key={email.id} email={email} />
    ))}
  </EmailList.Body>
</EmailList>
```

#### **3. Render Props Pattern**

```typescript
// DataProvider.tsx
interface DataProviderProps<T> {
  query: QueryKey
  children: (props: {
    data: T[]
    isLoading: boolean
    error: Error | null
    refetch: () => void
  }) => React.ReactNode
}

export function DataProvider<T>({ query, children }: DataProviderProps<T>) {
  const { data, isLoading, error, refetch } = useQuery(query)
  
  return (
    <>
      {children({ data, isLoading, error, refetch })}
    </>
  )
}

// Usage
<DataProvider query={['threads']}>
  {({ data, isLoading, error, refetch }) => (
    <div>
      {isLoading && <Loading />}
      {error && <Error error={error} onRetry={refetch} />}
      {data && <EmailList threads={data} />}
    </div>
  )}
</DataProvider>
```

### **Custom Hooks Development**

#### **1. Data Fetching Hooks**

```typescript
// useEmailData.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { emailApi } from '@/utils/api'

export function useEmailData(accountId?: string) {
  const queryClient = useQueryClient()
  
  // Query for threads
  const threadsQuery = useQuery({
    queryKey: ['threads', accountId],
    queryFn: () => emailApi.fetchThreads({ accountId }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })
  
  // Mutation for archiving
  const archiveMutation = useMutation({
    mutationFn: emailApi.archiveThread,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['threads'] })
    },
    onError: (error) => {
      console.error('Failed to archive thread:', error)
    }
  })
  
  // Mutation for bulk operations
  const bulkMutation = useMutation({
    mutationFn: emailApi.bulkOperation,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['threads'] })
      // Show success toast
    }
  })
  
  return {
    // Data
    threads: threadsQuery.data?.threads || [],
    isLoading: threadsQuery.isLoading,
    error: threadsQuery.error,
    
    // Actions
    archiveThread: archiveMutation.mutate,
    bulkOperation: bulkMutation.mutate,
    refetch: threadsQuery.refetch,
    
    // Status
    isArchiving: archiveMutation.isPending,
    isBulkOperating: bulkMutation.isPending
  }
}
```

#### **2. UI State Hooks**

```typescript
// useSelection.ts
import { useState, useCallback, useMemo } from 'react'

export function useSelection<T>(items: T[], getId: (item: T) => string) {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set())
  
  const selectedItems = useMemo(() => {
    return items.filter(item => selectedIds.has(getId(item)))
  }, [items, selectedIds, getId])
  
  const toggleSelection = useCallback((id: string) => {
    setSelectedIds(prev => {
      const next = new Set(prev)
      if (next.has(id)) {
        next.delete(id)
      } else {
        next.add(id)
      }
      return next
    })
  }, [])
  
  const selectAll = useCallback(() => {
    setSelectedIds(new Set(items.map(getId)))
  }, [items, getId])
  
  const clearSelection = useCallback(() => {
    setSelectedIds(new Set())
  }, [])
  
  const isSelected = useCallback((id: string) => {
    return selectedIds.has(id)
  }, [selectedIds])
  
  return {
    selectedIds: Array.from(selectedIds),
    selectedItems,
    selectedCount: selectedIds.size,
    isSelected,
    toggleSelection,
    selectAll,
    clearSelection,
    hasSelection: selectedIds.size > 0,
    isAllSelected: selectedIds.size === items.length
  }
}
```

#### **3. Complex State Hooks**

```typescript
// useEmailFilters.ts
import { useState, useMemo, useCallback } from 'react'
import type { EmailFilter, Thread } from '@/types'

export function useEmailFilters(threads: Thread[]) {
  const [filters, setFilters] = useState<EmailFilter>({
    search: '',
    category: null,
    unread: false,
    starred: false,
    hasAttachment: false,
    dateRange: null
  })
  
  const filteredThreads = useMemo(() => {
    return threads.filter(thread => {
      // Search filter
      if (filters.search) {
        const searchLower = filters.search.toLowerCase()
        const matchesSearch = 
          thread.snippet.toLowerCase().includes(searchLower) ||
          thread.messages.some(msg => 
            msg.subject.toLowerCase().includes(searchLower) ||
            msg.from.email.toLowerCase().includes(searchLower)
          )
        if (!matchesSearch) return false
      }
      
      // Category filter
      if (filters.category && thread.category !== filters.category) {
        return false
      }
      
      // Unread filter
      if (filters.unread && !thread.unread) {
        return false
      }
      
      // Starred filter
      if (filters.starred && !thread.starred) {
        return false
      }
      
      // Attachment filter
      if (filters.hasAttachment) {
        const hasAttachment = thread.messages.some(msg => 
          msg.attachments && msg.attachments.length > 0
        )
        if (!hasAttachment) return false
      }
      
      // Date range filter
      if (filters.dateRange) {
        const messageDate = new Date(thread.messages[0]?.date)
        if (messageDate < filters.dateRange.start || 
            messageDate > filters.dateRange.end) {
          return false
        }
      }
      
      return true
    })
  }, [threads, filters])
  
  const updateFilter = useCallback((key: keyof EmailFilter, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }, [])
  
  const clearFilters = useCallback(() => {
    setFilters({
      search: '',
      category: null,
      unread: false,
      starred: false,
      hasAttachment: false,
      dateRange: null
    })
  }, [])
  
  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(value => 
      value !== null && value !== false && value !== ''
    )
  }, [filters])
  
  return {
    filters,
    filteredThreads,
    updateFilter,
    clearFilters,
    hasActiveFilters,
    resultCount: filteredThreads.length
  }
}
```

## 🗃️ State Management

### **Zustand Store Development**

#### **1. Store Structure Pattern**

```typescript
// stores/emailStore.ts
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import type { Thread, EmailFilter, EmailSort } from '@/types'

interface EmailState {
  // Data
  threads: Thread[]
  currentThread: Thread | null
  
  // UI State
  isLoading: boolean
  error: string | null
  selectedThreadIds: string[]
  
  // Filters and Views
  filters: EmailFilter
  sort: EmailSort
  currentView: 'inbox' | 'sent' | 'drafts' | 'trash'
  
  // Multi-account support
  currentAccountId: string | null
  isUnifiedView: boolean
}

interface EmailActions {
  // Data actions
  setThreads: (threads: Thread[]) => void
  addThread: (thread: Thread) => void
  updateThread: (threadId: string, updates: Partial<Thread>) => void
  removeThread: (threadId: string) => void
  
  // Selection actions
  selectThread: (threadId: string) => void
  toggleThreadSelection: (threadId: string) => void
  clearSelection: () => void
  
  // Filter actions
  setFilter: (key: keyof EmailFilter, value: any) => void
  clearFilters: () => void
  setSort: (sort: EmailSort) => void
  
  // View actions
  setCurrentView: (view: EmailState['currentView']) => void
  setCurrentAccount: (accountId: string | null) => void
  toggleUnifiedView: () => void
  
  // Async actions
  fetchThreads: (params?: FetchParams) => Promise<void>
  archiveThread: (threadId: string) => Promise<void>
  deleteThread: (threadId: string) => Promise<void>
  
  // Utility actions
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  reset: () => void
}

type EmailStore = EmailState & EmailActions

const initialState: EmailState = {
  threads: [],
  currentThread: null,
  isLoading: false,
  error: null,
  selectedThreadIds: [],
  filters: {
    search: '',
    category: null,
    unread: false,
    starred: false,
    hasAttachment: false,
    dateRange: null
  },
  sort: { field: 'date', direction: 'desc' },
  currentView: 'inbox',
  currentAccountId: null,
  isUnifiedView: false
}

export const useEmailStore = create<EmailStore>()(
  persist(
    immer((set, get) => ({
      ...initialState,
      
      // Data actions
      setThreads: (threads) => set((state) => {
        state.threads = threads
        state.isLoading = false
        state.error = null
      }),
      
      addThread: (thread) => set((state) => {
        state.threads.unshift(thread)
      }),
      
      updateThread: (threadId, updates) => set((state) => {
        const index = state.threads.findIndex(t => t.id === threadId)
        if (index !== -1) {
          Object.assign(state.threads[index], updates)
        }
        
        if (state.currentThread?.id === threadId) {
          Object.assign(state.currentThread, updates)
        }
      }),
      
      removeThread: (threadId) => set((state) => {
        state.threads = state.threads.filter(t => t.id !== threadId)
        state.selectedThreadIds = state.selectedThreadIds.filter(id => id !== threadId)
        
        if (state.currentThread?.id === threadId) {
          state.currentThread = null
        }
      }),
      
      // Selection actions
      selectThread: (threadId) => set((state) => {
        state.currentThread = state.threads.find(t => t.id === threadId) || null
      }),
      
      toggleThreadSelection: (threadId) => set((state) => {
        const index = state.selectedThreadIds.indexOf(threadId)
        if (index === -1) {
          state.selectedThreadIds.push(threadId)
        } else {
          state.selectedThreadIds.splice(index, 1)
        }
      }),
      
      clearSelection: () => set((state) => {
        state.selectedThreadIds = []
      }),
      
      // Filter actions
      setFilter: (key, value) => set((state) => {
        state.filters[key] = value
      }),
      
      clearFilters: () => set((state) => {
        state.filters = { ...initialState.filters }
      }),
      
      setSort: (sort) => set((state) => {
        state.sort = sort
      }),
      
      // View actions
      setCurrentView: (view) => set((state) => {
        state.currentView = view
      }),
      
      setCurrentAccount: (accountId) => set((state) => {
        state.currentAccountId = accountId
        state.isUnifiedView = accountId === null
      }),
      
      toggleUnifiedView: () => set((state) => {
        state.isUnifiedView = !state.isUnifiedView
        if (state.isUnifiedView) {
          state.currentAccountId = null
        }
      }),
      
      // Async actions
      fetchThreads: async (params) => {
        set((state) => {
          state.isLoading = true
          state.error = null
        })
        
        try {
          const response = await emailApi.fetchThreads(params)
          set((state) => {
            state.threads = response.threads
            state.isLoading = false
          })
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Unknown error'
            state.isLoading = false
          })
        }
      },
      
      archiveThread: async (threadId) => {
        try {
          await emailApi.archiveThread(threadId)
          get().removeThread(threadId)
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Archive failed'
          })
          throw error
        }
      },
      
      deleteThread: async (threadId) => {
        try {
          await emailApi.deleteThread(threadId)
          get().removeThread(threadId)
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Delete failed'
          })
          throw error
        }
      },
      
      // Utility actions
      setLoading: (loading) => set((state) => {
        state.isLoading = loading
      }),
      
      setError: (error) => set((state) => {
        state.error = error
      }),
      
      reset: () => set(() => ({ ...initialState }))
    })),
    {
      name: 'email-store',
      partialize: (state) => ({
        // Only persist certain parts of the state
        filters: state.filters,
        sort: state.sort,
        currentView: state.currentView,
        currentAccountId: state.currentAccountId,
        isUnifiedView: state.isUnifiedView
      })
    }
  )
)

// Selectors for computed values
export const useEmailSelectors = () => {
  const store = useEmailStore()
  
  return useMemo(() => ({
    // Filtered threads
    filteredThreads: filterThreads(store.threads, store.filters),
    
    // Sorted threads
    sortedThreads: sortThreads(store.threads, store.sort),
    
    // Selected threads
    selectedThreads: store.threads.filter(t => 
      store.selectedThreadIds.includes(t.id)
    ),
    
    // Counts
    unreadCount: store.threads.filter(t => t.unread).length,
    selectedCount: store.selectedThreadIds.length,
    totalCount: store.threads.length,
    
    // Status
    hasSelection: store.selectedThreadIds.length > 0,
    hasFilters: Object.values(store.filters).some(v => 
      v !== null && v !== false && v !== ''
    )
  }), [store])
}
```

#### **2. Store Integration with React Query**

```typescript
// hooks/useEmailQuery.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useEmailStore } from '@/stores/emailStore'
import { emailApi } from '@/utils/api'

export function useEmailQuery(accountId?: string) {
  const { setThreads, setLoading, setError } = useEmailStore()
  const queryClient = useQueryClient()
  
  // Threads query
  const threadsQuery = useQuery({
    queryKey: ['threads', accountId],
    queryFn: () => emailApi.fetchThreads({ accountId }),
    onSuccess: (data) => {
      setThreads(data.threads)
    },
    onError: (error) => {
      setError(error.message)
    },
    onLoading: () => {
      setLoading(true)
    }
  })
  
  // Archive mutation
  const archiveMutation = useMutation({
    mutationFn: emailApi.archiveThread,
    onSuccess: (_, threadId) => {
      queryClient.invalidateQueries(['threads'])
      useEmailStore.getState().removeThread(threadId)
    }
  })
  
  return {
    ...threadsQuery,
    archiveThread: archiveMutation.mutate,
    isArchiving: archiveMutation.isPending
  }
}
```

## 🔌 API Development with MSW

### **MSW Handler Development**

#### **1. Handler Structure Pattern**

```typescript
// mocks/handlers/emails.ts
import { http, HttpResponse } from 'msw'
import { generateThreads, generateThread } from '../data/emails'
import type { ThreadListRequest, ThreadListResponse } from '@/types'

export const emailHandlers = [
  // Get threads with advanced filtering
  http.get('/api/google/threads', ({ request }) => {
    const url = new URL(request.url)
    
    // Parse query parameters
    const query = url.searchParams.get('q') || ''
    const labelId = url.searchParams.get('labelId')
    const type = url.searchParams.get('type')
    const pageToken = url.searchParams.get('pageToken')
    const maxResults = parseInt(url.searchParams.get('maxResults') || '50')
    const accountId = url.searchParams.get('accountId')
    
    // Parse Gmail-style search query
    const searchParams = parseGmailQuery(query)
    
    // Get filtered threads
    const filteredThreads = filterThreads(mockThreads, {
      ...searchParams,
      labelId,
      type,
      accountId
    })
    
    // Apply pagination
    const startIndex = pageToken ? parseInt(pageToken) : 0
    const endIndex = startIndex + maxResults
    const paginatedThreads = filteredThreads.slice(startIndex, endIndex)
    
    // Generate response
    const response: ThreadListResponse = {
      threads: paginatedThreads,
      nextPageToken: endIndex < filteredThreads.length ? 
        endIndex.toString() : undefined,
      resultSizeEstimate: filteredThreads.length
    }
    
    // Simulate network delay
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(HttpResponse.json(response))
      }, 200 + Math.random() * 800)
    })
  }),
  
  // Get single thread
  http.get('/api/google/threads/:threadId', ({ params }) => {
    const { threadId } = params
    const thread = mockThreads.find(t => t.id === threadId)
    
    if (!thread) {
      return HttpResponse.json(
        { error: { code: 'NOT_FOUND', message: 'Thread not found' } },
        { status: 404 }
      )
    }
    
    // Add AI analysis to thread
    const enhancedThread = {
      ...thread,
      aiAnalysis: generateAIAnalysis(thread)
    }
    
    return HttpResponse.json(enhancedThread)
  }),
  
  // Batch operations
  http.post('/api/google/threads/batch', async ({ request }) => {
    const body = await request.json() as BatchRequest
    const { operation, threadIds, labelId } = body
    
    // Validate request
    if (!operation || !threadIds || threadIds.length === 0) {
      return HttpResponse.json(
        { error: { code: 'INVALID_REQUEST', message: 'Missing required fields' } },
        { status: 400 }
      )
    }
    
    if (threadIds.length > 100) {
      return HttpResponse.json(
        { error: { code: 'LIMIT_EXCEEDED', message: 'Too many threads (max 100)' } },
        { status: 400 }
      )
    }
    
    // Process batch operation
    const results = await Promise.all(
      threadIds.map(async (threadId) => {
        try {
          await performBatchOperation(operation, threadId, labelId)
          return { threadId, success: true }
        } catch (error) {
          return { 
            threadId, 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        }
      })
    )
    
    const response = {
      results,
      successCount: results.filter(r => r.success).length,
      errorCount: results.filter(r => !r.success).length,
      totalProcessed: results.length
    }
    
    // Simulate processing time
    await new Promise(resolve => 
      setTimeout(resolve, 500 + Math.random() * 1500)
    )
    
    return HttpResponse.json(response)
  })
]

// Helper functions
function parseGmailQuery(query: string): SearchParams {
  const params: SearchParams = {}
  
  // Parse search operators
  const operators = [
    'from:', 'to:', 'subject:', 'has:', 'is:', 
    'label:', 'category:', 'after:', 'before:'
  ]
  
  for (const operator of operators) {
    const regex = new RegExp(`${operator}([^\\s]+)`, 'gi')
    const matches = query.match(regex)
    
    if (matches) {
      const key = operator.slice(0, -1) // Remove the ':'
      params[key] = matches.map(match => 
        match.slice(operator.length).replace(/['"]/g, '')
      )
    }
  }
  
  // Extract free text search
  let freeText = query
  for (const operator of operators) {
    freeText = freeText.replace(new RegExp(`${operator}[^\\s]+`, 'gi'), '')
  }
  params.freeText = freeText.trim()
  
  return params
}

function filterThreads(threads: Thread[], filters: FilterParams): Thread[] {
  return threads.filter(thread => {
    // Apply all filters
    if (filters.from && !thread.messages.some(msg => 
      filters.from.some(sender => 
        msg.from.email.toLowerCase().includes(sender.toLowerCase())
      )
    )) {
      return false
    }
    
    if (filters.subject && !thread.messages.some(msg =>
      filters.subject.some(subject =>
        msg.subject.toLowerCase().includes(subject.toLowerCase())
      )
    )) {
      return false
    }
    
    // ... more filters
    
    return true
  })
}

async function performBatchOperation(
  operation: string, 
  threadId: string, 
  labelId?: string
): Promise<void> {
  const thread = mockThreads.find(t => t.id === threadId)
  if (!thread) {
    throw new Error('Thread not found')
  }
  
  switch (operation) {
    case 'archive':
      thread.labels = thread.labels?.filter(l => l !== 'INBOX') || []
      break
      
    case 'delete':
      thread.labels = ['TRASH']
      break
      
    case 'markRead':
      thread.unread = false
      break
      
    case 'markUnread':
      thread.unread = true
      break
      
    case 'addLabel':
      if (labelId && !thread.labels?.includes(labelId)) {
        thread.labels = [...(thread.labels || []), labelId]
      }
      break
      
    case 'removeLabel':
      if (labelId) {
        thread.labels = thread.labels?.filter(l => l !== labelId) || []
      }
      break
      
    default:
      throw new Error(`Unknown operation: ${operation}`)
  }
  
  // Simulate processing time
  await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200))
}
```

#### **2. Realistic Data Generation**

```typescript
// mocks/data/emails.ts
import { faker } from '@faker-js/faker'
import type { Thread, ParsedMessage } from '@/types'

export function generateThreads(count: number = 150): Thread[] {
  const threads: Thread[] = []
  const senders = generateSenders()
  
  for (let i = 0; i < count; i++) {
    const sender = selectRandomSender(senders)
    const category = selectCategoryBySender(sender)
    const messageCount = getMessageCount(category)
    
    const thread: Thread = {
      id: `thread-${i.toString().padStart(4, '0')}`,
      historyId: faker.string.numeric(8),
      snippet: generateSnippet(category),
      messages: generateConversation(sender, messageCount, category),
      category,
      labels: generateLabels(category),
      unread: Math.random() < getUnreadRate(category),
      important: Math.random() < getImportanceRate(category),
      starred: Math.random() < 0.05, // 5% starred
      accountId: selectRandomAccount(),
      accountColor: getAccountColor(),
      accountName: getAccountName()
    }
    
    threads.push(thread)
  }
  
  // Sort by date (newest first)
  threads.sort((a, b) => 
    new Date(b.messages[0]?.date).getTime() - 
    new Date(a.messages[0]?.date).getTime()
  )
  
  return threads
}

function generateSenders(): SenderProfile[] {
  return [
    // Work senders
    ...generateWorkSenders(20),
    // Newsletter senders  
    ...generateNewsletterSenders(30),
    // Personal senders
    ...generatePersonalSenders(15),
    // Service senders
    ...generateServiceSenders(25),
    // Marketing senders
    ...generateMarketingSenders(20)
  ]
}

function generateWorkSenders(count: number): SenderProfile[] {
  const domains = [
    'company.com', 'corporation.org', 'business.net',
    'enterprise.com', 'startup.io', 'agency.co'
  ]
  
  return Array.from({ length: count }, () => ({
    email: faker.internet.email({ provider: faker.helpers.arrayElement(domains) }),
    name: faker.person.fullName(),
    category: 'Work',
    frequency: faker.helpers.arrayElement(['daily', 'weekly', 'monthly']),
    importance: faker.helpers.arrayElement(['high', 'medium', 'low']),
    replyRate: Math.random() * 0.8 + 0.2, // 20-100% reply rate
    avgResponseTime: faker.number.int({ min: 1800, max: 86400 }) // 30 min to 24 hours
  }))
}

function generateConversation(
  sender: SenderProfile, 
  messageCount: number, 
  category: string
): ParsedMessage[] {
  const messages: ParsedMessage[] = []
  const baseDate = faker.date.recent({ days: 30 })
  
  for (let i = 0; i < messageCount; i++) {
    const isInitial = i === 0
    const isReply = !isInitial && Math.random() < sender.replyRate
    
    const message: ParsedMessage = {
      id: `msg-${faker.string.uuid()}`,
      threadId: '', // Will be set later
      subject: isInitial ? generateSubject(category, sender) : 
               `Re: ${generateSubject(category, sender)}`,
      from: isReply ? generateRecipient() : sender,
      to: [isReply ? sender : generateRecipient()],
      cc: Math.random() < 0.1 ? [generateRecipient()] : [],
      bcc: [],
      date: new Date(
        baseDate.getTime() + (i * (sender.avgResponseTime + Math.random() * 3600000))
      ).toISOString(),
      body: generateEmailBody(category, sender, isReply),
      snippet: generateSnippet(category),
      attachments: generateAttachments(category),
      labels: generateMessageLabels(category),
      unread: i === messageCount - 1 && Math.random() < getUnreadRate(category),
      important: Math.random() < getImportanceRate(category),
      starred: Math.random() < 0.05
    }
    
    messages.push(message)
  }
  
  return messages
}

function generateEmailBody(
  category: string, 
  sender: SenderProfile, 
  isReply: boolean
): string {
  const templates = getEmailTemplates(category)
  const template = faker.helpers.arrayElement(templates)
  
  // Replace placeholders with realistic content
  return template
    .replace('{{sender}}', sender.name)
    .replace('{{company}}', faker.company.name())
    .replace('{{product}}', faker.commerce.productName())
    .replace('{{date}}', faker.date.future().toLocaleDateString())
    .replace('{{amount}}', faker.finance.amount())
    .replace('{{link}}', faker.internet.url())
}

function generateAttachments(category: string): Attachment[] {
  const shouldHaveAttachment = getAttachmentRate(category) > Math.random()
  
  if (!shouldHaveAttachment) return []
  
  const attachmentTypes = getAttachmentTypes(category)
  const attachmentCount = Math.random() < 0.8 ? 1 : faker.number.int({ min: 2, max: 4 })
  
  return Array.from({ length: attachmentCount }, () => {
    const type = faker.helpers.arrayElement(attachmentTypes)
    return {
      id: `att-${faker.string.uuid()}`,
      filename: generateFilename(type),
      mimeType: getMimeType(type),
      size: generateFileSize(type),
      data: faker.string.alphanumeric(100) // Placeholder data
    }
  })
}

// Email templates by category
function getEmailTemplates(category: string): string[] {
  const templates = {
    Work: [
      "Hi {{sender}},\n\nI wanted to follow up on our meeting yesterday. Please find the attached documents for your review.\n\nBest regards,\n{{sender}}",
      "Hello,\n\nThe quarterly report is ready. Please review and let me know if you have any questions.\n\nThanks,\n{{sender}}",
      "Hi team,\n\nOur next meeting is scheduled for {{date}}. Please confirm your attendance.\n\nRegards,\n{{sender}}"
    ],
    Newsletter: [
      "📧 Weekly Newsletter from {{company}}\n\nThis week's highlights:\n- New {{product}} launch\n- Industry insights\n- Upcoming events\n\nRead more: {{link}}\n\nUnsubscribe: {{link}}",
      "🔥 {{company}} Update\n\nDon't miss out on:\n- Latest features\n- Customer stories\n- Expert tips\n\nView in browser: {{link}}\nUnsubscribe: {{link}}"
    ],
    Receipt: [
      "Receipt for your purchase\n\nOrder #{{amount}}\nDate: {{date}}\nTotal: ${{amount}}\n\nThank you for your business!\n\n{{company}}",
      "Your payment confirmation\n\nTransaction ID: {{amount}}\nAmount: ${{amount}}\nDate: {{date}}\n\nQuestions? Contact support."
    ]
  }
  
  return templates[category] || templates.Work
}

// Category-specific rates
function getUnreadRate(category: string): number {
  const rates = {
    Work: 0.15,      // 15% unread
    Personal: 0.10,   // 10% unread  
    Newsletter: 0.25, // 25% unread
    Marketing: 0.35,  // 35% unread
    Notification: 0.05 // 5% unread
  }
  return rates[category] || 0.20
}

function getImportanceRate(category: string): number {
  const rates = {
    Work: 0.20,       // 20% important
    Personal: 0.15,   // 15% important
    Finance: 0.30,    // 30% important
    Newsletter: 0.05, // 5% important
    Marketing: 0.02   // 2% important
  }
  return rates[category] || 0.10
}

function getAttachmentRate(category: string): number {
  const rates = {
    Work: 0.30,       // 30% have attachments
    Finance: 0.80,    // 80% have attachments (receipts, etc.)
    Newsletter: 0.05, // 5% have attachments
    Personal: 0.15,   // 15% have attachments
    Marketing: 0.10   // 10% have attachments
  }
  return rates[category] || 0.15
}
```

### **Error Handling and Edge Cases**

```typescript
// Error handling in MSW handlers
export function createErrorHandler(errorType: string) {
  return ({ request }: { request: Request }) => {
    const url = new URL(request.url)
    const simulateError = url.searchParams.get('simulateError')
    
    if (simulateError === errorType) {
      switch (errorType) {
        case 'network':
          return HttpResponse.error()
          
        case 'timeout':
          return new Promise(() => {}) // Never resolves
          
        case 'rate_limit':
          return HttpResponse.json(
            { 
              error: { 
                code: 'RATE_LIMIT_EXCEEDED', 
                message: 'Too many requests' 
              } 
            },
            { 
              status: 429,
              headers: {
                'X-RateLimit-Limit': '1000',
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': (Date.now() + 3600000).toString()
              }
            }
          )
          
        case 'auth':
          return HttpResponse.json(
            { 
              error: { 
                code: 'UNAUTHORIZED', 
                message: 'Invalid or expired token' 
              } 
            },
            { status: 401 }
          )
          
        case 'server':
          return HttpResponse.json(
            { 
              error: { 
                code: 'INTERNAL_ERROR', 
                message: 'Internal server error' 
              } 
            },
            { status: 500 }
          )
          
        default:
          return HttpResponse.json(
            { 
              error: { 
                code: 'UNKNOWN_ERROR', 
                message: 'An unknown error occurred' 
              } 
            },
            { status: 500 }
          )
      }
    }
    
    // Continue with normal handling
    return null
  }
}

// Usage in handlers
http.get('/api/google/threads', ({ request }) => {
  // Check for simulated errors first
  const errorResponse = createErrorHandler('network')({ request })
  if (errorResponse) return errorResponse
  
  // Normal handling
  // ...
})
```

## 🧪 Testing

### **Testing Setup and Configuration**

```typescript
// test-utils.tsx
import React from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'
import { worker } from '@/mocks/browser'

// Test query client with disabled retries
const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      gcTime: 0,
    },
    mutations: {
      retry: false,
    },
  },
})

// Test wrapper component
interface AllTheProvidersProps {
  children: React.ReactNode
}

function AllTheProviders({ children }: AllTheProvidersProps) {
  const queryClient = createTestQueryClient()
  
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

// Custom render function
function customRender(
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) {
  return render(ui, { wrapper: AllTheProviders, ...options })
}

// Re-export everything
export * from '@testing-library/react'
export { customRender as render }

// Test setup
beforeAll(() => {
  // Start MSW worker
  worker.listen()
})

afterEach(() => {
  // Reset handlers after each test
  worker.resetHandlers()
})

afterAll(() => {
  // Clean up
  worker.close()
})

// Mock data utilities
export const createMockThread = (overrides = {}) => ({
  id: 'test-thread-1',
  historyId: '12345',
  snippet: 'Test email snippet',
  messages: [createMockMessage()],
  category: 'Work',
  labels: ['INBOX'],
  unread: false,
  important: false,
  starred: false,
  ...overrides
})

export const createMockMessage = (overrides = {}) => ({
  id: 'test-message-1',
  threadId: 'test-thread-1',
  subject: 'Test Subject',
  from: { email: '<EMAIL>', name: 'Test Sender' },
  to: [{ email: '<EMAIL>', name: 'Test User' }],
  cc: [],
  bcc: [],
  date: new Date().toISOString(),
  body: 'Test email body',
  snippet: 'Test snippet',
  attachments: [],
  labels: ['INBOX'],
  unread: false,
  important: false,
  starred: false,
  ...overrides
})
```

### **Component Testing Examples**

```typescript
// EmailList.test.tsx
import { screen, waitFor, fireEvent } from '@testing-library/react'
import { http, HttpResponse } from 'msw'
import { worker } from '@/mocks/browser'
import { render, createMockThread } from '@/test-utils'
import { EmailList } from '@/components/email-list/EmailList'

describe('EmailList', () => {
  const mockThreads = [
    createMockThread({ 
      id: 'thread-1', 
      snippet: 'First test email',
      unread: true 
    }),
    createMockThread({ 
      id: 'thread-2', 
      snippet: 'Second test email',
      unread: false 
    })
  ]
  
  beforeEach(() => {
    // Setup MSW handler for this test
    worker.use(
      http.get('/api/google/threads', () => {
        return HttpResponse.json({ threads: mockThreads })
      })
    )
  })
  
  it('renders email list with threads', async () => {
    render(<EmailList />)
    
    // Should show loading initially
    expect(screen.getByText('Loading...')).toBeInTheDocument()
    
    // Wait for emails to load
    await waitFor(() => {
      expect(screen.getByText('First test email')).toBeInTheDocument()
      expect(screen.getByText('Second test email')).toBeInTheDocument()
    })
  })
  
  it('handles thread selection', async () => {
    const onThreadSelect = jest.fn()
    render(<EmailList onThreadSelect={onThreadSelect} />)
    
    await waitFor(() => {
      expect(screen.getByText('First test email')).toBeInTheDocument()
    })
    
    // Click on first thread
    fireEvent.click(screen.getByText('First test email'))
    
    expect(onThreadSelect).toHaveBeenCalledWith('thread-1')
  })
  
  it('handles bulk selection', async () => {
    render(<EmailList />)
    
    await waitFor(() => {
      expect(screen.getByText('First test email')).toBeInTheDocument()
    })
    
    // Get checkboxes
    const checkboxes = screen.getAllByRole('checkbox')
    
    // Select first thread
    fireEvent.click(checkboxes[1]) // Index 0 is "select all"
    
    // Should show bulk actions
    expect(screen.getByText('1 selected')).toBeInTheDocument()
    expect(screen.getByText('Archive')).toBeInTheDocument()
  })
  
  it('handles search functionality', async () => {
    render(<EmailList />)
    
    await waitFor(() => {
      expect(screen.getByText('First test email')).toBeInTheDocument()
    })
    
    // Setup search handler
    worker.use(
      http.get('/api/google/threads', ({ request }) => {
        const url = new URL(request.url)
        const query = url.searchParams.get('q')
        
        if (query === 'test search') {
          return HttpResponse.json({ 
            threads: [mockThreads[0]] // Return only first thread
          })
        }
        
        return HttpResponse.json({ threads: mockThreads })
      })
    )
    
    // Search for specific term
    const searchInput = screen.getByRole('searchbox')
    fireEvent.change(searchInput, { target: { value: 'test search' } })
    fireEvent.submit(searchInput.closest('form'))
    
    // Should filter results
    await waitFor(() => {
      expect(screen.getByText('First test email')).toBeInTheDocument()
      expect(screen.queryByText('Second test email')).not.toBeInTheDocument()
    })
  })
  
  it('handles error states', async () => {
    // Setup error handler
    worker.use(
      http.get('/api/google/threads', () => {
        return HttpResponse.json(
          { error: { message: 'Failed to load emails' } },
          { status: 500 }
        )
      })
    )
    
    render(<EmailList />)
    
    await waitFor(() => {
      expect(screen.getByText('Failed to load emails')).toBeInTheDocument()
    })
    
    // Should show retry button
    expect(screen.getByText('Retry')).toBeInTheDocument()
  })
  
  it('handles empty state', async () => {
    // Setup empty response
    worker.use(
      http.get('/api/google/threads', () => {
        return HttpResponse.json({ threads: [] })
      })
    )
    
    render(<EmailList />)
    
    await waitFor(() => {
      expect(screen.getByText('No emails found')).toBeInTheDocument()
    })
  })
})
```

### **Hook Testing Examples**

```typescript
// useEmailData.test.ts
import { renderHook, waitFor } from '@testing-library/react'
import { http, HttpResponse } from 'msw'
import { worker } from '@/mocks/browser'
import { useEmailData } from '@/hooks/useEmailData'
import { createTestQueryClient, TestWrapper } from '@/test-utils'

describe('useEmailData', () => {
  it('fetches threads successfully', async () => {
    const mockThreads = [
      { id: 'thread-1', snippet: 'Test email 1' },
      { id: 'thread-2', snippet: 'Test email 2' }
    ]
    
    worker.use(
      http.get('/api/google/threads', () => {
        return HttpResponse.json({ threads: mockThreads })
      })
    )
    
    const { result } = renderHook(() => useEmailData(), {
      wrapper: TestWrapper
    })
    
    // Should be loading initially
    expect(result.current.isLoading).toBe(true)
    expect(result.current.threads).toEqual([])
    
    // Wait for data to load
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })
    
    expect(result.current.threads).toEqual(mockThreads)
    expect(result.current.error).toBeNull()
  })
  
  it('handles archive operation', async () => {
    const mockThreads = [
      { id: 'thread-1', snippet: 'Test email 1' }
    ]
    
    worker.use(
      http.get('/api/google/threads', () => {
        return HttpResponse.json({ threads: mockThreads })
      }),
      http.post('/api/google/threads/:threadId/archive', () => {
        return HttpResponse.json({ success: true })
      })
    )
    
    const { result } = renderHook(() => useEmailData(), {
      wrapper: TestWrapper
    })
    
    await waitFor(() => {
      expect(result.current.threads).toEqual(mockThreads)
    })
    
    // Archive thread
    result.current.archiveThread('thread-1')
    
    // Should be archiving
    expect(result.current.isArchiving).toBe(true)
    
    await waitFor(() => {
      expect(result.current.isArchiving).toBe(false)
    })
    
    // Thread should be removed from list
    expect(result.current.threads).toEqual([])
  })
  
  it('handles errors', async () => {
    worker.use(
      http.get('/api/google/threads', () => {
        return HttpResponse.json(
          { error: { message: 'Network error' } },
          { status: 500 }
        )
      })
    )
    
    const { result } = renderHook(() => useEmailData(), {
      wrapper: TestWrapper
    })
    
    await waitFor(() => {
      expect(result.current.isLoading).toBe(false)
    })
    
    expect(result.current.error).toBeTruthy()
    expect(result.current.threads).toEqual([])
  })
})
```

### **Integration Testing Examples**

```typescript
// email-workflow.test.tsx
import { screen, waitFor, fireEvent } from '@testing-library/react'
import { http, HttpResponse } from 'msw'
import { worker } from '@/mocks/browser'
import { render, createMockThread } from '@/test-utils'
import { App } from '@/App'

describe('Email Management Workflow', () => {
  const mockThreads = [
    createMockThread({ 
      id: 'thread-1', 
      snippet: 'Newsletter email',
      category: 'Newsletter',
      unread: true 
    }),
    createMockThread({ 
      id: 'thread-2', 
      snippet: 'Work email',
      category: 'Work',
      unread: false 
    })
  ]
  
  beforeEach(() => {
    worker.use(
      http.get('/api/google/threads', () => {
        return HttpResponse.json({ threads: mockThreads })
      }),
      http.post('/api/ai/categorize', () => {
        return HttpResponse.json({
          category: 'Newsletter',
          confidence: 0.92,
          suggestedActions: [
            { type: 'archive', priority: 'medium' }
          ]
        })
      }),
      http.post('/api/user/rules', () => {
        return HttpResponse.json({
          id: 'rule-1',
          name: 'Auto-archive newsletters',
          enabled: true
        })
      })
    )
  })
  
  it('completes full email automation workflow', async () => {
    render(<App />)
    
    // Navigate to inbox
    fireEvent.click(screen.getByText('Mail'))
    
    // Wait for emails to load
    await waitFor(() => {
      expect(screen.getByText('Newsletter email')).toBeInTheDocument()
    })
    
    // Select newsletter email
    fireEvent.click(screen.getByText('Newsletter email'))
    
    // Should show email details
    await waitFor(() => {
      expect(screen.getByText('Newsletter')).toBeInTheDocument() // Category
    })
    
    // Navigate to AI assistant
    fireEvent.click(screen.getByText('Assistant'))
    
    // Ask for rule suggestion
    const chatInput = screen.getByRole('textbox', { name: /message/i })
    fireEvent.change(chatInput, { 
      target: { value: 'Create a rule to archive newsletters' } 
    })
    fireEvent.submit(chatInput.closest('form'))
    
    // Wait for AI response
    await waitFor(() => {
      expect(screen.getByText(/I can help you create a rule/)).toBeInTheDocument()
    })
    
    // Click to create rule
    fireEvent.click(screen.getByText('Create Rule'))
    
    // Should navigate to rules page
    await waitFor(() => {
      expect(screen.getByText('Auto-archive newsletters')).toBeInTheDocument()
    })
    
    // Rule should be enabled
    expect(screen.getByText('Enabled')).toBeInTheDocument()
  })
  
  it('handles bulk operations workflow', async () => {
    render(<App />)
    
    // Navigate to bulk unsubscribe
    fireEvent.click(screen.getByText('Bulk Unsubscribe'))
    
    // Setup newsletter scan handler
    worker.use(
      http.post('/api/bulk/scan-newsletters', () => {
        return HttpResponse.json({
          newsletters: [
            {
              sender: '<EMAIL>',
              senderName: 'Example Newsletter',
              emailCount: 25,
              unsubscribeUrl: 'https://example.com/unsubscribe'
            }
          ]
        })
      })
    )
    
    // Start newsletter scan
    fireEvent.click(screen.getByText('Scan for Newsletters'))
    
    // Wait for scan results
    await waitFor(() => {
      expect(screen.getByText('Example Newsletter')).toBeInTheDocument()
      expect(screen.getByText('25 emails')).toBeInTheDocument()
    })
    
    // Select newsletter for unsubscribe
    fireEvent.click(screen.getByRole('checkbox'))
    
    // Setup unsubscribe handler
    worker.use(
      http.post('/api/bulk/unsubscribe', () => {
        return HttpResponse.json({
          operationId: 'op-123',
          status: 'success'
        })
      })
    )
    
    // Start unsubscribe process
    fireEvent.click(screen.getByText('Unsubscribe Selected'))
    
    // Should show success message
    await waitFor(() => {
      expect(screen.getByText(/successfully unsubscribed/i)).toBeInTheDocument()
    })
  })
})
```

## 🔧 Build & Deployment

### **Build Configuration**

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { TanStackRouterVite } from '@tanstack/router-vite-plugin'
import path from 'path'

export default defineConfig({
  plugins: [
    react(),
    TanStackRouterVite()
  ],
  
  // Path resolution
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/stores': path.resolve(__dirname, './src/stores'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/lib': path.resolve(__dirname, './src/lib')
    }
  },
  
  // Development server
  server: {
    port: 5173,
    host: true,
    open: true
  },
  
  // Build configuration
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          vendor: ['react', 'react-dom'],
          router: ['@tanstack/react-router'],
          query: ['@tanstack/react-query'],
          ui: ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          charts: ['recharts'],
          utils: ['date-fns', 'lodash', 'clsx']
        }
      }
    },
    chunkSizeWarningLimit: 1000
  },
  
  // Environment variables
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version)
  },
  
  // Preview server
  preview: {
    port: 4173,
    host: true
  }
})
```

### **Production Deployment**

#### **1. Static Hosting (Vercel)**

```json
// vercel.json
{
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm ci",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

#### **2. Docker Deployment**

```dockerfile
# Multi-stage build for production
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Compression
    gzip on;
    gzip_types
        text/plain
        text/css
        text/js
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    server {
        listen 80;
        server_name localhost;
        
        root /usr/share/nginx/html;
        index index.html;
        
        # Handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # Cache static assets
        location /assets/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

### **Performance Optimization**

#### **1. Bundle Analysis**

```bash
# Install bundle analyzer
npm install --save-dev rollup-plugin-visualizer

# Add to vite.config.ts
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    // ... other plugins
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true
    })
  ]
})

# Generate bundle analysis
npm run build
```

#### **2. Code Splitting Strategy**

```typescript
// Dynamic imports for route components
const Dashboard = lazy(() => import('@/routes/index'))
const MailRoute = lazy(() => import('@/routes/mail/index'))
const AssistantRoute = lazy(() => import('@/routes/assistant/index'))
const AnalyticsRoute = lazy(() => import('@/routes/stats/index'))

// Feature-based code splitting
const AIAssistant = lazy(() => 
  import('@/components/assistant-chat/Chat').then(module => ({
    default: module.Chat
  }))
)

const EmailAnalytics = lazy(() => 
  import('@/components/charts/EmailVolumeChart').then(module => ({
    default: module.EmailVolumeChart
  }))
)

// Usage with Suspense
<Suspense fallback={<LoadingSkeleton />}>
  <AIAssistant />
</Suspense>
```

#### **3. Performance Monitoring**

```typescript
// Performance monitoring setup
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric: any) {
  // Send metrics to your analytics service
  console.log(metric)
}

// Measure Core Web Vitals
getCLS(sendToAnalytics)
getFID(sendToAnalytics)
getFCP(sendToAnalytics)
getLCP(sendToAnalytics)
getTTFB(sendToAnalytics)

// Custom performance marks
performance.mark('email-list-start')
// ... render email list
performance.mark('email-list-end')
performance.measure('email-list-render', 'email-list-start', 'email-list-end')
```

## 🎯 Best Practices

### **Code Quality**

#### **1. TypeScript Best Practices**
- Use strict mode in `tsconfig.json`
- Define interfaces for all props and data structures
- Use type guards for runtime type checking
- Leverage utility types (`Partial`, `Pick`, `Omit`)
- Create custom type utilities for common patterns

#### **2. React Best Practices**
- Use functional components with hooks
- Memoize expensive calculations with `useMemo`
- Memoize callbacks with `useCallback`
- Extract reusable logic into custom hooks
- Use compound components for complex UI

#### **3. Performance Best Practices**
- Implement virtual scrolling for large lists
- Use React.memo for pure components
- Debounce search inputs and filters
- Lazy load heavy components
- Optimize re-renders with proper dependencies

### **Development Workflow**

#### **1. Git Workflow**
```bash
# Feature branch workflow
git checkout -b feature/email-rules-enhancement
git add .
git commit -m "feat: add advanced rule conditions"
git push -u origin feature/email-rules-enhancement

# Create pull request
gh pr create --title "Add advanced rule conditions" --body "Description..."
```

#### **2. Commit Convention**
```bash
# Conventional commits
feat: add new feature
fix: bug fix
docs: documentation changes
style: formatting changes
refactor: code refactoring
test: adding tests
chore: maintenance tasks

# Examples
feat(rules): add multiple condition support
fix(email-list): resolve infinite scroll issue
docs(api): update endpoint documentation
```

#### **3. Code Review Checklist**
- [ ] TypeScript types are properly defined
- [ ] Components are properly tested
- [ ] Performance implications considered
- [ ] Accessibility requirements met
- [ ] Error handling implemented
- [ ] Documentation updated

### **Debugging and Troubleshooting**

#### **1. Development Tools**
```typescript
// Debug utilities
export const debugUtils = {
  logRender: (componentName: string, props?: any) => {
    if (import.meta.env.DEV) {
      console.log(`[RENDER] ${componentName}`, props)
    }
  },
  
  logStateChange: (storeName: string, oldState: any, newState: any) => {
    if (import.meta.env.DEV) {
      console.log(`[STATE] ${storeName}`, { oldState, newState })
    }
  },
  
  measurePerformance: (label: string, fn: () => void) => {
    const start = performance.now()
    fn()
    const end = performance.now()
    console.log(`[PERF] ${label}: ${end - start}ms`)
  }
}
```

#### **2. Error Boundaries**
```typescript
// ErrorBoundary component for development
export class DevelopmentErrorBoundary extends React.Component {
  constructor(props: any) {
    super(props)
    this.state = { hasError: false, error: null }
  }
  
  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }
  
  componentDidCatch(error: Error, errorInfo: any) {
    if (import.meta.env.DEV) {
      console.error('Component Error:', error, errorInfo)
    }
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <details>
            <summary>Error details</summary>
            <pre>{this.state.error?.toString()}</pre>
          </details>
        </div>
      )
    }
    
    return this.props.children
  }
}
```

This comprehensive developer guide provides everything needed to understand, extend, and contribute to the E-Connect 2 email management platform. From setting up the development environment to implementing complex features and deploying to production, this guide covers all aspects of the development lifecycle.