# E-Connect 2 - User Guide

## 🌟 Welcome to E-Connect 2

E-Connect 2 is your intelligent email management companion, designed to help you achieve inbox zero with the power of AI automation, smart rules, and comprehensive analytics. This guide will walk you through all the features and help you get the most out of your email management experience.

## 🚀 Getting Started

### **First Launch**
When you first open E-Connect 2, you'll see a clean, intuitive interface with your main dashboard. The application comes pre-loaded with sample data so you can immediately explore all features.

### **Navigation Overview**
The main navigation is located on the left side of the screen:

- **🏠 Dashboard** - Overview of your email activity and quick stats
- **📧 Mail** - Your main inbox and email management center
- **🤖 Assistant** - AI-powered email automation and chat
- **⚙️ Automation** - Create and manage email rules
- **🗑️ Bulk Unsubscribe** - Mass unsubscribe from newsletters
- **🧹 Clean** - AI-powered inbox organization
- **🚫 Cold Email Blocker** - Block unwanted cold emails
- **📊 Stats** - Comprehensive email analytics
- **⚙️ Settings** - Account and application preferences

## 📧 Email Management

### **Main Inbox**
Your inbox is the heart of E-Connect 2, providing a clean, efficient way to manage your emails.

#### **Reading Emails**
- **Click any email** to view its content in the right panel
- **Use keyboard shortcuts** for faster navigation:
  - `j` - Next email
  - `k` - Previous email
  - `Enter` - Open selected email
  - `?` - Show all keyboard shortcuts

#### **Email Actions**
Each email offers quick actions:
- **Archive** (`a`) - Remove from inbox but keep accessible
- **Delete** (`#`) - Move to trash
- **Star** (`s`) - Mark as important
- **Mark as Read/Unread** (`r`) - Toggle read status
- **Reply** (`r`) - Reply to the sender
- **Forward** (`f`) - Forward to another recipient

#### **Bulk Actions**
Select multiple emails using checkboxes to perform bulk operations:
1. **Select emails** by clicking the checkboxes
2. **Choose action** from the bulk actions bar:
   - Archive selected emails
   - Delete selected emails
   - Mark as read/unread
   - Add or remove labels
   - Move to folder

### **Search & Filtering**
E-Connect 2 supports powerful Gmail-style search operators:

#### **Basic Search**
- Type in the search box to find emails by content
- Search works across subject, sender, and email body

#### **Advanced Search Operators**
- `from:<EMAIL>` - Find emails from specific sender
- `to:<EMAIL>` - Find emails sent to specific recipient
- `subject:"meeting notes"` - Search in subject line
- `has:attachment` - Find emails with attachments
- `is:unread` - Show only unread emails
- `is:starred` - Show starred emails
- `label:important` - Filter by labels
- `category:newsletter` - Filter by AI categories
- `after:2024/01/01` - Emails after specific date
- `before:2024/12/31` - Emails before specific date

#### **Example Advanced Searches**
```
from:<EMAIL> has:attachment is:unread
category:work after:2024/01/01
subject:"invoice" OR subject:"receipt"
```

### **Email Categories**
E-Connect 2 automatically categorizes your emails using AI:

- **📧 Personal** - Personal correspondence
- **💼 Work** - Work-related emails
- **📰 Newsletter** - Newsletters and subscriptions
- **💰 Finance** - Financial statements and receipts
- **🛒 Marketing** - Promotional emails
- **🔔 Notification** - System notifications
- **📱 Social** - Social media notifications
- **🚨 Important** - High-priority emails
- **⚠️ Spam** - Suspected spam emails

### **Threading & Conversations**
Related emails are automatically grouped into conversations:
- **Expand conversations** to see all messages
- **Reply to specific messages** within a thread
- **Archive entire conversation** with one action

## 🤖 AI Assistant

### **Getting Started with the Assistant**
The AI Assistant is your personal email management consultant. Access it from the Assistant tab in the main navigation.

#### **Chat Interface**
- **Type your questions** about email management
- **Ask for help** with creating rules
- **Get suggestions** for organizing your inbox
- **Receive insights** about your email patterns

#### **Example Questions**
- "Help me create a rule to archive newsletters"
- "How can I reduce my inbox clutter?"
- "Show me emails that need urgent attention"
- "What's the best way to organize my work emails?"

### **Smart Suggestions**
The Assistant provides proactive suggestions:
- **Rule recommendations** based on your email patterns
- **Time-saving tips** for email management
- **Automation opportunities** you might have missed
- **Productivity insights** from your email habits

### **Rule Creation with AI**
Let the Assistant help you create powerful automation rules:

1. **Describe what you want** in natural language
2. **Review the suggested rule** structure
3. **Test the rule** against your existing emails
4. **Activate the rule** once you're satisfied

Example conversation:
```
You: "I want to automatically archive emails from LinkedIn"
Assistant: "I can help you create a rule for that. I'll set up a rule to archive emails from LinkedIn automatically. Would you also like to add a label to these emails for easy finding later?"
```

## ⚙️ Automation & Rules

### **Understanding Email Rules**
Rules are automated actions that E-Connect 2 performs on your emails based on conditions you set.

#### **Rule Components**
Every rule has two parts:
1. **Conditions** - When the rule should trigger
2. **Actions** - What the rule should do

#### **Creating Your First Rule**

1. **Go to Automation** in the main navigation
2. **Click "Create New Rule"**
3. **Name your rule** (e.g., "Archive Newsletters")
4. **Set conditions:**
   - Choose condition type (From, Subject, Body, etc.)
   - Select operator (Contains, Equals, Starts with, etc.)
   - Enter the value to match
5. **Add actions:**
   - Choose what to do when conditions are met
   - Configure action parameters if needed
6. **Test the rule** against existing emails
7. **Activate the rule**

#### **Example Rules**

**Newsletter Auto-Archive:**
- **Condition:** Body contains "unsubscribe"
- **Action:** Archive email, Add label "Newsletter"

**Work Email Organization:**
- **Condition:** From contains "@company.com"
- **Action:** Add label "Work", Mark as important

**Receipt Filing:**
- **Condition:** Subject contains "receipt" OR Subject contains "invoice"
- **Action:** Add label "Receipts", Archive email

### **Advanced Rule Features**

#### **Multiple Conditions**
Combine multiple conditions with AND/OR logic:
- **AND:** All conditions must be met
- **OR:** Any condition can trigger the rule

#### **Complex Actions**
Chain multiple actions together:
1. Archive the email
2. Add a label
3. Forward to another address
4. Mark as read

#### **Rule Scheduling**
Choose when rules should run:
- **Immediate** - As soon as email arrives
- **Batch** - Process emails in groups
- **Daily** - Run once per day
- **Weekly** - Run weekly

### **Rule Management**

#### **Viewing Rule Performance**
Track how your rules are performing:
- **Execution count** - How many times the rule has run
- **Success rate** - Percentage of successful executions
- **Time saved** - Estimated time savings from automation
- **Last execution** - When the rule last ran

#### **Editing Rules**
1. **Go to Automation > Rules**
2. **Click the rule** you want to edit
3. **Modify conditions or actions**
4. **Test the updated rule**
5. **Save changes**

#### **Disabling Rules**
Temporarily disable rules without deleting them:
- **Toggle the enabled switch** in the rules list
- **Disabled rules** won't process new emails
- **Re-enable anytime** to resume automation

## 🗑️ Bulk Operations

### **Bulk Unsubscribe**
Clean up your inbox by unsubscribing from multiple newsletters at once.

#### **Scanning for Newsletters**
1. **Go to Bulk Unsubscribe**
2. **Click "Scan for Newsletters"**
3. **Review the detected newsletters**
4. **Select which ones to unsubscribe from**
5. **Choose unsubscribe method:**
   - **Automatic** - E-Connect 2 handles it
   - **Manual** - You handle it yourself

#### **Newsletter Analysis**
For each newsletter, you'll see:
- **Sender name and email**
- **Number of emails received**
- **Frequency** (daily, weekly, monthly)
- **Last email date**
- **Estimated time saved** by unsubscribing

#### **Bulk Unsubscribe Process**
1. **Select newsletters** to unsubscribe from
2. **Choose additional options:**
   - Delete existing emails after unsubscribing
   - Add sender to blocked list
3. **Start the unsubscribe process**
4. **Monitor progress** in real-time
5. **Review results** and handle any failures

### **Clean Inbox**
Use AI to intelligently organize and clean your inbox.

#### **Smart Cleaning Process**
1. **Go to Clean Inbox**
2. **Choose cleaning scope:**
   - Last 30 days
   - Last 3 months
   - Last 6 months
   - All emails
3. **Select cleaning criteria:**
   - Archive old newsletters
   - Delete obvious spam
   - Organize by categories
   - Clean up duplicate emails
4. **Preview changes** before applying
5. **Execute the cleaning**

#### **Cleaning Categories**
The AI identifies several types of emails for cleaning:
- **Newsletters** - Automatically archive or unsubscribe
- **Notifications** - Archive old system notifications
- **Marketing** - Delete promotional emails
- **Social** - Archive social media notifications
- **Receipts** - Organize financial emails

### **Cold Email Blocker**
Protect your inbox from unwanted cold emails and spam.

#### **Setting Up Protection**
1. **Go to Cold Email Blocker**
2. **Configure detection sensitivity:**
   - **Conservative** - Block obvious spam only
   - **Moderate** - Balanced approach (recommended)
   - **Aggressive** - Block anything suspicious
3. **Set up whitelist:**
   - Add trusted senders
   - Add trusted domains
   - Import contacts as trusted

#### **Reviewing Blocked Emails**
1. **Check the "Blocked" folder** regularly
2. **Review blocked emails** for false positives
3. **Unblock legitimate emails** if needed
4. **Add false positives to whitelist**

## 📊 Analytics & Insights

### **Email Statistics Dashboard**
Get comprehensive insights into your email habits and productivity.

#### **Key Metrics**
- **Total emails** received and sent
- **Unread count** and percentage
- **Response time** averages
- **Inbox zero** achievement rate
- **Time saved** through automation

#### **Visual Analytics**

**Email Volume Chart:**
- Shows daily, weekly, or monthly email volume
- Track trends over time
- Compare received vs. sent emails

**Sender Analysis:**
- Top senders by email count
- Sender importance ratings
- Response time by sender

**Category Breakdown:**
- Distribution of email categories
- Trends in email types
- Read vs. unread by category

**Rule Performance:**
- Automation effectiveness
- Time saved through rules
- Rule execution success rates

### **Productivity Insights**
Understand your email productivity patterns:

#### **Response Time Analysis**
- **Average response time** to emails
- **Response time by sender** category
- **Peak response hours** during the day
- **Response time trends** over time

#### **Inbox Management**
- **Inbox zero achievement** frequency
- **Email processing speed**
- **Backlog accumulation** patterns
- **Cleanup effectiveness**

#### **Automation Impact**
- **Rules execution** statistics
- **Time saved** through automation
- **Manual vs. automated** actions
- **Automation opportunities** identified

### **Exporting Data**
Export your analytics data for external analysis:

1. **Choose data to export:**
   - Email statistics
   - Sender analytics
   - Rule performance
   - Time-based patterns
2. **Select date range**
3. **Choose format:**
   - CSV for spreadsheets
   - PNG for charts
   - JSON for developers
4. **Download the export**

## ⚙️ Settings & Customization

### **Account Settings**
Manage your email accounts and preferences.

#### **Adding Email Accounts**
1. **Go to Settings > Account**
2. **Click "Add Account"**
3. **Choose provider:**
   - Gmail
   - Outlook
   - Yahoo
   - IMAP (custom)
   - Exchange
4. **Enter account details**
5. **Authorize the connection**
6. **Configure sync settings**

#### **Account Management**
For each account, you can:
- **Edit account settings**
- **Configure sync frequency**
- **Set account-specific rules**
- **Manage authentication**
- **Remove account**

### **Preferences**
Customize E-Connect 2 to match your workflow.

#### **Email Preferences**
- **Default view** (list, cards, compact)
- **Thread grouping** settings
- **Auto-refresh** intervals
- **Notification** preferences
- **Reading pane** position

#### **UI Preferences**
- **Theme** (light, dark, auto)
- **Color scheme** customization
- **Font size** adjustments
- **Density** (compact, comfortable, spacious)
- **Language** settings

#### **Keyboard Shortcuts**
Customize keyboard shortcuts for:
- **Email actions** (archive, delete, reply)
- **Navigation** (next, previous, search)
- **Views** (inbox, sent, drafts)
- **Bulk actions** (select all, bulk archive)

### **Privacy & Security**
Control your data and privacy settings.

#### **Data Handling**
- **Data retention** periods
- **Local storage** preferences
- **Sync frequency** controls
- **Backup settings**

#### **Privacy Controls**
- **Email tracking** protection
- **Image loading** preferences
- **External content** blocking
- **Read receipt** settings

#### **Security Features**
- **Two-factor authentication**
- **Account isolation** settings
- **Audit log** access
- **Session management**

## 🔧 Advanced Features

### **Multi-Account Management**
Handle multiple email accounts seamlessly.

#### **Account Switching**
- **Quick switcher** in the top navigation
- **Recent accounts** for easy access
- **Keyboard shortcuts** for account switching
- **Visual indicators** for active account

#### **Unified Inbox**
View emails from all accounts in one place:
1. **Enable unified inbox** in settings
2. **Choose accounts** to include
3. **Set sorting preferences**
4. **Configure visual indicators**

#### **Cross-Account Features**
- **Search across all accounts**
- **Unified analytics** and insights
- **Cross-account rules** (if enabled)
- **Backup and sync** all accounts

### **Email Composition**
Create and send emails with advanced features.

#### **Rich Text Editor**
- **Formatting tools** (bold, italic, lists)
- **Font and color** customization
- **Link insertion** and management
- **Image embedding**
- **HTML source** editing

#### **Email Templates**
Save time with reusable templates:
1. **Create templates** for common emails
2. **Insert templates** while composing
3. **Variable substitution** for personalization
4. **Template organization** by category

#### **Attachments**
- **Drag and drop** file attachments
- **Multiple file** selection
- **Attachment preview**
- **Size warnings** for large files

### **Search & Organization**
Advanced tools for finding and organizing emails.

#### **Saved Searches**
Save frequently used search queries:
1. **Create a search** using operators
2. **Save the search** with a name
3. **Access saved searches** from the sidebar
4. **Share searches** with team members

#### **Labels & Folders**
Organize emails with labels and folders:
- **Create custom labels**
- **Color-code labels** for visual organization
- **Nested folders** for hierarchical organization
- **Auto-labeling** rules

#### **Smart Folders**
Dynamic folders that update automatically:
- **Unread emails** from last week
- **Emails requiring action**
- **VIP sender** messages
- **Attachments** from this month

## 🔍 Troubleshooting

### **Common Issues**

#### **Emails Not Loading**
1. **Check internet connection**
2. **Refresh the page** (Ctrl+R or Cmd+R)
3. **Clear browser cache**
4. **Check account authentication**

#### **Rules Not Working**
1. **Verify rule conditions** are correct
2. **Check if rule is enabled**
3. **Test rule** against sample emails
4. **Review rule execution logs**

#### **Sync Issues**
1. **Check account credentials**
2. **Verify internet connection**
3. **Refresh account authentication**
4. **Check sync frequency settings**

#### **Performance Issues**
1. **Close unnecessary browser tabs**
2. **Clear browser cache**
3. **Reduce email list size** (use filters)
4. **Disable unused features**

### **Getting Help**

#### **In-App Help**
- **Press `?`** for keyboard shortcuts
- **Hover over elements** for tooltips
- **Check status indicators** for account health
- **Review logs** in settings

#### **Error Messages**
When you encounter errors:
1. **Note the error message** text
2. **Check your internet connection**
3. **Try refreshing** the page
4. **Review account settings**

## 💡 Tips & Best Practices

### **Inbox Zero Strategy**
Achieve and maintain inbox zero:

1. **Process emails regularly** (2-3 times daily)
2. **Use the 2-minute rule** - if it takes less than 2 minutes, do it now
3. **Archive, don't delete** - keep emails accessible
4. **Use labels and folders** for organization
5. **Set up automation rules** for recurring patterns

### **Rule Creation Tips**
Create effective automation rules:

1. **Start simple** - begin with basic conditions
2. **Test thoroughly** before activating
3. **Review rule performance** regularly
4. **Use specific conditions** to avoid false positives
5. **Combine multiple actions** for efficiency

### **Productivity Maximization**
Get the most out of E-Connect 2:

1. **Learn keyboard shortcuts** for speed
2. **Use bulk actions** for similar emails
3. **Set up templates** for common responses
4. **Enable notifications** for important emails only
5. **Review analytics** to identify improvement areas

### **Security Best Practices**
Keep your email secure:

1. **Use strong passwords** for email accounts
2. **Enable two-factor authentication**
3. **Review permissions** regularly
4. **Be cautious with attachments** from unknown senders
5. **Keep software updated**

## 🎯 Quick Reference

### **Essential Keyboard Shortcuts**
| Action | Shortcut |
|--------|----------|
| Next email | `j` |
| Previous email | `k` |
| Archive | `a` |
| Delete | `#` |
| Reply | `r` |
| Forward | `f` |
| Star | `s` |
| Mark read/unread | `u` |
| Search | `/` |
| Compose | `c` |
| Help | `?` |

### **Search Operators Quick Reference**
| Operator | Example | Description |
|----------|---------|-------------|
| `from:` | `from:<EMAIL>` | Emails from specific sender |
| `to:` | `to:<EMAIL>` | Emails to specific recipient |
| `subject:` | `subject:"meeting notes"` | Search in subject line |
| `has:` | `has:attachment` | Emails with attachments |
| `is:` | `is:unread` | Emails with specific status |
| `label:` | `label:important` | Emails with specific label |
| `category:` | `category:work` | Emails in specific category |
| `after:` | `after:2024/01/01` | Emails after date |
| `before:` | `before:2024/12/31` | Emails before date |

### **Getting Started Checklist**
- [ ] Explore the sample data
- [ ] Set up your first automation rule
- [ ] Try the AI assistant
- [ ] Configure keyboard shortcuts
- [ ] Customize your theme
- [ ] Add additional email accounts
- [ ] Set up bulk unsubscribe
- [ ] Review analytics dashboard
- [ ] Create email templates
- [ ] Configure notification preferences

---

**Congratulations!** You're now ready to master email management with E-Connect 2. Remember, the key to email productivity is consistent habits and smart automation. Start small, use the AI assistant for guidance, and gradually build up your automation rules as you discover patterns in your email workflow.

For additional help or questions, refer to the in-app help system by pressing `?` at any time.