import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Rule, RuleExecution, RuleTemplate, RuleGroup, RuleTesting, RuleStats } from '@/types/rules';

interface RulesState {
  // State
  rules: Rule[];
  executions: RuleExecution[];
  templates: RuleTemplate[];
  groups: RuleGroup[];
  selectedRule: Rule | null;
  isLoading: boolean;
  error: string | null;
  
  // Filtering and sorting
  searchQuery: string;
  selectedTags: string[];
  enabledFilter: 'all' | 'enabled' | 'disabled';
  sortBy: 'name' | 'priority' | 'created' | 'lastTriggered' | 'matches';
  sortOrder: 'asc' | 'desc';
  
  // Bulk operations
  selectedRules: string[];
  
  // Testing
  currentTest: RuleTesting | null;
  testResults: any;
  
  // Performance data
  performanceData: {
    avgExecutionTime: number;
    totalRules: number;
    activeRules: number;
    totalExecutions: number;
    successRate: number;
  };
}

interface RulesActions {
  // Rule CRUD operations
  fetchRules: () => Promise<void>;
  createRule: (rule: Omit<Rule, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Rule>;
  updateRule: (id: string, updates: Partial<Rule>) => Promise<Rule>;
  deleteRule: (id: string) => Promise<void>;
  duplicateRule: (id: string) => Promise<Rule>;
  
  // Rule state management
  toggleRule: (id: string) => Promise<void>;
  setRulePriority: (id: string, priority: number) => Promise<void>;
  
  // Bulk operations
  selectRule: (id: string) => void;
  selectAllRules: () => void;
  clearSelection: () => void;
  bulkToggleRules: (enabled: boolean) => Promise<void>;
  bulkDeleteRules: () => Promise<void>;
  bulkUpdatePriority: (priority: number) => Promise<void>;
  
  // Rule testing
  testRule: (ruleId: string, testCases: any[]) => Promise<any>;
  runBatchTest: (ruleIds: string[], emailSample: any[]) => Promise<any>;
  clearTestResults: () => void;
  
  // Rule execution and history
  fetchExecutions: (ruleId?: string) => Promise<void>;
  getExecutionStats: (ruleId: string, days?: number) => Promise<RuleStats>;
  rollbackExecution: (executionId: string) => Promise<void>;
  
  // Templates
  fetchTemplates: () => Promise<void>;
  createRuleFromTemplate: (templateId: string, customizations?: any) => Promise<Rule>;
  saveAsTemplate: (ruleId: string, templateData: Partial<RuleTemplate>) => Promise<RuleTemplate>;
  
  // Groups
  fetchGroups: () => Promise<void>;
  createGroup: (group: Omit<RuleGroup, 'id'>) => Promise<RuleGroup>;
  updateGroup: (id: string, updates: Partial<RuleGroup>) => Promise<RuleGroup>;
  deleteGroup: (id: string) => Promise<void>;
  addRuleToGroup: (groupId: string, ruleId: string) => Promise<void>;
  removeRuleFromGroup: (groupId: string, ruleId: string) => Promise<void>;
  
  // Import/Export
  exportRules: (ruleIds?: string[]) => Promise<string>;
  importRules: (data: string) => Promise<Rule[]>;
  
  // Filtering and sorting
  setSearchQuery: (query: string) => void;
  setSelectedTags: (tags: string[]) => void;
  setEnabledFilter: (filter: 'all' | 'enabled' | 'disabled') => void;
  setSorting: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  
  // UI state
  setSelectedRule: (rule: Rule | null) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Performance monitoring
  updatePerformanceData: () => Promise<void>;
  
  // Computed getters
  getFilteredRules: () => Rule[];
  getRulesByGroup: (groupId: string) => Rule[];
  getExecutionsByRule: (ruleId: string) => RuleExecution[];
  getRuleConflicts: () => Array<{ ruleId1: string; ruleId2: string; reason: string }>;
  
  // Initialize
  initialize: () => Promise<void>;
}

type RulesStore = RulesState & RulesActions;

const initialState: RulesState = {
  rules: [],
  executions: [],
  templates: [],
  groups: [],
  selectedRule: null,
  isLoading: false,
  error: null,
  searchQuery: '',
  selectedTags: [],
  enabledFilter: 'all',
  sortBy: 'priority',
  sortOrder: 'asc',
  selectedRules: [],
  currentTest: null,
  testResults: null,
  performanceData: {
    avgExecutionTime: 0,
    totalRules: 0,
    activeRules: 0,
    totalExecutions: 0,
    successRate: 0
  }
};

export const useRulesStore = create<RulesStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Rule CRUD operations
      fetchRules: async () => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch('/api/rules');
          if (!response.ok) throw new Error('Failed to fetch rules');
          const rules = await response.json();
          set({ rules, isLoading: false });
          get().updatePerformanceData();
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
        }
      },

      createRule: async (ruleData) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch('/api/rules', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(ruleData)
          });
          if (!response.ok) throw new Error('Failed to create rule');
          const newRule = await response.json();
          set(state => ({ 
            rules: [...state.rules, newRule], 
            isLoading: false 
          }));
          get().updatePerformanceData();
          return newRule;
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
          throw error;
        }
      },

      updateRule: async (id, updates) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch(`/api/rules/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updates)
          });
          if (!response.ok) throw new Error('Failed to update rule');
          const updatedRule = await response.json();
          set(state => ({
            rules: state.rules.map(rule => rule.id === id ? updatedRule : rule),
            selectedRule: state.selectedRule?.id === id ? updatedRule : state.selectedRule,
            isLoading: false
          }));
          return updatedRule;
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
          throw error;
        }
      },

      deleteRule: async (id) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch(`/api/rules/${id}`, { method: 'DELETE' });
          if (!response.ok) throw new Error('Failed to delete rule');
          set(state => ({
            rules: state.rules.filter(rule => rule.id !== id),
            selectedRules: state.selectedRules.filter(ruleId => ruleId !== id),
            selectedRule: state.selectedRule?.id === id ? null : state.selectedRule,
            isLoading: false
          }));
          get().updatePerformanceData();
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
        }
      },

      duplicateRule: async (id) => {
        const originalRule = get().rules.find(rule => rule.id === id);
        if (!originalRule) throw new Error('Rule not found');
        
        const duplicatedData = {
          ...originalRule,
          name: `${originalRule.name} (Copy)`,
          enabled: false,
          stats: undefined
        };
        
        return get().createRule(duplicatedData);
      },

      toggleRule: async (id) => {
        const rule = get().rules.find(r => r.id === id);
        if (!rule) throw new Error('Rule not found');
        await get().updateRule(id, { enabled: !rule.enabled });
      },

      setRulePriority: async (id, priority) => {
        await get().updateRule(id, { priority });
      },

      // Bulk operations
      selectRule: (id) => {
        set(state => ({
          selectedRules: state.selectedRules.includes(id)
            ? state.selectedRules.filter(ruleId => ruleId !== id)
            : [...state.selectedRules, id]
        }));
      },

      selectAllRules: () => {
        const filteredRules = get().getFilteredRules();
        set({ selectedRules: filteredRules.map(rule => rule.id) });
      },

      clearSelection: () => {
        set({ selectedRules: [] });
      },

      bulkToggleRules: async (enabled) => {
        const { selectedRules } = get();
        const promises = selectedRules.map(id => get().updateRule(id, { enabled }));
        await Promise.all(promises);
        set({ selectedRules: [] });
      },

      bulkDeleteRules: async () => {
        const { selectedRules } = get();
        const promises = selectedRules.map(id => get().deleteRule(id));
        await Promise.all(promises);
        set({ selectedRules: [] });
      },

      bulkUpdatePriority: async (priority) => {
        const { selectedRules } = get();
        const promises = selectedRules.map(id => get().updateRule(id, { priority }));
        await Promise.all(promises);
        set({ selectedRules: [] });
      },

      // Rule testing
      testRule: async (ruleId, testCases) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch(`/api/rules/${ruleId}/test`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ testCases })
          });
          if (!response.ok) throw new Error('Failed to test rule');
          const results = await response.json();
          set({ testResults: results, isLoading: false });
          return results;
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
          throw error;
        }
      },

      runBatchTest: async (ruleIds, emailSample) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch('/api/rules/batch-test', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ruleIds, emailSample })
          });
          if (!response.ok) throw new Error('Failed to run batch test');
          const results = await response.json();
          set({ testResults: results, isLoading: false });
          return results;
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
          throw error;
        }
      },

      clearTestResults: () => {
        set({ testResults: null, currentTest: null });
      },

      // Rule execution and history
      fetchExecutions: async (ruleId) => {
        set({ isLoading: true, error: null });
        try {
          const url = ruleId ? `/api/rules/${ruleId}/executions` : '/api/executions';
          const response = await fetch(url);
          if (!response.ok) throw new Error('Failed to fetch executions');
          const executions = await response.json();
          set({ executions, isLoading: false });
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
        }
      },

      getExecutionStats: async (ruleId, days = 30) => {
        try {
          const response = await fetch(`/api/rules/${ruleId}/stats?days=${days}`);
          if (!response.ok) throw new Error('Failed to fetch execution stats');
          return await response.json();
        } catch (error) {
          set({ error: (error as Error).message });
          throw error;
        }
      },

      rollbackExecution: async (executionId) => {
        set({ isLoading: true, error: null });
        try {
          const response = await fetch(`/api/executions/${executionId}/rollback`, {
            method: 'POST'
          });
          if (!response.ok) throw new Error('Failed to rollback execution');
          await get().fetchExecutions();
        } catch (error) {
          set({ error: (error as Error).message, isLoading: false });
        }
      },

      // Templates
      fetchTemplates: async () => {
        try {
          const response = await fetch('/api/rule-templates');
          if (!response.ok) throw new Error('Failed to fetch templates');
          const templates = await response.json();
          set({ templates });
        } catch (error) {
          set({ error: (error as Error).message });
        }
      },

      createRuleFromTemplate: async (templateId, customizations = {}) => {
        const template = get().templates.find(t => t.id === templateId);
        if (!template) throw new Error('Template not found');
        
        const ruleData = {
          ...template.rule,
          ...customizations,
          name: customizations.name || template.rule.name || template.name
        };
        
        return get().createRule(ruleData);
      },

      saveAsTemplate: async (ruleId, templateData) => {
        const rule = get().rules.find(r => r.id === ruleId);
        if (!rule) throw new Error('Rule not found');
        
        try {
          const response = await fetch('/api/rule-templates', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              ...templateData,
              rule: {
                name: rule.name,
                description: rule.description,
                conditions: rule.conditions,
                conditionLogic: rule.conditionLogic,
                actions: rule.actions,
                priority: rule.priority,
                schedule: rule.schedule,
                exceptions: rule.exceptions
              }
            })
          });
          if (!response.ok) throw new Error('Failed to save template');
          const newTemplate = await response.json();
          set(state => ({ templates: [...state.templates, newTemplate] }));
          return newTemplate;
        } catch (error) {
          set({ error: (error as Error).message });
          throw error;
        }
      },

      // Groups
      fetchGroups: async () => {
        try {
          const response = await fetch('/api/rule-groups');
          if (!response.ok) throw new Error('Failed to fetch groups');
          const groups = await response.json();
          set({ groups });
        } catch (error) {
          set({ error: (error as Error).message });
        }
      },

      createGroup: async (groupData) => {
        try {
          const response = await fetch('/api/rule-groups', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(groupData)
          });
          if (!response.ok) throw new Error('Failed to create group');
          const newGroup = await response.json();
          set(state => ({ groups: [...state.groups, newGroup] }));
          return newGroup;
        } catch (error) {
          set({ error: (error as Error).message });
          throw error;
        }
      },

      updateGroup: async (id, updates) => {
        try {
          const response = await fetch(`/api/rule-groups/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updates)
          });
          if (!response.ok) throw new Error('Failed to update group');
          const updatedGroup = await response.json();
          set(state => ({
            groups: state.groups.map(group => group.id === id ? updatedGroup : group)
          }));
          return updatedGroup;
        } catch (error) {
          set({ error: (error as Error).message });
          throw error;
        }
      },

      deleteGroup: async (id) => {
        try {
          const response = await fetch(`/api/rule-groups/${id}`, { method: 'DELETE' });
          if (!response.ok) throw new Error('Failed to delete group');
          set(state => ({ groups: state.groups.filter(group => group.id !== id) }));
        } catch (error) {
          set({ error: (error as Error).message });
        }
      },

      addRuleToGroup: async (groupId, ruleId) => {
        const group = get().groups.find(g => g.id === groupId);
        if (!group) throw new Error('Group not found');
        
        const updatedRules = [...group.rules, ruleId];
        await get().updateGroup(groupId, { rules: updatedRules });
      },

      removeRuleFromGroup: async (groupId, ruleId) => {
        const group = get().groups.find(g => g.id === groupId);
        if (!group) throw new Error('Group not found');
        
        const updatedRules = group.rules.filter(id => id !== ruleId);
        await get().updateGroup(groupId, { rules: updatedRules });
      },

      // Import/Export
      exportRules: async (ruleIds) => {
        const { rules } = get();
        const rulesToExport = ruleIds 
          ? rules.filter(rule => ruleIds.includes(rule.id))
          : rules;
        
        const exportData = {
          version: '1.0',
          timestamp: new Date().toISOString(),
          rules: rulesToExport.map(rule => ({
            ...rule,
            id: undefined, // Remove ID for import
            stats: undefined, // Remove stats
            createdAt: undefined,
            updatedAt: undefined,
            lastTriggered: undefined
          }))
        };
        
        return JSON.stringify(exportData, null, 2);
      },

      importRules: async (data) => {
        try {
          const importData = JSON.parse(data);
          const rules = importData.rules || [];
          
          const createPromises = rules.map((rule: any) => get().createRule(rule));
          const createdRules = await Promise.all(createPromises);
          
          return createdRules;
        } catch (error) {
          set({ error: (error as Error).message });
          throw error;
        }
      },

      // Filtering and sorting
      setSearchQuery: (query) => set({ searchQuery: query }),
      setSelectedTags: (tags) => set({ selectedTags: tags }),
      setEnabledFilter: (filter) => set({ enabledFilter: filter }),
      setSorting: (sortBy, sortOrder) => set({ sortBy, sortOrder }),

      // UI state
      setSelectedRule: (rule) => set({ selectedRule: rule }),
      setError: (error) => set({ error }),
      clearError: () => set({ error: null }),

      // Performance monitoring
      updatePerformanceData: async () => {
        const { rules, executions } = get();
        const activeRules = rules.filter(rule => rule.enabled).length;
        const totalExecutions = executions.length;
        const successfulExecutions = executions.filter(exec => exec.success).length;
        const avgExecutionTime = executions.length > 0
          ? executions.reduce((sum, exec) => sum + exec.totalTime, 0) / executions.length
          : 0;
        
        set({
          performanceData: {
            avgExecutionTime,
            totalRules: rules.length,
            activeRules,
            totalExecutions,
            successRate: totalExecutions > 0 ? successfulExecutions / totalExecutions : 0
          }
        });
      },

      // Computed getters
      getFilteredRules: () => {
        const { rules, searchQuery, selectedTags, enabledFilter, sortBy, sortOrder } = get();
        
        let filtered = rules.filter(rule => {
          // Search filter
          if (searchQuery) {
            const query = searchQuery.toLowerCase();
            if (!rule.name.toLowerCase().includes(query) && 
                !rule.description?.toLowerCase().includes(query) &&
                !rule.tags?.some(tag => tag.toLowerCase().includes(query))) {
              return false;
            }
          }
          
          // Tags filter
          if (selectedTags.length > 0) {
            if (!rule.tags?.some(tag => selectedTags.includes(tag))) {
              return false;
            }
          }
          
          // Enabled filter
          if (enabledFilter === 'enabled' && !rule.enabled) return false;
          if (enabledFilter === 'disabled' && rule.enabled) return false;
          
          return true;
        });
        
        // Sort
        filtered.sort((a, b) => {
          let aVal: any, bVal: any;
          
          switch (sortBy) {
            case 'name':
              aVal = a.name.toLowerCase();
              bVal = b.name.toLowerCase();
              break;
            case 'priority':
              aVal = a.priority;
              bVal = b.priority;
              break;
            case 'created':
              aVal = a.createdAt.getTime();
              bVal = b.createdAt.getTime();
              break;
            case 'lastTriggered':
              aVal = a.lastTriggered?.getTime() || 0;
              bVal = b.lastTriggered?.getTime() || 0;
              break;
            case 'matches':
              aVal = a.stats?.totalMatches || 0;
              bVal = b.stats?.totalMatches || 0;
              break;
            default:
              return 0;
          }
          
          if (aVal < bVal) return sortOrder === 'asc' ? -1 : 1;
          if (aVal > bVal) return sortOrder === 'asc' ? 1 : -1;
          return 0;
        });
        
        return filtered;
      },

      getRulesByGroup: (groupId) => {
        const { rules, groups } = get();
        const group = groups.find(g => g.id === groupId);
        if (!group) return [];
        return rules.filter(rule => group.rules.includes(rule.id));
      },

      getExecutionsByRule: (ruleId) => {
        return get().executions.filter(exec => exec.ruleId === ruleId);
      },

      getRuleConflicts: () => {
        const { rules } = get();
        const conflicts: Array<{ ruleId1: string; ruleId2: string; reason: string }> = [];
        
        // Check for priority conflicts
        const enabledRules = rules.filter(rule => rule.enabled);
        const priorityGroups = enabledRules.reduce((groups, rule) => {
          if (!groups[rule.priority]) groups[rule.priority] = [];
          groups[rule.priority].push(rule);
          return groups;
        }, {} as Record<number, Rule[]>);
        
        Object.entries(priorityGroups).forEach(([priority, rulesInPriority]) => {
          if (rulesInPriority.length > 1) {
            for (let i = 0; i < rulesInPriority.length; i++) {
              for (let j = i + 1; j < rulesInPriority.length; j++) {
                conflicts.push({
                  ruleId1: rulesInPriority[i].id,
                  ruleId2: rulesInPriority[j].id,
                  reason: `Same priority level (${priority}) may cause execution order conflicts`
                });
              }
            }
          }
        });
        
        return conflicts;
      },

      // Initialize store
      initialize: async () => {
        try {
          // Load essential data
          await Promise.all([
            get().fetchRules(),
            get().fetchTemplates(),
            get().fetchGroups(),
            get().fetchExecutions()
          ])
        } catch (error) {
          console.warn('Failed to initialize rules store:', error)
        }
      }
    }),
    { name: 'rules-store' }
  )
);