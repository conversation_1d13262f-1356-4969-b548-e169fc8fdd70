import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { 
  ColdEmailDetection, 
  BlockedSender, 
  WhitelistedSender,
  ColdEmailSettings,
  ColdEmailAnalytics,
  ColdEmailExport,
  TrainingData
} from '../types/cold-email';

interface ColdEmailStore {
  // State
  detections: ColdEmailDetection[];
  blockedSenders: BlockedSender[];
  whitelistedSenders: WhitelistedSender[];
  settings: ColdEmailSettings | null;
  analytics: ColdEmailAnalytics | null;
  exports: ColdEmailExport[];
  
  // Loading states
  loading: {
    detections: boolean;
    senders: boolean;
    analytics: boolean;
    settings: boolean;
  };
  
  // Error states
  errors: {
    detections?: string;
    senders?: string;
    analytics?: string;
    settings?: string;
  };
  
  // Actions - Detections
  loadDetections: () => Promise<void>;
  reviewDetection: (id: string, decision: 'block' | 'allow' | 'whitelist', feedback?: any) => Promise<void>;
  bulkReviewDetections: (ids: string[], decision: 'block' | 'allow') => Promise<void>;
  
  // Actions - Senders
  loadSenders: () => Promise<void>;
  blockSender: (sender: Partial<BlockedSender>) => Promise<void>;
  whitelistSender: (sender: Partial<WhitelistedSender>) => Promise<void>;
  removeSender: (id: string, type: 'blocked' | 'whitelisted') => Promise<void>;
  importSenders: (file: File, type: 'blocked' | 'whitelisted') => Promise<void>;
  exportSenders: (type: 'blocked' | 'whitelisted') => Promise<void>;
  
  // Actions - Analytics
  loadAnalytics: (period?: { start: Date; end: Date }) => Promise<void>;
  
  // Actions - Settings
  loadSettings: () => Promise<void>;
  updateSettings: (settings: Partial<ColdEmailSettings>) => Promise<void>;
  
  // Actions - Training
  submitTrainingData: (data: TrainingData) => Promise<void>;
  
  // Actions - Real-time
  detectEmail: (emailId: string) => Promise<{ detection: ColdEmailDetection; shouldBlock: boolean }>;
  
  // Actions - Export
  createExport: (options: {
    type: 'blocked' | 'allowed' | 'all';
    format: 'csv' | 'json' | 'pdf';
    dateRange: { start: Date; end: Date };
  }) => Promise<void>;
  
  // Utility actions
  clearErrors: () => void;
  reset: () => void;
  
  // Initialize
  initialize: () => Promise<void>;
}

const initialState = {
  detections: [],
  blockedSenders: [],
  whitelistedSenders: [],
  settings: null,
  analytics: null,
  exports: [],
  loading: {
    detections: false,
    senders: false,
    analytics: false,
    settings: false,
  },
  errors: {},
};

export const useColdEmailStore = create<ColdEmailStore>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // Detections
      loadDetections: async () => {
        set(state => ({ 
          loading: { ...state.loading, detections: true },
          errors: { ...state.errors, detections: undefined }
        }));
        
        try {
          const response = await fetch('/api/cold-email/detections');
          if (!response.ok) throw new Error('Failed to load detections');
          
          const data = await response.json();
          set({ 
            detections: data.detections,
            loading: { ...get().loading, detections: false }
          });
        } catch (error) {
          set(state => ({ 
            loading: { ...state.loading, detections: false },
            errors: { ...state.errors, detections: error.message }
          }));
        }
      },
      
      reviewDetection: async (id, decision, feedback) => {
        try {
          const response = await fetch(`/api/cold-email/detections/${id}/review`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ decision, feedback })
          });
          
          if (!response.ok) throw new Error('Failed to review detection');
          
          // Remove from detections list
          set(state => ({
            detections: state.detections.filter(d => d.id !== id)
          }));
          
          // If whitelisting, add to whitelist
          if (decision === 'whitelist') {
            const detection = get().detections.find(d => d.id === id);
            if (detection) {
              await get().whitelistSender({
                email: detection.email.from,
                type: 'email',
                reason: 'Whitelisted from review queue'
              });
            }
          }
          
          // Refresh analytics
          await get().loadAnalytics();
        } catch (error) {
          console.error('Failed to review detection:', error);
        }
      },
      
      bulkReviewDetections: async (ids, decision) => {
        try {
          const response = await fetch('/api/cold-email/detections/bulk-review', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ ids, decision })
          });
          
          if (!response.ok) throw new Error('Failed to bulk review');
          
          // Remove from detections list
          set(state => ({
            detections: state.detections.filter(d => !ids.includes(d.id))
          }));
          
          // Refresh analytics
          await get().loadAnalytics();
        } catch (error) {
          console.error('Failed to bulk review:', error);
        }
      },
      
      // Senders
      loadSenders: async () => {
        set(state => ({ 
          loading: { ...state.loading, senders: true },
          errors: { ...state.errors, senders: undefined }
        }));
        
        try {
          const [blocklistRes, whitelistRes] = await Promise.all([
            fetch('/api/cold-email/blocklist'),
            fetch('/api/cold-email/whitelist')
          ]);
          
          if (!blocklistRes.ok || !whitelistRes.ok) {
            throw new Error('Failed to load senders');
          }
          
          const [blocklistData, whitelistData] = await Promise.all([
            blocklistRes.json(),
            whitelistRes.json()
          ]);
          
          set({ 
            blockedSenders: blocklistData.senders,
            whitelistedSenders: whitelistData.senders,
            loading: { ...get().loading, senders: false }
          });
        } catch (error) {
          set(state => ({ 
            loading: { ...state.loading, senders: false },
            errors: { ...state.errors, senders: error.message }
          }));
        }
      },
      
      blockSender: async (sender) => {
        try {
          const response = await fetch('/api/cold-email/blocklist', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(sender)
          });
          
          if (!response.ok) throw new Error('Failed to block sender');
          
          const newSender = await response.json();
          set(state => ({
            blockedSenders: [...state.blockedSenders, newSender]
          }));
        } catch (error) {
          console.error('Failed to block sender:', error);
        }
      },
      
      whitelistSender: async (sender) => {
        try {
          const response = await fetch('/api/cold-email/whitelist', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(sender)
          });
          
          if (!response.ok) throw new Error('Failed to whitelist sender');
          
          const newSender = await response.json();
          set(state => ({
            whitelistedSenders: [...state.whitelistedSenders, newSender]
          }));
        } catch (error) {
          console.error('Failed to whitelist sender:', error);
        }
      },
      
      removeSender: async (id, type) => {
        try {
          const endpoint = type === 'blocked' ? 'blocklist' : 'whitelist';
          const response = await fetch(`/api/cold-email/${endpoint}/${id}`, {
            method: 'DELETE'
          });
          
          if (!response.ok) throw new Error('Failed to remove sender');
          
          if (type === 'blocked') {
            set(state => ({
              blockedSenders: state.blockedSenders.filter(s => s.id !== id)
            }));
          } else {
            set(state => ({
              whitelistedSenders: state.whitelistedSenders.filter(s => s.id !== id)
            }));
          }
        } catch (error) {
          console.error('Failed to remove sender:', error);
        }
      },
      
      importSenders: async (file, type) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);
        
        try {
          const response = await fetch('/api/cold-email/import', {
            method: 'POST',
            body: formData
          });
          
          if (!response.ok) throw new Error('Failed to import');
          
          // Reload senders
          await get().loadSenders();
        } catch (error) {
          console.error('Failed to import:', error);
        }
      },
      
      exportSenders: async (type) => {
        try {
          const response = await fetch(`/api/cold-email/export?type=${type}`);
          if (!response.ok) throw new Error('Failed to export');
          
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `${type}-senders-${new Date().toISOString().split('T')[0]}.csv`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
          window.URL.revokeObjectURL(url);
        } catch (error) {
          console.error('Failed to export:', error);
        }
      },
      
      // Analytics
      loadAnalytics: async (period) => {
        set(state => ({ 
          loading: { ...state.loading, analytics: true },
          errors: { ...state.errors, analytics: undefined }
        }));
        
        try {
          const params = new URLSearchParams();
          if (period) {
            params.append('start', period.start.toISOString());
            params.append('end', period.end.toISOString());
          }
          
          const response = await fetch(`/api/cold-email/analytics?${params}`);
          if (!response.ok) throw new Error('Failed to load analytics');
          
          const data = await response.json();
          set({ 
            analytics: data,
            loading: { ...get().loading, analytics: false }
          });
        } catch (error) {
          set(state => ({ 
            loading: { ...state.loading, analytics: false },
            errors: { ...state.errors, analytics: error.message }
          }));
        }
      },
      
      // Settings
      loadSettings: async () => {
        set(state => ({ 
          loading: { ...state.loading, settings: true },
          errors: { ...state.errors, settings: undefined }
        }));
        
        try {
          const response = await fetch('/api/cold-email/settings');
          if (!response.ok) throw new Error('Failed to load settings');
          
          const data = await response.json();
          set({ 
            settings: data,
            loading: { ...get().loading, settings: false }
          });
        } catch (error) {
          set(state => ({ 
            loading: { ...state.loading, settings: false },
            errors: { ...state.errors, settings: error.message }
          }));
        }
      },
      
      updateSettings: async (updates) => {
        const currentSettings = get().settings;
        if (!currentSettings) return;
        
        const newSettings = { ...currentSettings, ...updates };
        set({ settings: newSettings });
        
        try {
          const response = await fetch('/api/cold-email/settings', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(newSettings)
          });
          
          if (!response.ok) throw new Error('Failed to update settings');
        } catch (error) {
          // Revert on error
          set({ settings: currentSettings });
          console.error('Failed to update settings:', error);
        }
      },
      
      // Training
      submitTrainingData: async (data) => {
        try {
          const response = await fetch('/api/cold-email/train', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
          });
          
          if (!response.ok) throw new Error('Failed to submit training data');
          
          // Refresh analytics to show updated model performance
          await get().loadAnalytics();
        } catch (error) {
          console.error('Failed to submit training data:', error);
        }
      },
      
      // Real-time detection
      detectEmail: async (emailId) => {
        try {
          const response = await fetch('/api/cold-email/detect', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ emailId })
          });
          
          if (!response.ok) throw new Error('Failed to detect email');
          
          const result = await response.json();
          
          // Add to detections if it requires review
          if (!result.shouldBlock && get().settings?.actions.requireReview) {
            set(state => ({
              detections: [result.detection, ...state.detections]
            }));
          }
          
          return result;
        } catch (error) {
          console.error('Failed to detect email:', error);
          throw error;
        }
      },
      
      // Export
      createExport: async (options) => {
        try {
          const response = await fetch('/api/cold-email/export/create', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(options)
          });
          
          if (!response.ok) throw new Error('Failed to create export');
          
          const exportData = await response.json();
          set(state => ({
            exports: [...state.exports, exportData]
          }));
        } catch (error) {
          console.error('Failed to create export:', error);
        }
      },
      
      // Utility
      clearErrors: () => set({ errors: {} }),
      
      reset: () => set(initialState),
      
      // Initialize store
      initialize: async () => {
        try {
          // Load essential data
          await Promise.all([
            get().loadDetections(),
            get().loadSenders(),
            get().loadSettings(),
            get().loadAnalytics()
          ])
        } catch (error) {
          console.warn('Failed to initialize cold email store:', error)
        }
      },
    }),
    {
      name: 'cold-email-store',
    }
  )
);