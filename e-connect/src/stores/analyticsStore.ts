import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { useMemo } from 'react'
import { 
  EmailStats, 
  SenderStats, 
  CategoryStats, 
  VolumeData,
  ResponsePattern,
  LabelStats,
  AttachmentStats,
  PerformanceMetrics,
  AnalyticsEvent
} from '@/types/analytics'

// Cache configuration
interface CacheConfig {
  ttl: number // Time to live in milliseconds
  maxSize: number // Maximum number of cached items
}

interface CachedData<T> {
  data: T
  timestamp: number
  key: string
}

// Filter configuration for analytics
interface AnalyticsFilters {
  dateRange: {
    start: Date
    end: Date
    label: string
    timezone?: string
  }
  categories?: string[]
  senders?: string[]
  importance?: ('high' | 'medium' | 'low')[]
  tags?: string[]
  granularity: 'hourly' | 'daily' | 'weekly' | 'monthly'
  limit?: number
  offset?: number
}

// Performance optimization settings
interface PerformanceSettings {
  enableCaching: boolean
  enablePrefetch: boolean
  enableRealTime: boolean
  batchSize: number
  debounceMs: number
}

interface AnalyticsState {
  // Core analytics data
  emailStats: EmailStats | null
  senderAnalytics: SenderStats[] | null
  timeAnalysis: any | null
  rulePerformance: any | null
  categoryBreakdown: CategoryStats[] | null
  responsePatterns: ResponsePattern[] | null
  labelStats: LabelStats[] | null
  attachmentStats: AttachmentStats | null
  performanceMetrics: PerformanceMetrics | null
  
  // Advanced analytics
  trends: Record<string, number> | null
  anomalies: any[] | null
  correlations: Record<string, number> | null
  predictions: any | null
  
  // Cache management
  cache: Map<string, CachedData<any>>
  cacheConfig: CacheConfig
  
  // Loading states
  isLoadingStats: boolean
  isLoadingSenders: boolean
  isLoadingTime: boolean
  isLoadingRules: boolean
  isLoadingCategories: boolean
  isLoadingTrends: boolean
  isExporting: boolean
  
  // Filter and pagination state
  filters: AnalyticsFilters
  dateRange: {
    start: Date
    end: Date
    label: string
    timezone?: string
  }
  
  // Performance settings
  performance: PerformanceSettings
  isRealTimeEnabled: boolean
  
  // Real-time data
  realTimeEvents: AnalyticsEvent[]
  lastUpdateTimestamp: number | null
  
  // Error handling
  errors: Record<string, string | null>
  
  // Actions - Data Management
  setEmailStats: (stats: EmailStats) => void
  setSenderAnalytics: (analytics: SenderStats[]) => void
  setTimeAnalysis: (analysis: any) => void
  setRulePerformance: (performance: any) => void
  setCategoryBreakdown: (categories: CategoryStats[]) => void
  setResponsePatterns: (patterns: ResponsePattern[]) => void
  setLabelStats: (stats: LabelStats[]) => void
  setAttachmentStats: (stats: AttachmentStats) => void
  setPerformanceMetrics: (metrics: PerformanceMetrics) => void
  
  // Actions - Advanced Analytics
  setTrends: (trends: Record<string, number>) => void
  setAnomalies: (anomalies: any[]) => void
  setCorrelations: (correlations: Record<string, number>) => void
  setPredictions: (predictions: any) => void
  
  // Actions - State Management
  setFilters: (filters: Partial<AnalyticsFilters>) => void
  setLoading: (type: string, loading: boolean) => void
  setError: (type: string, error: string | null) => void
  setPerformanceSettings: (settings: Partial<PerformanceSettings>) => void
  
  // Actions - Cache Management
  getCachedData: <T>(key: string) => T | null
  setCachedData: <T>(key: string, data: T) => void
  clearCache: () => void
  invalidateCache: (pattern?: string) => void
  
  // Actions - Data Fetching
  fetchEmailStats: (forceRefresh?: boolean) => Promise<void>
  fetchSenderAnalytics: (forceRefresh?: boolean) => Promise<void>
  fetchTimeAnalysis: (forceRefresh?: boolean) => Promise<void>
  fetchRulePerformance: (forceRefresh?: boolean) => Promise<void>
  fetchCategoryBreakdown: (forceRefresh?: boolean) => Promise<void>
  fetchResponsePatterns: (forceRefresh?: boolean) => Promise<void>
  fetchLabelStats: (forceRefresh?: boolean) => Promise<void>
  fetchAttachmentStats: (forceRefresh?: boolean) => Promise<void>
  fetchPerformanceMetrics: (forceRefresh?: boolean) => Promise<void>
  
  // Actions - Advanced Analytics
  calculateTrends: () => Promise<void>
  detectAnomalies: () => Promise<void>
  calculateCorrelations: () => Promise<void>
  generatePredictions: () => Promise<void>
  
  // Actions - Batch Operations
  fetchAllData: (forceRefresh?: boolean) => Promise<void>
  prefetchData: () => Promise<void>
  refreshData: () => Promise<void>
  
  // Actions - Export
  exportData: (format: 'csv' | 'json' | 'pdf', type: string, options?: any) => Promise<void>
  exportMultipleDatasets: (exports: Array<{format: string, type: string, options?: any}>) => Promise<void>
  
  // Actions - Real-time
  enableRealTimeUpdates: () => void
  disableRealTimeUpdates: () => void
  addRealTimeEvent: (event: AnalyticsEvent) => void
  
  // Actions - Utilities
  aggregateData: (data: any[], period: string) => any[]
  compareDatasets: (dataset1: any[], dataset2: any[]) => any
  generateReport: (template: string) => Promise<any>
  
  // Initialize
  initialize: () => Promise<void>
}

export const useAnalyticsStore = create<AnalyticsState>()(subscribeWithSelector((set, get) => ({
  // Initial state - Core data
  emailStats: null,
  senderAnalytics: null,
  timeAnalysis: null,
  rulePerformance: null,
  categoryBreakdown: null,
  responsePatterns: null,
  labelStats: null,
  attachmentStats: null,
  performanceMetrics: null,
  
  // Advanced analytics
  trends: null,
  anomalies: null,
  correlations: null,
  predictions: null,
  
  // Cache
  cache: new Map(),
  cacheConfig: {
    ttl: 5 * 60 * 1000, // 5 minutes
    maxSize: 100
  },
  
  // Loading states
  isLoadingStats: false,
  isLoadingSenders: false,
  isLoadingTime: false,
  isLoadingRules: false,
  isLoadingCategories: false,
  isLoadingTrends: false,
  isExporting: false,
  
  // Filters
  filters: {
    dateRange: {
      start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      end: new Date(),
      label: 'Last 7 days',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    },
    granularity: 'daily',
    limit: 100,
    offset: 0
  },
  
  // Date range (separate from filters for easier access)
  dateRange: {
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    end: new Date(),
    label: 'Last 7 days',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  },
  
  // Performance settings
  performance: {
    enableCaching: true,
    enablePrefetch: true,
    enableRealTime: false,
    batchSize: 50,
    debounceMs: 300
  },
  
  // Real-time settings
  isRealTimeEnabled: false,
  realTimeEvents: [],
  lastUpdateTimestamp: null,
  
  // Errors
  errors: {},
  
  // Actions
  setEmailStats: (stats) => set({ emailStats: stats }),
  setSenderAnalytics: (analytics) => set({ senderAnalytics: analytics }),
  setTimeAnalysis: (analysis) => set({ timeAnalysis: analysis }),
  setRulePerformance: (performance) => set({ rulePerformance: performance }),
  setDateRange: (range) => set({ dateRange: range }),
  
  setLoading: (type, loading) => {
    switch (type) {
      case 'stats':
        set({ isLoadingStats: loading })
        break
      case 'senders':
        set({ isLoadingSenders: loading })
        break
      case 'time':
        set({ isLoadingTime: loading })
        break
      case 'rules':
        set({ isLoadingRules: loading })
        break
    }
  },
  
  // Fetch functions
  fetchEmailStats: async (forceRefresh = false) => {
    set({ isLoadingStats: true })
    try {
      const { dateRange } = get()
      const response = await fetch(`/api/analytics/email-stats?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`)
      if (!response.ok) throw new Error('Failed to fetch email stats')
      const stats = await response.json()
      set({ emailStats: stats })
    } catch (error) {
      console.error('Error fetching email stats:', error)
    } finally {
      set({ isLoadingStats: false })
    }
  },
  
  fetchSenderAnalytics: async (forceRefresh = false) => {
    set({ isLoadingSenders: true })
    try {
      const { dateRange } = get()
      const response = await fetch(`/api/analytics/senders?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`)
      if (!response.ok) throw new Error('Failed to fetch sender analytics')
      const analytics = await response.json()
      set({ senderAnalytics: analytics })
    } catch (error) {
      console.error('Error fetching sender analytics:', error)
    } finally {
      set({ isLoadingSenders: false })
    }
  },
  
  fetchTimeAnalysis: async (forceRefresh = false) => {
    set({ isLoadingTime: true })
    try {
      const { dateRange } = get()
      const response = await fetch(`/api/analytics/time-patterns?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`)
      if (!response.ok) throw new Error('Failed to fetch time analysis')
      const analysis = await response.json()
      set({ timeAnalysis: analysis })
    } catch (error) {
      console.error('Error fetching time analysis:', error)
    } finally {
      set({ isLoadingTime: false })
    }
  },
  
  fetchRulePerformance: async (forceRefresh = false) => {
    set({ isLoadingRules: true })
    try {
      const { dateRange } = get()
      const response = await fetch(`/api/analytics/rules?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`)
      if (!response.ok) throw new Error('Failed to fetch rule performance')
      const performance = await response.json()
      set({ rulePerformance: performance })
    } catch (error) {
      console.error('Error fetching rule performance:', error)
    } finally {
      set({ isLoadingRules: false })
    }
  },

  fetchCategoryBreakdown: async (forceRefresh = false) => {
    // Implementation for category breakdown
  },

  fetchResponsePatterns: async (forceRefresh = false) => {
    // Implementation for response patterns
  },

  fetchLabelStats: async (forceRefresh = false) => {
    // Implementation for label stats
  },

  fetchAttachmentStats: async (forceRefresh = false) => {
    // Implementation for attachment stats
  },

  fetchPerformanceMetrics: async (forceRefresh = false) => {
    // Implementation for performance metrics
  },

  // Advanced analytics functions
  calculateTrends: async () => {
    // Implementation for trend calculation
  },

  detectAnomalies: async () => {
    // Implementation for anomaly detection
  },

  calculateCorrelations: async () => {
    // Implementation for correlation calculation
  },

  generatePredictions: async () => {
    // Implementation for prediction generation
  },

  // Batch operations
  fetchAllData: async (forceRefresh = false) => {
    // Implementation for fetching all data
  },

  prefetchData: async () => {
    // Implementation for prefetching data
  },

  refreshData: async () => {
    // Implementation for refreshing data
  },

  // Export functions
  exportMultipleDatasets: async (exports) => {
    // Implementation for exporting multiple datasets
  },

  // Real-time functions
  enableRealTimeUpdates: () => {
    set({ isRealTimeEnabled: true })
  },

  disableRealTimeUpdates: () => {
    set({ isRealTimeEnabled: false })
  },

  addRealTimeEvent: (event) => {
    set((state) => ({
      realTimeEvents: [event, ...state.realTimeEvents].slice(0, 100) // Keep last 100 events
    }))
  },

  // Utility functions
  aggregateData: (data, period) => {
    // Implementation for data aggregation
    return data
  },

  compareDatasets: (dataset1, dataset2) => {
    // Implementation for dataset comparison
    return {}
  },

  generateReport: async (template) => {
    // Implementation for report generation
    return {}
  },
  
  // Export functions
  exportData: async (format, type) => {
    try {
      const { dateRange } = get()
      const response = await fetch(`/api/analytics/export?format=${format}&type=${type}&start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`)
      
      if (!response.ok) throw new Error('Failed to export data')
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `analytics-${type}-${format}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Error exporting data:', error)
    }
  },

  // Missing method implementations
  setCategoryBreakdown: (categories) => set({ categoryBreakdown: categories }),
  setResponsePatterns: (patterns) => set({ responsePatterns: patterns }),
  setLabelStats: (stats) => set({ labelStats: stats }),
  setAttachmentStats: (stats) => set({ attachmentStats: stats }),
  setPerformanceMetrics: (metrics) => set({ performanceMetrics: metrics }),
  setTrends: (trends) => set({ trends }),
  setAnomalies: (anomalies) => set({ anomalies }),
  setCorrelations: (correlations) => set({ correlations }),
  setPredictions: (predictions) => set({ predictions }),
  setFilters: (filters) => set(state => ({ filters: { ...state.filters, ...filters } })),
  setError: (type, error) => set(state => ({ errors: { ...state.errors, [type]: error } })),
  setPerformanceSettings: (settings) => set(state => ({ performance: { ...state.performance, ...settings } })),
  getCachedData: (key) => {
    const cached = get().cache.get(key)
    if (!cached) return null
    if (Date.now() - cached.timestamp > get().cacheConfig.ttl) {
      get().cache.delete(key)
      return null
    }
    return cached.data
  },
  setCachedData: (key, data) => {
    const { cache, cacheConfig } = get()
    if (cache.size >= cacheConfig.maxSize) {
      const oldestKey = cache.keys().next().value
      cache.delete(oldestKey)
    }
    cache.set(key, { data, timestamp: Date.now(), key })
  },
  clearCache: () => set({ cache: new Map() }),
  invalidateCache: (pattern) => {
    const { cache } = get()
    if (pattern) {
      for (const [key] of cache) {
        if (key.includes(pattern)) {
          cache.delete(key)
        }
      }
    } else {
      cache.clear()
    }
  },

  // Initialize store
  initialize: async () => {
    try {
      // Load essential analytics data
      await Promise.all([
        get().fetchEmailStats(),
        get().fetchSenderAnalytics(),
        get().fetchTimeAnalysis(),
        get().fetchRulePerformance()
      ])
    } catch (error) {
      console.warn('Failed to initialize analytics store:', error)
    }
  },
})))

// Utility function for downloading blobs
function downloadBlob(blob: Blob, filename: string) {
  const url = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = filename
  document.body.appendChild(a)
  a.click()
  window.URL.revokeObjectURL(url)
  document.body.removeChild(a)
}

// Debounced version of store actions for performance
export const useDebouncedAnalyticsStore = () => {
  const store = useAnalyticsStore()
  
  const debouncedFetchEmailStats = useMemo(
    () => debounce(store.fetchEmailStats, store.performance.debounceMs),
    [store.fetchEmailStats, store.performance.debounceMs]
  )
  
  const debouncedFetchSenderAnalytics = useMemo(
    () => debounce(store.fetchSenderAnalytics, store.performance.debounceMs),
    [store.fetchSenderAnalytics, store.performance.debounceMs]
  )
  
  return {
    ...store,
    fetchEmailStats: debouncedFetchEmailStats,
    fetchSenderAnalytics: debouncedFetchSenderAnalytics
  }
}

// Simple debounce implementation
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): T {
  let timeout: NodeJS.Timeout | null = null
  
  return ((...args: Parameters<T>) => {
    const later = () => {
      timeout = null
      func(...args)
    }
    
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }) as T
}

// Selector hooks for optimized re-renders
export const useEmailStatsSelector = () => 
  useAnalyticsStore(state => state.emailStats)

export const useSenderAnalyticsSelector = () => 
  useAnalyticsStore(state => state.senderAnalytics)

export const useTimeAnalysisSelector = () => 
  useAnalyticsStore(state => state.timeAnalysis)

export const useRulePerformanceSelector = () => 
  useAnalyticsStore(state => state.rulePerformance)

export const useCategoryBreakdownSelector = () => 
  useAnalyticsStore(state => state.categoryBreakdown)

export const useResponsePatternsSelector = () => 
  useAnalyticsStore(state => state.responsePatterns)

export const useLabelStatsSelector = () => 
  useAnalyticsStore(state => state.labelStats)

export const useAttachmentStatsSelector = () => 
  useAnalyticsStore(state => state.attachmentStats)

export const useAnalyticsFiltersSelector = () => 
  useAnalyticsStore(state => state.filters)

export const useAnalyticsLoadingSelector = () => 
  useAnalyticsStore(state => ({
    isLoadingStats: state.isLoadingStats,
    isLoadingSenders: state.isLoadingSenders,
    isLoadingTime: state.isLoadingTime,
    isLoadingRules: state.isLoadingRules,
    isLoadingCategories: state.isLoadingCategories,
    isLoadingTrends: state.isLoadingTrends,
    isExporting: state.isExporting
  }))

export const useAnalyticsErrorsSelector = () => 
  useAnalyticsStore(state => state.errors)

// Performance monitoring hook
export const useAnalyticsPerformance = () => {
  const performance = useAnalyticsStore(state => state.performance)
  const cache = useAnalyticsStore(state => state.cache)
  const errors = useAnalyticsStore(state => state.errors)
  
  return {
    performance,
    cacheSize: cache.size,
    errorCount: Object.values(errors).filter(Boolean).length,
    cacheHitRate: cache.size > 0 ? 0.85 : 0 // Mock calculation
  }
}

// Helper functions for analytics calculations
export const calculateMetrics = {
  responseRate: (sent: number, received: number): number => {
    if (received === 0) return 0
    return (sent / received) * 100
  },
  
  inboxZeroRate: (unread: number, total: number): number => {
    if (total === 0) return 100
    return ((total - unread) / total) * 100
  },
  
  growthRate: (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  },
  
  averageResponseTime: (responseTimes: number[]): number => {
    if (responseTimes.length === 0) return 0
    return responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
  },
  
  categoryPercentage: (categoryCount: number, totalCount: number): number => {
    if (totalCount === 0) return 0
    return (categoryCount / totalCount) * 100
  },
}

// Mock data generator for development
export const generateMockAnalytics = () => {
  const now = new Date()
  const daysAgo = (days: number) => new Date(now.getTime() - days * 24 * 60 * 60 * 1000)
  
  // Generate volume data for the last 30 days
  const volumeData: VolumeData[] = Array.from({ length: 30 }, (_, i) => {
    const date = daysAgo(29 - i)
    const baseReceived = 50 + Math.random() * 50
    const baseSent = baseReceived * (0.3 + Math.random() * 0.4)
    
    return {
      date,
      received: Math.floor(baseReceived),
      sent: Math.floor(baseSent),
      archived: Math.floor(baseReceived * 0.7),
      deleted: Math.floor(baseReceived * 0.1),
      categories: {
        Work: Math.floor(baseReceived * 0.4),
        Personal: Math.floor(baseReceived * 0.3),
        Newsletters: Math.floor(baseReceived * 0.2),
        Spam: Math.floor(baseReceived * 0.1),
      }
    }
  })
  
  // Generate category stats
  const categoryBreakdown: CategoryStats[] = [
    {
      category: 'Work',
      count: 450,
      percentage: 40,
      unreadCount: 23,
      trend: 'increasing',
      trendPercentage: 12.5,
    },
    {
      category: 'Personal',
      count: 340,
      percentage: 30,
      unreadCount: 8,
      trend: 'stable',
      trendPercentage: 2.1,
    },
    {
      category: 'Newsletters',
      count: 225,
      percentage: 20,
      unreadCount: 45,
      trend: 'decreasing',
      trendPercentage: -8.3,
    },
    {
      category: 'Spam',
      count: 113,
      percentage: 10,
      unreadCount: 0,
      trend: 'decreasing',
      trendPercentage: -25.6,
    },
  ]
  
  // Generate sender stats
  const senderAnalytics: SenderStats[] = [
    {
      email: '<EMAIL>',
      name: 'Company Notifications',
      domain: 'company.com',
      messageCount: 123,
      threadCount: 45,
      unreadCount: 3,
      avgResponseTime: 2.5,
      lastMessageDate: new Date(),
      firstMessageDate: daysAgo(90),
      categories: ['Work'],
      importance: 'high',
      isFrequent: true,
      isRecent: true,
    },
    {
      email: '<EMAIL>',
      name: 'Service Support',
      domain: 'service.com',
      messageCount: 89,
      threadCount: 34,
      unreadCount: 1,
      avgResponseTime: 4.2,
      lastMessageDate: daysAgo(2),
      firstMessageDate: daysAgo(120),
      categories: ['Work'],
      importance: 'medium',
      isFrequent: true,
      isRecent: true,
    },
    // Add more mock senders...
  ]
  
  return {
    emailStats: {
      totals: {
        threads: 1128,
        messages: 2340,
        unread: 76,
        starred: 23,
        important: 45,
        archived: 1890,
        deleted: 234,
        sent: 567,
        received: 1773,
        draft: 12,
        spam: 113,
      },
      metrics: {
        avgResponseTime: 3.2,
        avgThreadLength: 2.1,
        avgMessageSize: 15420,
        avgAttachmentSize: 2.3,
        emailsPerDay: 58.9,
        peakHours: [9, 10, 14, 15],
        busiestDays: ['Tuesday', 'Wednesday', 'Thursday'],
        growthRate: 8.5,
      },
      categoryBreakdown,
      topSenders: senderAnalytics,
      volumeData,
      labelStats: [],
      attachmentStats: {
        totalCount: 234,
        totalSize: 45600000,
        avgSize: 195000,
        types: [],
        largestAttachments: [],
        byMonth: [],
      },
      responsePatterns: [],
      period: {
        start: daysAgo(30),
        end: now,
        timezone: 'UTC',
      },
    },
    senderAnalytics,
    volumeData,
  }
}