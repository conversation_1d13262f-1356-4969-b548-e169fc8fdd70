import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { OAuth<PERSON>ser, OAuthToken } from '../types/api'

interface User {
  id: string
  email: string
  name?: string
  picture?: string
  locale?: string
  provider?: string
}

interface AuthState {
  // State
  user: User | null
  token: string | null
  refreshToken: string | null
  expiresAt: Date | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null

  // Actions
  login: (email: string, password: string) => Promise<void>
  loginWithGoogle: (code: string) => Promise<void>
  logout: () => Promise<void>
  register: (email: string, password: string, name: string) => Promise<void>
  refreshSession: () => Promise<void>
  updateProfile: (updates: Partial<User>) => Promise<void>
  checkSession: () => Promise<void>
  initialize: () => Promise<void>
  clearError: () => void
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        user: null,
        token: null,
        refreshToken: null,
        expiresAt: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,

        // Login with email/password
        login: async (email: string, password: string) => {
          set({ isLoading: true, error: null })
          
          try {
            const response = await fetch('/api/auth/login', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email, password }),
            })

            if (!response.ok) {
              const error = await response.json()
              throw new Error(error.error || 'Login failed')
            }

            const data = await response.json()
            
            set({
              user: data.user,
              token: data.token,
              refreshToken: data.refreshToken,
              expiresAt: new Date(data.expires),
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } catch (error) {
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Login failed',
              isAuthenticated: false,
            })
            throw error
          }
        },

        // Login with Google OAuth
        loginWithGoogle: async (code: string) => {
          set({ isLoading: true, error: null })
          
          try {
            const response = await fetch('/api/auth/google/callback', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ code }),
            })

            if (!response.ok) {
              const error = await response.json()
              throw new Error(error.error || 'Google login failed')
            }

            const data = await response.json()
            
            set({
              user: data.user,
              token: data.token,
              refreshToken: data.refreshToken,
              expiresAt: new Date(data.expires),
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } catch (error) {
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Google login failed',
              isAuthenticated: false,
            })
            throw error
          }
        },

        // Logout
        logout: async () => {
          set({ isLoading: true })
          
          try {
            await fetch('/api/auth/logout', {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${get().token}`,
              },
            })
          } catch (error) {
            console.error('Logout error:', error)
          } finally {
            set({
              user: null,
              token: null,
              refreshToken: null,
              expiresAt: null,
              isAuthenticated: false,
              isLoading: false,
              error: null,
            })
          }
        },

        // Register new user
        register: async (email: string, password: string, name: string) => {
          set({ isLoading: true, error: null })
          
          try {
            const response = await fetch('/api/auth/register', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ email, password, name }),
            })

            if (!response.ok) {
              const error = await response.json()
              throw new Error(error.error || 'Registration failed')
            }

            const data = await response.json()
            
            set({
              user: data.user,
              token: data.token,
              refreshToken: data.refreshToken,
              expiresAt: new Date(data.expires),
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } catch (error) {
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Registration failed',
              isAuthenticated: false,
            })
            throw error
          }
        },

        // Refresh session
        refreshSession: async () => {
          const { refreshToken } = get()
          
          if (!refreshToken) {
            set({ isAuthenticated: false })
            return
          }

          try {
            const response = await fetch('/api/auth/refresh', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ refreshToken }),
            })

            if (!response.ok) {
              throw new Error('Session refresh failed')
            }

            const data = await response.json()
            
            set({
              token: data.token,
              refreshToken: data.refreshToken,
              expiresAt: new Date(data.expires),
              error: null,
            })
          } catch (error) {
            set({
              isAuthenticated: false,
              user: null,
              token: null,
              refreshToken: null,
              error: 'Session expired',
            })
          }
        },

        // Update user profile
        updateProfile: async (updates: Partial<User>) => {
          set({ isLoading: true, error: null })
          
          try {
            const response = await fetch('/api/auth/me', {
              method: 'PATCH',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${get().token}`,
              },
              body: JSON.stringify(updates),
            })

            if (!response.ok) {
              throw new Error('Profile update failed')
            }

            const updatedUser = await response.json()
            
            set({
              user: updatedUser,
              isLoading: false,
              error: null,
            })
          } catch (error) {
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Profile update failed',
            })
            throw error
          }
        },

        // Check current session
        checkSession: async () => {
          const { token, expiresAt } = get()
          
          if (!token) {
            set({ isAuthenticated: false })
            return
          }

          // Check if token is expired
          if (expiresAt && new Date() > new Date(expiresAt)) {
            await get().refreshSession()
            return
          }

          set({ isLoading: true })
          
          try {
            const response = await fetch('/api/auth/session', {
              headers: {
                'Authorization': `Bearer ${token}`,
              },
            })

            if (!response.ok) {
              throw new Error('Session check failed')
            }

            const data = await response.json()
            
            set({
              user: data.user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            })
          } catch (error) {
            set({
              isAuthenticated: false,
              user: null,
              token: null,
              isLoading: false,
            })
          }
        },

        // Clear error
        initialize: async () => {
          try {
            // Check if we have a valid session
            const state = get()
            if (state.token && state.expiresAt && new Date() < state.expiresAt) {
              // Session is still valid
              return
            }
            
            // Try to check session with server
            await get().checkSession()
          } catch (error) {
            console.warn('Failed to initialize auth, using defaults:', error)
          }
        },

        clearError: () => set({ error: null }),
      }),
      {
        name: 'auth-storage',
        partialize: (state) => ({
          user: state.user,
          token: state.token,
          refreshToken: state.refreshToken,
          expiresAt: state.expiresAt,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    )
  )
)