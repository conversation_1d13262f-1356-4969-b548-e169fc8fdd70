import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { Thread, ParsedMessage, EmailFilter, EmailSort, EmailCategory } from '../types/email'
import type { PaginationParams } from '../types/api'
import type { UnifiedThread, UnifiedInboxFilter } from '../types/multi-account'

interface EmailState {
  // Multi-account support
  currentAccountId: string | null
  isUnifiedView: boolean
  
  // State (account-specific or unified)
  threads: Thread[] | UnifiedThread[]
  selectedThread: Thread | UnifiedThread | null
  selectedMessage: ParsedMessage | null
  isLoading: boolean
  error: string | null
  
  // Pagination
  currentPage: number
  pageSize: number
  totalThreads: number
  hasMore: boolean
  
  // Filters and sorting
  filter: EmailFilter | UnifiedInboxFilter
  sort: EmailSort
  
  // Selection
  selectedThreadIds: Set<string>
  isSelectMode: boolean
  
  // Stats (account-specific or aggregated)
  stats: {
    totalUnread: number
    totalImportant: number
    categories: Record<string, number>
    accountBreakdown?: Array<{
      accountId: string
      accountName: string
      unread: number
      total: number
    }>
  }
  
  // Actions
  fetchThreads: (params?: PaginationParams & (EmailFilter | UnifiedInboxFilter)) => Promise<void>
  fetchThread: (threadId: string, accountId?: string) => Promise<void>
  
  // Multi-account actions
  switchToAccount: (accountId: string | null) => void
  switchToUnifiedView: () => void
  fetchUnifiedThreads: (params?: PaginationParams & UnifiedInboxFilter) => Promise<void>
  selectThread: (threadId: string) => void
  selectMessage: (messageId: string) => void
  toggleThreadSelection: (threadId: string) => void
  selectAllThreads: () => void
  clearSelection: () => void
  setSelectMode: (enabled: boolean) => void
  
  // Thread actions
  markAsRead: (threadIds: string[], isRead: boolean) => Promise<void>
  star: (threadIds: string[], isStarred: boolean) => Promise<void>
  archive: (threadIds: string[]) => Promise<void>
  deleteThreads: (threadIds: string[]) => Promise<void>
  categorize: (threadIds: string[], category: EmailCategory) => Promise<void>
  
  // Message actions
  sendReply: (threadId: string, message: string) => Promise<void>
  forward: (messageId: string, to: string, message: string) => Promise<void>
  
  // Filter and sort
  setFilter: (filter: Partial<EmailFilter>) => void
  clearFilter: () => void
  setSort: (sort: EmailSort) => void
  
  // Pagination
  loadMore: () => Promise<void>
  goToPage: (page: number) => Promise<void>
  setPageSize: (size: number) => Promise<void>
  
  // Search
  search: (query: string) => Promise<void>
  
  // Refresh
  refresh: () => Promise<void>
  
  // Initialize
  initialize: () => Promise<void>
  
  clearError: () => void
}

export const useEmailStore = create<EmailState>()(
  devtools(
    (set, get) => ({
      // Multi-account state
      currentAccountId: null,
      isUnifiedView: false,
      
      // Initial state
      threads: [],
      selectedThread: null,
      selectedMessage: null,
      isLoading: false,
      error: null,
      
      // Pagination
      currentPage: 1,
      pageSize: 20,
      totalThreads: 0,
      hasMore: false,
      
      // Filters and sorting
      filter: {},
      sort: { field: 'date', order: 'desc' },
      
      // Selection
      selectedThreadIds: new Set(),
      isSelectMode: false,
      
      // Stats
      stats: {
        totalUnread: 0,
        totalImportant: 0,
        categories: {},
      },
      
      // Fetch threads (account-specific or unified)
      fetchThreads: async (params) => {
        const { isUnifiedView } = get()
        
        if (isUnifiedView) {
          return get().fetchUnifiedThreads(params)
        }
        
        set({ isLoading: true, error: null })
        
        try {
          const { filter, sort, currentPage, pageSize, currentAccountId } = get()
          
          if (!currentAccountId) {
            throw new Error('No account selected')
          }
          
          const queryParams = new URLSearchParams({
            page: String(params?.page || currentPage),
            pageSize: String(params?.pageSize || pageSize),
            sort: sort.field,
            order: sort.order,
            accountId: currentAccountId,
            ...Object.entries({ ...filter, ...params })
              .filter(([_, value]) => value !== undefined && value !== null)
              .reduce((acc, [key, value]) => ({ ...acc, [key]: String(value) }), {}),
          })
          
          const response = await fetch(`/api/threads?${queryParams}`)
          
          if (!response.ok) {
            throw new Error('Failed to fetch threads')
          }
          
          const data = await response.json()
          
          set({
            threads: data.data,
            totalThreads: data.pagination.totalItems,
            currentPage: data.pagination.page,
            hasMore: data.pagination.hasNext,
            stats: data.summary || get().stats,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch threads',
          })
        }
      },
      
      // Fetch single thread
      fetchThread: async (threadId: string, accountId?: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const { currentAccountId } = get()
          const targetAccountId = accountId || currentAccountId
          
          if (!targetAccountId) {
            throw new Error('No account specified')
          }
          
          const response = await fetch(`/api/threads/${threadId}?accountId=${targetAccountId}`)
          
          if (!response.ok) {
            throw new Error('Failed to fetch thread')
          }
          
          const thread = await response.json()
          
          set({
            selectedThread: thread,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch thread',
          })
        }
      },
      
      // Select thread
      selectThread: (threadId: string) => {
        const thread = get().threads.find(t => t.id === threadId)
        set({ selectedThread: thread || null })
      },
      
      // Select message
      selectMessage: (messageId: string) => {
        const { threads } = get()
        let foundMessage: ParsedMessage | null = null
        
        for (const thread of threads) {
          const message = thread.messages.find(m => m.id === messageId)
          if (message) {
            foundMessage = message
            break
          }
        }
        
        set({ selectedMessage: foundMessage })
      },
      
      // Toggle thread selection
      toggleThreadSelection: (threadId: string) => {
        const { selectedThreadIds } = get()
        const newSelection = new Set(selectedThreadIds)
        
        if (newSelection.has(threadId)) {
          newSelection.delete(threadId)
        } else {
          newSelection.add(threadId)
        }
        
        set({ selectedThreadIds: newSelection })
      },
      
      // Select all threads
      selectAllThreads: () => {
        const { threads } = get()
        set({ selectedThreadIds: new Set(threads.map(t => t.id)) })
      },
      
      // Clear selection
      clearSelection: () => {
        set({ selectedThreadIds: new Set(), isSelectMode: false })
      },
      
      // Set select mode
      setSelectMode: (enabled: boolean) => {
        set({ isSelectMode: enabled })
        if (!enabled) {
          set({ selectedThreadIds: new Set() })
        }
      },
      
      // Mark as read/unread
      markAsRead: async (threadIds: string[], isRead: boolean) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}/read`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ isRead }),
            })
          )
          
          await Promise.all(promises)
          
          // Update local state
          const { threads } = get()
          const updatedThreads = threads.map(thread => {
            if (threadIds.includes(thread.id)) {
              return {
                ...thread,
                status: { ...thread.status, isUnread: !isRead },
                unreadCount: isRead ? 0 : thread.messageCount,
              }
            }
            return thread
          })
          
          set({
            threads: updatedThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to update threads',
          })
        }
      },
      
      // Star/unstar threads
      star: async (threadIds: string[], isStarred: boolean) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}/star`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ isStarred }),
            })
          )
          
          await Promise.all(promises)
          
          // Update local state
          const { threads } = get()
          const updatedThreads = threads.map(thread => {
            if (threadIds.includes(thread.id)) {
              return {
                ...thread,
                status: { ...thread.status, isStarred },
              }
            }
            return thread
          })
          
          set({
            threads: updatedThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to update threads',
          })
        }
      },
      
      // Archive threads
      archive: async (threadIds: string[]) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}/archive`, {
              method: 'POST',
            })
          )
          
          await Promise.all(promises)
          
          // Remove from current view
          const { threads } = get()
          const remainingThreads = threads.filter(t => !threadIds.includes(t.id))
          
          set({
            threads: remainingThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to archive threads',
          })
        }
      },
      
      // Delete threads
      deleteThreads: async (threadIds: string[]) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}`, {
              method: 'DELETE',
            })
          )
          
          await Promise.all(promises)
          
          // Remove from local state
          const { threads } = get()
          const remainingThreads = threads.filter(t => !threadIds.includes(t.id))
          
          set({
            threads: remainingThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to delete threads',
          })
        }
      },
      
      // Categorize threads
      categorize: async (threadIds: string[], category: EmailCategory) => {
        set({ isLoading: true, error: null })
        
        try {
          const promises = threadIds.map(threadId =>
            fetch(`/api/threads/${threadId}/categorize`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ category }),
            })
          )
          
          await Promise.all(promises)
          
          // Update local state
          const { threads } = get()
          const updatedThreads = threads.map(thread => {
            if (threadIds.includes(thread.id)) {
              return { ...thread, category }
            }
            return thread
          })
          
          set({
            threads: updatedThreads,
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to categorize threads',
          })
        }
      },
      
      // Send reply
      sendReply: async (threadId: string, message: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await fetch('/api/messages/send', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              threadId,
              body: message,
              type: 'reply',
            }),
          })
          
          if (!response.ok) {
            throw new Error('Failed to send reply')
          }
          
          // Refresh thread to get updated messages
          await get().fetchThread(threadId)
          
          set({
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to send reply',
          })
          throw error
        }
      },
      
      // Forward message
      forward: async (messageId: string, to: string, message: string) => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await fetch('/api/messages/forward', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              messageId,
              to,
              message,
            }),
          })
          
          if (!response.ok) {
            throw new Error('Failed to forward message')
          }
          
          set({
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to forward message',
          })
          throw error
        }
      },
      
      // Set filter
      setFilter: (filter: Partial<EmailFilter>) => {
        set((state) => ({
          filter: { ...state.filter, ...filter },
          currentPage: 1, // Reset to first page when filter changes
        }))
        get().fetchThreads()
      },
      
      // Clear filter
      clearFilter: () => {
        set({ filter: {}, currentPage: 1 })
        get().fetchThreads()
      },
      
      // Set sort
      setSort: (sort: EmailSort) => {
        set({ sort, currentPage: 1 })
        get().fetchThreads()
      },
      
      // Load more
      loadMore: async () => {
        const { currentPage, hasMore } = get()
        if (!hasMore) return
        
        await get().fetchThreads({ page: currentPage + 1 })
      },
      
      // Go to page
      goToPage: async (page: number) => {
        await get().fetchThreads({ page })
      },
      
      // Set page size
      setPageSize: async (size: number) => {
        set({ pageSize: size, currentPage: 1 })
        await get().fetchThreads()
      },
      
      // Search
      search: async (query: string) => {
        set({ filter: { query }, currentPage: 1 })
        await get().fetchThreads()
      },
      
      // Refresh
      refresh: async () => {
        await get().fetchThreads()
      },
      
      // Multi-account actions
      switchToAccount: (accountId: string | null) => {
        set({
          currentAccountId: accountId,
          isUnifiedView: false,
          threads: [],
          selectedThread: null,
          selectedMessage: null,
          selectedThreadIds: new Set(),
          currentPage: 1,
          filter: {},
        })
        
        // Fetch threads for the new account
        if (accountId) {
          get().fetchThreads()
        }
      },

      switchToUnifiedView: () => {
        set({
          isUnifiedView: true,
          currentAccountId: null,
          threads: [],
          selectedThread: null,
          selectedMessage: null,
          selectedThreadIds: new Set(),
          currentPage: 1,
          filter: {},
        })
        
        // Fetch unified threads
        get().fetchUnifiedThreads()
      },

      fetchUnifiedThreads: async (params) => {
        set({ isLoading: true, error: null })
        
        try {
          const { filter, sort, currentPage, pageSize } = get()
          
          const requestBody = {
            page: params?.page || currentPage,
            pageSize: params?.pageSize || pageSize,
            sort: sort.field,
            order: sort.order,
            filter: { ...filter, ...params },
          }
          
          const response = await fetch('/api/unified/threads', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(requestBody),
          })
          
          if (!response.ok) {
            throw new Error('Failed to fetch unified threads')
          }
          
          const data = await response.json()
          
          set({
            threads: data.data as UnifiedThread[],
            totalThreads: data.pagination.totalItems,
            currentPage: data.pagination.page,
            hasMore: data.pagination.hasNext,
            stats: {
              ...data.summary,
              accountBreakdown: data.accountBreakdown,
            },
            isLoading: false,
            error: null,
          })
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch unified threads',
          })
        }
      },

      // Initialize store
      initialize: async () => {
        try {
          // Load initial threads for default account if available
          const { currentAccountId, isUnifiedView } = get()
          if (currentAccountId || isUnifiedView) {
            await get().fetchThreads()
          }
        } catch (error) {
          console.warn('Failed to initialize email store:', error)
        }
      },

      // Clear error
      clearError: () => set({ error: null }),
    })
  )
)