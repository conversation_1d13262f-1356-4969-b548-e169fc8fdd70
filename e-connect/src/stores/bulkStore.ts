import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { 
  BulkOperation, 
  BulkOperationType, 
  BulkOperationStatus, 
  BulkFilter,
  BulkOperationParams,
  BulkOperationResult,
  BulkOperationError,
  BulkOperationSummary,
  RecurringSchedule
} from '../types/bulk'

interface BulkState {
  // Current operations
  operations: BulkOperation[]
  activeOperation: BulkOperation | null
  
  // UI state
  isLoading: boolean
  error: string | null
  
  // Selection for new operations
  selectedThreadIds: Set<string>
  selectedMessageIds: Set<string>
  
  // Operation history
  completedOperations: BulkOperation[]
  
  // Settings
  maxConcurrentOperations: number
  defaultBatchSize: number
  
  // Actions
  startOperation: (
    type: BulkOperationType,
    params: BulkOperationParams,
    selection: {
      type: 'manual' | 'filter' | 'all'
      threadIds?: string[]
      messageIds?: string[]
      filter?: BulkFilter
      totalCount: number
    }
  ) => Promise<BulkOperation>
  
  pauseOperation: (operationId: string) => Promise<void>
  resumeOperation: (operationId: string) => Promise<void>
  cancelOperation: (operationId: string) => Promise<void>
  retryOperation: (operationId: string) => Promise<void>
  
  updateOperationProgress: (
    operationId: string,
    progress: Partial<BulkOperation['progress']>
  ) => void
  
  addOperationResult: (
    operationId: string,
    result: BulkOperationResult
  ) => void
  
  addOperationError: (
    operationId: string,
    error: BulkOperationError
  ) => void
  
  completeOperation: (
    operationId: string,
    summary: BulkOperationSummary
  ) => void
  
  // Selection management
  selectThreads: (threadIds: string[]) => void
  selectMessages: (messageIds: string[]) => void
  clearSelection: () => void
  
  // History management
  getOperationHistory: (limit?: number) => BulkOperation[]
  clearHistory: () => void
  
  // Scheduling
  scheduleOperation: (
    operation: BulkOperation,
    scheduledFor: Date,
    recurring?: RecurringSchedule
  ) => Promise<void>
  
  getScheduledOperations: () => BulkOperation[]
  
  // Cleanup
  cleanup: () => void
}

export const useBulkStore = create<BulkState>()(
  devtools(
    (set, get) => ({
      // Initial state
      operations: [],
      activeOperation: null,
      isLoading: false,
      error: null,
      selectedThreadIds: new Set(),
      selectedMessageIds: new Set(),
      completedOperations: [],
      maxConcurrentOperations: 3,
      defaultBatchSize: 50,

      // Actions
      startOperation: async (type, params, selection) => {
        const operation: BulkOperation = {
          id: `bulk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type,
          status: 'pending',
          createdAt: new Date(),
          createdBy: 'user', // Would come from auth in real app
          selection,
          progress: {
            processed: 0,
            succeeded: 0,
            failed: 0,
            skipped: 0,
            percentage: 0
          },
          params,
          errors: [],
          retryCount: 0,
          maxRetries: 3
        }

        set((state) => ({
          operations: [...state.operations, operation],
          activeOperation: operation,
          isLoading: true,
          error: null
        }))

        // In a real app, this would trigger the actual bulk operation
        // For now, we'll just simulate it
        setTimeout(() => {
          get().updateOperationProgress(operation.id, {
            percentage: 100,
            processed: selection.totalCount,
            succeeded: selection.totalCount
          })
          get().completeOperation(operation.id, {
            totalItems: selection.totalCount,
            processedItems: selection.totalCount,
            successfulItems: selection.totalCount,
            failedItems: 0,
            skippedItems: 0,
            totalTime: 5000,
            avgTimePerItem: 5000 / selection.totalCount,
            changes: {}
          })
        }, 5000)

        return operation
      },

      pauseOperation: async (operationId) => {
        set((state) => ({
          operations: state.operations.map(op =>
            op.id === operationId ? { ...op, status: 'paused' as BulkOperationStatus } : op
          )
        }))
      },

      resumeOperation: async (operationId) => {
        set((state) => ({
          operations: state.operations.map(op =>
            op.id === operationId ? { ...op, status: 'running' as BulkOperationStatus } : op
          )
        }))
      },

      cancelOperation: async (operationId) => {
        set((state) => ({
          operations: state.operations.map(op =>
            op.id === operationId ? { ...op, status: 'cancelled' as BulkOperationStatus } : op
          ),
          activeOperation: state.activeOperation?.id === operationId ? null : state.activeOperation
        }))
      },

      retryOperation: async (operationId) => {
        set((state) => ({
          operations: state.operations.map(op =>
            op.id === operationId ? {
              ...op,
              status: 'pending' as BulkOperationStatus,
              retryCount: op.retryCount + 1,
              errors: []
            } : op
          )
        }))
      },

      updateOperationProgress: (operationId, progress) => {
        set((state) => ({
          operations: state.operations.map(op =>
            op.id === operationId ? {
              ...op,
              progress: { ...op.progress, ...progress },
              status: progress.percentage === 100 ? 'completed' as BulkOperationStatus : op.status
            } : op
          )
        }))
      },

      addOperationResult: (operationId, result) => {
        set((state) => ({
          operations: state.operations.map(op =>
            op.id === operationId ? {
              ...op,
              results: [...(op.results || []), result]
            } : op
          )
        }))
      },

      addOperationError: (operationId, error) => {
        set((state) => ({
          operations: state.operations.map(op =>
            op.id === operationId ? {
              ...op,
              errors: [...op.errors, error]
            } : op
          )
        }))
      },

      completeOperation: (operationId, summary) => {
        set((state) => {
          const operation = state.operations.find(op => op.id === operationId)
          if (!operation) return state

          const completedOperation = {
            ...operation,
            status: 'completed' as BulkOperationStatus,
            completedAt: new Date(),
            summary
          }

          return {
            operations: state.operations.filter(op => op.id !== operationId),
            completedOperations: [...state.completedOperations, completedOperation],
            activeOperation: state.activeOperation?.id === operationId ? null : state.activeOperation,
            isLoading: false
          }
        })
      },

      selectThreads: (threadIds) => {
        set((state) => {
          const newSelected = new Set(state.selectedThreadIds)
          threadIds.forEach(id => newSelected.add(id))
          return { selectedThreadIds: newSelected }
        })
      },

      selectMessages: (messageIds) => {
        set((state) => {
          const newSelected = new Set(state.selectedMessageIds)
          messageIds.forEach(id => newSelected.add(id))
          return { selectedMessageIds: newSelected }
        })
      },

      clearSelection: () => {
        set({
          selectedThreadIds: new Set(),
          selectedMessageIds: new Set()
        })
      },

      getOperationHistory: (limit = 50) => {
        const { completedOperations } = get()
        return completedOperations
          .sort((a, b) => (b.completedAt?.getTime() || 0) - (a.completedAt?.getTime() || 0))
          .slice(0, limit)
      },

      clearHistory: () => {
        set({ completedOperations: [] })
      },

      scheduleOperation: async (operation, scheduledFor, recurring) => {
        set((state) => ({
          operations: state.operations.map(op =>
            op.id === operation.id ? {
              ...op,
              scheduled: true,
              scheduledFor,
              recurring
            } : op
          )
        }))
      },

      getScheduledOperations: () => {
        const { operations } = get()
        return operations.filter(op => op.scheduled && op.scheduledFor)
      },

      cleanup: () => {
        set({
          operations: [],
          activeOperation: null,
          isLoading: false,
          error: null,
          selectedThreadIds: new Set(),
          selectedMessageIds: new Set()
        })
      }
    }),
    {
      name: 'bulk-store'
    }
  )
)