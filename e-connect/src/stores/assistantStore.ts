import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { 
  ChatMessage, 
  Conversation, 
  ConversationContext, 
  Suggestion, 
  AIResponse,
  ConversationSettings 
} from '@/types/assistant';
import { generateId } from '@/utils';

interface AssistantStore {
  // Current conversation state
  currentConversation: Conversation | null;
  conversations: Conversation[];
  
  // Chat state
  isTyping: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Input state
  inputValue: string;
  attachments: any[];
  suggestions: Suggestion[];
  
  // Settings
  settings: ConversationSettings;
  
  // Actions
  createConversation: (context?: Partial<ConversationContext>) => Conversation;
  setCurrentConversation: (conversation: Conversation | null) => void;
  addMessage: (message: Omit<ChatMessage, 'id' | 'timestamp'>) => void;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  deleteMessage: (messageId: string) => void;
  regenerateMessage: (messageId: string) => Promise<void>;
  
  // Chat actions
  sendMessage: (content: string, attachments?: any[]) => Promise<void>;
  setTyping: (isTyping: boolean) => void;
  setInputValue: (value: string) => void;
  addAttachment: (attachment: any) => void;
  removeAttachment: (attachmentId: string) => void;
  clearAttachments: () => void;
  
  // Suggestions
  setSuggestions: (suggestions: Suggestion[]) => void;
  applySuggestion: (suggestionId: string) => Promise<void>;
  dismissSuggestion: (suggestionId: string) => void;
  
  // Conversation management
  archiveConversation: (conversationId: string) => void;
  deleteConversation: (conversationId: string) => void;
  exportConversation: (conversationId: string) => string;
  importConversation: (data: string) => Conversation;
  
  // Settings
  updateSettings: (settings: Partial<ConversationSettings>) => void;
  
  // Utilities
  clearError: () => void;
  reset: () => void;
}

// Mock API functions - replace with real API calls
const mockApiCall = async (message: string): Promise<AIResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  
  return {
    id: generateId(),
    query: message,
    response: `I understand you're asking about "${message}". Let me help you with email management and automation. Here are some suggestions:

1. **Rule Creation**: I can help you create rules to automatically organize your emails
2. **Bulk Actions**: Process multiple emails efficiently 
3. **Smart Suggestions**: Get AI-powered recommendations for email handling

Would you like me to help you with any specific email management task?`,
    type: 'answer',
    model: 'gpt-4',
    tokens: {
      prompt: message.length,
      completion: 150,
      total: message.length + 150
    },
    latency: 1200,
    confidence: 0.95,
    data: {
      answer: {
        text: 'AI response about email management',
        type: 'instructional',
        confidence: 0.95,
        followUp: ['Create a rule', 'Bulk unsubscribe', 'Analyze inbox']
      }
    }
  };
};

const generateSuggestions = (message: string): Suggestion[] => {
  const suggestions: Suggestion[] = [];
  
  if (message.toLowerCase().includes('unsubscribe')) {
    suggestions.push({
      id: generateId(),
      type: 'unsubscribe',
      title: 'Bulk Unsubscribe',
      description: 'Unsubscribe from multiple newsletters at once',
      confidence: 0.9,
      priority: 'high',
      action: {
        type: 'bulk_unsubscribe',
        params: { category: 'newsletters' }
      },
      impact: {
        timesSaved: 30,
        emailsAffected: 50,
        clutterReduced: 25
      }
    });
  }
  
  if (message.toLowerCase().includes('rule') || message.toLowerCase().includes('automat')) {
    suggestions.push({
      id: generateId(),
      type: 'createRule',
      title: 'Create Automation Rule',
      description: 'Set up rules to automatically organize emails',
      confidence: 0.85,
      priority: 'medium',
      action: {
        type: 'create_rule',
        params: { template: 'auto_categorize' }
      },
      impact: {
        timesSaved: 60,
        emailsAffected: 100
      }
    });
  }
  
  return suggestions;
};

export const useAssistantStore = create<AssistantStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    currentConversation: null,
    conversations: [],
    isTyping: false,
    isLoading: false,
    error: null,
    inputValue: '',
    attachments: [],
    suggestions: [],
    settings: {
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2048,
      autoSuggest: true,
      streamResponse: true
    },

    // Actions
    createConversation: (context = {}) => {
      const conversation: Conversation = {
        id: generateId(),
        title: 'New Conversation',
        messages: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        status: 'active',
        context: {
          focusArea: 'general',
          ...context
        }
      };
      
      set(state => ({
        conversations: [conversation, ...state.conversations],
        currentConversation: conversation
      }));
      
      return conversation;
    },

    setCurrentConversation: (conversation) => {
      set({ currentConversation: conversation });
    },

    addMessage: (messageData) => {
      const message: ChatMessage = {
        ...messageData,
        id: generateId(),
        timestamp: new Date()
      };

      set(state => {
        if (!state.currentConversation) return state;

        const updatedConversation = {
          ...state.currentConversation,
          messages: [...state.currentConversation.messages, message],
          updatedAt: new Date()
        };

        return {
          currentConversation: updatedConversation,
          conversations: state.conversations.map(conv =>
            conv.id === updatedConversation.id ? updatedConversation : conv
          )
        };
      });
    },

    updateMessage: (messageId, updates) => {
      set(state => {
        if (!state.currentConversation) return state;

        const updatedConversation = {
          ...state.currentConversation,
          messages: state.currentConversation.messages.map(msg =>
            msg.id === messageId ? { ...msg, ...updates } : msg
          ),
          updatedAt: new Date()
        };

        return {
          currentConversation: updatedConversation,
          conversations: state.conversations.map(conv =>
            conv.id === updatedConversation.id ? updatedConversation : conv
          )
        };
      });
    },

    deleteMessage: (messageId) => {
      set(state => {
        if (!state.currentConversation) return state;

        const updatedConversation = {
          ...state.currentConversation,
          messages: state.currentConversation.messages.filter(msg => msg.id !== messageId),
          updatedAt: new Date()
        };

        return {
          currentConversation: updatedConversation,
          conversations: state.conversations.map(conv =>
            conv.id === updatedConversation.id ? updatedConversation : conv
          )
        };
      });
    },

    regenerateMessage: async (messageId) => {
      const { currentConversation, updateMessage } = get();
      if (!currentConversation) return;

      const message = currentConversation.messages.find(m => m.id === messageId);
      if (!message || message.role !== 'assistant') return;

      // Find the previous user message
      const messageIndex = currentConversation.messages.findIndex(m => m.id === messageId);
      const userMessage = currentConversation.messages
        .slice(0, messageIndex)
        .reverse()
        .find(m => m.role === 'user');

      if (!userMessage) return;

      // Mark as loading
      updateMessage(messageId, { isLoading: true, isError: false });

      try {
        const response = await mockApiCall(userMessage.content);
        updateMessage(messageId, {
          content: response.response,
          isLoading: false,
          metadata: {
            model: response.model,
            tokens: response.tokens.total,
            confidence: response.confidence
          }
        });
      } catch (error) {
        updateMessage(messageId, {
          isLoading: false,
          isError: true,
          error: error instanceof Error ? error.message : 'Failed to regenerate response'
        });
      }
    },

    sendMessage: async (content, attachments = []) => {
      const { currentConversation, createConversation, addMessage, setTyping } = get();
      
      if (!content.trim()) return;

      // Create conversation if none exists
      let conversation = currentConversation;
      if (!conversation) {
        conversation = createConversation();
      }

      // Add user message
      addMessage({
        role: 'user',
        content,
        attachments: attachments.map(att => ({
          id: generateId(),
          ...att
        }))
      });

      // Generate suggestions
      const suggestions = generateSuggestions(content);
      set({ suggestions });

      // Clear input
      set({ inputValue: '', attachments: [] });

      // Start typing indicator
      setTyping(true);

      try {
        // Simulate AI response
        const response = await mockApiCall(content);
        
        setTyping(false);
        
        // Add AI message
        addMessage({
          role: 'assistant',
          content: response.response,
          metadata: {
            model: response.model,
            tokens: response.tokens.total,
            confidence: response.confidence
          },
          suggestedActions: suggestions
        });

      } catch (error) {
        setTyping(false);
        addMessage({
          role: 'assistant',
          content: 'I apologize, but I encountered an error processing your request. Please try again.',
          isError: true,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    },

    setTyping: (isTyping) => set({ isTyping }),
    setInputValue: (inputValue) => set({ inputValue }),

    addAttachment: (attachment) => {
      set(state => ({
        attachments: [...state.attachments, { id: generateId(), ...attachment }]
      }));
    },

    removeAttachment: (attachmentId) => {
      set(state => ({
        attachments: state.attachments.filter(att => att.id !== attachmentId)
      }));
    },

    clearAttachments: () => set({ attachments: [] }),

    setSuggestions: (suggestions) => set({ suggestions }),

    applySuggestion: async (suggestionId) => {
      const { suggestions } = get();
      const suggestion = suggestions.find(s => s.id === suggestionId);
      if (!suggestion) return;

      // Mark as applied
      set(state => ({
        suggestions: state.suggestions.map(s =>
          s.id === suggestionId ? { ...s, isApplied: true, appliedAt: new Date() } : s
        )
      }));

      // Here you would implement the actual action
      console.log('Applying suggestion:', suggestion);
    },

    dismissSuggestion: (suggestionId) => {
      set(state => ({
        suggestions: state.suggestions.map(s =>
          s.id === suggestionId ? { ...s, isDismissed: true, dismissedAt: new Date() } : s
        )
      }));
    },

    archiveConversation: (conversationId) => {
      set(state => ({
        conversations: state.conversations.map(conv =>
          conv.id === conversationId ? { ...conv, status: 'archived' as const } : conv
        ),
        currentConversation: state.currentConversation?.id === conversationId 
          ? null 
          : state.currentConversation
      }));
    },

    deleteConversation: (conversationId) => {
      set(state => ({
        conversations: state.conversations.filter(conv => conv.id !== conversationId),
        currentConversation: state.currentConversation?.id === conversationId 
          ? null 
          : state.currentConversation
      }));
    },

    exportConversation: (conversationId) => {
      const { conversations } = get();
      const conversation = conversations.find(c => c.id === conversationId);
      if (!conversation) return '';
      
      return JSON.stringify(conversation, null, 2);
    },

    importConversation: (data) => {
      try {
        const conversation = JSON.parse(data) as Conversation;
        conversation.id = generateId(); // New ID to avoid conflicts
        
        set(state => ({
          conversations: [conversation, ...state.conversations]
        }));
        
        return conversation;
      } catch (error) {
        throw new Error('Invalid conversation data');
      }
    },

    updateSettings: (newSettings) => {
      set(state => ({
        settings: { ...state.settings, ...newSettings }
      }));
    },

    clearError: () => set({ error: null }),

    reset: () => {
      set({
        currentConversation: null,
        conversations: [],
        isTyping: false,
        isLoading: false,
        error: null,
        inputValue: '',
        attachments: [],
        suggestions: []
      });
    }
  }))
);

// Computed selectors
export const useCurrentMessages = () => {
  const currentConversation = useAssistantStore(state => state.currentConversation);
  return currentConversation?.messages ?? [];
};

export const useActiveConversations = () => {
  return useAssistantStore(state => 
    state.conversations.filter(conv => conv.status === 'active')
  );
};

export const useActiveSuggestions = () => {
  return useAssistantStore(state => 
    state.suggestions.filter(s => !s.isApplied && !s.isDismissed)
  );
};