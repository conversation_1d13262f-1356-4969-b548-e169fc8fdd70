import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type { 
  EmailAccount,
  MultiAccountState,
  MultiAccountActions,
  MultiAccountSettings,
  AccountStatus,
  SyncStatus,
  UnifiedThread,
  UnifiedInboxFilter,
  UnifiedSearchResult,
  AccountMigration,
  AccountBackup,
  AccountAnalytics,
  SearchOptions,
  AccountError,
  MultiAccountEvent,
  AccountSwitchEvent
} from '../types/multi-account'

interface MultiAccountStore extends MultiAccountState, MultiAccountActions {}

const defaultSettings: MultiAccountSettings = {
  autoSwitchOnEmail: true,
  showAccountInEmailList: true,
  groupByAccount: false,
  unifiedInboxEnabled: true,
  unifiedInboxAccounts: [],
  unifiedSorting: 'chronological',
  consolidateNotifications: true,
  notifyAllAccounts: true,
  accountSpecificSounds: false,
  searchAllAccountsByDefault: true,
  searchTimeout: 30,
  maxSearchResults: 100,
  maxConcurrentSyncs: 3,
  syncThrottling: true,
  offlineMode: false,
  dataIsolation: true,
  sharedContacts: false,
  sharedRules: false,
  lockOnAccountSwitch: false,
  sessionTimeout: 60,
  encryptAccountData: true,
}

export const useMultiAccountStore = create<MultiAccountStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        accounts: [],
        activeAccountId: null,
        defaultAccountId: null,
        isLoading: false,
        error: null,
        recentAccounts: [],
        switchHistory: [],
        globalSyncStatus: 'idle',
        syncProgress: [],
        unifiedSearch: {
          query: '',
          accountFilter: [],
          isSearching: false,
          results: [],
        },
        settings: defaultSettings,

        // Initialization
        initializeMultiAccount: async () => {
          try {
            set({ isLoading: true, error: null })
            
            // Load accounts from API or storage
            const response = await fetch('/api/accounts')
            if (response.ok) {
              const accounts = await response.json()
              set({ 
                accounts: accounts || [],
                isLoading: false 
              })
            } else {
              set({ isLoading: false })
            }
          } catch (error) {
            console.warn('Failed to initialize multi-account, using defaults:', error)
            set({ isLoading: false })
          }
        },

        // Account management
        addAccount: async (accountConfig) => {
          set({ isLoading: true, error: null })
          
          try {
            const newAccount: EmailAccount = {
              ...accountConfig,
              id: crypto.randomUUID(),
              createdAt: new Date(),
              updatedAt: new Date(),
              status: 'disconnected',
              stats: {
                totalEmails: 0,
                unreadEmails: 0,
                syncedEmails: 0,
                lastSyncDuration: 0,
                averageSyncDuration: 0,
                syncErrors: 0,
                consecutiveFailures: 0,
                apiCalls: 0,
                rateLimitHits: 0,
                bandwidthUsed: 0,
                storageUsed: 0,
                attachmentStorage: 0,
                emailsSent: 0,
                emailsReceived: 0,
                threadsProcessed: 0,
              },
            }

            // API call to add account
            const response = await fetch('/api/accounts', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(newAccount),
            })

            if (!response.ok) {
              throw new Error('Failed to add account')
            }

            const addedAccount = await response.json()

            set((state) => {
              const accounts = [...state.accounts, addedAccount]
              const isFirstAccount = state.accounts.length === 0
              
              return {
                accounts,
                activeAccountId: isFirstAccount ? addedAccount.id : state.activeAccountId,
                defaultAccountId: isFirstAccount ? addedAccount.id : state.defaultAccountId,
                isLoading: false,
                error: null,
              }
            })

            // Emit event
            get().emitEvent({
              type: 'account_added',
              accountId: addedAccount.id,
              account: addedAccount,
              timestamp: new Date(),
            })

          } catch (error) {
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to add account',
            })
            throw error
          }
        },

        removeAccount: async (accountId: string) => {
          set({ isLoading: true, error: null })
          
          try {
            const response = await fetch(`/api/accounts/${accountId}`, {
              method: 'DELETE',
            })

            if (!response.ok) {
              throw new Error('Failed to remove account')
            }

            set((state) => {
              const accounts = state.accounts.filter(acc => acc.id !== accountId)
              const newActiveId = state.activeAccountId === accountId 
                ? (accounts.length > 0 ? accounts[0].id : null)
                : state.activeAccountId
              const newDefaultId = state.defaultAccountId === accountId
                ? (accounts.length > 0 ? accounts[0].id : null)
                : state.defaultAccountId

              return {
                accounts,
                activeAccountId: newActiveId,
                defaultAccountId: newDefaultId,
                recentAccounts: state.recentAccounts.filter(id => id !== accountId),
                isLoading: false,
                error: null,
              }
            })

            // Emit event
            get().emitEvent({
              type: 'account_removed',
              accountId,
              timestamp: new Date(),
            })

          } catch (error) {
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to remove account',
            })
            throw error
          }
        },

        updateAccount: async (accountId: string, updates: Partial<EmailAccount>) => {
          set({ isLoading: true, error: null })
          
          try {
            const response = await fetch(`/api/accounts/${accountId}`, {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...updates,
                updatedAt: new Date(),
              }),
            })

            if (!response.ok) {
              throw new Error('Failed to update account')
            }

            const updatedAccount = await response.json()

            set((state) => ({
              accounts: state.accounts.map(acc => 
                acc.id === accountId ? { ...acc, ...updatedAccount } : acc
              ),
              isLoading: false,
              error: null,
            }))

          } catch (error) {
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Failed to update account',
            })
            throw error
          }
        },

        // Account status
        refreshAccount: async (accountId: string) => {
          try {
            const response = await fetch(`/api/accounts/${accountId}/refresh`, {
              method: 'POST',
            })

            if (!response.ok) {
              throw new Error('Failed to refresh account')
            }

            const refreshedAccount = await response.json()

            set((state) => ({
              accounts: state.accounts.map(acc => 
                acc.id === accountId ? refreshedAccount : acc
              ),
            }))

          } catch (error) {
            get().setAccountStatus(accountId, 'error')
            throw error
          }
        },

        refreshAllAccounts: async () => {
          const { accounts } = get()
          const promises = accounts.map(account => 
            get().refreshAccount(account.id).catch(console.error)
          )
          await Promise.allSettled(promises)
        },

        setAccountStatus: (accountId: string, status: AccountStatus) => {
          set((state) => ({
            accounts: state.accounts.map(acc => 
              acc.id === accountId 
                ? { ...acc, status, updatedAt: new Date() }
                : acc
            ),
          }))
        },

        // Account switching
        switchToAccount: async (accountId: string) => {
          const { activeAccountId, accounts } = get()
          const account = accounts.find(acc => acc.id === accountId)
          
          if (!account) {
            throw new Error('Account not found')
          }

          if (activeAccountId === accountId) {
            return // Already active
          }

          // Record switch event
          const switchEvent: AccountSwitchEvent = {
            fromAccountId: activeAccountId,
            toAccountId: accountId,
            timestamp: new Date(),
            trigger: 'user',
          }

          set((state) => ({
            activeAccountId: accountId,
            recentAccounts: [
              accountId,
              ...state.recentAccounts.filter(id => id !== accountId)
            ].slice(0, 10), // Keep last 10
            switchHistory: [switchEvent, ...state.switchHistory].slice(0, 50),
          }))

          // Update last access time
          await get().updateAccount(accountId, {
            lastAccessAt: new Date(),
          })

          // Emit event
          get().emitEvent({
            type: 'account_switched',
            fromAccountId: activeAccountId,
            toAccountId: accountId,
            timestamp: new Date(),
          })
        },

        setDefaultAccount: async (accountId: string) => {
          const { accounts } = get()
          const account = accounts.find(acc => acc.id === accountId)
          
          if (!account) {
            throw new Error('Account not found')
          }

          // Update all accounts to remove default flag
          const updates = accounts.map(async (acc) => {
            if (acc.id === accountId) {
              return get().updateAccount(acc.id, { isDefault: true })
            } else if (acc.isDefault) {
              return get().updateAccount(acc.id, { isDefault: false })
            }
          }).filter(Boolean)

          await Promise.all(updates)

          set({ defaultAccountId: accountId })
        },

        reorderAccounts: async (accountIds: string[]) => {
          const { accounts } = get()
          
          // Update display order for each account
          const updates = accountIds.map((accountId, index) => 
            get().updateAccount(accountId, { displayOrder: index })
          )

          await Promise.all(updates)

          // Reorder in state
          const reorderedAccounts = accountIds
            .map(id => accounts.find(acc => acc.id === id))
            .filter(Boolean) as EmailAccount[]

          set({ accounts: reorderedAccounts })
        },

        // Authentication
        authenticateAccount: async (accountId: string, credentials: any) => {
          set({ isLoading: true, error: null })
          
          try {
            const response = await fetch(`/api/accounts/${accountId}/authenticate`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(credentials),
            })

            if (!response.ok) {
              throw new Error('Authentication failed')
            }

            const authResult = await response.json()

            await get().updateAccount(accountId, {
              auth: authResult.auth,
              status: 'active',
            })

            set({ isLoading: false, error: null })

          } catch (error) {
            get().setAccountStatus(accountId, 'error')
            set({
              isLoading: false,
              error: error instanceof Error ? error.message : 'Authentication failed',
            })
            throw error
          }
        },

        refreshAuthentication: async (accountId: string) => {
          try {
            const response = await fetch(`/api/accounts/${accountId}/auth/refresh`, {
              method: 'POST',
            })

            if (!response.ok) {
              throw new Error('Failed to refresh authentication')
            }

            const authResult = await response.json()

            await get().updateAccount(accountId, {
              auth: authResult.auth,
              status: 'active',
            })

          } catch (error) {
            get().setAccountStatus(accountId, 'error')
            throw error
          }
        },

        revokeAuthentication: async (accountId: string) => {
          try {
            await fetch(`/api/accounts/${accountId}/auth/revoke`, {
              method: 'POST',
            })

            get().setAccountStatus(accountId, 'disconnected')

          } catch (error) {
            console.error('Failed to revoke authentication:', error)
          }
        },

        // Sync operations
        syncAccount: async (accountId: string, force = false) => {
          const { accounts, syncProgress } = get()
          const account = accounts.find(acc => acc.id === accountId)
          
          if (!account) {
            throw new Error('Account not found')
          }

          // Check if already syncing
          const existingProgress = syncProgress.find(p => p.accountId === accountId)
          if (existingProgress && existingProgress.status === 'syncing' && !force) {
            return
          }

          // Start sync progress tracking
          set((state) => ({
            syncProgress: [
              ...state.syncProgress.filter(p => p.accountId !== accountId),
              {
                accountId,
                status: 'syncing' as SyncStatus,
                progress: 0,
                currentTask: 'Initializing sync...',
                estimatedRemaining: 0,
                itemsProcessed: 0,
                totalItems: 0,
                errors: [],
              }
            ]
          }))

          try {
            const response = await fetch(`/api/accounts/${accountId}/sync`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ force }),
            })

            if (!response.ok) {
              throw new Error('Sync failed')
            }

            const syncResult = await response.json()

            // Update account with sync results
            await get().updateAccount(accountId, {
              lastSyncAt: new Date(),
              stats: {
                ...account.stats,
                ...syncResult.stats,
              },
            })

            // Update sync progress
            set((state) => ({
              syncProgress: state.syncProgress.filter(p => p.accountId !== accountId)
            }))

            // Emit event
            get().emitEvent({
              type: 'account_synced',
              accountId,
              syncDuration: syncResult.duration || 0,
              itemsSynced: syncResult.itemsSynced || 0,
              timestamp: new Date(),
            })

          } catch (error) {
            // Update sync progress with error
            set((state) => ({
              syncProgress: state.syncProgress.map(p => 
                p.accountId === accountId 
                  ? { ...p, status: 'failed' as SyncStatus, errors: [error instanceof Error ? error.message : 'Sync failed'] }
                  : p
              )
            }))

            get().setAccountStatus(accountId, 'error')
            throw error
          }
        },

        syncAllAccounts: async () => {
          const { accounts, settings } = get()
          const activeAccounts = accounts.filter(acc => acc.status === 'active')
          
          set({ globalSyncStatus: 'syncing' })

          try {
            // Sync accounts in batches based on maxConcurrentSyncs
            const batches = []
            for (let i = 0; i < activeAccounts.length; i += settings.maxConcurrentSyncs) {
              batches.push(activeAccounts.slice(i, i + settings.maxConcurrentSyncs))
            }

            for (const batch of batches) {
              const syncPromises = batch.map(account => 
                get().syncAccount(account.id).catch(console.error)
              )
              await Promise.allSettled(syncPromises)
            }

            set({ globalSyncStatus: 'completed' })

          } catch (error) {
            set({ globalSyncStatus: 'failed' })
            throw error
          }
        },

        pauseSync: (accountId: string) => {
          set((state) => ({
            syncProgress: state.syncProgress.map(p => 
              p.accountId === accountId 
                ? { ...p, status: 'paused' as SyncStatus }
                : p
            )
          }))
        },

        resumeSync: (accountId: string) => {
          get().syncAccount(accountId)
        },

        // Unified inbox
        getUnifiedThreads: async (filter?: UnifiedInboxFilter) => {
          const { accounts, settings } = get()
          
          if (!settings.unifiedInboxEnabled) {
            return []
          }

          const targetAccounts = filter?.accountIds 
            ? accounts.filter(acc => filter.accountIds!.includes(acc.id))
            : settings.unifiedInboxAccounts.length > 0
              ? accounts.filter(acc => settings.unifiedInboxAccounts.includes(acc.id))
              : accounts.filter(acc => acc.status === 'active')

          try {
            const response = await fetch('/api/unified/threads', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                accountIds: targetAccounts.map(acc => acc.id),
                filter,
                sorting: settings.unifiedSorting,
              }),
            })

            if (!response.ok) {
              throw new Error('Failed to fetch unified threads')
            }

            return await response.json()

          } catch (error) {
            console.error('Failed to get unified threads:', error)
            return []
          }
        },

        searchAllAccounts: async (query: string, options?: SearchOptions) => {
          const { accounts, settings } = get()
          
          set((state) => ({
            unifiedSearch: {
              ...state.unifiedSearch,
              query,
              isSearching: true,
              results: [],
            }
          }))

          try {
            const targetAccounts = options?.accountIds
              ? accounts.filter(acc => options.accountIds!.includes(acc.id))
              : accounts.filter(acc => acc.status === 'active')

            const response = await fetch('/api/unified/search', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                query,
                accountIds: targetAccounts.map(acc => acc.id),
                options: {
                  maxResults: settings.maxSearchResults,
                  timeout: settings.searchTimeout,
                  ...options,
                },
              }),
            })

            if (!response.ok) {
              throw new Error('Search failed')
            }

            const results = await response.json()

            set((state) => ({
              unifiedSearch: {
                ...state.unifiedSearch,
                isSearching: false,
                results,
              }
            }))

            return results

          } catch (error) {
            set((state) => ({
              unifiedSearch: {
                ...state.unifiedSearch,
                isSearching: false,
              }
            }))
            throw error
          }
        },

        // Cross-account operations
        moveToAccount: async (threadIds: string[], fromAccountId: string, toAccountId: string) => {
          try {
            const response = await fetch('/api/accounts/move', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                threadIds,
                fromAccountId,
                toAccountId,
                operation: 'move',
              }),
            })

            if (!response.ok) {
              throw new Error('Failed to move threads')
            }

            return await response.json()

          } catch (error) {
            console.error('Failed to move to account:', error)
            throw error
          }
        },

        copyToAccount: async (threadIds: string[], fromAccountId: string, toAccountId: string) => {
          try {
            const response = await fetch('/api/accounts/copy', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                threadIds,
                fromAccountId,
                toAccountId,
                operation: 'copy',
              }),
            })

            if (!response.ok) {
              throw new Error('Failed to copy threads')
            }

            return await response.json()

          } catch (error) {
            console.error('Failed to copy to account:', error)
            throw error
          }
        },

        // Migration and backup
        migrateAccount: async (migration: Omit<AccountMigration, 'id'>) => {
          try {
            const response = await fetch('/api/accounts/migrate', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...migration,
                id: crypto.randomUUID(),
              }),
            })

            if (!response.ok) {
              throw new Error('Failed to start migration')
            }

            const result = await response.json()
            return result.migrationId

          } catch (error) {
            console.error('Failed to migrate account:', error)
            throw error
          }
        },

        createBackup: async (accountId: string, config: Partial<AccountBackup>) => {
          try {
            const response = await fetch(`/api/accounts/${accountId}/backup`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                ...config,
                id: crypto.randomUUID(),
                accountId,
                createdAt: new Date(),
              }),
            })

            if (!response.ok) {
              throw new Error('Failed to create backup')
            }

            const result = await response.json()
            return result.backupId

          } catch (error) {
            console.error('Failed to create backup:', error)
            throw error
          }
        },

        restoreBackup: async (backupId: string, accountId: string) => {
          try {
            const response = await fetch(`/api/accounts/${accountId}/restore/${backupId}`, {
              method: 'POST',
            })

            if (!response.ok) {
              throw new Error('Failed to restore backup')
            }

            // Refresh account after restore
            await get().refreshAccount(accountId)

          } catch (error) {
            console.error('Failed to restore backup:', error)
            throw error
          }
        },

        // Analytics
        getAccountAnalytics: async (accountId: string, period?: { start: Date; end: Date }) => {
          try {
            const queryParams = new URLSearchParams()
            if (period) {
              queryParams.set('start', period.start.toISOString())
              queryParams.set('end', period.end.toISOString())
            }

            const response = await fetch(`/api/accounts/${accountId}/analytics?${queryParams}`)

            if (!response.ok) {
              throw new Error('Failed to get analytics')
            }

            return await response.json()

          } catch (error) {
            console.error('Failed to get account analytics:', error)
            throw error
          }
        },

        getUnifiedAnalytics: async (accountIds?: string[], period?: { start: Date; end: Date }) => {
          try {
            const body: any = {}
            if (accountIds) body.accountIds = accountIds
            if (period) body.period = period

            const response = await fetch('/api/unified/analytics', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(body),
            })

            if (!response.ok) {
              throw new Error('Failed to get unified analytics')
            }

            return await response.json()

          } catch (error) {
            console.error('Failed to get unified analytics:', error)
            throw error
          }
        },

        // Settings
        updateSettings: async (settings: Partial<MultiAccountSettings>) => {
          try {
            const response = await fetch('/api/multi-account/settings', {
              method: 'PATCH',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(settings),
            })

            if (!response.ok) {
              throw new Error('Failed to update settings')
            }

            set((state) => ({
              settings: { ...state.settings, ...settings }
            }))

          } catch (error) {
            console.error('Failed to update settings:', error)
            throw error
          }
        },

        // Error handling
        clearError: () => set({ error: null }),

        retryFailedOperation: async (operationId: string) => {
          try {
            const response = await fetch(`/api/operations/${operationId}/retry`, {
              method: 'POST',
            })

            if (!response.ok) {
              throw new Error('Failed to retry operation')
            }

            return await response.json()

          } catch (error) {
            console.error('Failed to retry operation:', error)
            throw error
          }
        },

        // Event emission (for internal use)
        emitEvent: (event: MultiAccountEvent) => {
          // This could be enhanced to emit to external listeners
          console.log('Multi-account event:', event)
        },
      }),
      {
        name: 'multi-account-storage',
        partialize: (state) => ({
          accounts: state.accounts,
          activeAccountId: state.activeAccountId,
          defaultAccountId: state.defaultAccountId,
          recentAccounts: state.recentAccounts,
          settings: state.settings,
        }),
      }
    )
  )
)