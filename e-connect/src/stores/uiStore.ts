import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

type Theme = 'light' | 'dark' | 'system'
type ViewMode = 'comfortable' | 'compact' | 'spacious'
type SidebarView = 'inbox' | 'assistant' | 'rules' | 'analytics' | 'settings'

interface Modal {
  id: string
  type: 'compose' | 'settings' | 'rule' | 'bulk-action' | 'confirm' | 'error'
  title?: string
  data?: any
  onConfirm?: () => void
  onCancel?: () => void
}

interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

interface UIState {
  // Layout
  sidebarOpen: boolean
  sidebarCollapsed: boolean
  sidebarView: SidebarView
  detailPanelOpen: boolean
  commandPaletteOpen: boolean
  searchOpen: boolean
  
  // Theme and appearance
  theme: Theme
  viewMode: ViewMode
  fontSize: number
  reducedMotion: boolean
  highContrast: boolean
  
  // Modals
  modals: Modal[]
  activeModal: Modal | null
  
  // Toasts/Notifications
  toasts: Toast[]
  
  // Loading states
  globalLoading: boolean
  loadingMessage: string | null
  
  // Keyboard shortcuts
  shortcutsEnabled: boolean
  shortcutGuideOpen: boolean
  
  // Mobile/Responsive
  isMobile: boolean
  isTablet: boolean
  
  // Actions
  toggleSidebar: () => void
  collapseSidebar: (collapsed: boolean) => void
  setSidebarView: (view: SidebarView) => void
  toggleDetailPanel: () => void
  toggleCommandPalette: () => void
  toggleSearch: () => void
  
  // Theme actions
  setTheme: (theme: Theme) => void
  setViewMode: (mode: ViewMode) => void
  setFontSize: (size: number) => void
  toggleReducedMotion: () => void
  toggleHighContrast: () => void
  
  // Modal actions
  openModal: (modal: Omit<Modal, 'id'>) => void
  closeModal: (modalId?: string) => void
  closeAllModals: () => void
  
  // Toast actions
  showToast: (toast: Omit<Toast, 'id'>) => void
  dismissToast: (toastId: string) => void
  clearToasts: () => void
  
  // Loading actions
  setGlobalLoading: (loading: boolean, message?: string) => void
  
  // Keyboard shortcuts
  toggleShortcuts: () => void
  toggleShortcutGuide: () => void
  
  // Responsive
  setDeviceType: (isMobile: boolean, isTablet: boolean) => void
  
  // Utility
  reset: () => void
}

const initialState = {
  // Layout
  sidebarOpen: true,
  sidebarCollapsed: false,
  sidebarView: 'inbox' as SidebarView,
  detailPanelOpen: false,
  commandPaletteOpen: false,
  searchOpen: false,
  
  // Theme and appearance
  theme: 'system' as Theme,
  viewMode: 'comfortable' as ViewMode,
  fontSize: 16,
  reducedMotion: false,
  highContrast: false,
  
  // Modals
  modals: [],
  activeModal: null,
  
  // Toasts
  toasts: [],
  
  // Loading
  globalLoading: false,
  loadingMessage: null,
  
  // Keyboard shortcuts
  shortcutsEnabled: true,
  shortcutGuideOpen: false,
  
  // Responsive
  isMobile: false,
  isTablet: false,
}

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // Layout actions
      toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),
      
      collapseSidebar: (collapsed: boolean) => set({ sidebarCollapsed: collapsed }),
      
      setSidebarView: (view: SidebarView) => set({ sidebarView: view }),
      
      toggleDetailPanel: () => set((state) => ({ detailPanelOpen: !state.detailPanelOpen })),
      
      toggleCommandPalette: () => set((state) => ({ commandPaletteOpen: !state.commandPaletteOpen })),
      
      toggleSearch: () => set((state) => ({ searchOpen: !state.searchOpen })),
      
      // Theme actions
      setTheme: (theme: Theme) => {
        set({ theme })
        
        // Apply theme to document
        if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
      },
      
      setViewMode: (mode: ViewMode) => set({ viewMode: mode }),
      
      setFontSize: (size: number) => {
        set({ fontSize: size })
        document.documentElement.style.fontSize = `${size}px`
      },
      
      toggleReducedMotion: () => set((state) => {
        const newValue = !state.reducedMotion
        if (newValue) {
          document.documentElement.classList.add('reduce-motion')
        } else {
          document.documentElement.classList.remove('reduce-motion')
        }
        return { reducedMotion: newValue }
      }),
      
      toggleHighContrast: () => set((state) => {
        const newValue = !state.highContrast
        if (newValue) {
          document.documentElement.classList.add('high-contrast')
        } else {
          document.documentElement.classList.remove('high-contrast')
        }
        return { highContrast: newValue }
      }),
      
      // Modal actions
      openModal: (modal) => {
        const newModal: Modal = {
          ...modal,
          id: `modal-${Date.now()}`,
        }
        
        set((state) => ({
          modals: [...state.modals, newModal],
          activeModal: newModal,
        }))
      },
      
      closeModal: (modalId?: string) => {
        if (!modalId) {
          // Close the active modal
          const { activeModal, modals } = get()
          if (!activeModal) return
          
          const remainingModals = modals.filter(m => m.id !== activeModal.id)
          
          set({
            modals: remainingModals,
            activeModal: remainingModals[remainingModals.length - 1] || null,
          })
        } else {
          // Close specific modal
          const { modals, activeModal } = get()
          const remainingModals = modals.filter(m => m.id !== modalId)
          
          set({
            modals: remainingModals,
            activeModal: activeModal?.id === modalId 
              ? remainingModals[remainingModals.length - 1] || null 
              : activeModal,
          })
        }
      },
      
      closeAllModals: () => set({ modals: [], activeModal: null }),
      
      // Toast actions
      showToast: (toast) => {
        const newToast: Toast = {
          ...toast,
          id: `toast-${Date.now()}`,
          duration: toast.duration ?? 5000,
        }
        
        set((state) => ({
          toasts: [...state.toasts, newToast],
        }))
        
        // Auto dismiss after duration
        if (newToast.duration > 0) {
          setTimeout(() => {
            get().dismissToast(newToast.id)
          }, newToast.duration)
        }
      },
      
      dismissToast: (toastId: string) => {
        set((state) => ({
          toasts: state.toasts.filter(t => t.id !== toastId),
        }))
      },
      
      clearToasts: () => set({ toasts: [] }),
      
      // Loading actions
      setGlobalLoading: (loading: boolean, message?: string) => {
        set({
          globalLoading: loading,
          loadingMessage: message || null,
        })
      },
      
      // Keyboard shortcuts
      toggleShortcuts: () => set((state) => ({ shortcutsEnabled: !state.shortcutsEnabled })),
      
      toggleShortcutGuide: () => set((state) => ({ shortcutGuideOpen: !state.shortcutGuideOpen })),
      
      // Responsive
      setDeviceType: (isMobile: boolean, isTablet: boolean) => {
        set({ isMobile, isTablet })
        
        // Auto-collapse sidebar on mobile
        if (isMobile) {
          set({ sidebarOpen: false })
        }
      },
      
      // Reset
      reset: () => set(initialState),
    })
  )
)

// Helper functions for common toast patterns
export const showSuccessToast = (message: string, action?: Toast['action']) => {
  useUIStore.getState().showToast({
    type: 'success',
    message,
    action,
  })
}

export const showErrorToast = (message: string, action?: Toast['action']) => {
  useUIStore.getState().showToast({
    type: 'error',
    message,
    duration: 0, // Don't auto-dismiss errors
    action,
  })
}

export const showWarningToast = (message: string, action?: Toast['action']) => {
  useUIStore.getState().showToast({
    type: 'warning',
    message,
    action,
  })
}

export const showInfoToast = (message: string, action?: Toast['action']) => {
  useUIStore.getState().showToast({
    type: 'info',
    message,
    action,
  })
}

// Helper for confirmation modals
export const showConfirmModal = (
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void
) => {
  useUIStore.getState().openModal({
    type: 'confirm',
    title,
    data: { message },
    onConfirm,
    onCancel,
  })
}