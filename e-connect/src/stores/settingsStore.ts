import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import {
  SettingsState,
  SettingsActions,
  UserSettings,
  UserProfile,
  SecuritySettings,
  PrivacySettings,
  NotificationSettings,
  ThemeSettings,
  EmailPreferences,
  IntegrationSettings,
  AISettings,
  PerformanceSettings,
  KeyboardShortcuts,
  SettingsMetadata,
  SettingsBackup,
  SettingsImportResult,
  SettingsHealthReport,
  SettingsHealthIssue,
  WebhookConfiguration,
  ApiKeyConfiguration,
  EmailSignature,
  OAuthConnection,
  SETTINGS_PRESETS,
  SettingsPreset,
} from '../types/settings';
import { z } from 'zod';

// Default settings
const createDefaultSettings = (): UserSettings => ({
  profile: {
    id: '',
    name: '',
    email: '',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    locale: navigator.language || 'en-US',
  },
  security: {
    twoFactorEnabled: false,
    twoFactorMethod: 'authenticator',
    backupCodes: [],
    sessionTimeout: 480, // 8 hours
    maxSessions: 5,
    passwordLastChanged: new Date(),
    securityNotifications: true,
    suspiciousActivityAlerts: true,
    loginHistory: [],
  },
  privacy: {
    dataProcessingConsent: false,
    marketingEmailsConsent: false,
    analyticsConsent: false,
    thirdPartyIntegrationsConsent: false,
    dataRetentionPeriod: 365,
    allowDataExport: true,
    allowDataDeletion: true,
    gdprCompliant: true,
    cookiePreferences: {
      essential: true,
      analytics: false,
      marketing: false,
      personalization: false,
      thirdParty: false,
    },
  },
  notifications: {
    email: {
      enabled: true,
      newEmails: true,
      importantEmails: true,
      mentions: true,
      rules: false,
      bulkOperations: false,
      securityAlerts: true,
      systemUpdates: false,
      frequency: 'immediate',
    },
    push: {
      enabled: false,
      newEmails: false,
      importantEmails: false,
      mentions: false,
      sound: true,
      vibration: true,
      showPreview: false,
    },
    inApp: {
      enabled: true,
      position: 'top-right',
      duration: 5,
      showIcons: true,
      groupSimilar: true,
    },
    digest: {
      enabled: false,
      frequency: 'daily',
      time: '09:00',
      includeStats: true,
      includeTopSenders: true,
      includeRuleActivity: false,
    },
    quiet: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      weekendsOnly: false,
      exceptions: [],
    },
  },
  theme: {
    mode: 'auto',
    accentColor: '#3b82f6',
    colorScheme: 'blue',
    density: 'comfortable',
    fontSize: 'medium',
    fontFamily: 'system-ui, -apple-system, sans-serif',
    reducedMotion: false,
    highContrast: false,
  },
  email: {
    signature: {
      id: 'default',
      name: 'Default',
      content: '',
      isDefault: true,
      useForReplies: true,
      useForForwards: false,
    },
    autoReply: {
      enabled: false,
      subject: 'Auto-reply',
      message: 'Thank you for your email. I will get back to you soon.',
      sendToKnownContactsOnly: true,
      sendOnce: true,
      excludeDomains: [],
    },
    threading: {
      enabled: true,
      groupBySubject: true,
      maxThreadDepth: 10,
      collapseRead: false,
      showParticipants: true,
      sortOrder: 'chronological',
    },
    reading: {
      markAsReadDelay: 2,
      autoAdvance: false,
      showImages: 'known-senders',
      showExternalContent: false,
      previewLength: 200,
      openInNewTab: false,
      keyboardShortcuts: true,
    },
    compose: {
      defaultFormat: 'html',
      autoSave: true,
      autoSaveInterval: 30,
      spellCheck: true,
      suggestContacts: true,
      sendDelay: 5,
      requireSubject: true,
      warnLargeAttachments: 25,
    },
    forwarding: {
      enabled: false,
      forwardTo: [],
      keepCopy: true,
      forwardFiltered: false,
      includeOriginalHeaders: false,
    },
  },
  integrations: {
    oauth: [],
    webhooks: [],
    apiKeys: [],
    calendar: {
      enabled: false,
      provider: 'google',
      syncMeetingInvites: false,
      createEventsFromEmails: false,
      defaultCalendar: '',
      reminderSettings: {
        enabled: false,
        minutesBefore: 15,
      },
    },
    crm: {
      enabled: false,
      provider: 'salesforce',
      syncContacts: false,
      createLeadsFromEmails: false,
      trackEmailActivity: false,
      customFields: {},
    },
    productivity: [],
  },
  ai: {
    model: {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 1000,
    },
    features: {
      autoCategories: true,
      smartReplies: true,
      summaries: false,
      priorityScoring: true,
      sentimentAnalysis: false,
      languageDetection: true,
      spamDetection: true,
      phishingDetection: true,
    },
    training: {
      personalizeResponses: false,
      learnFromCorrections: false,
      shareAnonymizedData: false,
      retainTrainingData: 30,
    },
    privacy: {
      processLocally: false,
      encryptData: true,
      anonymizeBeforeProcessing: true,
      optOutOfImprovement: false,
    },
  },
  performance: {
    cache: {
      enabled: true,
      maxSize: 100,
      ttl: 3600,
      preloadNextPage: false,
      cacheImages: true,
      cacheAttachments: false,
    },
    sync: {
      enabled: true,
      interval: 30,
      batchSize: 50,
      conflictResolution: 'server-wins',
      offlineMode: false,
      backgroundSync: true,
    },
    debug: {
      enabled: false,
      logLevel: 'warn',
      includeNetworkLogs: false,
      includePerformanceLogs: false,
      maxLogSize: 10,
      uploadLogs: false,
    },
    experimental: {
      enabled: false,
      features: {},
      betaOptIn: false,
      feedbackEnabled: false,
    },
  },
  shortcuts: {
    enabled: true,
    shortcuts: {
      'compose': {
        id: 'compose',
        name: 'Compose Email',
        description: 'Open compose dialog',
        keys: ['c'],
        action: 'compose',
        enabled: true,
      },
      'search': {
        id: 'search',
        name: 'Search',
        description: 'Focus search bar',
        keys: ['/', 'cmd+k'],
        action: 'search',
        enabled: true,
      },
      'archive': {
        id: 'archive',
        name: 'Archive',
        description: 'Archive selected emails',
        keys: ['e'],
        action: 'archive',
        context: 'email-list',
        enabled: true,
      },
      'delete': {
        id: 'delete',
        name: 'Delete',
        description: 'Delete selected emails',
        keys: ['#', 'delete'],
        action: 'delete',
        context: 'email-list',
        enabled: true,
      },
      'reply': {
        id: 'reply',
        name: 'Reply',
        description: 'Reply to current email',
        keys: ['r'],
        action: 'reply',
        context: 'email-view',
        enabled: true,
      },
      'reply-all': {
        id: 'reply-all',
        name: 'Reply All',
        description: 'Reply to all recipients',
        keys: ['a'],
        action: 'reply-all',
        context: 'email-view',
        enabled: true,
      },
      'forward': {
        id: 'forward',
        name: 'Forward',
        description: 'Forward current email',
        keys: ['f'],
        action: 'forward',
        context: 'email-view',
        enabled: true,
      },
    },
    customShortcuts: [],
  },
  metadata: {
    version: '1.0.0',
    lastModified: new Date(),
    modifiedBy: '',
    checksum: '',
    conflicts: [],
  },
});

type SettingsStore = SettingsState & SettingsActions;

export const useSettingsStore = create<SettingsStore>()(
  persist(
    immer((set, get) => ({
      // Initial state
      settings: createDefaultSettings(),
      loading: false,
      saving: false,
      error: null,
      hasUnsavedChanges: false,
      backups: [],
      searchQuery: '',
      activeSection: 'overview',
      isImporting: false,
      isExporting: false,

      // Initialize settings
      initializeSettings: async () => {
        try {
          await get().loadSettings();
        } catch (error) {
          console.warn('Failed to load settings, using defaults:', error);
          // Settings will fall back to defaults
        }
      },

      // Load and save actions
      loadSettings: async () => {
        set((state) => {
          state.loading = true;
          state.error = null;
        });

        try {
          const response = await fetch('/api/settings');
          if (!response.ok) throw new Error('Failed to load settings');
          
          const settings = await response.json();
          
          set((state) => {
            state.settings = { ...state.settings, ...settings };
            state.loading = false;
            state.hasUnsavedChanges = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to load settings';
            state.loading = false;
          });
        }
      },

      saveSettings: async (partialSettings) => {
        set((state) => {
          state.saving = true;
          state.error = null;
        });

        try {
          const settingsToSave = partialSettings || get().settings;
          
          // Update metadata
          const updatedSettings = {
            ...settingsToSave,
            metadata: {
              ...settingsToSave.metadata,
              lastModified: new Date(),
              version: get().settings.metadata.version,
            },
          };

          const response = await fetch('/api/settings', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(updatedSettings),
          });

          if (!response.ok) throw new Error('Failed to save settings');

          set((state) => {
            state.settings = updatedSettings;
            state.saving = false;
            state.hasUnsavedChanges = false;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to save settings';
            state.saving = false;
          });
        }
      },

      resetSettings: async () => {
        const defaultSettings = createDefaultSettings();
        await get().saveSettings(defaultSettings);
      },

      // Individual setting updates
      updateProfile: (profile) => {
        set((state) => {
          state.settings.profile = { ...state.settings.profile, ...profile };
          state.hasUnsavedChanges = true;
        });
      },

      updateSecurity: (security) => {
        set((state) => {
          state.settings.security = { ...state.settings.security, ...security };
          state.hasUnsavedChanges = true;
        });
      },

      updatePrivacy: (privacy) => {
        set((state) => {
          state.settings.privacy = { ...state.settings.privacy, ...privacy };
          state.hasUnsavedChanges = true;
        });
      },

      updateNotifications: (notifications) => {
        set((state) => {
          state.settings.notifications = { ...state.settings.notifications, ...notifications };
          state.hasUnsavedChanges = true;
        });
      },

      updateTheme: (theme) => {
        set((state) => {
          state.settings.theme = { ...state.settings.theme, ...theme };
          state.hasUnsavedChanges = true;
        });
      },

      updateEmailPreferences: (email) => {
        set((state) => {
          state.settings.email = { ...state.settings.email, ...email };
          state.hasUnsavedChanges = true;
        });
      },

      updateIntegrations: (integrations) => {
        set((state) => {
          state.settings.integrations = { ...state.settings.integrations, ...integrations };
          state.hasUnsavedChanges = true;
        });
      },

      updateAI: (ai) => {
        set((state) => {
          state.settings.ai = { ...state.settings.ai, ...ai };
          state.hasUnsavedChanges = true;
        });
      },

      updatePerformance: (performance) => {
        set((state) => {
          state.settings.performance = { ...state.settings.performance, ...performance };
          state.hasUnsavedChanges = true;
        });
      },

      updateShortcuts: (shortcuts) => {
        set((state) => {
          state.settings.shortcuts = { ...state.settings.shortcuts, ...shortcuts };
          state.hasUnsavedChanges = true;
        });
      },

      // OAuth and integrations
      connectOAuth: async (provider, credentials) => {
        try {
          const response = await fetch('/api/settings/oauth/connect', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ provider, credentials }),
          });

          if (!response.ok) throw new Error('Failed to connect OAuth');

          const connection = await response.json();
          
          set((state) => {
            state.settings.integrations.oauth.push(connection);
            state.hasUnsavedChanges = true;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to connect OAuth';
          });
        }
      },

      disconnectOAuth: async (connectionId) => {
        try {
          const response = await fetch(`/api/settings/oauth/${connectionId}`, {
            method: 'DELETE',
          });

          if (!response.ok) throw new Error('Failed to disconnect OAuth');

          set((state) => {
            state.settings.integrations.oauth = state.settings.integrations.oauth.filter(
              (conn) => conn.id !== connectionId
            );
            state.hasUnsavedChanges = true;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to disconnect OAuth';
          });
        }
      },

      addWebhook: async (webhook) => {
        try {
          const response = await fetch('/api/settings/webhooks', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(webhook),
          });

          if (!response.ok) throw new Error('Failed to add webhook');

          const newWebhook = await response.json();
          
          set((state) => {
            state.settings.integrations.webhooks.push(newWebhook);
            state.hasUnsavedChanges = true;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to add webhook';
          });
        }
      },

      updateWebhook: async (id, webhook) => {
        try {
          const response = await fetch(`/api/settings/webhooks/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(webhook),
          });

          if (!response.ok) throw new Error('Failed to update webhook');

          const updatedWebhook = await response.json();
          
          set((state) => {
            const index = state.settings.integrations.webhooks.findIndex((w) => w.id === id);
            if (index !== -1) {
              state.settings.integrations.webhooks[index] = updatedWebhook;
              state.hasUnsavedChanges = true;
            }
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update webhook';
          });
        }
      },

      deleteWebhook: async (id) => {
        try {
          const response = await fetch(`/api/settings/webhooks/${id}`, {
            method: 'DELETE',
          });

          if (!response.ok) throw new Error('Failed to delete webhook');

          set((state) => {
            state.settings.integrations.webhooks = state.settings.integrations.webhooks.filter(
              (w) => w.id !== id
            );
            state.hasUnsavedChanges = true;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete webhook';
          });
        }
      },

      testWebhook: async (id) => {
        try {
          const response = await fetch(`/api/settings/webhooks/${id}/test`, {
            method: 'POST',
          });

          return response.ok;
        } catch (error) {
          return false;
        }
      },

      // API keys
      addApiKey: async (apiKey) => {
        try {
          const response = await fetch('/api/settings/api-keys', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(apiKey),
          });

          if (!response.ok) throw new Error('Failed to add API key');

          const newApiKey = await response.json();
          
          set((state) => {
            state.settings.integrations.apiKeys.push(newApiKey);
            state.hasUnsavedChanges = true;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to add API key';
          });
        }
      },

      updateApiKey: async (id, apiKey) => {
        try {
          const response = await fetch(`/api/settings/api-keys/${id}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(apiKey),
          });

          if (!response.ok) throw new Error('Failed to update API key');

          const updatedApiKey = await response.json();
          
          set((state) => {
            const index = state.settings.integrations.apiKeys.findIndex((k) => k.id === id);
            if (index !== -1) {
              state.settings.integrations.apiKeys[index] = updatedApiKey;
              state.hasUnsavedChanges = true;
            }
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to update API key';
          });
        }
      },

      deleteApiKey: async (id) => {
        try {
          const response = await fetch(`/api/settings/api-keys/${id}`, {
            method: 'DELETE',
          });

          if (!response.ok) throw new Error('Failed to delete API key');

          set((state) => {
            state.settings.integrations.apiKeys = state.settings.integrations.apiKeys.filter(
              (k) => k.id !== id
            );
            state.hasUnsavedChanges = true;
          });
        } catch (error) {
          set((state) => {
            state.error = error instanceof Error ? error.message : 'Failed to delete API key';
          });
        }
      },

      // Email signatures
      addSignature: async (signature) => {
        const newSignature: EmailSignature = {
          ...signature,
          id: `sig_${Date.now()}`,
        };

        set((state) => {
          // If this is set as default, unset other defaults
          if (newSignature.isDefault) {
            state.settings.email.signature.isDefault = false;
          }
          
          // Add to signatures array (we'll need to extend the email preferences structure)
          state.hasUnsavedChanges = true;
        });
      },

      updateSignature: async (id, signature) => {
        set((state) => {
          if (id === 'default') {
            state.settings.email.signature = { ...state.settings.email.signature, ...signature };
          }
          state.hasUnsavedChanges = true;
        });
      },

      deleteSignature: async (id) => {
        // Implementation for deleting signatures from array
        set((state) => {
          state.hasUnsavedChanges = true;
        });
      },

      setDefaultSignature: async (id) => {
        set((state) => {
          state.settings.email.signature.isDefault = true;
          state.hasUnsavedChanges = true;
        });
      },

      // Backup and restore
      createBackup: async (name, description) => {
        const backup: SettingsBackup = {
          id: `backup_${Date.now()}`,
          name,
          description,
          settings: get().settings,
          createdAt: new Date(),
          size: JSON.stringify(get().settings).length,
          checksum: '', // Calculate checksum
        };

        set((state) => {
          state.backups.push(backup);
        });
      },

      restoreBackup: async (backupId) => {
        const backup = get().backups.find((b) => b.id === backupId);
        if (backup) {
          await get().saveSettings(backup.settings);
        }
      },

      deleteBackup: async (backupId) => {
        set((state) => {
          state.backups = state.backups.filter((b) => b.id !== backupId);
        });
      },

      // Import/export
      exportSettings: async (format) => {
        set((state) => {
          state.isExporting = true;
        });

        try {
          const settings = get().settings;
          let data: string;

          if (format === 'json') {
            data = JSON.stringify(settings, null, 2);
          } else {
            // YAML export would require yaml library
            data = JSON.stringify(settings, null, 2);
          }

          return data;
        } finally {
          set((state) => {
            state.isExporting = false;
          });
        }
      },

      importSettings: async (data, format) => {
        set((state) => {
          state.isImporting = true;
          state.error = null;
        });

        try {
          let settings: Partial<UserSettings>;

          if (format === 'json') {
            settings = JSON.parse(data);
          } else {
            // YAML import would require yaml library
            settings = JSON.parse(data);
          }

          // Validate settings structure
          const validationErrors = await get().validateSettings(settings);
          
          if (validationErrors.length > 0) {
            return {
              success: false,
              imported: [],
              skipped: [],
              errors: validationErrors,
              warnings: [],
            };
          }

          // Merge with current settings
          set((state) => {
            state.settings = { ...state.settings, ...settings };
            state.hasUnsavedChanges = true;
          });

          return {
            success: true,
            imported: Object.keys(settings),
            skipped: [],
            errors: [],
            warnings: [],
          };
        } catch (error) {
          return {
            success: false,
            imported: [],
            skipped: [],
            errors: [error instanceof Error ? error.message : 'Import failed'],
            warnings: [],
          };
        } finally {
          set((state) => {
            state.isImporting = false;
          });
        }
      },

      // Search and navigation
      setSearchQuery: (query) => {
        set((state) => {
          state.searchQuery = query;
        });
      },

      setActiveSection: (section) => {
        set((state) => {
          state.activeSection = section;
        });
      },

      // Validation
      validateSettings: async (settings) => {
        const errors: string[] = [];

        try {
          // Validate individual sections
          if (settings.profile) {
            // Profile validation
          }
          if (settings.security) {
            // Security validation
          }
          // Add more validation as needed
        } catch (error) {
          errors.push(error instanceof Error ? error.message : 'Validation failed');
        }

        return errors;
      },

      // Conflict resolution
      resolveConflict: async (conflictId, resolution) => {
        set((state) => {
          const conflict = state.settings.metadata.conflicts.find((c) => c.id === conflictId);
          if (conflict) {
            conflict.resolved = true;
            // Apply resolution logic here
          }
        });
      },

      // Health check
      checkHealth: async () => {
        const settings = get().settings;
        const issues: SettingsHealthIssue[] = [];
        let score = 100;

        // Security checks
        if (!settings.security.twoFactorEnabled) {
          issues.push({
            id: 'no-2fa',
            severity: 'high',
            category: 'security',
            title: 'Two-Factor Authentication Disabled',
            description: 'Enable 2FA to improve account security',
            fix: 'Go to Security settings and enable Two-Factor Authentication',
            autoFixable: false,
          });
          score -= 20;
        }

        // Privacy checks
        if (!settings.privacy.dataProcessingConsent) {
          issues.push({
            id: 'no-privacy-consent',
            severity: 'medium',
            category: 'privacy',
            title: 'Privacy Settings Not Configured',
            description: 'Review and configure your privacy preferences',
            fix: 'Go to Privacy settings and review your preferences',
            autoFixable: false,
          });
          score -= 10;
        }

        // Performance checks
        if (settings.performance.cache.maxSize > 500) {
          issues.push({
            id: 'large-cache',
            severity: 'low',
            category: 'performance',
            title: 'Large Cache Size',
            description: 'Large cache size may impact performance',
            fix: 'Reduce cache size in Performance settings',
            autoFixable: true,
          });
          score -= 5;
        }

        const overall: 'healthy' | 'warning' | 'critical' = 
          score >= 80 ? 'healthy' : score >= 60 ? 'warning' : 'critical';

        return {
          overall,
          issues,
          recommendations: [
            'Enable two-factor authentication for better security',
            'Review your privacy settings regularly',
            'Keep your notification preferences up to date',
          ],
          score,
        };
      },
    })),
    {
      name: 'e-connect-settings',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        settings: state.settings,
        backups: state.backups,
        activeSection: state.activeSection,
      }),
    }
  )
);

// Preset application utility
export const applySettingsPreset = (preset: SettingsPreset) => {
  const store = useSettingsStore.getState();
  const presetSettings = SETTINGS_PRESETS[preset];
  
  // Deep merge preset with current settings
  const mergedSettings = {
    ...store.settings,
    ...presetSettings,
  };
  
  store.saveSettings(mergedSettings);
};

// Settings validation utilities
export const validateSettingsData = (data: unknown): data is Partial<UserSettings> => {
  try {
    return typeof data === 'object' && data !== null;
  } catch {
    return false;
  }
};

export const getSettingsChangesSummary = (oldSettings: UserSettings, newSettings: UserSettings) => {
  const changes: string[] = [];
  
  // Compare settings sections and build summary
  if (oldSettings.theme.mode !== newSettings.theme.mode) {
    changes.push(`Theme mode changed to ${newSettings.theme.mode}`);
  }
  
  if (oldSettings.notifications.email.enabled !== newSettings.notifications.email.enabled) {
    changes.push(`Email notifications ${newSettings.notifications.email.enabled ? 'enabled' : 'disabled'}`);
  }
  
  // Add more comparison logic as needed
  
  return changes;
};