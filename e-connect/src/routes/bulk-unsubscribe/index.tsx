import { createFileRoute } from '@tanstack/react-router'
import { BulkUnsubscribe } from '../../components/bulk-actions/BulkUnsubscribe'
import { Container } from '../../components/ui/Container'

export const Route = createFileRoute('/bulk-unsubscribe/')({
  component: RouteComponent,
})

function RouteComponent() {
  return (
    <Container className="py-8">
      <BulkUnsubscribe 
        onComplete={(operation) => {
          console.log('Unsubscribe operation completed:', operation)
        }}
      />
    </Container>
  )
}
