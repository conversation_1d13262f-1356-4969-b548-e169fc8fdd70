import { createFileRoute } from '@tanstack/react-router'
import { useState, useMemo } from 'react'
import { format, subDays, startOfDay, endOfDay, subMonths } from 'date-fns'
import {
  CogIcon,
  ClockIcon,
  BoltIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowArrowTrendingUpIcon,
  ArrowArrowTrendingDownIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  InformationCircleIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
  PlayIcon,
  PauseIcon,
  ChartBarIcon,
  LightBulbIcon,
  ShieldCheckIcon,
  BeakerIcon,
} from '@heroicons/react/24/outline'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Tabs } from '@/components/ui/Tabs'
import { Select } from '@/components/ui/Select'
import { Loading } from '@/components/ui/Loading'
import { useQuery } from '@tanstack/react-query'
import { useTheme } from '../../hooks/useTheme'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ScatterChart,
  Scatter,
  PieChart,
  Pie,
  Cell,
  ComposedChart,
  FunnelChart,
  Funnel,
  LabelList,
} from 'recharts'

export const Route = createFileRoute('/stats/rules')({
  component: RulePerformanceAnalytics,
})

interface DateRange {
  start: Date
  end: Date
  label: string
}

const predefinedRanges: DateRange[] = [
  { start: startOfDay(new Date()), end: endOfDay(new Date()), label: 'Today' },
  { start: startOfDay(subDays(new Date(), 7)), end: endOfDay(new Date()), label: 'Last 7 days' },
  { start: startOfDay(subDays(new Date(), 30)), end: endOfDay(new Date()), label: 'Last 30 days' },
  { start: startOfDay(subDays(new Date(), 90)), end: endOfDay(new Date()), label: 'Last 90 days' },
  { start: startOfDay(subMonths(new Date(), 6)), end: endOfDay(new Date()), label: 'Last 6 months' },
]

const sortOptions = [
  { value: 'executions', label: 'Execution Count' },
  { value: 'efficiency', label: 'Efficiency Score' },
  { value: 'timeSaved', label: 'Time Saved' },
  { value: 'successRate', label: 'Success Rate' },
  { value: 'impact', label: 'Impact Score' },
  { value: 'recent', label: 'Recently Modified' },
]

const filterOptions = [
  { value: 'all', label: 'All Rules' },
  { value: 'active', label: 'Active Rules' },
  { value: 'inactive', label: 'Inactive Rules' },
  { value: 'high-performance', label: 'High Performance' },
  { value: 'low-performance', label: 'Low Performance' },
  { value: 'recently-created', label: 'Recently Created' },
  { value: 'needs-attention', label: 'Needs Attention' },
]

const ruleCategories = [
  { value: 'all', label: 'All Categories' },
  { value: 'inbox-management', label: 'Inbox Management' },
  { value: 'labeling', label: 'Labeling' },
  { value: 'forwarding', label: 'Forwarding' },
  { value: 'filtering', label: 'Filtering' },
  { value: 'notifications', label: 'Notifications' },
  { value: 'archiving', label: 'Archiving' },
]

function RulePerformanceAnalytics() {
  const [dateRange, setDateRange] = useState<DateRange>(predefinedRanges[1])
  const [sortBy, setSortBy] = useState('efficiency')
  const [filterBy, setFilterBy] = useState('all')
  const [category, setCategory] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [refreshKey, setRefreshKey] = useState(0)
  const { isDark } = useTheme()

  // Fetch rule performance data
  const { data: ruleData, isLoading: isLoadingRules } = useQuery({
    queryKey: ['rule-performance', dateRange, sortBy, filterBy, category, refreshKey],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/rules?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&sort=${sortBy}&filter=${filterBy}&category=${category}`)
      if (!response.ok) throw new Error('Failed to fetch rule data')
      return response.json()
    },
  })

  const { data: efficiencyData, isLoading: isLoadingEfficiency } = useQuery({
    queryKey: ['rule-efficiency', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/rule-efficiency?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`)
      if (!response.ok) throw new Error('Failed to fetch efficiency data')
      return response.json()
    },
  })

  const { data: automationImpact, isLoading: isLoadingImpact } = useQuery({
    queryKey: ['automation-impact', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/automation-impact?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`)
      if (!response.ok) throw new Error('Failed to fetch automation impact')
      return response.json()
    },
  })

  const { data: conflictAnalysis, isLoading: isLoadingConflicts } = useQuery({
    queryKey: ['rule-conflicts', dateRange, refreshKey],
    queryFn: async () => {
      const response = await fetch(`/api/analytics/rule-conflicts?start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}`)
      if (!response.ok) throw new Error('Failed to fetch conflict analysis')
      return response.json()
    },
  })

  const isLoading = isLoadingRules || isLoadingEfficiency || isLoadingImpact || isLoadingConflicts

  // Filter rules based on search term
  const filteredRules = useMemo(() => {
    if (!ruleData?.rules) return []
    
    let filtered = ruleData.rules
    
    if (searchTerm) {
      filtered = filtered.filter((rule: any) =>
        rule.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        rule.category?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }
    
    return filtered
  }, [ruleData, searchTerm])

  // Calculate summary metrics
  const metrics = useMemo(() => {
    if (!ruleData) return null

    const { summary, topPerformers, underperformers } = ruleData
    return {
      totalRules: summary.totalRules || 0,
      activeRules: summary.activeRules || 0,
      totalExecutions: summary.totalExecutions || 0,
      totalTimeSaved: summary.totalTimeSaved || 0,
      avgEfficiency: summary.avgEfficiency || 0,
      avgSuccessRate: summary.avgSuccessRate || 0,
      topPerformersCount: topPerformers?.length || 0,
      underperformersCount: underperformers?.length || 0,
    }
  }, [ruleData])

  const productivityGains = useMemo(() => {
    if (!automationImpact) return null

    const { productivity, timeSaving, errorReduction } = automationImpact
    return {
      emailsProcessed: productivity.emailsProcessed || 0,
      hoursPerWeek: timeSaving.hoursPerWeek || 0,
      hoursPerMonth: timeSaving.hoursPerMonth || 0,
      errorReductionPercent: errorReduction.percentage || 0,
      manualActionsAvoided: productivity.manualActionsAvoided || 0,
    }
  }, [automationImpact])

  const handleExport = async (format: 'csv' | 'pdf') => {
    try {
      const response = await fetch(`/api/analytics/export?type=rule-performance&format=${format}&start=${dateRange.start.toISOString()}&end=${dateRange.end.toISOString()}&sort=${sortBy}&filter=${filterBy}&category=${category}`)
      if (!response.ok) throw new Error('Export failed')
      
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `rule-analytics-${dateRange.label.toLowerCase().replace(/\s+/g, '-')}.${format}`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)
    } catch (error) {
      console.error('Export failed:', error)
      alert('Export failed. Please try again.')
    }
  }

  const handleToggleRule = async (ruleId: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/rules/${ruleId}/toggle`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !isActive }),
      })
      if (response.ok) {
        setRefreshKey(prev => prev + 1)
      }
    } catch (error) {
      console.error('Failed to toggle rule:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loading size="lg" />
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold">Rule Performance Analytics</h1>
          <p className="text-muted-foreground mt-1">
            Deep insights into automation efficiency and optimization opportunities
          </p>
        </div>
        
        <div className="flex flex-wrap items-center gap-3">
          <div className="relative">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search rules..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
          
          <Select
            value={category}
            onValueChange={setCategory}
            options={ruleCategories}
          />
          
          <Select
            value={filterBy}
            onValueChange={setFilterBy}
            options={filterOptions}
          />
          
          <Select
            value={sortBy}
            onValueChange={setSortBy}
            options={sortOptions}
          />
          
          <Select
            value={dateRange.label}
            onValueChange={(value) => {
              const range = predefinedRanges.find(r => r.label === value)
              if (range) setDateRange(range)
            }}
            options={predefinedRanges.map(r => ({ value: r.label, label: r.label }))}
          />
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setRefreshKey(prev => prev + 1)}
            className="gap-2"
          >
            <ArrowPathIcon className="h-4 w-4" />
            Refresh
          </Button>
          
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('csv')}
              className="gap-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              CSV
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('pdf')}
              className="gap-2"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
              PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Rules"
          value={metrics?.totalRules.toLocaleString() || '0'}
          subtitle={`${metrics?.activeRules || 0} active`}
          icon={<CogIcon className="h-5 w-5" />}
          className="bg-blue-50 dark:bg-blue-900/20"
        />
        
        <MetricCard
          title="Executions"
          value={metrics?.totalExecutions.toLocaleString() || '0'}
          subtitle="In selected period"
          icon={<BoltIcon className="h-5 w-5" />}
          trend={15.2}
          className="bg-green-50 dark:bg-green-900/20"
        />
        
        <MetricCard
          title="Time Saved"
          value={`${(metrics?.totalTimeSaved / 60).toFixed(1) || 0}h`}
          subtitle="Total automation benefit"
          icon={<ClockIcon className="h-5 w-5" />}
          trend={8.7}
          className="bg-purple-50 dark:bg-purple-900/20"
        />
        
        <MetricCard
          title="Avg Efficiency"
          value={`${metrics?.avgEfficiency.toFixed(1) || 0}%`}
          subtitle={`${metrics?.avgSuccessRate.toFixed(1) || 0}% success rate`}
          icon={<ChartBarIcon className="h-5 w-5" />}
          trend={4.3}
          className="bg-orange-50 dark:bg-orange-900/20"
        />
      </div>

      {/* Productivity Impact */}
      <Card className="p-6">
        <h2 className="text-xl font-semibold mb-4">Automation Impact</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="space-y-3">
            <h3 className="font-medium flex items-center gap-2">
              <BoltIcon className="h-4 w-4 text-blue-500" />
              Emails Processed
            </h3>
            <div className="space-y-2">
              <p className="text-2xl font-bold">{productivityGains?.emailsProcessed.toLocaleString() || 0}</p>
              <p className="text-sm text-muted-foreground">Automatically handled</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <h3 className="font-medium flex items-center gap-2">
              <ClockIcon className="h-4 w-4 text-green-500" />
              Time Savings
            </h3>
            <div className="space-y-2">
              <p className="text-2xl font-bold">{productivityGains?.hoursPerWeek.toFixed(1) || 0}h</p>
              <p className="text-sm text-muted-foreground">Per week saved</p>
              <p className="text-xs text-muted-foreground">{productivityGains?.hoursPerMonth.toFixed(1) || 0}h per month</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <h3 className="font-medium flex items-center gap-2">
              <ShieldCheckIcon className="h-4 w-4 text-purple-500" />
              Error Reduction
            </h3>
            <div className="space-y-2">
              <p className="text-2xl font-bold">{productivityGains?.errorReductionPercent.toFixed(1) || 0}%</p>
              <p className="text-sm text-muted-foreground">Fewer manual errors</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <h3 className="font-medium flex items-center gap-2">
              <BeakerIcon className="h-4 w-4 text-orange-500" />
              Manual Actions
            </h3>
            <div className="space-y-2">
              <p className="text-2xl font-bold">{productivityGains?.manualActionsAvoided.toLocaleString() || 0}</p>
              <p className="text-sm text-muted-foreground">Actions automated</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="performance" className="space-y-4">
        <Tabs.List className="grid w-full grid-cols-5">
          <Tabs.Trigger value="performance">Performance</Tabs.Trigger>
          <Tabs.Trigger value="efficiency">Efficiency</Tabs.Trigger>
          <Tabs.Trigger value="conflicts">Conflicts</Tabs.Trigger>
          <Tabs.Trigger value="optimization">Optimization</Tabs.Trigger>
          <Tabs.Trigger value="insights">Insights</Tabs.Trigger>
        </Tabs.List>

        <Tabs.Content value="performance" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Execution Trends</h3>
              <ExecutionTrendsChart data={ruleData?.executionTrends} isDark={isDark} />
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Success Rate Distribution</h3>
              <SuccessRateChart data={ruleData?.successRateDistribution} isDark={isDark} />
            </Card>
          </div>
          
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Rule Performance Details</h3>
            <RuleTable 
              rules={filteredRules} 
              searchTerm={searchTerm}
              onToggleRule={handleToggleRule}
            />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="efficiency" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Efficiency Scores</h3>
              <EfficiencyScoreChart data={efficiencyData?.scores} isDark={isDark} />
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Time Savings Breakdown</h3>
              <TimeSavingsChart data={efficiencyData?.timeSavings} isDark={isDark} />
            </Card>
          </div>
          
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">ROI Analysis</h3>
            <ROIAnalysisTable data={efficiencyData?.roiAnalysis} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="conflicts" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Conflict Detection</h3>
              <ConflictChart data={conflictAnalysis?.conflicts} isDark={isDark} />
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Rule Overlap Analysis</h3>
              <OverlapAnalysisChart data={conflictAnalysis?.overlaps} isDark={isDark} />
            </Card>
          </div>
          
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Conflict Resolution</h3>
            <ConflictTable conflicts={conflictAnalysis?.conflictList} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="optimization" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Performance Funnel</h3>
              <PerformanceFunnelChart data={ruleData?.performanceFunnel} isDark={isDark} />
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Optimization Opportunities</h3>
              <OptimizationChart data={efficiencyData?.optimizationOpportunities} isDark={isDark} />
            </Card>
          </div>
          
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Cost-Benefit Analysis</h3>
            <CostBenefitTable data={efficiencyData?.costBenefit} />
          </Card>
        </Tabs.Content>

        <Tabs.Content value="insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Usage Patterns</h3>
              <UsagePatternsChart data={ruleData?.usagePatterns} isDark={isDark} />
            </Card>
            
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Category Performance</h3>
              <CategoryPerformanceChart data={ruleData?.categoryPerformance} isDark={isDark} />
            </Card>
          </div>
          
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4">Actionable Recommendations</h3>
            <RecommendationsTable recommendations={ruleData?.recommendations} />
          </Card>
        </Tabs.Content>
      </Tabs>
    </div>
  )
}

// Metric Card Component
interface MetricCardProps {
  title: string
  value: string
  subtitle: string
  icon: React.ReactNode
  trend?: number
  className?: string
}

function MetricCard({ title, value, subtitle, icon, trend, className }: MetricCardProps) {
  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold">{value}</p>
          <p className="text-sm text-muted-foreground">{subtitle}</p>
        </div>
        <div className="p-2 rounded-lg bg-background/50">
          {icon}
        </div>
      </div>
      
      {trend !== undefined && (
        <div className="mt-4 flex items-center gap-1">
          {trend > 0 ? (
            <ArrowTrendingUpIcon className="h-4 w-4 text-green-600" />
          ) : (
            <ArrowTrendingDownIcon className="h-4 w-4 text-red-600" />
          )}
          <span className={`text-sm font-medium ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {Math.abs(trend).toFixed(1)}%
          </span>
          <span className="text-sm text-muted-foreground">vs last period</span>
        </div>
      )}
    </Card>
  )
}

// Chart Components
function ExecutionTrendsChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No execution data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <ComposedChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="date" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Legend />
        <Area
          type="monotone"
          dataKey="executions"
          stroke="#3b82f6"
          fill="#3b82f6"
          fillOpacity={0.3}
          name="Executions"
        />
        <Line type="monotone" dataKey="successRate" stroke="#10b981" strokeWidth={2} name="Success Rate %" />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

function SuccessRateChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No success rate data available</p>
      </div>
    )
  }

  const COLORS = ['#ef4444', '#f59e0b', '#10b981', '#3b82f6']

  return (
    <ResponsiveContainer width="100%" height={250}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
          outerRadius={80}
          fill="#8884d8"
          dataKey="count"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ResponsiveContainer>
  )
}

function EfficiencyScoreChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No efficiency data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis 
          dataKey="ruleName" 
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="efficiency" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function TimeSavingsChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No time savings data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="period" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any) => [`${value} min`, 'Time Saved']}
        />
        <Area
          type="monotone"
          dataKey="timeSaved"
          stroke="#10b981"
          fill="#10b981"
          fillOpacity={0.6}
        />
      </AreaChart>
    </ResponsiveContainer>
  )
}

function ConflictChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No conflict data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="type" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="count" fill="#ef4444" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function OverlapAnalysisChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No overlap data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <ScatterChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis type="number" dataKey="similarity" name="Similarity %" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis type="number" dataKey="impact" name="Impact Score" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any, name: string) => [
            `${value}${name === 'similarity' ? '%' : ''}`,
            name === 'similarity' ? 'Similarity' : 'Impact Score'
          ]}
        />
        <Scatter dataKey="impact" fill="#f59e0b" />
      </ScatterChart>
    </ResponsiveContainer>
  )
}

function PerformanceFunnelChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No funnel data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <FunnelChart>
        <Funnel
          dataKey="value"
          data={data}
          isAnimationActive
          fill="#3b82f6"
        >
          <LabelList position="center" />
        </Funnel>
        <Tooltip />
      </FunnelChart>
    </ResponsiveContainer>
  )
}

function OptimizationChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No optimization data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="opportunity" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="potential" fill="#10b981" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

function UsagePatternsChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No usage pattern data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="hour" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Line type="monotone" dataKey="executions" stroke="#8b5cf6" strokeWidth={2} />
      </LineChart>
    </ResponsiveContainer>
  )
}

function CategoryPerformanceChart({ data, isDark }: { data: any[]; isDark: boolean }) {
  if (!data || data.length === 0) {
    return (
      <div className="h-64 flex items-center justify-center text-muted-foreground">
        <p>No category performance data available</p>
      </div>
    )
  }

  return (
    <ResponsiveContainer width="100%" height={250}>
      <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis dataKey="category" stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <YAxis stroke={isDark ? '#9ca3af' : '#6b7280'} />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar dataKey="performance" fill="#06b6d4" radius={[4, 4, 0, 0]} />
      </BarChart>
    </ResponsiveContainer>
  )
}

// Table Components
function RuleTable({ rules, searchTerm, onToggleRule }: { rules: any[]; searchTerm: string; onToggleRule: (id: string, isActive: boolean) => void }) {
  if (!rules || rules.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No rules found {searchTerm ? `matching "${searchTerm}"` : ''}</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3">Rule</th>
            <th className="text-right p-3">Executions</th>
            <th className="text-right p-3">Success Rate</th>
            <th className="text-right p-3">Efficiency</th>
            <th className="text-right p-3">Time Saved</th>
            <th className="text-right p-3">Status</th>
            <th className="text-right p-3">Actions</th>
          </tr>
        </thead>
        <tbody>
          {rules.slice(0, 20).map((rule, index) => (
            <tr key={index} className="border-b hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="p-3">
                <div>
                  <p className="font-medium">{rule.name}</p>
                  <p className="text-sm text-muted-foreground">{rule.description}</p>
                  <p className="text-xs text-muted-foreground capitalize">{rule.category}</p>
                </div>
              </td>
              <td className="text-right p-3">
                <span className="font-medium">{rule.executions}</span>
              </td>
              <td className="text-right p-3">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  rule.successRate > 90 ? 'bg-green-100 text-green-700' :
                  rule.successRate > 70 ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {rule.successRate.toFixed(1)}%
                </span>
              </td>
              <td className="text-right p-3">
                <span className="text-sm">{rule.efficiency.toFixed(1)}%</span>
              </td>
              <td className="text-right p-3">
                <span className="text-sm">{(rule.timeSaved / 60).toFixed(1)}h</span>
              </td>
              <td className="text-right p-3">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  rule.isActive ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                }`}>
                  {rule.isActive ? 'Active' : 'Inactive'}
                </span>
              </td>
              <td className="text-right p-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggleRule(rule.id, rule.isActive)}
                  className="gap-1"
                >
                  {rule.isActive ? (
                    <PauseIcon className="h-4 w-4" />
                  ) : (
                    <PlayIcon className="h-4 w-4" />
                  )}
                </Button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function ROIAnalysisTable({ data }: { data: any[] }) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No ROI analysis available</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3">Metric</th>
            <th className="text-right p-3">Value</th>
            <th className="text-right p-3">Monthly Impact</th>
            <th className="text-right p-3">Annual Projection</th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={index} className="border-b">
              <td className="p-3 font-medium">{item.metric}</td>
              <td className="text-right p-3">{item.value}</td>
              <td className="text-right p-3">{item.monthlyImpact}</td>
              <td className="text-right p-3">{item.annualProjection}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function ConflictTable({ conflicts }: { conflicts: any[] }) {
  if (!conflicts || conflicts.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No conflicts detected</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {conflicts.map((conflict, index) => (
        <div key={index} className="border rounded-lg p-4">
          <div className="flex items-start gap-3">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="font-medium">{conflict.title}</h4>
              <p className="text-sm text-muted-foreground mt-1">{conflict.description}</p>
              <div className="mt-3 flex items-center gap-4">
                <span className="text-xs text-muted-foreground">Affected Rules:</span>
                <div className="flex flex-wrap gap-1">
                  {conflict.affectedRules?.map((rule: string, idx: number) => (
                    <span key={idx} className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs">
                      {rule}
                    </span>
                  ))}
                </div>
              </div>
              <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                <div className="flex items-start gap-2">
                  <LightBulbIcon className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-blue-700 dark:text-blue-300">{conflict.recommendation}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

function CostBenefitTable({ data }: { data: any[] }) {
  if (!data || data.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No cost-benefit analysis available</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b">
            <th className="text-left p-3">Rule Category</th>
            <th className="text-right p-3">Setup Cost</th>
            <th className="text-right p-3">Maintenance Cost</th>
            <th className="text-right p-3">Time Savings</th>
            <th className="text-right p-3">ROI</th>
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => (
            <tr key={index} className="border-b">
              <td className="p-3 font-medium capitalize">{item.category}</td>
              <td className="text-right p-3">{item.setupCost}</td>
              <td className="text-right p-3">{item.maintenanceCost}</td>
              <td className="text-right p-3">{item.timeSavings}</td>
              <td className="text-right p-3">
                <span className={`px-2 py-1 rounded-full text-xs ${
                  item.roi > 200 ? 'bg-green-100 text-green-700' :
                  item.roi > 100 ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {item.roi}%
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )
}

function RecommendationsTable({ recommendations }: { recommendations: any[] }) {
  if (!recommendations || recommendations.length === 0) {
    return (
      <div className="text-center text-muted-foreground py-8">
        <p>No recommendations available</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {recommendations.map((rec, index) => (
        <div key={index} className="border rounded-lg p-4">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0 mt-1">
              <div className={`w-3 h-3 rounded-full ${
                rec.priority === 'high' ? 'bg-red-500' :
                rec.priority === 'medium' ? 'bg-yellow-500' :
                'bg-green-500'
              }`} />
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <h4 className="font-medium">{rec.title}</h4>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  rec.impact === 'high' ? 'bg-purple-100 text-purple-700' :
                  rec.impact === 'medium' ? 'bg-blue-100 text-blue-700' :
                  'bg-gray-100 text-gray-700'
                }`}>
                  {rec.impact} impact
                </span>
              </div>
              <p className="text-sm text-muted-foreground">{rec.description}</p>
              <div className="mt-3 p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                <div className="flex items-start gap-2">
                  <LightBulbIcon className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm text-green-700 dark:text-green-300 font-medium">Action:</p>
                    <p className="text-sm text-green-700 dark:text-green-300">{rec.action}</p>
                  </div>
                </div>
              </div>
              {rec.expectedBenefit && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Expected benefit: {rec.expectedBenefit}
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}