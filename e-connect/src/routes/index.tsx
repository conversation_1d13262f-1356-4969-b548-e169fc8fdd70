import { createFileRoute } from '@tanstack/react-router'
import { Container } from '@/components/ui/Container'
import { EmailList } from '@/components/EmailList'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'

export const Route = createFileRoute('/')({
  component: Index,
})

function Index() {
  return (
    <Container className="py-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">E-Connect 2</h1>
        <p className="text-gray-600 mt-2">
          Email management powered by AI - TanStack Router version of inbox-zero
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <EmailList />
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Unread emails</span>
                  <span className="font-semibold">12</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total emails</span>
                  <span className="font-semibold">1,234</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Rules active</span>
                  <span className="font-semibold">5</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600">Archived 3 newsletters</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-600">Applied rule to 5 emails</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-600">AI categorized 10 emails</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Container>
  )
}
