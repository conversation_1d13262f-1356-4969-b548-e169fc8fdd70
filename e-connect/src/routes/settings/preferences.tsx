import { createFileRoute } from '@tanstack/react-router';
import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { 
  SettingsSection, 
  SettingsItem, 
  SettingsGrid, 
  SettingsCard, 
  SettingsCardGroup,
  SettingsDropdownSelect,
  SettingsColorPicker,
  SettingsSlider
} from '../../components/settings';
import { Toggle } from '../../components/ui/Toggle';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Container } from '../../components/ui/Container';
import { Badge } from '../../components/ui/Badge';
import { Tabs } from '../../components/ui/Tabs';
import { clsx } from 'clsx';

export const Route = createFileRoute('/settings/preferences')({
  component: EmailPreferences,
});

function EmailPreferences() {
  const {
    settings,
    updateNotifications,
    updateTheme,
    updateEmailPreferences,
    saveSettings,
    hasUnsavedChanges,
    saving,
  } = useSettingsStore();

  const [activeTab, setActiveTab] = React.useState('email');
  const [signatureContent, setSignatureContent] = React.useState(settings.email.signature.content);

  const handleNotificationChange = (section: string, field: string, value: any) => {
    updateNotifications({
      [section]: {
        ...settings.notifications[section as keyof typeof settings.notifications],
        [field]: value,
      }
    });
  };

  const handleThemeChange = (field: string, value: any) => {
    updateTheme({ [field]: value });
  };

  const handleEmailPreferenceChange = (section: string, field: string, value: any) => {
    updateEmailPreferences({
      [section]: {
        ...settings.email[section as keyof typeof settings.email],
        [field]: value,
      }
    });
  };

  const themeOptions = [
    { value: 'light', label: 'Light', description: 'Light theme' },
    { value: 'dark', label: 'Dark', description: 'Dark theme' },
    { value: 'auto', label: 'Auto', description: 'System preference' },
  ];

  const densityOptions = [
    { value: 'compact', label: 'Compact', description: 'More content, less spacing' },
    { value: 'comfortable', label: 'Comfortable', description: 'Balanced spacing' },
    { value: 'spacious', label: 'Spacious', description: 'More spacing, easier reading' },
  ];

  const fontSizeOptions = [
    { value: 'small', label: 'Small', description: '12px' },
    { value: 'medium', label: 'Medium', description: '14px' },
    { value: 'large', label: 'Large', description: '16px' },
    { value: 'extra-large', label: 'Extra Large', description: '18px' },
  ];

  const emailFormatOptions = [
    { value: 'html', label: 'Rich Text (HTML)', description: 'Formatting, images, links' },
    { value: 'plain', label: 'Plain Text', description: 'Simple text only' },
  ];

  const imageOptions = [
    { value: 'always', label: 'Always show', description: 'Load all images' },
    { value: 'known-senders', label: 'Known senders only', description: 'Load from contacts' },
    { value: 'never', label: 'Never show', description: 'Block all images' },
  ];

  const tabs = [
    { id: 'email', label: 'Email & Compose', icon: '✉️' },
    { id: 'notifications', label: 'Notifications', icon: '🔔' },
    { id: 'theme', label: 'Theme & Appearance', icon: '🎨' },
    { id: 'reading', label: 'Reading', icon: '📖' },
  ];

  return (
    <Container className="py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Email Preferences</h1>
            <p className="text-gray-600 mt-1">
              Customize your email experience and notification settings
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {hasUnsavedChanges && (
              <Badge variant="warning" className="hidden sm:inline-flex">
                Unsaved changes
              </Badge>
            )}
            <Button
              onClick={() => saveSettings()}
              disabled={!hasUnsavedChanges || saving}
              variant={hasUnsavedChanges ? 'primary' : 'secondary'}
              size="sm"
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={clsx(
                  'py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2',
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Email & Compose Tab */}
        {activeTab === 'email' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Email Signature"
              description="Create and manage your email signature"
            >
              <div className="space-y-6">
                <SettingsItem label="Signature Name">
                  <Input
                    value={settings.email.signature.name}
                    onChange={(e) => handleEmailPreferenceChange('signature', 'name', e.target.value)}
                    placeholder="Enter signature name"
                  />
                </SettingsItem>

                <SettingsItem label="Signature Content" orientation="vertical">
                  <div className="space-y-2">
                    <div className="border border-gray-300 rounded-md">
                      <div className="flex items-center justify-between px-3 py-2 border-b border-gray-200 bg-gray-50">
                        <div className="flex items-center gap-2">
                          <Button size="sm" variant="ghost" className="p-1">
                            <strong>B</strong>
                          </Button>
                          <Button size="sm" variant="ghost" className="p-1">
                            <em>I</em>
                          </Button>
                          <Button size="sm" variant="ghost" className="p-1">
                            <u>U</u>
                          </Button>
                          <div className="w-px h-4 bg-gray-300 mx-1"></div>
                          <Button size="sm" variant="ghost" className="p-1">
                            🔗
                          </Button>
                          <Button size="sm" variant="ghost" className="p-1">
                            📷
                          </Button>
                        </div>
                        <span className="text-xs text-gray-500">Rich Text Editor</span>
                      </div>
                      <textarea
                        value={signatureContent}
                        onChange={(e) => {
                          setSignatureContent(e.target.value);
                          handleEmailPreferenceChange('signature', 'content', e.target.value);
                        }}
                        placeholder="Enter your email signature..."
                        rows={6}
                        className="w-full px-3 py-2 border-0 focus:outline-none resize-none"
                      />
                    </div>
                    <p className="text-xs text-gray-500">
                      Use HTML tags for formatting. Variables: {'{name}'}, {'{email}'}, {'{company}'}
                    </p>
                  </div>
                </SettingsItem>

                <SettingsGrid columns={2}>
                  <SettingsItem label="Use for Replies">
                    <Toggle
                      checked={settings.email.signature.useForReplies}
                      onChange={(enabled) => handleEmailPreferenceChange('signature', 'useForReplies', enabled)}
                    />
                  </SettingsItem>

                  <SettingsItem label="Use for Forwards">
                    <Toggle
                      checked={settings.email.signature.useForForwards}
                      onChange={(enabled) => handleEmailPreferenceChange('signature', 'useForForwards', enabled)}
                    />
                  </SettingsItem>
                </SettingsGrid>
              </div>
            </SettingsCard>

            <SettingsCard
              title="Compose Settings"
              description="Configure email composition preferences"
            >
              <SettingsGrid columns={2}>
                <SettingsItem label="Default Format">
                  <SettingsDropdownSelect
                    value={settings.email.compose.defaultFormat}
                    onChange={(value) => handleEmailPreferenceChange('compose', 'defaultFormat', value)}
                    options={emailFormatOptions}
                    showDescription
                  />
                </SettingsItem>

                <SettingsItem label="Auto-save">
                  <Toggle
                    checked={settings.email.compose.autoSave}
                    onChange={(enabled) => handleEmailPreferenceChange('compose', 'autoSave', enabled)}
                    label="Save drafts automatically"
                  />
                </SettingsItem>

                <SettingsItem label="Auto-save Interval">
                  <SettingsSlider
                    value={settings.email.compose.autoSaveInterval}
                    onChange={(value) => handleEmailPreferenceChange('compose', 'autoSaveInterval', value)}
                    min={10}
                    max={300}
                    step={10}
                    formatValue={(val) => `${val}s`}
                    disabled={!settings.email.compose.autoSave}
                  />
                </SettingsItem>

                <SettingsItem label="Send Delay">
                  <SettingsSlider
                    value={settings.email.compose.sendDelay}
                    onChange={(value) => handleEmailPreferenceChange('compose', 'sendDelay', value)}
                    min={0}
                    max={30}
                    step={1}
                    formatValue={(val) => val === 0 ? 'Immediate' : `${val}s`}
                  />
                </SettingsItem>

                <SettingsItem label="Spell Check">
                  <Toggle
                    checked={settings.email.compose.spellCheck}
                    onChange={(enabled) => handleEmailPreferenceChange('compose', 'spellCheck', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Suggest Contacts">
                  <Toggle
                    checked={settings.email.compose.suggestContacts}
                    onChange={(enabled) => handleEmailPreferenceChange('compose', 'suggestContacts', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Require Subject">
                  <Toggle
                    checked={settings.email.compose.requireSubject}
                    onChange={(enabled) => handleEmailPreferenceChange('compose', 'requireSubject', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Large Attachment Warning">
                  <div className="flex items-center gap-2">
                    <SettingsSlider
                      value={settings.email.compose.warnLargeAttachments}
                      onChange={(value) => handleEmailPreferenceChange('compose', 'warnLargeAttachments', value)}
                      min={1}
                      max={100}
                      step={1}
                      formatValue={(val) => `${val}MB`}
                      className="flex-1"
                    />
                  </div>
                </SettingsItem>
              </SettingsGrid>
            </SettingsCard>

            <SettingsCard
              title="Auto-Reply"
              description="Set up automatic responses when you're away"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Auto-Reply">
                  <Toggle
                    checked={settings.email.autoReply.enabled}
                    onChange={(enabled) => handleEmailPreferenceChange('autoReply', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.email.autoReply.enabled && (
                  <>
                    <SettingsGrid columns={2}>
                      <SettingsItem label="Subject">
                        <Input
                          value={settings.email.autoReply.subject}
                          onChange={(e) => handleEmailPreferenceChange('autoReply', 'subject', e.target.value)}
                          placeholder="Auto-reply subject"
                        />
                      </SettingsItem>

                      <SettingsItem label="Send Once Per Contact">
                        <Toggle
                          checked={settings.email.autoReply.sendOnce}
                          onChange={(enabled) => handleEmailPreferenceChange('autoReply', 'sendOnce', enabled)}
                        />
                      </SettingsItem>
                    </SettingsGrid>

                    <SettingsItem label="Message" orientation="vertical">
                      <textarea
                        value={settings.email.autoReply.message}
                        onChange={(e) => handleEmailPreferenceChange('autoReply', 'message', e.target.value)}
                        placeholder="Enter your auto-reply message..."
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </SettingsItem>

                    <SettingsItem label="Known Contacts Only">
                      <Toggle
                        checked={settings.email.autoReply.sendToKnownContactsOnly}
                        onChange={(enabled) => handleEmailPreferenceChange('autoReply', 'sendToKnownContactsOnly', enabled)}
                        label="Only send to people in your contacts"
                      />
                    </SettingsItem>
                  </>
                )}
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* Notifications Tab */}
        {activeTab === 'notifications' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Email Notifications"
              description="Configure when to receive email notifications"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Email Notifications">
                  <Toggle
                    checked={settings.notifications.email.enabled}
                    onChange={(enabled) => handleNotificationChange('email', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.notifications.email.enabled && (
                  <>
                    <SettingsItem label="Notification Frequency">
                      <SettingsDropdownSelect
                        value={settings.notifications.email.frequency}
                        onChange={(value) => handleNotificationChange('email', 'frequency', value)}
                        options={[
                          { value: 'immediate', label: 'Immediate', description: 'Send as they happen' },
                          { value: 'hourly', label: 'Hourly', description: 'Bundle into hourly digest' },
                          { value: 'daily', label: 'Daily', description: 'Once per day summary' },
                          { value: 'weekly', label: 'Weekly', description: 'Weekly summary' },
                        ]}
                        showDescription
                      />
                    </SettingsItem>

                    <SettingsSection title="Notification Types">
                      <SettingsGrid columns={2}>
                        <SettingsItem label="New Emails">
                          <Toggle
                            checked={settings.notifications.email.newEmails}
                            onChange={(enabled) => handleNotificationChange('email', 'newEmails', enabled)}
                          />
                        </SettingsItem>

                        <SettingsItem label="Important Emails">
                          <Toggle
                            checked={settings.notifications.email.importantEmails}
                            onChange={(enabled) => handleNotificationChange('email', 'importantEmails', enabled)}
                          />
                        </SettingsItem>

                        <SettingsItem label="Mentions">
                          <Toggle
                            checked={settings.notifications.email.mentions}
                            onChange={(enabled) => handleNotificationChange('email', 'mentions', enabled)}
                          />
                        </SettingsItem>

                        <SettingsItem label="Rule Executions">
                          <Toggle
                            checked={settings.notifications.email.rules}
                            onChange={(enabled) => handleNotificationChange('email', 'rules', enabled)}
                          />
                        </SettingsItem>

                        <SettingsItem label="Bulk Operations">
                          <Toggle
                            checked={settings.notifications.email.bulkOperations}
                            onChange={(enabled) => handleNotificationChange('email', 'bulkOperations', enabled)}
                          />
                        </SettingsItem>

                        <SettingsItem label="Security Alerts">
                          <Toggle
                            checked={settings.notifications.email.securityAlerts}
                            onChange={(enabled) => handleNotificationChange('email', 'securityAlerts', enabled)}
                          />
                        </SettingsItem>
                      </SettingsGrid>
                    </SettingsSection>
                  </>
                )}
              </div>
            </SettingsCard>

            <SettingsCard
              title="Push Notifications"
              description="Configure browser and mobile push notifications"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Push Notifications">
                  <Toggle
                    checked={settings.notifications.push.enabled}
                    onChange={(enabled) => handleNotificationChange('push', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.notifications.push.enabled && (
                  <SettingsGrid columns={2}>
                    <SettingsItem label="Sound">
                      <Toggle
                        checked={settings.notifications.push.sound}
                        onChange={(enabled) => handleNotificationChange('push', 'sound', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Vibration">
                      <Toggle
                        checked={settings.notifications.push.vibration}
                        onChange={(enabled) => handleNotificationChange('push', 'vibration', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Show Preview">
                      <Toggle
                        checked={settings.notifications.push.showPreview}
                        onChange={(enabled) => handleNotificationChange('push', 'showPreview', enabled)}
                        label="Show email content in notifications"
                      />
                    </SettingsItem>
                  </SettingsGrid>
                )}
              </div>
            </SettingsCard>

            <SettingsCard
              title="Quiet Hours"
              description="Disable notifications during specific times"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Quiet Hours">
                  <Toggle
                    checked={settings.notifications.quiet.enabled}
                    onChange={(enabled) => handleNotificationChange('quiet', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.notifications.quiet.enabled && (
                  <SettingsGrid columns={2}>
                    <SettingsItem label="Start Time">
                      <Input
                        type="time"
                        value={settings.notifications.quiet.startTime}
                        onChange={(e) => handleNotificationChange('quiet', 'startTime', e.target.value)}
                      />
                    </SettingsItem>

                    <SettingsItem label="End Time">
                      <Input
                        type="time"
                        value={settings.notifications.quiet.endTime}
                        onChange={(e) => handleNotificationChange('quiet', 'endTime', e.target.value)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Weekends Only">
                      <Toggle
                        checked={settings.notifications.quiet.weekendsOnly}
                        onChange={(enabled) => handleNotificationChange('quiet', 'weekendsOnly', enabled)}
                      />
                    </SettingsItem>
                  </SettingsGrid>
                )}
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* Theme Tab */}
        {activeTab === 'theme' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Theme Settings"
              description="Customize the look and feel of the application"
            >
              <SettingsGrid columns={2}>
                <SettingsItem label="Theme Mode">
                  <SettingsDropdownSelect
                    value={settings.theme.mode}
                    onChange={(value) => handleThemeChange('mode', value)}
                    options={themeOptions}
                    showDescription
                  />
                </SettingsItem>

                <SettingsItem label="Accent Color">
                  <SettingsColorPicker
                    value={settings.theme.accentColor}
                    onChange={(color) => handleThemeChange('accentColor', color)}
                  />
                </SettingsItem>

                <SettingsItem label="Density">
                  <SettingsDropdownSelect
                    value={settings.theme.density}
                    onChange={(value) => handleThemeChange('density', value)}
                    options={densityOptions}
                    showDescription
                  />
                </SettingsItem>

                <SettingsItem label="Font Size">
                  <SettingsDropdownSelect
                    value={settings.theme.fontSize}
                    onChange={(value) => handleThemeChange('fontSize', value)}
                    options={fontSizeOptions}
                    showDescription
                  />
                </SettingsItem>

                <SettingsItem label="Reduced Motion">
                  <Toggle
                    checked={settings.theme.reducedMotion}
                    onChange={(enabled) => handleThemeChange('reducedMotion', enabled)}
                    label="Reduce animations and transitions"
                  />
                </SettingsItem>

                <SettingsItem label="High Contrast">
                  <Toggle
                    checked={settings.theme.highContrast}
                    onChange={(enabled) => handleThemeChange('highContrast', enabled)}
                    label="Increase color contrast for accessibility"
                  />
                </SettingsItem>
              </SettingsGrid>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* Reading Tab */}
        {activeTab === 'reading' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Reading Preferences"
              description="Configure how emails are displayed and read"
            >
              <SettingsGrid columns={2}>
                <SettingsItem label="Mark as Read Delay">
                  <SettingsSlider
                    value={settings.email.reading.markAsReadDelay}
                    onChange={(value) => handleEmailPreferenceChange('reading', 'markAsReadDelay', value)}
                    min={0}
                    max={10}
                    step={0.5}
                    formatValue={(val) => val === 0 ? 'Immediate' : `${val}s`}
                  />
                </SettingsItem>

                <SettingsItem label="Auto Advance">
                  <Toggle
                    checked={settings.email.reading.autoAdvance}
                    onChange={(enabled) => handleEmailPreferenceChange('reading', 'autoAdvance', enabled)}
                    label="Go to next email after actions"
                  />
                </SettingsItem>

                <SettingsItem label="Show Images">
                  <SettingsDropdownSelect
                    value={settings.email.reading.showImages}
                    onChange={(value) => handleEmailPreferenceChange('reading', 'showImages', value)}
                    options={imageOptions}
                    showDescription
                  />
                </SettingsItem>

                <SettingsItem label="External Content">
                  <Toggle
                    checked={settings.email.reading.showExternalContent}
                    onChange={(enabled) => handleEmailPreferenceChange('reading', 'showExternalContent', enabled)}
                    label="Load external content automatically"
                  />
                </SettingsItem>

                <SettingsItem label="Preview Length">
                  <SettingsSlider
                    value={settings.email.reading.previewLength}
                    onChange={(value) => handleEmailPreferenceChange('reading', 'previewLength', value)}
                    min={50}
                    max={500}
                    step={25}
                    formatValue={(val) => `${val} chars`}
                  />
                </SettingsItem>

                <SettingsItem label="Open in New Tab">
                  <Toggle
                    checked={settings.email.reading.openInNewTab}
                    onChange={(enabled) => handleEmailPreferenceChange('reading', 'openInNewTab', enabled)}
                  />
                </SettingsItem>
              </SettingsGrid>
            </SettingsCard>

            <SettingsCard
              title="Threading"
              description="Configure email conversation threading"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Threading">
                  <Toggle
                    checked={settings.email.threading.enabled}
                    onChange={(enabled) => handleEmailPreferenceChange('threading', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.email.threading.enabled && (
                  <SettingsGrid columns={2}>
                    <SettingsItem label="Group by Subject">
                      <Toggle
                        checked={settings.email.threading.groupBySubject}
                        onChange={(enabled) => handleEmailPreferenceChange('threading', 'groupBySubject', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Show Participants">
                      <Toggle
                        checked={settings.email.threading.showParticipants}
                        onChange={(enabled) => handleEmailPreferenceChange('threading', 'showParticipants', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Collapse Read Messages">
                      <Toggle
                        checked={settings.email.threading.collapseRead}
                        onChange={(enabled) => handleEmailPreferenceChange('threading', 'collapseRead', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Max Thread Depth">
                      <SettingsSlider
                        value={settings.email.threading.maxThreadDepth}
                        onChange={(value) => handleEmailPreferenceChange('threading', 'maxThreadDepth', value)}
                        min={3}
                        max={20}
                        step={1}
                        formatValue={(val) => `${val} levels`}
                      />
                    </SettingsItem>
                  </SettingsGrid>
                )}
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}
      </div>
    </Container>
  );
}
