import { createFileRoute } from '@tanstack/react-router';
import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { 
  SettingsSection, 
  SettingsItem, 
  SettingsGrid, 
  SettingsCard, 
  SettingsCardGroup 
} from '../../components/settings';
import { Toggle } from '../../components/ui/Toggle';
import { Button } from '../../components/ui/Button';
import { Badge } from '../../components/ui/Badge';
import { Input } from '../../components/ui/Input';
import { Container } from '../../components/ui/Container';
import { clsx } from 'clsx';

export const Route = createFileRoute('/settings/')({
  component: SettingsDashboard,
});

function SettingsDashboard() {
  const {
    settings,
    searchQuery,
    setSearchQuery,
    setActiveSection,
    hasUnsavedChanges,
    saveSettings,
    saving,
    checkHealth,
    loadSettings,
    loading,
  } = useSettingsStore();

  const [healthReport, setHealthReport] = React.useState<any>(null);
  const [showQuickSettings, setShowQuickSettings] = React.useState(true);

  React.useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  React.useEffect(() => {
    checkHealth().then(setHealthReport);
  }, [checkHealth]);

  const quickSettings = [
    {
      key: 'emailNotifications',
      label: 'Email Notifications',
      value: settings.notifications.email.enabled,
      onChange: (enabled: boolean) => {
        useSettingsStore.getState().updateNotifications({
          email: { ...settings.notifications.email, enabled }
        });
      },
    },
    {
      key: 'darkMode',
      label: 'Dark Mode',
      value: settings.theme.mode === 'dark',
      onChange: (enabled: boolean) => {
        useSettingsStore.getState().updateTheme({
          mode: enabled ? 'dark' : 'light'
        });
      },
    },
    {
      key: 'keyboardShortcuts',
      label: 'Keyboard Shortcuts',
      value: settings.shortcuts.enabled,
      onChange: (enabled: boolean) => {
        useSettingsStore.getState().updateShortcuts({ enabled });
      },
    },
    {
      key: 'twoFactor',
      label: 'Two-Factor Authentication',
      value: settings.security.twoFactorEnabled,
      onChange: (enabled: boolean) => {
        useSettingsStore.getState().updateSecurity({ twoFactorEnabled: enabled });
      },
    },
  ];

  const settingSections = [
    {
      id: 'account',
      title: 'Account',
      description: 'Manage your profile and account security',
      icon: '👤',
      href: '/settings/account',
      issues: healthReport?.issues.filter((i: any) => i.category === 'security').length || 0,
    },
    {
      id: 'preferences',
      title: 'Email Preferences',
      description: 'Configure email behavior and notifications',
      icon: '✉️',
      href: '/settings/preferences',
      issues: 0,
    },
    {
      id: 'integrations',
      title: 'Integrations',
      description: 'Connect external services and manage API keys',
      icon: '🔗',
      href: '/settings/integrations',
      issues: 0,
    },
    {
      id: 'advanced',
      title: 'Advanced Settings',
      description: 'AI models, performance, and developer tools',
      icon: '⚙️',
      href: '/settings/advanced',
      issues: healthReport?.issues.filter((i: any) => i.category === 'performance').length || 0,
    },
  ];

  const recentActivity = [
    {
      action: 'Updated theme preferences',
      time: '2 hours ago',
      section: 'Appearance',
    },
    {
      action: 'Connected Google account',
      time: '1 day ago',
      section: 'Integrations',
    },
    {
      action: 'Modified notification settings',
      time: '3 days ago',
      section: 'Preferences',
    },
  ];

  if (loading) {
    return (
      <Container className="py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container className="py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
            <p className="text-gray-600 mt-1">
              Manage your account preferences and application settings
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {hasUnsavedChanges && (
              <Badge variant="warning" className="hidden sm:inline-flex">
                Unsaved changes
              </Badge>
            )}
            <Button
              onClick={() => saveSettings()}
              disabled={!hasUnsavedChanges || saving}
              variant={hasUnsavedChanges ? 'primary' : 'secondary'}
              size="sm"
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className="max-w-md">
          <Input
            type="search"
            placeholder="Search settings..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full"
          />
        </div>

        {/* Health Check */}
        {healthReport && (
          <SettingsCard
            title="Settings Health Check"
            className={clsx(
              'border-l-4',
              healthReport.overall === 'healthy' && 'border-l-green-500',
              healthReport.overall === 'warning' && 'border-l-yellow-500',
              healthReport.overall === 'critical' && 'border-l-red-500'
            )}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={clsx(
                  'w-3 h-3 rounded-full',
                  healthReport.overall === 'healthy' && 'bg-green-500',
                  healthReport.overall === 'warning' && 'bg-yellow-500',
                  healthReport.overall === 'critical' && 'bg-red-500'
                )}>
                </div>
                <div>
                  <p className="font-medium text-gray-900">
                    {healthReport.overall === 'healthy' && 'All systems healthy'}
                    {healthReport.overall === 'warning' && 'Some issues detected'}
                    {healthReport.overall === 'critical' && 'Critical issues found'}
                  </p>
                  <p className="text-sm text-gray-600">
                    Health score: {healthReport.score}/100
                    {healthReport.issues.length > 0 && (
                      <span> • {healthReport.issues.length} issue{healthReport.issues.length > 1 ? 's' : ''}</span>
                    )}
                  </p>
                </div>
              </div>
              
              {healthReport.issues.length > 0 && (
                <Button size="sm" variant="secondary">
                  View Issues
                </Button>
              )}
            </div>

            {healthReport.recommendations.length > 0 && (
              <div className="mt-4 space-y-1">
                <p className="text-sm font-medium text-gray-700">Recommendations:</p>
                <ul className="text-sm text-gray-600 space-y-1">
                  {healthReport.recommendations.slice(0, 3).map((rec: string, index: number) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-blue-500 mt-0.5">•</span>
                      {rec}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </SettingsCard>
        )}

        {/* Quick Settings */}
        {showQuickSettings && (
          <SettingsSection
            title="Quick Settings"
            description="Toggle frequently used settings"
            collapsible
            defaultExpanded={true}
            actions={
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setShowQuickSettings(false)}
              >
                Hide
              </Button>
            }
          >
            <SettingsGrid columns={2}>
              {quickSettings.map((setting) => (
                <SettingsItem
                  key={setting.key}
                  label={setting.label}
                  orientation="horizontal"
                >
                  <Toggle
                    checked={setting.value}
                    onChange={setting.onChange}
                    size="sm"
                  />
                </SettingsItem>
              ))}
            </SettingsGrid>
          </SettingsSection>
        )}

        {/* Settings Sections */}
        <SettingsSection title="Settings Categories">
          <SettingsGrid columns={2}>
            {settingSections.map((section) => (
              <SettingsCard
                key={section.id}
                className="hover:shadow-md transition-shadow cursor-pointer group"
                onClick={() => {
                  setActiveSection(section.id);
                  // Navigate to section - you'd use your router here
                  window.location.href = section.href;
                }}
              >
                <div className="flex items-start gap-4">
                  <div className="text-2xl">{section.icon}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {section.title}
                      </h3>
                      {section.issues > 0 && (
                        <Badge variant="warning" size="sm">
                          {section.issues}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600">{section.description}</p>
                  </div>
                  <div className="flex-shrink-0">
                    <svg 
                      className="w-5 h-5 text-gray-400 group-hover:text-blue-600 transition-colors" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </SettingsCard>
            ))}
          </SettingsGrid>
        </SettingsSection>

        {/* Recent Activity */}
        <SettingsSection 
          title="Recent Activity"
          description="Your recent settings changes"
        >
          <SettingsCard>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-xs text-gray-500">{activity.time}</span>
                      <span className="text-xs text-gray-400">•</span>
                      <span className="text-xs text-blue-600">{activity.section}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </SettingsCard>
        </SettingsSection>

        {/* Footer Actions */}
        <div className="flex flex-col sm:flex-row gap-4 p-6 bg-gray-50 rounded-lg">
          <div className="flex-1">
            <h3 className="font-medium text-gray-900 mb-1">Need help?</h3>
            <p className="text-sm text-gray-600">
              Check our documentation or contact support for assistance.
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="secondary" size="sm">
              Documentation
            </Button>
            <Button variant="secondary" size="sm">
              Contact Support
            </Button>
          </div>
        </div>
      </div>
    </Container>
  );
}
