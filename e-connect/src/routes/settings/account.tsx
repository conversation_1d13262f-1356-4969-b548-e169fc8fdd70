import { createFileRoute } from '@tanstack/react-router';
import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { 
  SettingsSection, 
  SettingsItem, 
  SettingsGrid, 
  SettingsCard, 
  SettingsCardGroup,
  SettingsDropdownSelect 
} from '../../components/settings';
import { Toggle } from '../../components/ui/Toggle';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Container } from '../../components/ui/Container';
import { Badge } from '../../components/ui/Badge';
import { Dialog } from '../../components/ui/Dialog';
import { Tooltip } from '../../components/ui/Tooltip';
import { clsx } from 'clsx';

export const Route = createFileRoute('/settings/account')({
  component: AccountSettings,
});

function AccountSettings() {
  const {
    settings,
    updateProfile,
    updateSecurity,
    updatePrivacy,
    saveSettings,
    hasUnsavedChanges,
    saving,
    disconnectOAuth,
  } = useSettingsStore();

  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const [showTwoFactorDialog, setShowTwoFactorDialog] = React.useState(false);
  const [profileForm, setProfileForm] = React.useState(settings.profile);
  const [passwordForm, setPasswordForm] = React.useState({
    current: '',
    new: '',
    confirm: '',
  });

  React.useEffect(() => {
    setProfileForm(settings.profile);
  }, [settings.profile]);

  const handleProfileChange = (field: string, value: string) => {
    const updatedProfile = { ...profileForm, [field]: value };
    setProfileForm(updatedProfile);
    updateProfile(updatedProfile);
  };

  const handleSecurityChange = (field: string, value: any) => {
    updateSecurity({ [field]: value });
  };

  const handlePrivacyChange = (field: string, value: any) => {
    updatePrivacy({ [field]: value });
  };

  const sessionTimeoutOptions = [
    { value: '15', label: '15 minutes', description: 'High security' },
    { value: '60', label: '1 hour', description: 'Balanced' },
    { value: '240', label: '4 hours', description: 'Convenient' },
    { value: '480', label: '8 hours', description: 'All day' },
    { value: '1440', label: '24 hours', description: 'Extended' },
  ];

  const twoFactorMethods = [
    { value: 'authenticator', label: 'Authenticator App', description: 'Most secure', icon: '📱' },
    { value: 'sms', label: 'SMS Text Message', description: 'Convenient', icon: '📱' },
    { value: 'email', label: 'Email Code', description: 'Backup option', icon: '✉️' },
  ];

  const dataRetentionOptions = [
    { value: '30', label: '30 days', description: 'Minimal retention' },
    { value: '90', label: '90 days', description: 'Short term' },
    { value: '365', label: '1 year', description: 'Standard' },
    { value: '1095', label: '3 years', description: 'Extended' },
    { value: '-1', label: 'Forever', description: 'Never delete' },
  ];

  return (
    <Container className="py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Account Settings</h1>
            <p className="text-gray-600 mt-1">
              Manage your profile, security, and privacy preferences
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {hasUnsavedChanges && (
              <Badge variant="warning" className="hidden sm:inline-flex">
                Unsaved changes
              </Badge>
            )}
            <Button
              onClick={() => saveSettings()}
              disabled={!hasUnsavedChanges || saving}
              variant={hasUnsavedChanges ? 'primary' : 'secondary'}
              size="sm"
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        <SettingsCardGroup>
          {/* Profile Information */}
          <SettingsCard
            title="Profile Information"
            description="Update your personal information and preferences"
          >
            <SettingsGrid columns={2}>
              <SettingsItem label="Full Name" required>
                <Input
                  value={profileForm.name}
                  onChange={(e) => handleProfileChange('name', e.target.value)}
                  placeholder="Enter your full name"
                />
              </SettingsItem>

              <SettingsItem label="Email Address" required>
                <Input
                  type="email"
                  value={profileForm.email}
                  onChange={(e) => handleProfileChange('email', e.target.value)}
                  placeholder="Enter your email"
                />
              </SettingsItem>

              <SettingsItem label="Company">
                <Input
                  value={profileForm.company || ''}
                  onChange={(e) => handleProfileChange('company', e.target.value)}
                  placeholder="Company name"
                />
              </SettingsItem>

              <SettingsItem label="Job Title">
                <Input
                  value={profileForm.jobTitle || ''}
                  onChange={(e) => handleProfileChange('jobTitle', e.target.value)}
                  placeholder="Your role"
                />
              </SettingsItem>

              <SettingsItem label="Location">
                <Input
                  value={profileForm.location || ''}
                  onChange={(e) => handleProfileChange('location', e.target.value)}
                  placeholder="City, Country"
                />
              </SettingsItem>

              <SettingsItem label="Timezone" required>
                <SettingsDropdownSelect
                  value={profileForm.timezone}
                  onChange={(value) => handleProfileChange('timezone', value)}
                  options={[
                    { value: 'UTC', label: 'UTC', description: 'Coordinated Universal Time' },
                    { value: 'America/New_York', label: 'Eastern Time', description: 'GMT-5' },
                    { value: 'America/Chicago', label: 'Central Time', description: 'GMT-6' },
                    { value: 'America/Denver', label: 'Mountain Time', description: 'GMT-7' },
                    { value: 'America/Los_Angeles', label: 'Pacific Time', description: 'GMT-8' },
                    { value: 'Europe/London', label: 'London', description: 'GMT+0' },
                    { value: 'Europe/Paris', label: 'Central Europe', description: 'GMT+1' },
                    { value: 'Asia/Tokyo', label: 'Tokyo', description: 'GMT+9' },
                  ]}
                  showDescription
                />
              </SettingsItem>
            </SettingsGrid>

            <SettingsItem label="Bio" orientation="vertical">
              <textarea
                value={profileForm.bio || ''}
                onChange={(e) => handleProfileChange('bio', e.target.value)}
                placeholder="Tell us about yourself..."
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </SettingsItem>
          </SettingsCard>

          {/* Security Settings */}
          <SettingsCard
            title="Security Settings"
            description="Manage your account security and authentication"
          >
            <div className="space-y-6">
              <SettingsItem
                label="Two-Factor Authentication"
                description="Add an extra layer of security to your account"
                help="Protect your account with 2FA using an authenticator app, SMS, or email"
              >
                <div className="flex items-center gap-3">
                  <Toggle
                    checked={settings.security.twoFactorEnabled}
                    onChange={(enabled) => handleSecurityChange('twoFactorEnabled', enabled)}
                  />
                  {settings.security.twoFactorEnabled && (
                    <Button size="sm" variant="secondary" onClick={() => setShowTwoFactorDialog(true)}>
                      Configure
                    </Button>
                  )}
                </div>
              </SettingsItem>

              {settings.security.twoFactorEnabled && (
                <SettingsItem label="2FA Method">
                  <SettingsDropdownSelect
                    value={settings.security.twoFactorMethod}
                    onChange={(value) => handleSecurityChange('twoFactorMethod', value)}
                    options={twoFactorMethods}
                    showDescription
                  />
                </SettingsItem>
              )}

              <SettingsItem
                label="Session Timeout"
                description="Automatically log out after this period of inactivity"
              >
                <SettingsDropdownSelect
                  value={settings.security.sessionTimeout.toString()}
                  onChange={(value) => handleSecurityChange('sessionTimeout', parseInt(value))}
                  options={sessionTimeoutOptions}
                  showDescription
                />
              </SettingsItem>

              <SettingsItem
                label="Maximum Active Sessions"
                description="Limit concurrent login sessions"
              >
                <Input
                  type="number"
                  min="1"
                  max="10"
                  value={settings.security.maxSessions}
                  onChange={(e) => handleSecurityChange('maxSessions', parseInt(e.target.value))}
                  className="w-20"
                />
              </SettingsItem>

              <SettingsItem label="Security Notifications">
                <Toggle
                  checked={settings.security.securityNotifications}
                  onChange={(enabled) => handleSecurityChange('securityNotifications', enabled)}
                  label="Email me about account security events"
                />
              </SettingsItem>

              <SettingsItem label="Suspicious Activity Alerts">
                <Toggle
                  checked={settings.security.suspiciousActivityAlerts}
                  onChange={(enabled) => handleSecurityChange('suspiciousActivityAlerts', enabled)}
                  label="Alert me about unusual login attempts"
                />
              </SettingsItem>
            </div>

            <div className="pt-6 border-t border-gray-200">
              <Button variant="secondary" size="sm">
                Change Password
              </Button>
            </div>
          </SettingsCard>

          {/* Privacy Settings */}
          <SettingsCard
            title="Privacy & Data"
            description="Control how your data is collected and used"
          >
            <div className="space-y-6">
              <SettingsItem
                label="Data Processing Consent"
                description="Allow processing of your data to provide services"
                help="Required for core functionality"
              >
                <Toggle
                  checked={settings.privacy.dataProcessingConsent}
                  onChange={(enabled) => handlePrivacyChange('dataProcessingConsent', enabled)}
                />
              </SettingsItem>

              <SettingsItem label="Marketing Communications">
                <Toggle
                  checked={settings.privacy.marketingEmailsConsent}
                  onChange={(enabled) => handlePrivacyChange('marketingEmailsConsent', enabled)}
                  label="Send me product updates and marketing emails"
                />
              </SettingsItem>

              <SettingsItem label="Analytics">
                <Toggle
                  checked={settings.privacy.analyticsConsent}
                  onChange={(enabled) => handlePrivacyChange('analyticsConsent', enabled)}
                  label="Help improve the product with usage analytics"
                />
              </SettingsItem>

              <SettingsItem label="Third-party Integrations">
                <Toggle
                  checked={settings.privacy.thirdPartyIntegrationsConsent}
                  onChange={(enabled) => handlePrivacyChange('thirdPartyIntegrationsConsent', enabled)}
                  label="Allow connections to third-party services"
                />
              </SettingsItem>

              <SettingsItem
                label="Data Retention Period"
                description="How long to keep your data after account deletion"
              >
                <SettingsDropdownSelect
                  value={settings.privacy.dataRetentionPeriod.toString()}
                  onChange={(value) => handlePrivacyChange('dataRetentionPeriod', parseInt(value))}
                  options={dataRetentionOptions}
                  showDescription
                />
              </SettingsItem>

              <SettingsItem label="Cookie Preferences">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Essential cookies</span>
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">Required</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Analytics cookies</span>
                    <Toggle
                      checked={settings.privacy.cookiePreferences.analytics}
                      onChange={(enabled) => handlePrivacyChange('cookiePreferences', {
                        ...settings.privacy.cookiePreferences,
                        analytics: enabled
                      })}
                      size="sm"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Marketing cookies</span>
                    <Toggle
                      checked={settings.privacy.cookiePreferences.marketing}
                      onChange={(enabled) => handlePrivacyChange('cookiePreferences', {
                        ...settings.privacy.cookiePreferences,
                        marketing: enabled
                      })}
                      size="sm"
                    />
                  </div>
                </div>
              </SettingsItem>
            </div>
          </SettingsCard>

          {/* Active Sessions */}
          <SettingsCard
            title="Active Sessions"
            description="Manage your logged-in devices and sessions"
          >
            <div className="space-y-4">
              {settings.security.loginHistory.map((session, index) => (
                <div key={session.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-start gap-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                      📱
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 flex items-center gap-2">
                        {session.deviceName}
                        {session.isCurrentSession && (
                          <Badge variant="primary" size="sm">Current</Badge>
                        )}
                      </p>
                      <p className="text-sm text-gray-600">{session.location}</p>
                      <p className="text-xs text-gray-500">
                        Last active: {session.lastAccessed.toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  {!session.isCurrentSession && (
                    <Button size="sm" variant="secondary">
                      Revoke
                    </Button>
                  )}
                </div>
              ))}
              
              {settings.security.loginHistory.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>No active sessions found</p>
                </div>
              )}
            </div>
          </SettingsCard>

          {/* Data Export & Deletion */}
          <SettingsCard
            title="Data Management"
            description="Export or delete your account data"
          >
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900">Export Account Data</h4>
                  <p className="text-sm text-gray-600">
                    Download a copy of all your data in JSON format
                  </p>
                </div>
                <Button variant="secondary" size="sm">
                  Export Data
                </Button>
              </div>

              <div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
                <div>
                  <h4 className="font-medium text-red-900">Delete Account</h4>
                  <p className="text-sm text-red-700">
                    Permanently delete your account and all associated data
                  </p>
                </div>
                <Button 
                  variant="secondary" 
                  size="sm"
                  className="text-red-600 border-red-300 hover:bg-red-100"
                  onClick={() => setShowDeleteDialog(true)}
                >
                  Delete Account
                </Button>
              </div>
            </div>
          </SettingsCard>
        </SettingsCardGroup>

        {/* Delete Account Dialog */}
        <Dialog
          isOpen={showDeleteDialog}
          onClose={() => setShowDeleteDialog(false)}
          title="Delete Account"
        >
          <div className="space-y-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Warning</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>This action cannot be undone. This will permanently delete your account and all associated data.</p>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Type your email to confirm deletion:
              </label>
              <Input
                type="email"
                placeholder={settings.profile.email}
                className="w-full"
              />
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => setShowDeleteDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                Delete Account
              </Button>
            </div>
          </div>
        </Dialog>
      </div>
    </Container>
  );
}
