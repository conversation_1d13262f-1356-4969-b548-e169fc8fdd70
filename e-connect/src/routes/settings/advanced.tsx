import { createFileRoute } from '@tanstack/react-router';
import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { 
  SettingsSection, 
  SettingsItem, 
  SettingsGrid, 
  SettingsCard, 
  SettingsCardGroup,
  SettingsDropdownSelect,
  SettingsSlider
} from '../../components/settings';
import { Toggle } from '../../components/ui/Toggle';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Container } from '../../components/ui/Container';
import { Badge } from '../../components/ui/Badge';
import { Dialog } from '../../components/ui/Dialog';
import { clsx } from 'clsx';

export const Route = createFileRoute('/settings/advanced')({
  component: AdvancedSettings,
});

function AdvancedSettings() {
  const {
    settings,
    updateAI,
    updatePerformance,
    updateShortcuts,
    saveSettings,
    hasUnsavedChanges,
    saving,
    exportSettings,
    importSettings,
    createBackup,
    restoreBackup,
    backups,
    isExporting,
    isImporting,
  } = useSettingsStore();

  const [activeTab, setActiveTab] = React.useState('ai');
  const [showImportDialog, setShowImportDialog] = React.useState(false);
  const [showBackupDialog, setShowBackupDialog] = React.useState(false);
  const [importData, setImportData] = React.useState('');
  const [backupName, setBackupName] = React.useState('');
  const [debugLogs, setDebugLogs] = React.useState<string[]>([]);

  const handleAIChange = (section: string, field: string, value: any) => {
    updateAI({
      [section]: {
        ...settings.ai[section as keyof typeof settings.ai],
        [field]: value,
      }
    });
  };

  const handlePerformanceChange = (section: string, field: string, value: any) => {
    updatePerformance({
      [section]: {
        ...settings.performance[section as keyof typeof settings.performance],
        [field]: value,
      }
    });
  };

  const handleExport = async () => {
    try {
      const data = await exportSettings('json');
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `e-connect-settings-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleImport = async () => {
    try {
      const result = await importSettings(importData, 'json');
      if (result.success) {
        setShowImportDialog(false);
        setImportData('');
      }
    } catch (error) {
      console.error('Import failed:', error);
    }
  };

  const handleCreateBackup = async () => {
    try {
      await createBackup(backupName, 'Manual backup');
      setShowBackupDialog(false);
      setBackupName('');
    } catch (error) {
      console.error('Backup failed:', error);
    }
  };

  const aiProviders = [
    { value: 'openai', label: 'OpenAI', description: 'GPT-4, GPT-3.5 Turbo' },
    { value: 'anthropic', label: 'Anthropic', description: 'Claude models' },
    { value: 'google', label: 'Google', description: 'Gemini Pro' },
    { value: 'local', label: 'Local Model', description: 'Self-hosted' },
  ];

  const logLevels = [
    { value: 'error', label: 'Error', description: 'Only errors' },
    { value: 'warn', label: 'Warning', description: 'Errors and warnings' },
    { value: 'info', label: 'Info', description: 'General information' },
    { value: 'debug', label: 'Debug', description: 'Detailed debugging' },
    { value: 'trace', label: 'Trace', description: 'Everything' },
  ];

  const conflictResolutions = [
    { value: 'server-wins', label: 'Server Wins', description: 'Server data takes precedence' },
    { value: 'client-wins', label: 'Client Wins', description: 'Local changes take precedence' },
    { value: 'manual', label: 'Manual', description: 'Ask user to resolve' },
  ];

  const tabs = [
    { id: 'ai', label: 'AI & Models', icon: '🤖' },
    { id: 'performance', label: 'Performance', icon: '⚡' },
    { id: 'shortcuts', label: 'Keyboard Shortcuts', icon: '⌨️' },
    { id: 'data', label: 'Data Management', icon: '💾' },
    { id: 'debug', label: 'Debug & Logs', icon: '🐛' },
  ];

  const addDebugLog = (message: string) => {
    setDebugLogs(prev => [...prev, `[${new Date().toLocaleTimeString()}] ${message}`].slice(-20));
  };

  React.useEffect(() => {
    if (activeTab === 'debug') {
      addDebugLog('Debug panel opened');
    }
  }, [activeTab]);

  return (
    <Container className="py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Advanced Settings</h1>
            <p className="text-gray-600 mt-1">
              Configure AI models, performance, and developer tools
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {hasUnsavedChanges && (
              <Badge variant="warning" className="hidden sm:inline-flex">
                Unsaved changes
              </Badge>
            )}
            <Button
              onClick={() => saveSettings()}
              disabled={!hasUnsavedChanges || saving}
              variant={hasUnsavedChanges ? 'primary' : 'secondary'}
              size="sm"
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={clsx(
                  'py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2',
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* AI & Models Tab */}
        {activeTab === 'ai' && (
          <SettingsCardGroup>
            <SettingsCard
              title="AI Model Configuration"
              description="Configure AI providers and model settings"
            >
              <SettingsGrid columns={2}>
                <SettingsItem label="AI Provider">
                  <SettingsDropdownSelect
                    value={settings.ai.model.provider}
                    onChange={(value) => handleAIChange('model', 'provider', value)}
                    options={aiProviders}
                    showDescription
                  />
                </SettingsItem>

                <SettingsItem label="Model">
                  <Input
                    value={settings.ai.model.model}
                    onChange={(e) => handleAIChange('model', 'model', e.target.value)}
                    placeholder="e.g., gpt-4, claude-3-sonnet"
                  />
                </SettingsItem>

                <SettingsItem label="Temperature">
                  <SettingsSlider
                    value={settings.ai.model.temperature}
                    onChange={(value) => handleAIChange('model', 'temperature', value)}
                    min={0}
                    max={2}
                    step={0.1}
                    formatValue={(val) => val.toFixed(1)}
                  />
                </SettingsItem>

                <SettingsItem label="Max Tokens">
                  <SettingsSlider
                    value={settings.ai.model.maxTokens}
                    onChange={(value) => handleAIChange('model', 'maxTokens', value)}
                    min={100}
                    max={4000}
                    step={100}
                    formatValue={(val) => val.toString()}
                  />
                </SettingsItem>

                <SettingsItem label="Custom Endpoint" orientation="vertical">
                  <Input
                    value={settings.ai.model.customEndpoint || ''}
                    onChange={(e) => handleAIChange('model', 'customEndpoint', e.target.value)}
                    placeholder="https://api.custom-ai.com/v1"
                  />
                </SettingsItem>
              </SettingsGrid>
            </SettingsCard>

            <SettingsCard
              title="AI Features"
              description="Enable or disable specific AI capabilities"
            >
              <SettingsGrid columns={2}>
                <SettingsItem label="Auto-categorization">
                  <Toggle
                    checked={settings.ai.features.autoCategories}
                    onChange={(enabled) => handleAIChange('features', 'autoCategories', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Smart Replies">
                  <Toggle
                    checked={settings.ai.features.smartReplies}
                    onChange={(enabled) => handleAIChange('features', 'smartReplies', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Email Summaries">
                  <Toggle
                    checked={settings.ai.features.summaries}
                    onChange={(enabled) => handleAIChange('features', 'summaries', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Priority Scoring">
                  <Toggle
                    checked={settings.ai.features.priorityScoring}
                    onChange={(enabled) => handleAIChange('features', 'priorityScoring', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Sentiment Analysis">
                  <Toggle
                    checked={settings.ai.features.sentimentAnalysis}
                    onChange={(enabled) => handleAIChange('features', 'sentimentAnalysis', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Language Detection">
                  <Toggle
                    checked={settings.ai.features.languageDetection}
                    onChange={(enabled) => handleAIChange('features', 'languageDetection', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Spam Detection">
                  <Toggle
                    checked={settings.ai.features.spamDetection}
                    onChange={(enabled) => handleAIChange('features', 'spamDetection', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Phishing Detection">
                  <Toggle
                    checked={settings.ai.features.phishingDetection}
                    onChange={(enabled) => handleAIChange('features', 'phishingDetection', enabled)}
                  />
                </SettingsItem>
              </SettingsGrid>
            </SettingsCard>

            <SettingsCard
              title="AI Privacy"
              description="Control how AI processes your data"
            >
              <div className="space-y-6">
                <SettingsItem label="Process Locally">
                  <Toggle
                    checked={settings.ai.privacy.processLocally}
                    onChange={(enabled) => handleAIChange('privacy', 'processLocally', enabled)}
                    label="Process data on device when possible"
                  />
                </SettingsItem>

                <SettingsItem label="Encrypt Data">
                  <Toggle
                    checked={settings.ai.privacy.encryptData}
                    onChange={(enabled) => handleAIChange('privacy', 'encryptData', enabled)}
                    label="Encrypt data before sending to AI providers"
                  />
                </SettingsItem>

                <SettingsItem label="Anonymize Data">
                  <Toggle
                    checked={settings.ai.privacy.anonymizeBeforeProcessing}
                    onChange={(enabled) => handleAIChange('privacy', 'anonymizeBeforeProcessing', enabled)}
                    label="Remove personal information before AI processing"
                  />
                </SettingsItem>

                <SettingsItem label="Opt Out of Improvement">
                  <Toggle
                    checked={settings.ai.privacy.optOutOfImprovement}
                    onChange={(enabled) => handleAIChange('privacy', 'optOutOfImprovement', enabled)}
                    label="Don't use my data to improve AI models"
                  />
                </SettingsItem>
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* Performance Tab */}
        {activeTab === 'performance' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Cache Settings"
              description="Control data caching and storage"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Caching">
                  <Toggle
                    checked={settings.performance.cache.enabled}
                    onChange={(enabled) => handlePerformanceChange('cache', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.performance.cache.enabled && (
                  <SettingsGrid columns={2}>
                    <SettingsItem label="Max Cache Size">
                      <SettingsSlider
                        value={settings.performance.cache.maxSize}
                        onChange={(value) => handlePerformanceChange('cache', 'maxSize', value)}
                        min={10}
                        max={1000}
                        step={10}
                        formatValue={(val) => `${val}MB`}
                      />
                    </SettingsItem>

                    <SettingsItem label="Cache TTL">
                      <SettingsSlider
                        value={settings.performance.cache.ttl}
                        onChange={(value) => handlePerformanceChange('cache', 'ttl', value)}
                        min={300}
                        max={86400}
                        step={300}
                        formatValue={(val) => `${Math.round(val / 3600)}h`}
                      />
                    </SettingsItem>

                    <SettingsItem label="Preload Next Page">
                      <Toggle
                        checked={settings.performance.cache.preloadNextPage}
                        onChange={(enabled) => handlePerformanceChange('cache', 'preloadNextPage', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Cache Images">
                      <Toggle
                        checked={settings.performance.cache.cacheImages}
                        onChange={(enabled) => handlePerformanceChange('cache', 'cacheImages', enabled)}
                      />
                    </SettingsItem>
                  </SettingsGrid>
                )}
              </div>
            </SettingsCard>

            <SettingsCard
              title="Sync Settings"
              description="Configure data synchronization"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Sync">
                  <Toggle
                    checked={settings.performance.sync.enabled}
                    onChange={(enabled) => handlePerformanceChange('sync', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.performance.sync.enabled && (
                  <SettingsGrid columns={2}>
                    <SettingsItem label="Sync Interval">
                      <SettingsSlider
                        value={settings.performance.sync.interval}
                        onChange={(value) => handlePerformanceChange('sync', 'interval', value)}
                        min={5}
                        max={300}
                        step={5}
                        formatValue={(val) => `${val}s`}
                      />
                    </SettingsItem>

                    <SettingsItem label="Batch Size">
                      <SettingsSlider
                        value={settings.performance.sync.batchSize}
                        onChange={(value) => handlePerformanceChange('sync', 'batchSize', value)}
                        min={10}
                        max={200}
                        step={10}
                        formatValue={(val) => val.toString()}
                      />
                    </SettingsItem>

                    <SettingsItem label="Conflict Resolution">
                      <SettingsDropdownSelect
                        value={settings.performance.sync.conflictResolution}
                        onChange={(value) => handlePerformanceChange('sync', 'conflictResolution', value)}
                        options={conflictResolutions}
                        showDescription
                      />
                    </SettingsItem>

                    <SettingsItem label="Background Sync">
                      <Toggle
                        checked={settings.performance.sync.backgroundSync}
                        onChange={(enabled) => handlePerformanceChange('sync', 'backgroundSync', enabled)}
                      />
                    </SettingsItem>
                  </SettingsGrid>
                )}
              </div>
            </SettingsCard>

            <SettingsCard
              title="Experimental Features"
              description="Enable experimental and beta features"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Experimental Features">
                  <Toggle
                    checked={settings.performance.experimental.enabled}
                    onChange={(enabled) => handlePerformanceChange('experimental', 'enabled', enabled)}
                  />
                </SettingsItem>

                <SettingsItem label="Beta Opt-in">
                  <Toggle
                    checked={settings.performance.experimental.betaOptIn}
                    onChange={(enabled) => handlePerformanceChange('experimental', 'betaOptIn', enabled)}
                    label="Receive beta features and updates"
                  />
                </SettingsItem>

                <SettingsItem label="Feedback Collection">
                  <Toggle
                    checked={settings.performance.experimental.feedbackEnabled}
                    onChange={(enabled) => handlePerformanceChange('experimental', 'feedbackEnabled', enabled)}
                    label="Help improve features with usage feedback"
                  />
                </SettingsItem>
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* Data Management Tab */}
        {activeTab === 'data' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Settings Backup & Restore"
              description="Backup and restore your settings"
            >
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <Button
                    onClick={() => setShowBackupDialog(true)}
                    variant="secondary"
                  >
                    Create Backup
                  </Button>
                  <Button
                    onClick={handleExport}
                    disabled={isExporting}
                    variant="secondary"
                  >
                    {isExporting ? 'Exporting...' : 'Export Settings'}
                  </Button>
                  <Button
                    onClick={() => setShowImportDialog(true)}
                    variant="secondary"
                  >
                    Import Settings
                  </Button>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium text-gray-900">Recent Backups</h4>
                  {backups.length === 0 ? (
                    <p className="text-sm text-gray-500">No backups found</p>
                  ) : (
                    backups.slice(0, 5).map((backup) => (
                      <div key={backup.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div>
                          <p className="font-medium text-gray-900">{backup.name}</p>
                          <p className="text-sm text-gray-600">{backup.description}</p>
                          <p className="text-xs text-gray-500">
                            {backup.createdAt.toLocaleDateString()} • {(backup.size / 1024).toFixed(1)}KB
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => restoreBackup(backup.id)}
                          >
                            Restore
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* Debug Tab */}
        {activeTab === 'debug' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Debug Settings"
              description="Configure logging and debugging options"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Debug Mode">
                  <Toggle
                    checked={settings.performance.debug.enabled}
                    onChange={(enabled) => handlePerformanceChange('debug', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.performance.debug.enabled && (
                  <SettingsGrid columns={2}>
                    <SettingsItem label="Log Level">
                      <SettingsDropdownSelect
                        value={settings.performance.debug.logLevel}
                        onChange={(value) => handlePerformanceChange('debug', 'logLevel', value)}
                        options={logLevels}
                        showDescription
                      />
                    </SettingsItem>

                    <SettingsItem label="Max Log Size">
                      <SettingsSlider
                        value={settings.performance.debug.maxLogSize}
                        onChange={(value) => handlePerformanceChange('debug', 'maxLogSize', value)}
                        min={1}
                        max={100}
                        step={1}
                        formatValue={(val) => `${val}MB`}
                      />
                    </SettingsItem>

                    <SettingsItem label="Include Network Logs">
                      <Toggle
                        checked={settings.performance.debug.includeNetworkLogs}
                        onChange={(enabled) => handlePerformanceChange('debug', 'includeNetworkLogs', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Include Performance Logs">
                      <Toggle
                        checked={settings.performance.debug.includePerformanceLogs}
                        onChange={(enabled) => handlePerformanceChange('debug', 'includePerformanceLogs', enabled)}
                      />
                    </SettingsItem>
                  </SettingsGrid>
                )}
              </div>
            </SettingsCard>

            <SettingsCard
              title="Debug Console"
              description="View debug logs and system information"
            >
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">Debug Logs</h4>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={() => setDebugLogs([])}
                  >
                    Clear Logs
                  </Button>
                </div>
                
                <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm h-64 overflow-y-auto">
                  {debugLogs.length === 0 ? (
                    <p className="text-gray-500">No debug logs available</p>
                  ) : (
                    debugLogs.map((log, index) => (
                      <div key={index} className="mb-1">
                        {log}
                      </div>
                    ))
                  )}
                </div>
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* Import Dialog */}
        <Dialog
          isOpen={showImportDialog}
          onClose={() => setShowImportDialog(false)}
          title="Import Settings"
        >
          <div className="space-y-4">
            <SettingsItem label="Settings Data" orientation="vertical">
              <textarea
                value={importData}
                onChange={(e) => setImportData(e.target.value)}
                placeholder="Paste your exported settings JSON here..."
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-mono text-sm"
              />
            </SettingsItem>

            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Warning:</strong> Importing settings will overwrite your current configuration. 
                Consider creating a backup first.
              </p>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => setShowImportDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleImport}
                disabled={!importData.trim() || isImporting}
                className="flex-1"
              >
                {isImporting ? 'Importing...' : 'Import Settings'}
              </Button>
            </div>
          </div>
        </Dialog>

        {/* Backup Dialog */}
        <Dialog
          isOpen={showBackupDialog}
          onClose={() => setShowBackupDialog(false)}
          title="Create Backup"
        >
          <div className="space-y-4">
            <SettingsItem label="Backup Name" required>
              <Input
                value={backupName}
                onChange={(e) => setBackupName(e.target.value)}
                placeholder="Enter backup name"
              />
            </SettingsItem>

            <div className="flex gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => setShowBackupDialog(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleCreateBackup}
                disabled={!backupName.trim()}
                className="flex-1"
              >
                Create Backup
              </Button>
            </div>
          </div>
        </Dialog>
      </div>
    </Container>
  );
}