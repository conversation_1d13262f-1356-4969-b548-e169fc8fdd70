import { createFileRoute } from '@tanstack/react-router';
import React from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { 
  SettingsSection, 
  SettingsItem, 
  SettingsGrid, 
  SettingsCard, 
  SettingsCardGroup,
  SettingsDropdownSelect 
} from '../../components/settings';
import { Toggle } from '../../components/ui/Toggle';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Container } from '../../components/ui/Container';
import { Badge } from '../../components/ui/Badge';
import { Dialog } from '../../components/ui/Dialog';
import { clsx } from 'clsx';

export const Route = createFileRoute('/settings/integrations')({
  component: IntegrationsSettings,
});

function IntegrationsSettings() {
  const {
    settings,
    updateIntegrations,
    saveSettings,
    hasUnsavedChanges,
    saving,
    connectOAuth,
    disconnectOAuth,
    addWebhook,
    updateWebhook,
    deleteWebhook,
    testWebhook,
    addApi<PERSON>ey,
    update<PERSON>pi<PERSON>ey,
    deleteApiKey,
  } = useSettingsStore();

  const [activeTab, setActiveTab] = React.useState('oauth');
  const [showAddWebhook, setShowAddWebhook] = React.useState(false);
  const [showAddApiKey, setShowAddApiKey] = React.useState(false);
  const [webhookForm, setWebhookForm] = React.useState({
    name: '',
    url: '',
    events: [] as string[],
    secret: '',
  });
  const [apiKeyForm, setApiKeyForm] = React.useState({
    name: '',
    service: '',
    key: '',
  });

  const handleIntegrationChange = (section: string, field: string, value: any) => {
    updateIntegrations({
      [section]: {
        ...settings.integrations[section as keyof typeof settings.integrations],
        [field]: value,
      }
    });
  };

  const handleConnectOAuth = async (provider: string) => {
    try {
      await connectOAuth(provider, {});
    } catch (error) {
      console.error('OAuth connection failed:', error);
    }
  };

  const handleAddWebhook = async () => {
    try {
      await addWebhook({
        name: webhookForm.name,
        url: webhookForm.url,
        events: webhookForm.events as any[],
        secret: webhookForm.secret,
        isActive: true,
        headers: {},
        failureCount: 0,
      });
      setShowAddWebhook(false);
      setWebhookForm({ name: '', url: '', events: [], secret: '' });
    } catch (error) {
      console.error('Failed to add webhook:', error);
    }
  };

  const handleAddApiKey = async () => {
    try {
      await addApiKey({
        name: apiKeyForm.name,
        service: apiKeyForm.service,
        keyPreview: `***${apiKeyForm.key.slice(-4)}`,
        scopes: [],
        isActive: true,
      });
      setShowAddApiKey(false);
      setApiKeyForm({ name: '', service: '', key: '' });
    } catch (error) {
      console.error('Failed to add API key:', error);
    }
  };

  const oauthProviders = [
    {
      id: 'google',
      name: 'Google',
      description: 'Gmail, Google Calendar, Google Drive',
      icon: '🟢',
      scopes: ['email', 'calendar', 'drive'],
    },
    {
      id: 'microsoft',
      name: 'Microsoft',
      description: 'Outlook, Office 365, OneDrive',
      icon: '🔵',
      scopes: ['email', 'calendar', 'onedrive'],
    },
    {
      id: 'github',
      name: 'GitHub',
      description: 'Repository notifications, issue tracking',
      icon: '⚫',
      scopes: ['notifications', 'issues'],
    },
    {
      id: 'slack',
      name: 'Slack',
      description: 'Team communication, notifications',
      icon: '💬',
      scopes: ['channels', 'messages'],
    },
  ];

  const webhookEvents = [
    { value: 'email.received', label: 'Email Received' },
    { value: 'email.sent', label: 'Email Sent' },
    { value: 'rule.executed', label: 'Rule Executed' },
    { value: 'bulk.completed', label: 'Bulk Operation Completed' },
    { value: 'user.login', label: 'User Login' },
    { value: 'settings.changed', label: 'Settings Changed' },
  ];

  const tabs = [
    { id: 'oauth', label: 'OAuth Connections', icon: '🔐' },
    { id: 'webhooks', label: 'Webhooks', icon: '🔗' },
    { id: 'api-keys', label: 'API Keys', icon: '🔑' },
    { id: 'services', label: 'External Services', icon: '🌐' },
  ];

  return (
    <Container className="py-8">
      <div className="space-y-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Integrations</h1>
            <p className="text-gray-600 mt-1">
              Connect external services and manage API access
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {hasUnsavedChanges && (
              <Badge variant="warning" className="hidden sm:inline-flex">
                Unsaved changes
              </Badge>
            )}
            <Button
              onClick={() => saveSettings()}
              disabled={!hasUnsavedChanges || saving}
              variant={hasUnsavedChanges ? 'primary' : 'secondary'}
              size="sm"
            >
              {saving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={clsx(
                  'py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2',
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                )}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* OAuth Connections Tab */}
        {activeTab === 'oauth' && (
          <SettingsCardGroup>
            <SettingsCard
              title="OAuth Connections"
              description="Connect your accounts from external services"
            >
              <div className="space-y-4">
                {oauthProviders.map((provider) => {
                  const connection = settings.integrations.oauth.find(c => c.provider === provider.id);
                  const isConnected = !!connection;
                  
                  return (
                    <div key={provider.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="text-2xl">{provider.icon}</div>
                        <div>
                          <h4 className="font-medium text-gray-900 flex items-center gap-2">
                            {provider.name}
                            {isConnected && (
                              <Badge variant="success" size="sm">Connected</Badge>
                            )}
                          </h4>
                          <p className="text-sm text-gray-600">{provider.description}</p>
                          {isConnected && connection && (
                            <p className="text-xs text-gray-500 mt-1">
                              Connected as {connection.email} • Last used: {connection.lastUsed.toLocaleDateString()}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {isConnected ? (
                          <>
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => connection && disconnectOAuth(connection.id)}
                            >
                              Disconnect
                            </Button>
                            <Button size="sm" variant="ghost">
                              Configure
                            </Button>
                          </>
                        ) : (
                          <Button
                            size="sm"
                            variant="primary"
                            onClick={() => handleConnectOAuth(provider.id)}
                          >
                            Connect
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </SettingsCard>

            {settings.integrations.oauth.length > 0 && (
              <SettingsCard
                title="Connection Details"
                description="Manage your connected accounts"
              >
                <div className="space-y-4">
                  {settings.integrations.oauth.map((connection) => (
                    <div key={connection.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-medium text-gray-900">{connection.provider}</h4>
                          <p className="text-sm text-gray-600">{connection.email}</p>
                          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
                            <span>Connected: {connection.connectedAt.toLocaleDateString()}</span>
                            <span>Status: {connection.isActive ? 'Active' : 'Inactive'}</span>
                            <span>Scopes: {connection.scopes.length}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Toggle
                            checked={connection.isActive}
                            onChange={(active) => {
                              // Update connection status
                            }}
                            size="sm"
                          />
                          <Button size="sm" variant="secondary">
                            Refresh
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </SettingsCard>
            )}
          </SettingsCardGroup>
        )}

        {/* Webhooks Tab */}
        {activeTab === 'webhooks' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Webhooks"
              description="Configure HTTP callbacks for events"
              header={
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Webhooks</h3>
                    <p className="text-sm text-gray-600">Configure HTTP callbacks for events</p>
                  </div>
                  <Button onClick={() => setShowAddWebhook(true)}>
                    Add Webhook
                  </Button>
                </div>
              }
            >
              <div className="space-y-4">
                {settings.integrations.webhooks.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No webhooks configured</p>
                    <p className="text-sm">Add a webhook to receive HTTP callbacks for events</p>
                  </div>
                ) : (
                  settings.integrations.webhooks.map((webhook) => (
                    <div key={webhook.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-gray-900">{webhook.name}</h4>
                            <Badge variant={webhook.isActive ? 'success' : 'secondary'} size="sm">
                              {webhook.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                            {webhook.failureCount > 0 && (
                              <Badge variant="warning" size="sm">
                                {webhook.failureCount} failures
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{webhook.url}</p>
                          <div className="flex flex-wrap gap-1 mb-2">
                            {webhook.events.map((event) => (
                              <Badge key={event} variant="secondary" size="sm">
                                {event}
                              </Badge>
                            ))}
                          </div>
                          <p className="text-xs text-gray-500">
                            Created: {webhook.createdAt.toLocaleDateString()}
                            {webhook.lastTriggered && (
                              <span> • Last triggered: {webhook.lastTriggered.toLocaleDateString()}</span>
                            )}
                          </p>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => testWebhook(webhook.id)}
                          >
                            Test
                          </Button>
                          <Button
                            size="sm"
                            variant="secondary"
                            onClick={() => {
                              // Edit webhook
                            }}
                          >
                            Edit
                          </Button>
                          <Button
                            size="sm"
                            variant="secondary"
                            className="text-red-600 hover:bg-red-50"
                            onClick={() => deleteWebhook(webhook.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* API Keys Tab */}
        {activeTab === 'api-keys' && (
          <SettingsCardGroup>
            <SettingsCard
              title="API Keys"
              description="Manage API keys for third-party services"
              header={
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">API Keys</h3>
                    <p className="text-sm text-gray-600">Manage API keys for third-party services</p>
                  </div>
                  <Button onClick={() => setShowAddApiKey(true)}>
                    Add API Key
                  </Button>
                </div>
              }
            >
              <div className="space-y-4">
                {settings.integrations.apiKeys.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No API keys configured</p>
                    <p className="text-sm">Add API keys to connect to external services</p>
                  </div>
                ) : (
                  settings.integrations.apiKeys.map((apiKey) => (
                    <div key={apiKey.id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-gray-900">{apiKey.name}</h4>
                            <Badge variant={apiKey.isActive ? 'success' : 'secondary'} size="sm">
                              {apiKey.isActive ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{apiKey.service}</p>
                          <p className="text-sm text-gray-500 font-mono mb-2">{apiKey.keyPreview}</p>
                          <div className="flex items-center gap-4 text-xs text-gray-500">
                            <span>Created: {apiKey.createdAt.toLocaleDateString()}</span>
                            {apiKey.lastUsed && (
                              <span>Last used: {apiKey.lastUsed.toLocaleDateString()}</span>
                            )}
                            {apiKey.expiresAt && (
                              <span>Expires: {apiKey.expiresAt.toLocaleDateString()}</span>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 ml-4">
                          <Toggle
                            checked={apiKey.isActive}
                            onChange={(active) => updateApiKey(apiKey.id, { isActive: active })}
                            size="sm"
                          />
                          <Button
                            size="sm"
                            variant="secondary"
                            className="text-red-600 hover:bg-red-50"
                            onClick={() => deleteApiKey(apiKey.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* External Services Tab */}
        {activeTab === 'services' && (
          <SettingsCardGroup>
            <SettingsCard
              title="Calendar Integration"
              description="Connect and sync with calendar services"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable Calendar Integration">
                  <Toggle
                    checked={settings.integrations.calendar.enabled}
                    onChange={(enabled) => handleIntegrationChange('calendar', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.integrations.calendar.enabled && (
                  <SettingsGrid columns={2}>
                    <SettingsItem label="Calendar Provider">
                      <SettingsDropdownSelect
                        value={settings.integrations.calendar.provider}
                        onChange={(value) => handleIntegrationChange('calendar', 'provider', value)}
                        options={[
                          { value: 'google', label: 'Google Calendar', description: 'Gmail integration' },
                          { value: 'outlook', label: 'Microsoft Outlook', description: 'Office 365' },
                          { value: 'apple', label: 'Apple Calendar', description: 'iCloud sync' },
                          { value: 'caldav', label: 'CalDAV', description: 'Custom server' },
                        ]}
                        showDescription
                      />
                    </SettingsItem>

                    <SettingsItem label="Default Calendar">
                      <Input
                        value={settings.integrations.calendar.defaultCalendar}
                        onChange={(e) => handleIntegrationChange('calendar', 'defaultCalendar', e.target.value)}
                        placeholder="Calendar name"
                      />
                    </SettingsItem>

                    <SettingsItem label="Sync Meeting Invites">
                      <Toggle
                        checked={settings.integrations.calendar.syncMeetingInvites}
                        onChange={(enabled) => handleIntegrationChange('calendar', 'syncMeetingInvites', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Create Events from Emails">
                      <Toggle
                        checked={settings.integrations.calendar.createEventsFromEmails}
                        onChange={(enabled) => handleIntegrationChange('calendar', 'createEventsFromEmails', enabled)}
                      />
                    </SettingsItem>
                  </SettingsGrid>
                )}
              </div>
            </SettingsCard>

            <SettingsCard
              title="CRM Integration"
              description="Connect to customer relationship management systems"
            >
              <div className="space-y-6">
                <SettingsItem label="Enable CRM Integration">
                  <Toggle
                    checked={settings.integrations.crm.enabled}
                    onChange={(enabled) => handleIntegrationChange('crm', 'enabled', enabled)}
                  />
                </SettingsItem>

                {settings.integrations.crm.enabled && (
                  <SettingsGrid columns={2}>
                    <SettingsItem label="CRM Provider">
                      <SettingsDropdownSelect
                        value={settings.integrations.crm.provider}
                        onChange={(value) => handleIntegrationChange('crm', 'provider', value)}
                        options={[
                          { value: 'salesforce', label: 'Salesforce', description: 'World\'s #1 CRM' },
                          { value: 'hubspot', label: 'HubSpot', description: 'Inbound marketing' },
                          { value: 'pipedrive', label: 'Pipedrive', description: 'Sales pipeline' },
                          { value: 'custom', label: 'Custom', description: 'Custom API' },
                        ]}
                        showDescription
                      />
                    </SettingsItem>

                    <SettingsItem label="Sync Contacts">
                      <Toggle
                        checked={settings.integrations.crm.syncContacts}
                        onChange={(enabled) => handleIntegrationChange('crm', 'syncContacts', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Create Leads from Emails">
                      <Toggle
                        checked={settings.integrations.crm.createLeadsFromEmails}
                        onChange={(enabled) => handleIntegrationChange('crm', 'createLeadsFromEmails', enabled)}
                      />
                    </SettingsItem>

                    <SettingsItem label="Track Email Activity">
                      <Toggle
                        checked={settings.integrations.crm.trackEmailActivity}
                        onChange={(enabled) => handleIntegrationChange('crm', 'trackEmailActivity', enabled)}
                      />
                    </SettingsItem>
                  </SettingsGrid>
                )}
              </div>
            </SettingsCard>
          </SettingsCardGroup>
        )}

        {/* Add Webhook Dialog */}
        <Dialog
          isOpen={showAddWebhook}
          onClose={() => setShowAddWebhook(false)}
          title="Add Webhook"
        >
          <div className="space-y-4">
            <SettingsItem label="Name" required>
              <Input
                value={webhookForm.name}
                onChange={(e) => setWebhookForm({ ...webhookForm, name: e.target.value })}
                placeholder="Webhook name"
              />
            </SettingsItem>

            <SettingsItem label="URL" required>
              <Input
                value={webhookForm.url}
                onChange={(e) => setWebhookForm({ ...webhookForm, url: e.target.value })}
                placeholder="https://example.com/webhook"
              />
            </SettingsItem>

            <SettingsItem label="Events" orientation="vertical">
              <div className="space-y-2">
                {webhookEvents.map((event) => (
                  <div key={event.value} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      id={event.value}
                      checked={webhookForm.events.includes(event.value)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setWebhookForm({
                            ...webhookForm,
                            events: [...webhookForm.events, event.value]
                          });
                        } else {
                          setWebhookForm({
                            ...webhookForm,
                            events: webhookForm.events.filter(ev => ev !== event.value)
                          });
                        }
                      }}
                      className="rounded border-gray-300"
                    />
                    <label htmlFor={event.value} className="text-sm text-gray-700">
                      {event.label}
                    </label>
                  </div>
                ))}
              </div>
            </SettingsItem>

            <SettingsItem label="Secret (Optional)">
              <Input
                value={webhookForm.secret}
                onChange={(e) => setWebhookForm({ ...webhookForm, secret: e.target.value })}
                placeholder="Webhook secret for verification"
                type="password"
              />
            </SettingsItem>

            <div className="flex gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => setShowAddWebhook(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleAddWebhook}
                disabled={!webhookForm.name || !webhookForm.url || webhookForm.events.length === 0}
                className="flex-1"
              >
                Add Webhook
              </Button>
            </div>
          </div>
        </Dialog>

        {/* Add API Key Dialog */}
        <Dialog
          isOpen={showAddApiKey}
          onClose={() => setShowAddApiKey(false)}
          title="Add API Key"
        >
          <div className="space-y-4">
            <SettingsItem label="Name" required>
              <Input
                value={apiKeyForm.name}
                onChange={(e) => setApiKeyForm({ ...apiKeyForm, name: e.target.value })}
                placeholder="API key name"
              />
            </SettingsItem>

            <SettingsItem label="Service" required>
              <Input
                value={apiKeyForm.service}
                onChange={(e) => setApiKeyForm({ ...apiKeyForm, service: e.target.value })}
                placeholder="Service name (e.g., OpenAI, Stripe)"
              />
            </SettingsItem>

            <SettingsItem label="API Key" required>
              <Input
                value={apiKeyForm.key}
                onChange={(e) => setApiKeyForm({ ...apiKeyForm, key: e.target.value })}
                placeholder="Enter your API key"
                type="password"
              />
            </SettingsItem>

            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Security Notice:</strong> API keys are encrypted before storage. 
                Only the last 4 characters will be visible for identification.
              </p>
            </div>

            <div className="flex gap-3 pt-4">
              <Button
                variant="secondary"
                onClick={() => setShowAddApiKey(false)}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleAddApiKey}
                disabled={!apiKeyForm.name || !apiKeyForm.service || !apiKeyForm.key}
                className="flex-1"
              >
                Add API Key
              </Button>
            </div>
          </div>
        </Dialog>
      </div>
    </Container>
  );
}
