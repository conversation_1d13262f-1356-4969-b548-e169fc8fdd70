// AI Assistant and chat-related types
export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system' | 'function';
  content: string;
  timestamp: Date;
  
  // Optional metadata
  metadata?: {
    model?: string;
    tokens?: number;
    processingTime?: number;
    confidence?: number;
    sources?: string[];
    feedback?: {
      rating: number;
      helpful: boolean;
    };
  };
  
  // For function calls
  functionCall?: {
    name: string;
    arguments: Record<string, any>;
    result?: any;
  };
  
  // UI state
  isLoading?: boolean;
  isError?: boolean;
  error?: string;
  isEdited?: boolean;
  editedAt?: Date;
  
  // Attachments
  attachments?: ChatAttachment[];
  
  // Related items
  relatedThreads?: string[];
  relatedMessages?: string[];
  suggestedActions?: Suggestion[];
}

export interface ChatAttachment {
  id: string;
  type: 'email' | 'thread' | 'file' | 'image' | 'link';
  name: string;
  content?: any;
  url?: string;
  preview?: string;
  metadata?: Record<string, any>;
}

export interface Conversation {
  id: string;
  title?: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
  context: ConversationContext;
  settings?: ConversationSettings;
  status: 'active' | 'archived' | 'deleted';
  tags?: string[];
  summary?: string;
}

export interface ConversationContext {
  threadIds?: string[];
  messageIds?: string[];
  category?: string;
  timeRange?: {
    start: Date;
    end: Date;
  };
  focusArea?: 'inbox' | 'rules' | 'analytics' | 'general';
  userIntent?: string;
  extractedEntities?: Entity[];
  currentTask?: Task;
}

export interface ConversationSettings {
  model?: 'gpt-4' | 'gpt-3.5-turbo' | 'claude' | 'custom';
  temperature?: number;
  maxTokens?: number;
  systemPrompt?: string;
  tools?: string[]; // Available tools/functions
  autoSuggest?: boolean;
  streamResponse?: boolean;
}

export interface Suggestion {
  id: string;
  type: SuggestionType;
  title: string;
  description?: string;
  confidence: number;
  priority: 'high' | 'medium' | 'low';
  
  // Action details
  action?: {
    type: string;
    params?: Record<string, any>;
    preview?: string;
  };
  
  // Reasoning
  reasoning?: string;
  impact?: {
    timesSaved?: number; // minutes
    emailsAffected?: number;
    clutterReduced?: number; // percentage
  };
  
  // UI state
  isApplied?: boolean;
  isDismissed?: boolean;
  feedback?: 'helpful' | 'not-helpful';
  appliedAt?: Date;
  dismissedAt?: Date;
}

export type SuggestionType = 
  | 'unsubscribe'
  | 'archive'
  | 'createRule'
  | 'bulkAction'
  | 'organize'
  | 'respond'
  | 'schedule'
  | 'delegate'
  | 'summarize'
  | 'extract'
  | 'custom';

export interface AIResponse {
  id: string;
  query: string;
  response: string;
  type: 'answer' | 'action' | 'suggestion' | 'summary' | 'extraction';
  
  // Structured data
  data?: {
    actions?: PlannedAction[];
    suggestions?: Suggestion[];
    summary?: Summary;
    extraction?: Extraction;
    answer?: Answer;
  };
  
  // Metadata
  model: string;
  tokens: {
    prompt: number;
    completion: number;
    total: number;
  };
  latency: number;
  cost?: number;
  
  // Quality metrics
  confidence?: number;
  sources?: string[];
  citations?: Citation[];
  
  // User feedback
  feedback?: {
    rating?: number;
    helpful?: boolean;
    comment?: string;
    timestamp?: Date;
  };
}

export interface Summary {
  text: string;
  keyPoints: string[];
  actionItems?: string[];
  sentiment?: 'positive' | 'negative' | 'neutral' | 'mixed';
  length: 'brief' | 'detailed' | 'comprehensive';
  topics?: string[];
  entities?: Entity[];
}

export interface Extraction {
  type: 'contact' | 'event' | 'task' | 'order' | 'invoice' | 'custom';
  fields: Record<string, any>;
  confidence: Record<string, number>;
  raw?: string;
  structured?: any;
}

export interface Answer {
  text: string;
  type: 'factual' | 'analytical' | 'creative' | 'instructional';
  confidence: number;
  alternatives?: string[];
  followUp?: string[];
}

export interface Entity {
  type: 'person' | 'organization' | 'location' | 'date' | 'money' | 'email' | 'phone' | 'url' | 'custom';
  value: string;
  normalized?: string;
  metadata?: Record<string, any>;
  confidence?: number;
  position?: {
    start: number;
    end: number;
  };
}

export interface Citation {
  source: string;
  quote?: string;
  url?: string;
  messageId?: string;
  threadId?: string;
  relevance?: number;
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  type: 'email' | 'follow-up' | 'research' | 'organize' | 'custom';
  status: 'pending' | 'in-progress' | 'completed' | 'cancelled';
  priority: 'urgent' | 'high' | 'medium' | 'low';
  
  // Task details
  assignee?: string;
  dueDate?: Date;
  reminderDate?: Date;
  estimatedDuration?: number; // minutes
  actualDuration?: number;
  
  // Related items
  relatedThreadIds?: string[];
  relatedMessageIds?: string[];
  relatedTasks?: string[];
  
  // Progress
  subtasks?: Subtask[];
  completedSubtasks?: number;
  progress?: number; // 0-100
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  createdBy?: string;
  completedBy?: string;
  
  // AI assistance
  aiGenerated?: boolean;
  aiSuggestions?: string[];
  automationEnabled?: boolean;
}

export interface Subtask {
  id: string;
  title: string;
  completed: boolean;
  completedAt?: Date;
  order: number;
}

// AI Models and configurations
export interface AIModel {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'google' | 'custom';
  type: 'chat' | 'completion' | 'embedding' | 'classification';
  capabilities: string[];
  limits: {
    maxTokens: number;
    maxContextLength: number;
    rateLimit?: number;
  };
  pricing?: {
    inputTokens: number; // per 1k tokens
    outputTokens: number;
    currency: string;
  };
  isDefault?: boolean;
  isAvailable?: boolean;
}

// AI Training and feedback
export interface AITrainingData {
  id: string;
  type: 'classification' | 'extraction' | 'suggestion' | 'response';
  input: any;
  expectedOutput: any;
  actualOutput?: any;
  feedback?: 'correct' | 'incorrect' | 'partial';
  corrections?: any;
  timestamp: Date;
  userId?: string;
  modelVersion?: string;
}

export interface AIPerformanceMetrics {
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  avgResponseTime: number;
  totalRequests: number;
  successRate: number;
  userSatisfaction?: number;
  costPerRequest?: number;
  breakdown?: {
    byType: Record<string, AIPerformanceMetrics>;
    byModel: Record<string, AIPerformanceMetrics>;
    byTimeframe: Record<string, AIPerformanceMetrics>;
  };
}

// Assistant prompts and templates
export interface AssistantPrompt {
  id: string;
  name: string;
  template: string;
  variables?: string[];
  category: 'email' | 'analysis' | 'organization' | 'custom';
  examples?: {
    input: Record<string, any>;
    output: string;
  }[];
  isActive: boolean;
  version: number;
}

export interface PlannedAction {
  id: string;
  type: string;
  description: string;
  params?: Record<string, any>;
  confidence?: number;
  impact?: string;
  requiresConfirmation?: boolean;
}