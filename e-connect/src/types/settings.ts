// Settings types for comprehensive configuration management

import { z } from 'zod';

// Base Settings Types
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// User Profile Settings
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  bio?: string;
  company?: string;
  jobTitle?: string;
  location?: string;
  timezone: string;
  locale: string;
}

// Security Settings
export interface SecuritySettings {
  twoFactorEnabled: boolean;
  twoFactorMethod: 'sms' | 'authenticator' | 'email';
  backupCodes: string[];
  sessionTimeout: number; // minutes
  maxSessions: number;
  passwordLastChanged: Date;
  securityNotifications: boolean;
  suspiciousActivityAlerts: boolean;
  loginHistory: LoginSession[];
}

export interface LoginSession {
  id: string;
  deviceName: string;
  location: string;
  ipAddress: string;
  userAgent: string;
  lastAccessed: Date;
  isCurrentSession: boolean;
}

// Privacy Settings
export interface PrivacySettings {
  dataProcessingConsent: boolean;
  marketingEmailsConsent: boolean;
  analyticsConsent: boolean;
  thirdPartyIntegrationsConsent: boolean;
  dataRetentionPeriod: number; // days
  allowDataExport: boolean;
  allowDataDeletion: boolean;
  gdprCompliant: boolean;
  cookiePreferences: CookiePreferences;
}

export interface CookiePreferences {
  essential: boolean; // always true, cannot be disabled
  analytics: boolean;
  marketing: boolean;
  personalization: boolean;
  thirdParty: boolean;
}

// Notification Settings
export interface NotificationSettings {
  email: EmailNotificationSettings;
  push: PushNotificationSettings;
  inApp: InAppNotificationSettings;
  digest: DigestSettings;
  quiet: QuietHoursSettings;
}

export interface EmailNotificationSettings {
  enabled: boolean;
  newEmails: boolean;
  importantEmails: boolean;
  mentions: boolean;
  rules: boolean;
  bulkOperations: boolean;
  securityAlerts: boolean;
  systemUpdates: boolean;
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
}

export interface PushNotificationSettings {
  enabled: boolean;
  newEmails: boolean;
  importantEmails: boolean;
  mentions: boolean;
  sound: boolean;
  vibration: boolean;
  showPreview: boolean;
}

export interface InAppNotificationSettings {
  enabled: boolean;
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  duration: number; // seconds
  showIcons: boolean;
  groupSimilar: boolean;
}

export interface DigestSettings {
  enabled: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  time: string; // HH:MM format
  includeStats: boolean;
  includeTopSenders: boolean;
  includeRuleActivity: boolean;
}

export interface QuietHoursSettings {
  enabled: boolean;
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  timezone: string;
  weekendsOnly: boolean;
  exceptions: string[]; // email addresses that can override quiet hours
}

// Theme and Appearance Settings
export interface ThemeSettings {
  mode: 'light' | 'dark' | 'auto';
  accentColor: string;
  colorScheme: string;
  density: 'compact' | 'comfortable' | 'spacious';
  fontSize: 'small' | 'medium' | 'large' | 'extra-large';
  fontFamily: string;
  reducedMotion: boolean;
  highContrast: boolean;
  customCSS?: string;
}

// Email Preferences
export interface EmailPreferences {
  signature: EmailSignature;
  autoReply: AutoReplySettings;
  threading: ThreadingSettings;
  reading: ReadingSettings;
  compose: ComposeSettings;
  forwarding: ForwardingSettings;
}

export interface EmailSignature {
  id: string;
  name: string;
  content: string; // HTML content
  isDefault: boolean;
  useForReplies: boolean;
  useForForwards: boolean;
  conditions?: SignatureCondition[];
}

export interface SignatureCondition {
  type: 'sender' | 'recipient' | 'subject' | 'time';
  operator: 'contains' | 'equals' | 'startsWith' | 'endsWith';
  value: string;
}

export interface AutoReplySettings {
  enabled: boolean;
  subject: string;
  message: string;
  startDate?: Date;
  endDate?: Date;
  sendToKnownContactsOnly: boolean;
  sendOnce: boolean;
  excludeDomains: string[];
}

export interface ThreadingSettings {
  enabled: boolean;
  groupBySubject: boolean;
  maxThreadDepth: number;
  collapseRead: boolean;
  showParticipants: boolean;
  sortOrder: 'chronological' | 'reverse-chronological';
}

export interface ReadingSettings {
  markAsReadDelay: number; // seconds
  autoAdvance: boolean;
  showImages: 'always' | 'known-senders' | 'never';
  showExternalContent: boolean;
  previewLength: number; // characters
  openInNewTab: boolean;
  keyboardShortcuts: boolean;
}

export interface ComposeSettings {
  defaultFormat: 'html' | 'plain';
  autoSave: boolean;
  autoSaveInterval: number; // seconds
  spellCheck: boolean;
  suggestContacts: boolean;
  sendDelay: number; // seconds
  requireSubject: boolean;
  warnLargeAttachments: number; // MB
}

export interface ForwardingSettings {
  enabled: boolean;
  forwardTo: string[];
  keepCopy: boolean;
  forwardFiltered: boolean;
  includeOriginalHeaders: boolean;
}

// Integration Settings
export interface IntegrationSettings {
  oauth: OAuthConnection[];
  webhooks: WebhookConfiguration[];
  apiKeys: ApiKeyConfiguration[];
  calendar: CalendarIntegration;
  crm: CrmIntegration;
  productivity: ProductivityIntegration[];
}

export interface OAuthConnection {
  id: string;
  provider: 'google' | 'microsoft' | 'github' | 'slack' | 'salesforce';
  email: string;
  displayName: string;
  scopes: string[];
  connectedAt: Date;
  lastUsed: Date;
  isActive: boolean;
  refreshToken?: string;
  expiresAt?: Date;
}

export interface WebhookConfiguration {
  id: string;
  name: string;
  url: string;
  events: WebhookEvent[];
  secret?: string;
  isActive: boolean;
  createdAt: Date;
  lastTriggered?: Date;
  failureCount: number;
  headers: Record<string, string>;
}

export type WebhookEvent =
  | 'email.received'
  | 'email.sent'
  | 'rule.executed'
  | 'bulk.completed'
  | 'user.login'
  | 'settings.changed';

export interface ApiKeyConfiguration {
  id: string;
  name: string;
  service: string;
  keyPreview: string; // masked key
  scopes: string[];
  createdAt: Date;
  lastUsed?: Date;
  isActive: boolean;
  expiresAt?: Date;
}

export interface CalendarIntegration {
  enabled: boolean;
  provider: 'google' | 'outlook' | 'apple' | 'caldav';
  syncMeetingInvites: boolean;
  createEventsFromEmails: boolean;
  defaultCalendar: string;
  reminderSettings: {
    enabled: boolean;
    minutesBefore: number;
  };
}

export interface CrmIntegration {
  enabled: boolean;
  provider: 'salesforce' | 'hubspot' | 'pipedrive' | 'custom';
  syncContacts: boolean;
  createLeadsFromEmails: boolean;
  trackEmailActivity: boolean;
  customFields: Record<string, any>;
}

export interface ProductivityIntegration {
  type: 'task' | 'note' | 'storage' | 'communication';
  provider: string;
  enabled: boolean;
  config: Record<string, any>;
}

// AI and Advanced Settings
export interface AISettings {
  model: AIModelConfiguration;
  features: AIFeatureSettings;
  training: AITrainingSettings;
  privacy: AIPrivacySettings;
}

export interface AIModelConfiguration {
  provider: 'openai' | 'anthropic' | 'google' | 'local';
  model: string;
  temperature: number;
  maxTokens: number;
  customEndpoint?: string;
  apiKey?: string;
}

export interface AIFeatureSettings {
  autoCategories: boolean;
  smartReplies: boolean;
  summaries: boolean;
  priorityScoring: boolean;
  sentimentAnalysis: boolean;
  languageDetection: boolean;
  spamDetection: boolean;
  phishingDetection: boolean;
}

export interface AITrainingSettings {
  personalizeResponses: boolean;
  learnFromCorrections: boolean;
  shareAnonymizedData: boolean;
  retainTrainingData: number; // days
}

export interface AIPrivacySettings {
  processLocally: boolean;
  encryptData: boolean;
  anonymizeBeforeProcessing: boolean;
  optOutOfImprovement: boolean;
}

// Performance and Debug Settings
export interface PerformanceSettings {
  cache: CacheSettings;
  sync: SyncSettings;
  debug: DebugSettings;
  experimental: ExperimentalFeatures;
}

export interface CacheSettings {
  enabled: boolean;
  maxSize: number; // MB
  ttl: number; // seconds
  preloadNextPage: boolean;
  cacheImages: boolean;
  cacheAttachments: boolean;
}

export interface SyncSettings {
  enabled: boolean;
  interval: number; // seconds
  batchSize: number;
  conflictResolution: 'server-wins' | 'client-wins' | 'manual';
  offlineMode: boolean;
  backgroundSync: boolean;
}

export interface DebugSettings {
  enabled: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  includeNetworkLogs: boolean;
  includePerformanceLogs: boolean;
  maxLogSize: number; // MB
  uploadLogs: boolean;
}

export interface ExperimentalFeatures {
  enabled: boolean;
  features: Record<string, boolean>;
  betaOptIn: boolean;
  feedbackEnabled: boolean;
}

// Keyboard Shortcuts
export interface KeyboardShortcuts {
  enabled: boolean;
  shortcuts: Record<string, KeyboardShortcut>;
  customShortcuts: KeyboardShortcut[];
}

export interface KeyboardShortcut {
  id: string;
  name: string;
  description: string;
  keys: string[];
  action: string;
  context?: string;
  enabled: boolean;
}

// Settings Management
export interface SettingsMetadata {
  version: string;
  lastModified: Date;
  modifiedBy: string;
  checksum: string;
  conflicts: SettingsConflict[];
}

export interface SettingsConflict {
  id: string;
  field: string;
  serverValue: any;
  clientValue: any;
  timestamp: Date;
  resolved: boolean;
}

export interface SettingsBackup {
  id: string;
  name: string;
  description?: string;
  settings: UserSettings;
  createdAt: Date;
  size: number; // bytes
  checksum: string;
}

export interface SettingsImportResult {
  success: boolean;
  imported: string[];
  skipped: string[];
  errors: string[];
  warnings: string[];
}

// User Settings (Combined)
export interface UserSettings {
  profile: UserProfile;
  security: SecuritySettings;
  privacy: PrivacySettings;
  notifications: NotificationSettings;
  theme: ThemeSettings;
  email: EmailPreferences;
  integrations: IntegrationSettings;
  ai: AISettings;
  performance: PerformanceSettings;
  shortcuts: KeyboardShortcuts;
  metadata: SettingsMetadata;
}

// Settings Store State
export interface SettingsState {
  settings: UserSettings;
  loading: boolean;
  saving: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
  backups: SettingsBackup[];
  searchQuery: string;
  activeSection: string;
  isImporting: boolean;
  isExporting: boolean;
}

// Settings Actions
export interface SettingsActions {
  // Load and save
  loadSettings: () => Promise<void>;
  saveSettings: (settings?: Partial<UserSettings>) => Promise<void>;
  resetSettings: () => Promise<void>;
  
  // Individual setting updates
  updateProfile: (profile: Partial<UserProfile>) => void;
  updateSecurity: (security: Partial<SecuritySettings>) => void;
  updatePrivacy: (privacy: Partial<PrivacySettings>) => void;
  updateNotifications: (notifications: Partial<NotificationSettings>) => void;
  updateTheme: (theme: Partial<ThemeSettings>) => void;
  updateEmailPreferences: (email: Partial<EmailPreferences>) => void;
  updateIntegrations: (integrations: Partial<IntegrationSettings>) => void;
  updateAI: (ai: Partial<AISettings>) => void;
  updatePerformance: (performance: Partial<PerformanceSettings>) => void;
  updateShortcuts: (shortcuts: Partial<KeyboardShortcuts>) => void;
  
  // OAuth and integrations
  connectOAuth: (provider: string, credentials: any) => Promise<void>;
  disconnectOAuth: (connectionId: string) => Promise<void>;
  addWebhook: (webhook: Omit<WebhookConfiguration, 'id' | 'createdAt'>) => Promise<void>;
  updateWebhook: (id: string, webhook: Partial<WebhookConfiguration>) => Promise<void>;
  deleteWebhook: (id: string) => Promise<void>;
  testWebhook: (id: string) => Promise<boolean>;
  
  // API keys
  addApiKey: (apiKey: Omit<ApiKeyConfiguration, 'id' | 'createdAt'>) => Promise<void>;
  updateApiKey: (id: string, apiKey: Partial<ApiKeyConfiguration>) => Promise<void>;
  deleteApiKey: (id: string) => Promise<void>;
  
  // Email signatures
  addSignature: (signature: Omit<EmailSignature, 'id'>) => Promise<void>;
  updateSignature: (id: string, signature: Partial<EmailSignature>) => Promise<void>;
  deleteSignature: (id: string) => Promise<void>;
  setDefaultSignature: (id: string) => Promise<void>;
  
  // Backup and restore
  createBackup: (name: string, description?: string) => Promise<void>;
  restoreBackup: (backupId: string) => Promise<void>;
  deleteBackup: (backupId: string) => Promise<void>;
  
  // Import/export
  exportSettings: (format: 'json' | 'yaml') => Promise<string>;
  importSettings: (data: string, format: 'json' | 'yaml') => Promise<SettingsImportResult>;
  
  // Search and navigation
  setSearchQuery: (query: string) => void;
  setActiveSection: (section: string) => void;
  
  // Validation
  validateSettings: (settings: Partial<UserSettings>) => Promise<string[]>;
  
  // Conflict resolution
  resolveConflict: (conflictId: string, resolution: 'server' | 'client') => Promise<void>;
  
  // Health check
  checkHealth: () => Promise<SettingsHealthReport>;
}

export interface SettingsHealthReport {
  overall: 'healthy' | 'warning' | 'critical';
  issues: SettingsHealthIssue[];
  recommendations: string[];
  score: number; // 0-100
}

export interface SettingsHealthIssue {
  id: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'security' | 'privacy' | 'performance' | 'compatibility';
  title: string;
  description: string;
  fix?: string;
  autoFixable: boolean;
}

// Zod Schemas for Validation
export const UserProfileSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Name is required'),
  email: z.string().email('Invalid email address'),
  avatar: z.string().url().optional(),
  bio: z.string().max(500).optional(),
  company: z.string().max(100).optional(),
  jobTitle: z.string().max(100).optional(),
  location: z.string().max(100).optional(),
  timezone: z.string(),
  locale: z.string(),
});

export const SecuritySettingsSchema = z.object({
  twoFactorEnabled: z.boolean(),
  twoFactorMethod: z.enum(['sms', 'authenticator', 'email']),
  sessionTimeout: z.number().min(5).max(1440), // 5 minutes to 24 hours
  maxSessions: z.number().min(1).max(10),
  securityNotifications: z.boolean(),
  suspiciousActivityAlerts: z.boolean(),
});

export const ThemeSettingsSchema = z.object({
  mode: z.enum(['light', 'dark', 'auto']),
  accentColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid hex color'),
  density: z.enum(['compact', 'comfortable', 'spacious']),
  fontSize: z.enum(['small', 'medium', 'large', 'extra-large']),
  fontFamily: z.string(),
  reducedMotion: z.boolean(),
  highContrast: z.boolean(),
});

export const NotificationSettingsSchema = z.object({
  email: z.object({
    enabled: z.boolean(),
    frequency: z.enum(['immediate', 'hourly', 'daily', 'weekly']),
  }),
  push: z.object({
    enabled: z.boolean(),
    sound: z.boolean(),
    vibration: z.boolean(),
    showPreview: z.boolean(),
  }),
});

// Settings Presets
export type SettingsPreset = 'default' | 'minimal' | 'power-user' | 'privacy-focused' | 'productivity';

export const SETTINGS_PRESETS: Record<SettingsPreset, Partial<UserSettings>> = {
  default: {
    theme: {
      mode: 'auto',
      accentColor: '#3b82f6',
      density: 'comfortable',
      fontSize: 'medium',
      fontFamily: 'system-ui',
      reducedMotion: false,
      highContrast: false,
    },
    notifications: {
      email: {
        enabled: true,
        newEmails: true,
        importantEmails: true,
        mentions: true,
        frequency: 'immediate',
      } as EmailNotificationSettings,
    },
  },
  minimal: {
    theme: {
      mode: 'light',
      density: 'compact',
      fontSize: 'small',
      reducedMotion: true,
    },
    notifications: {
      email: {
        enabled: false,
        frequency: 'daily',
      } as EmailNotificationSettings,
      push: {
        enabled: false,
      } as PushNotificationSettings,
    },
  },
  'power-user': {
    theme: {
      density: 'spacious',
      fontSize: 'medium',
    },
    shortcuts: {
      enabled: true,
    },
    performance: {
      cache: {
        enabled: true,
        maxSize: 500,
        preloadNextPage: true,
      } as CacheSettings,
    },
  },
  'privacy-focused': {
    privacy: {
      analyticsConsent: false,
      marketingEmailsConsent: false,
      thirdPartyIntegrationsConsent: false,
      dataRetentionPeriod: 30,
    },
    ai: {
      privacy: {
        processLocally: true,
        encryptData: true,
        anonymizeBeforeProcessing: true,
        optOutOfImprovement: true,
      } as AIPrivacySettings,
    },
  },
  productivity: {
    email: {
      threading: {
        enabled: true,
        groupBySubject: true,
        collapseRead: true,
      } as ThreadingSettings,
      reading: {
        autoAdvance: true,
        keyboardShortcuts: true,
      } as ReadingSettings,
    },
    ai: {
      features: {
        autoCategories: true,
        smartReplies: true,
        summaries: true,
        priorityScoring: true,
      } as AIFeatureSettings,
    },
  },
};

export type { SettingsState, SettingsActions };