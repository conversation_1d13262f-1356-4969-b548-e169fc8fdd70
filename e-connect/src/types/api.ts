// API request and response types
export interface ApiRequest<T = any> {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  endpoint: string;
  params?: Record<string, any>;
  query?: Record<string, any>;
  body?: T;
  headers?: Record<string, string>;
  auth?: {
    type: 'bearer' | 'basic' | 'apikey' | 'oauth';
    credentials?: string;
    token?: string;
  };
  timeout?: number;
  retries?: number;
  cache?: {
    enabled: boolean;
    duration?: number; // seconds
    key?: string;
  };
}

export interface ApiResponse<T = any> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  request?: ApiRequest;
  timestamp: Date;
  duration: number; // milliseconds
  cached?: boolean;
  error?: ApiError;
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  statusCode?: number;
  field?: string;
  timestamp: Date;
  requestId?: string;
  retryable?: boolean;
  retryAfter?: number; // seconds
}

// Pagination types
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  cursor?: string;
  sort?: string | string[];
  order?: 'asc' | 'desc';
  offset?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationMeta;
  total?: number;
  hasMore?: boolean;
  nextCursor?: string;
  previousCursor?: string;
}

export interface PaginationMeta {
  page: number;
  pageSize: number;
  totalPages: number;
  totalItems: number;
  hasNext: boolean;
  hasPrevious: boolean;
  nextPage?: number;
  previousPage?: number;
}

// Error types
export interface ValidationError extends ApiError {
  code: 'VALIDATION_ERROR';
  validationErrors: FieldError[];
}

export interface FieldError {
  field: string;
  message: string;
  code: string;
  value?: any;
  constraints?: Record<string, any>;
}

export interface AuthError extends ApiError {
  code: 'AUTH_ERROR';
  authType?: string;
  expiresAt?: Date;
  refreshable?: boolean;
}

export interface RateLimitError extends ApiError {
  code: 'RATE_LIMIT_ERROR';
  limit: number;
  remaining: number;
  reset: Date;
  retryAfter: number;
}

export interface NetworkError extends ApiError {
  code: 'NETWORK_ERROR';
  type: 'timeout' | 'connection' | 'dns' | 'unknown';
  originalError?: any;
}

// Specific API endpoints types
export interface ThreadListRequest extends ApiRequest {
  query?: {
    category?: string;
    labels?: string[];
    unread?: boolean;
    important?: boolean;
    starred?: boolean;
    from?: string;
    to?: string;
    subject?: string;
    hasAttachment?: boolean;
    dateFrom?: string;
    dateTo?: string;
    search?: string;
  } & PaginationParams;
}

export interface ThreadListResponse extends PaginatedResponse<Thread> {
  summary?: {
    totalUnread: number;
    totalImportant: number;
    categories: Record<string, number>;
  };
}

export interface MessageRequest extends ApiRequest {
  params: {
    messageId: string;
  };
  query?: {
    format?: 'full' | 'minimal' | 'raw' | 'metadata';
    includeAttachments?: boolean;
    includeHeaders?: boolean;
  };
}

export interface BulkActionRequest extends ApiRequest {
  body: {
    action: string;
    threadIds?: string[];
    messageIds?: string[];
    filter?: any;
    params?: Record<string, any>;
  };
}

export interface BulkActionResponse extends ApiResponse {
  data: {
    operationId: string;
    status: string;
    affectedCount: number;
    successCount?: number;
    failedCount?: number;
    errors?: ApiError[];
  };
}

// Webhook types
export interface WebhookRequest {
  id: string;
  type: WebhookEventType;
  timestamp: Date;
  data: any;
  signature?: string;
  retryCount?: number;
}

export type WebhookEventType = 
  | 'email.received'
  | 'email.sent'
  | 'email.bounced'
  | 'email.opened'
  | 'email.clicked'
  | 'thread.created'
  | 'thread.updated'
  | 'thread.deleted'
  | 'rule.triggered'
  | 'bulk.completed'
  | 'ai.processed';

export interface WebhookResponse {
  received: boolean;
  processedAt: Date;
  statusCode?: number;
  message?: string;
}

// OAuth types
export interface OAuthConfig {
  provider: 'google' | 'microsoft' | 'custom';
  clientId: string;
  clientSecret?: string;
  redirectUri: string;
  scopes: string[];
  authorizationUrl?: string;
  tokenUrl?: string;
  userInfoUrl?: string;
}

export interface OAuthToken {
  accessToken: string;
  refreshToken?: string;
  expiresIn: number;
  expiresAt: Date;
  tokenType: string;
  scope?: string;
  idToken?: string;
}

export interface OAuthUser {
  id: string;
  email: string;
  name?: string;
  picture?: string;
  locale?: string;
  provider: string;
  raw?: Record<string, any>;
}

// File upload types
export interface FileUploadRequest {
  file: File | Blob;
  filename?: string;
  mimeType?: string;
  metadata?: Record<string, any>;
  onProgress?: (progress: UploadProgress) => void;
}

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  speed?: number; // bytes per second
  remainingTime?: number; // seconds
}

export interface FileUploadResponse {
  id: string;
  filename: string;
  size: number;
  mimeType: string;
  url: string;
  thumbnailUrl?: string;
  metadata?: Record<string, any>;
  uploadedAt: Date;
}

// API Configuration
export interface ApiConfig {
  baseUrl: string;
  timeout?: number;
  retries?: number;
  headers?: Record<string, string>;
  auth?: {
    type: 'bearer' | 'basic' | 'apikey' | 'oauth';
    credentials?: string;
  };
  interceptors?: {
    request?: (config: ApiRequest) => ApiRequest | Promise<ApiRequest>;
    response?: (response: ApiResponse) => ApiResponse | Promise<ApiResponse>;
    error?: (error: ApiError) => any;
  };
  cache?: {
    enabled: boolean;
    duration?: number;
    maxSize?: number;
    exclude?: string[];
  };
  rateLimit?: {
    maxRequests: number;
    perMilliseconds: number;
    retryAfter?: number;
  };
}

// API Statistics
export interface ApiStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  cacheHitRate: number;
  byEndpoint: Record<string, {
    count: number;
    avgTime: number;
    errors: number;
    cacheHits: number;
  }>;
  byStatus: Record<number, number>;
  errors: {
    type: string;
    count: number;
    lastOccurred: Date;
  }[];
}

// Import PlannedAction from email types
import type { Thread } from './email';
export type { Thread };