// Multi-account types for comprehensive email account management

import type { UserSettings } from './settings'
import type { Thread, ParsedMessage, EmailFilter } from './email'
import type { ApiStats, OAuthToken } from './api'

// Account Provider Types
export type AccountProvider = 'gmail' | 'outlook' | 'imap' | 'exchange' | 'yahoo' | 'custom'

export type AccountStatus = 
  | 'active'           // Account is connected and syncing
  | 'disconnected'     // Temporarily disconnected
  | 'error'           // Error in authentication or syncing
  | 'rate_limited'    // Hit API rate limits
  | 'maintenance'     // Provider maintenance
  | 'suspended'       // Account suspended by user
  | 'expired'         // Token expired, needs re-auth

export type SyncStatus = 
  | 'idle'
  | 'syncing'
  | 'completed'
  | 'failed'
  | 'partial'
  | 'paused'

// Account Configuration
export interface EmailAccount {
  id: string
  name: string                    // User-friendly name
  email: string                   // Primary email address
  provider: AccountProvider
  status: AccountStatus
  isDefault: boolean             // Primary account for sending
  isPrimary: boolean             // Main account (first added)
  
  // Visual customization
  color: string                  // Hex color for UI identification
  avatar?: string               // Account avatar URL
  displayOrder: number          // Order in account switcher
  
  // Provider-specific configuration
  config: AccountConfiguration
  
  // Authentication
  auth: AccountAuthentication
  
  // Sync settings
  sync: AccountSyncSettings
  
  // Statistics
  stats: AccountStats
  
  // Timestamps
  createdAt: Date
  updatedAt: Date
  lastSyncAt?: Date
  lastAccessAt?: Date
}

export interface AccountConfiguration {
  // Provider-specific settings
  provider: AccountProvider
  
  // IMAP/SMTP settings (for custom providers)
  imap?: {
    host: string
    port: number
    secure: boolean
    auth: {
      user: string
      pass?: string
    }
  }
  
  smtp?: {
    host: string
    port: number
    secure: boolean
    auth: {
      user: string
      pass?: string
    }
  }
  
  // OAuth settings (for supported providers)
  oauth?: {
    clientId: string
    scopes: string[]
    redirectUri: string
  }
  
  // Advanced settings
  folderMapping?: Record<string, string>
  labelMapping?: Record<string, string>
  customHeaders?: Record<string, string>
}

export interface AccountAuthentication {
  type: 'oauth' | 'password' | 'app_password' | 'certificate'
  
  // OAuth tokens
  oauth?: {
    accessToken: string
    refreshToken?: string
    idToken?: string
    expiresAt: Date
    scope: string
    tokenType: string
  }
  
  // Password-based auth
  credentials?: {
    username: string
    password: string    // Encrypted
    appPassword?: string // For app-specific passwords
  }
  
  // Certificate-based auth
  certificate?: {
    cert: string
    key: string
    passphrase?: string
  }
  
  // Authentication status
  isValid: boolean
  lastValidated: Date
  validationErrors?: string[]
}

export interface AccountSyncSettings {
  enabled: boolean
  syncInterval: number           // minutes
  fullSyncInterval: number       // hours
  
  // What to sync
  emails: boolean
  contacts: boolean
  calendar: boolean
  
  // Email sync options
  syncFolders: string[]         // Which folders to sync
  excludeFolders: string[]      // Folders to exclude
  syncLabels: boolean
  syncAttachments: boolean
  maxAttachmentSize: number     // MB
  
  // Sync limits
  daysToSync: number            // How far back to sync
  maxEmailsPerSync: number
  
  // Conflict resolution
  conflictResolution: 'server_wins' | 'client_wins' | 'merge' | 'ask_user'
  
  // Background sync
  backgroundSync: boolean
  wifiOnly: boolean
  lowBatteryMode: boolean
}

export interface AccountStats {
  totalEmails: number
  unreadEmails: number
  syncedEmails: number
  lastSyncDuration: number      // milliseconds
  averageSyncDuration: number
  
  // Error tracking
  syncErrors: number
  lastSyncError?: string
  consecutiveFailures: number
  
  // Performance metrics
  apiCalls: number
  rateLimitHits: number
  bandwidthUsed: number         // bytes
  
  // Storage usage
  storageUsed: number           // bytes
  attachmentStorage: number     // bytes
  
  // Activity metrics
  emailsSent: number
  emailsReceived: number
  threadsProcessed: number
}

// Multi-Account Management
export interface MultiAccountState {
  // Account management
  accounts: EmailAccount[]
  activeAccountId: string | null
  defaultAccountId: string | null
  
  // UI state
  isLoading: boolean
  error: string | null
  
  // Account switching
  recentAccounts: string[]      // Recently accessed account IDs
  switchHistory: AccountSwitchEvent[]
  
  // Sync state
  globalSyncStatus: SyncStatus
  syncProgress: AccountSyncProgress[]
  
  // Search across accounts
  unifiedSearch: {
    query: string
    accountFilter: string[]
    isSearching: boolean
    results: UnifiedSearchResult[]
  }
  
  // Settings
  settings: MultiAccountSettings
}

export interface AccountSwitchEvent {
  fromAccountId: string | null
  toAccountId: string
  timestamp: Date
  trigger: 'user' | 'auto' | 'system'
  context?: string              // Which UI triggered the switch
}

export interface AccountSyncProgress {
  accountId: string
  status: SyncStatus
  progress: number              // 0-100
  currentTask: string
  estimatedRemaining: number    // seconds
  itemsProcessed: number
  totalItems: number
  errors: string[]
}

export interface UnifiedSearchResult {
  accountId: string
  accountName: string
  accountColor: string
  threads: Thread[]
  totalMatches: number
  searchDuration: number        // milliseconds
}

export interface MultiAccountSettings {
  // Account switching
  autoSwitchOnEmail: boolean    // Auto-switch when clicking email from different account
  showAccountInEmailList: boolean
  groupByAccount: boolean
  
  // Unified inbox
  unifiedInboxEnabled: boolean
  unifiedInboxAccounts: string[]
  unifiedSorting: 'chronological' | 'by_account' | 'by_importance'
  
  // Notifications
  consolidateNotifications: boolean
  notifyAllAccounts: boolean
  accountSpecificSounds: boolean
  
  // Search
  searchAllAccountsByDefault: boolean
  searchTimeout: number         // seconds
  maxSearchResults: number
  
  // Performance
  maxConcurrentSyncs: number
  syncThrottling: boolean
  offlineMode: boolean
  
  // Data management
  dataIsolation: boolean        // Keep account data completely separate
  sharedContacts: boolean       // Allow contacts to be shared across accounts
  sharedRules: boolean          // Apply rules across accounts
  
  // Security
  lockOnAccountSwitch: boolean
  sessionTimeout: number        // minutes per account
  encryptAccountData: boolean
}

// Unified Inbox Types
export interface UnifiedThread extends Thread {
  accountId: string
  accountName: string
  accountColor: string
  accountProvider: AccountProvider
}

export interface UnifiedInboxFilter extends EmailFilter {
  accountIds?: string[]
  providers?: AccountProvider[]
  accountGroup?: 'all' | 'active' | 'default' | 'custom'
}

export interface UnifiedInboxStats {
  totalAccounts: number
  activeAccounts: number
  totalThreads: number
  totalUnread: number
  accountBreakdown: Array<{
    accountId: string
    accountName: string
    threads: number
    unread: number
    lastSync: Date
  }>
}

// Account Migration and Backup
export interface AccountMigration {
  id: string
  sourceAccountId: string
  targetAccountId: string
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'cancelled'
  
  // What to migrate
  includeEmails: boolean
  includeContacts: boolean
  includeSettings: boolean
  includeRules: boolean
  
  // Progress tracking
  progress: number              // 0-100
  itemsToMigrate: number
  itemsMigrated: number
  itemsFailed: number
  
  // Results
  startedAt?: Date
  completedAt?: Date
  errors: Array<{
    item: string
    error: string
    timestamp: Date
  }>
  
  // Configuration
  preserveOriginal: boolean     // Keep original emails
  conflictResolution: 'skip' | 'overwrite' | 'rename'
}

export interface AccountBackup {
  id: string
  accountId: string
  name: string
  description?: string
  
  // Backup configuration
  includeEmails: boolean
  includeContacts: boolean
  includeSettings: boolean
  includeRules: boolean
  includeAttachments: boolean
  
  // Backup data
  size: number                  // bytes
  compressed: boolean
  encrypted: boolean
  
  // Metadata
  createdAt: Date
  expiresAt?: Date
  restorable: boolean
  
  // Storage location
  storageType: 'local' | 'cloud' | 'external'
  storagePath: string
  checksum: string
}

// Cross-Account Operations
export interface CrossAccountOperation {
  id: string
  type: 'move' | 'copy' | 'sync' | 'merge' | 'archive'
  status: 'pending' | 'in_progress' | 'completed' | 'failed'
  
  // Source and target
  sourceAccountId: string
  targetAccountId?: string
  
  // Items to process
  threadIds: string[]
  messageIds: string[]
  
  // Operation configuration
  preserveLabels: boolean
  preserveStatus: boolean
  createBackup: boolean
  
  // Progress and results
  progress: number
  itemsProcessed: number
  itemsSuccess: number
  itemsFailed: number
  errors: string[]
  
  // Timestamps
  createdAt: Date
  startedAt?: Date
  completedAt?: Date
}

// Account Analytics
export interface AccountAnalytics {
  accountId: string
  period: {
    start: Date
    end: Date
  }
  
  // Email metrics
  emailMetrics: {
    sent: number
    received: number
    replied: number
    forwarded: number
    deleted: number
    archived: number
  }
  
  // Time-based analysis
  activityByHour: Record<string, number>
  activityByDay: Record<string, number>
  activityByWeek: Record<string, number>
  
  // Contact analysis
  topSenders: Array<{
    email: string
    name?: string
    count: number
    percentage: number
  }>
  
  topRecipients: Array<{
    email: string
    name?: string
    count: number
    percentage: number
  }>
  
  // Content analysis
  categories: Record<string, number>
  averageResponseTime: number
  responseRate: number
  
  // Performance metrics
  syncPerformance: {
    averageSyncTime: number
    syncSuccess: number
    syncFailures: number
    uptime: number              // percentage
  }
}

// Multi-Account Store Actions
export interface MultiAccountActions {
  // Account management
  addAccount: (config: Omit<EmailAccount, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>
  removeAccount: (accountId: string) => Promise<void>
  updateAccount: (accountId: string, updates: Partial<EmailAccount>) => Promise<void>
  
  // Account status
  refreshAccount: (accountId: string) => Promise<void>
  refreshAllAccounts: () => Promise<void>
  setAccountStatus: (accountId: string, status: AccountStatus) => void
  
  // Account switching
  switchToAccount: (accountId: string) => Promise<void>
  setDefaultAccount: (accountId: string) => Promise<void>
  reorderAccounts: (accountIds: string[]) => Promise<void>
  
  // Authentication
  authenticateAccount: (accountId: string, credentials: any) => Promise<void>
  refreshAuthentication: (accountId: string) => Promise<void>
  revokeAuthentication: (accountId: string) => Promise<void>
  
  // Sync operations
  syncAccount: (accountId: string, force?: boolean) => Promise<void>
  syncAllAccounts: () => Promise<void>
  pauseSync: (accountId: string) => void
  resumeSync: (accountId: string) => void
  
  // Unified inbox
  getUnifiedThreads: (filter?: UnifiedInboxFilter) => Promise<UnifiedThread[]>
  searchAllAccounts: (query: string, options?: SearchOptions) => Promise<UnifiedSearchResult[]>
  
  // Cross-account operations
  moveToAccount: (threadIds: string[], fromAccountId: string, toAccountId: string) => Promise<void>
  copyToAccount: (threadIds: string[], fromAccountId: string, toAccountId: string) => Promise<void>
  
  // Migration and backup
  migrateAccount: (migration: Omit<AccountMigration, 'id'>) => Promise<string>
  createBackup: (accountId: string, config: Partial<AccountBackup>) => Promise<string>
  restoreBackup: (backupId: string, accountId: string) => Promise<void>
  
  // Analytics
  getAccountAnalytics: (accountId: string, period?: { start: Date; end: Date }) => Promise<AccountAnalytics>
  getUnifiedAnalytics: (accountIds?: string[], period?: { start: Date; end: Date }) => Promise<AccountAnalytics>
  
  // Settings
  updateSettings: (settings: Partial<MultiAccountSettings>) => Promise<void>
  
  // Error handling
  clearError: () => void
  retryFailedOperation: (operationId: string) => Promise<void>
}

export interface SearchOptions {
  accountIds?: string[]
  maxResults?: number
  timeout?: number
  includeArchived?: boolean
  sortBy?: 'relevance' | 'date' | 'account'
}

// Account Provider Capabilities
export interface ProviderCapabilities {
  provider: AccountProvider
  
  // Authentication methods
  authMethods: Array<'oauth' | 'password' | 'app_password' | 'certificate'>
  
  // Supported features
  features: {
    realTimeSync: boolean
    pushNotifications: boolean
    contactSync: boolean
    calendarSync: boolean
    searchOperators: boolean
    batchOperations: boolean
    customLabels: boolean
    threadSupport: boolean
    attachmentPreview: boolean
  }
  
  // API limitations
  limits: {
    rateLimitPerMinute: number
    rateLimitPerDay: number
    maxAttachmentSize: number    // MB
    maxEmailSize: number         // MB
    maxBatchSize: number
  }
  
  // Sync capabilities
  sync: {
    incrementalSync: boolean
    bidirectionalSync: boolean
    realTimeUpdates: boolean
    folderSync: boolean
    labelSync: boolean
    flagSync: boolean
  }
}

// Error types specific to multi-account
export interface AccountError {
  accountId: string
  code: string
  message: string
  details?: any
  timestamp: Date
  retryable: boolean
  actionRequired?: 'reauth' | 'config' | 'upgrade' | 'contact_support'
}

export interface SyncError extends AccountError {
  syncType: 'full' | 'incremental' | 'realtime'
  affectedItems: string[]
  partialSuccess: boolean
}

export interface AuthenticationError extends AccountError {
  authType: 'oauth' | 'password' | 'certificate'
  expiresAt?: Date
  refreshable: boolean
}

// Event types for multi-account system
export type MultiAccountEvent = 
  | AccountAddedEvent
  | AccountRemovedEvent
  | AccountSwitchedEvent
  | AccountSyncedEvent
  | AccountErrorEvent

export interface AccountAddedEvent {
  type: 'account_added'
  accountId: string
  account: EmailAccount
  timestamp: Date
}

export interface AccountRemovedEvent {
  type: 'account_removed'
  accountId: string
  timestamp: Date
}

export interface AccountSwitchedEvent {
  type: 'account_switched'
  fromAccountId: string | null
  toAccountId: string
  timestamp: Date
}

export interface AccountSyncedEvent {
  type: 'account_synced'
  accountId: string
  syncDuration: number
  itemsSynced: number
  timestamp: Date
}

export interface AccountErrorEvent {
  type: 'account_error'
  accountId: string
  error: AccountError
  timestamp: Date
}