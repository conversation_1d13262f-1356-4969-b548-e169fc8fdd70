// Central export file for all TypeScript types

// Email types
export * from './email';
export type {
  Thread,
  ParsedMessage,
  EmailHeaders,
  EmailAddress,
  EmailParticipant,
  Attachment,
  EmailCategory,
  ExecutionPlan,
  PlannedAction,
  ExecutionResult,
  EmailFilter,
  EmailSort,
} from './email';

// Rules types
export * from './rules';
export type {
  Rule,
  RuleCondition,
  RuleConditionType,
  RuleOperator,
  RuleAction,
  RuleActionType,
  RuleActionCondition,
  RuleStats,
  RuleSchedule,
  RuleException,
  RuleTemplate,
  RuleExecution,
  RuleTesting,
  RuleGroup,
} from './rules';

// Analytics types
export * from './analytics';
export type {
  EmailStats,
  CategoryStats,
  SenderStats,
  VolumeData,
  LabelStats,
  AttachmentStats,
  ResponsePattern,
  ChartData,
  ChartDataset,
  ChartOptions,
  ChartScale,
  DashboardWidget,
  DashboardLayout,
  AnalyticsEvent,
  PerformanceMetrics,
} from './analytics';

// Bulk operation types
export * from './bulk';
export type {
  BulkOperation,
  BulkOperationType,
  BulkOperationStatus,
  BulkFilter,
  BulkOperationParams,
  BulkOperationResult,
  BulkOperationSummary,
  BulkOperationError,
  RecurringSchedule,
  UnsubscribeOperation,
  UnsubscribeResult,
  ArchiveOperation,
  DeleteOperation,
  LabelOperation,
  ExportOperation,
  BulkOperationQueue,
} from './bulk';

// Assistant types
export * from './assistant';
export type {
  ChatMessage,
  ChatAttachment,
  Conversation,
  ConversationContext,
  ConversationSettings,
  Suggestion,
  SuggestionType,
  AIResponse,
  Summary,
  Extraction,
  Answer,
  Entity,
  Citation,
  Task,
  Subtask,
  AIModel,
  AITrainingData,
  AIPerformanceMetrics,
  AssistantPrompt,
} from './assistant';

// API types
export * from './api';
export type {
  ApiRequest,
  ApiResponse,
  ApiError,
  PaginationParams,
  PaginatedResponse,
  PaginationMeta,
  ValidationError,
  FieldError,
  AuthError,
  RateLimitError,
  NetworkError,
  ThreadListRequest,
  ThreadListResponse,
  MessageRequest,
  BulkActionRequest,
  BulkActionResponse,
  WebhookRequest,
  WebhookEventType,
  WebhookResponse,
  OAuthConfig,
  OAuthToken,
  OAuthUser,
  FileUploadRequest,
  UploadProgress,
  FileUploadResponse,
  ApiConfig,
  ApiStats,
} from './api';

// Common types
export * from './common';
export type {
  BaseEntity,
  User,
  UserPreferences,
  InboxSettings,
  UserStats,
  Subscription,
  DateRange,
  TimeWindow,
  Status,
  LoadingState,
  SortConfig,
  FilterConfig,
  Result,
  BatchResult,
  Permission,
  Role,
  FeatureFlag,
  Notification,
  CacheEntry,
  QueueItem,
  AuditLog,
  Nullable,
  Optional,
  Maybe,
  DeepPartial,
  RecursivePartial,
  Omit,
  ValueOf,
  PromiseType,
  ArrayElement,
  Environment,
} from './common';

// Settings types
export * from './settings';
export type {
  UserSettings,
  UserProfile,
  SecuritySettings,
  ThemeSettings,
  EmailPreferences,
  IntegrationSettings,
  PerformanceSettings,
  KeyboardShortcuts,
  SettingsMetadata,
  SettingsBackup,
  SettingsImportResult,
  SettingsHealthReport,
  SettingsHealthIssue,
  OAuthConnection,
  WebhookConfiguration,
  ApiKeyConfiguration,
  EmailSignature,
  AutoReplySettings,
  ThreadingSettings,
  ReadingSettings,
  ComposeSettings,
  ForwardingSettings,
  CalendarIntegration,
  CrmIntegration,
  ProductivityIntegration,
  AIModelConfiguration,
  AIFeatureSettings,
  AITrainingSettings,
  AIPrivacySettings,
  CacheSettings,
  SyncSettings,
  DebugSettings,
  ExperimentalFeatures,
  KeyboardShortcut,
  SettingsConflict,
  SettingsState,
  SettingsActions,
  SettingsPreset,
} from './settings';

// Multi-account types
export * from './multi-account';
export type {
  EmailAccount,
  AccountProvider,
  AccountStatus,
  SyncStatus,
  AccountConfiguration,
  AccountAuthentication,
  AccountSyncSettings,
  AccountStats,
  MultiAccountState,
  MultiAccountActions,
  MultiAccountSettings,
  UnifiedThread,
  UnifiedInboxFilter,
  UnifiedInboxStats,
  UnifiedSearchResult,
  AccountMigration,
  AccountBackup,
  AccountAnalytics,
  CrossAccountOperation,
  AccountError,
  SyncError,
  AuthenticationError,
  MultiAccountEvent,
  AccountSwitchEvent,
  AccountSyncProgress,
  ProviderCapabilities,
  SearchOptions,
} from './multi-account';