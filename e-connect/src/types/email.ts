// Email thread and message types
export interface Thread {
  id: string;
  messages: ParsedMessage[];
  snippet: string;
  subject: string;
  category?: EmailCategory;
  labels: string[];
  participants: EmailParticipant[];
  lastMessageDate: Date;
  firstMessageDate: Date;
  messageCount: number;
  unreadCount: number;
  status: {
    isUnread: boolean;
    isImportant: boolean;
    isStarred: boolean;
    isDraft: boolean;
    isSpam: boolean;
    isTrash: boolean;
    isSnoozed: boolean;
    snoozedUntil?: Date;
  };
  plan?: ExecutionPlan;
  aiSummary?: string;
  priority?: 'high' | 'medium' | 'low';
}

export interface ParsedMessage {
  id: string;
  threadId: string;
  messageId: string;
  subject: string;
  snippet: string;
  body: {
    text?: string;
    html?: string;
    plain?: string;
  };
  headers: EmailHeaders;
  from: EmailAddress;
  to: EmailAddress[];
  cc?: EmailAddress[];
  bcc?: EmailAddress[];
  replyTo?: EmailAddress;
  date: Date;
  receivedDate: Date;
  attachments: Attachment[];
  labels: string[];
  flags: {
    isUnread: boolean;
    isImportant: boolean;
    isStarred: boolean;
    isDraft: boolean;
    isSpam: boolean;
    isTrash: boolean;
  };
  size: number;
  rawSize: number;
  historyId: string;
  internalDate: string;
  category?: EmailCategory;
  aiAnalysis?: {
    summary?: string;
    sentiment?: 'positive' | 'negative' | 'neutral';
    actionRequired?: boolean;
    suggestedActions?: string[];
    extractedData?: Record<string, any>;
  };
}

export interface EmailHeaders {
  'message-id': string;
  'in-reply-to'?: string;
  references?: string;
  subject: string;
  from: string;
  to: string;
  cc?: string;
  bcc?: string;
  date: string;
  'content-type'?: string;
  'content-transfer-encoding'?: string;
  'x-mailer'?: string;
  'x-priority'?: string;
  'list-unsubscribe'?: string;
  'list-unsubscribe-post'?: string;
  [key: string]: string | undefined;
}

export interface EmailAddress {
  email: string;
  name?: string;
  type?: 'personal' | 'work' | 'service' | 'other';
}

export interface EmailParticipant extends EmailAddress {
  role: 'sender' | 'recipient' | 'cc' | 'bcc';
  messageCount: number;
  lastMessageDate: Date;
}

export interface Attachment {
  id: string;
  filename: string;
  mimeType: string;
  size: number;
  inline: boolean;
  contentId?: string;
  contentLocation?: string;
  data?: string; // Base64 encoded
  url?: string; // Download URL
}

export type EmailCategory = 
  | 'Newsletter'
  | 'Receipt'
  | 'Marketing' 
  | 'Social'
  | 'Updates'
  | 'Personal'
  | 'Work'
  | 'Finance'
  | 'Travel'
  | 'Security'
  | 'Notification'
  | 'Important'
  | 'Spam'
  | 'Other';

export interface ExecutionPlan {
  id: string;
  threadId: string;
  createdAt: Date;
  updatedAt: Date;
  status: 'pending' | 'in-progress' | 'completed' | 'failed' | 'cancelled';
  actions: PlannedAction[];
  summary: string;
  reasoning?: string;
  confidence?: number;
  executedAt?: Date;
  executedBy?: string;
  result?: ExecutionResult;
}

export interface PlannedAction {
  id: string;
  type: 'archive' | 'delete' | 'label' | 'reply' | 'forward' | 'snooze' | 'unsubscribe' | 'mark-read' | 'star';
  params?: {
    label?: string;
    message?: string;
    recipient?: string;
    duration?: number;
    template?: string;
    [key: string]: any;
  };
  order: number;
  status: 'pending' | 'completed' | 'failed' | 'skipped';
  error?: string;
  completedAt?: Date;
}

export interface ExecutionResult {
  success: boolean;
  completedActions: string[];
  failedActions: string[];
  errors: Array<{
    action: string;
    error: string;
    timestamp: Date;
  }>;
  summary: string;
  duration: number;
}

export interface EmailFilter {
  query?: string;
  category?: EmailCategory[];
  labels?: string[];
  from?: string[];
  to?: string[];
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  hasAttachment?: boolean;
  isUnread?: boolean;
  isImportant?: boolean;
  isStarred?: boolean;
  minSize?: number;
  maxSize?: number;
  subject?: string;
  body?: string;
}

export interface EmailSort {
  field: 'date' | 'subject' | 'from' | 'size' | 'importance';
  order: 'asc' | 'desc';
}