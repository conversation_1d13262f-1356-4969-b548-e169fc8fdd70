// Common shared utility types used across the application

// Base entity type
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// User types
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  preferences?: UserPreferences;
  stats?: UserStats;
  subscription?: Subscription;
  createdAt: Date;
  lastActiveAt: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: NotificationSettings;
  inbox: InboxSettings;
  ai: AISettings;
  privacy: PrivacySettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  desktop: boolean;
  sound: boolean;
  digest: 'never' | 'daily' | 'weekly';
  types: {
    newEmail: boolean;
    mentions: boolean;
    replies: boolean;
    bulkComplete: boolean;
    ruleTriggered: boolean;
    aiSuggestions: boolean;
  };
}

export interface InboxSettings {
  defaultView: 'all' | 'unread' | 'important' | 'category';
  groupBy: 'none' | 'date' | 'sender' | 'category';
  density: 'comfortable' | 'compact' | 'spacious';
  showPreview: boolean;
  previewLines: number;
  autoMarkRead: boolean;
  autoMarkReadDelay: number; // seconds
  conversationView: boolean;
  showImages: boolean;
  categoriesEnabled: string[];
}

export interface AISettings {
  enabled: boolean;
  autoSuggest: boolean;
  autoProcess: boolean;
  summaryLength: 'brief' | 'detailed';
  confidence: number; // 0-1
  preferredModel?: string;
  customPrompts?: boolean;
}

export interface PrivacySettings {
  trackingProtection: boolean;
  blockExternalImages: boolean;
  anonymizeData: boolean;
  dataRetention: number; // days
  shareAnalytics: boolean;
}

export interface UserStats {
  totalEmails: number;
  emailsProcessed: number;
  timesSaved: number; // minutes
  rulesCreated: number;
  bulkActions: number;
  aiInteractions: number;
  inboxZeroDays: number;
  currentStreak: number;
}

export interface Subscription {
  plan: 'free' | 'basic' | 'pro' | 'enterprise';
  status: 'active' | 'trialing' | 'cancelled' | 'expired';
  startDate: Date;
  endDate?: Date;
  features: string[];
  limits: {
    emailsPerMonth: number;
    rulesCount: number;
    aiRequestsPerDay: number;
    storageGB: number;
    teamMembers?: number;
  };
  billing?: {
    interval: 'monthly' | 'yearly';
    amount: number;
    currency: string;
    nextBillingDate?: Date;
  };
}

// Date and time utilities
export interface DateRange {
  start: Date;
  end: Date;
}

export interface TimeWindow {
  value: number;
  unit: 'minutes' | 'hours' | 'days' | 'weeks' | 'months';
}

// Status types
export type Status = 'idle' | 'loading' | 'success' | 'error';

export interface LoadingState<T = any> {
  status: Status;
  data?: T;
  error?: Error;
  lastFetch?: Date;
}

// Sort and filter types
export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

export interface FilterConfig {
  field: string;
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'lt' | 'between' | 'in';
  value: any;
}

// Generic result types
export interface Result<T, E = Error> {
  success: boolean;
  data?: T;
  error?: E;
}

export interface BatchResult<T> {
  successful: T[];
  failed: Array<{
    item: T;
    error: Error;
  }>;
  skipped?: T[];
}

// Permission and role types
export interface Permission {
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

export interface Role {
  id: string;
  name: string;
  permissions: Permission[];
  isSystem?: boolean;
}

// Feature flag types
export interface FeatureFlag {
  key: string;
  enabled: boolean;
  rolloutPercentage?: number;
  conditions?: {
    users?: string[];
    roles?: string[];
    plans?: string[];
    custom?: (user: User) => boolean;
  };
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message?: string;
  timestamp: Date;
  read: boolean;
  action?: {
    label: string;
    url?: string;
    handler?: () => void;
  };
  metadata?: Record<string, any>;
}

// Cache types
export interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  ttl?: number; // seconds
  tags?: string[];
}

// Queue types
export interface QueueItem<T = any> {
  id: string;
  data: T;
  priority: number;
  attempts: number;
  maxAttempts: number;
  nextAttempt?: Date;
  createdAt: Date;
  processedAt?: Date;
  error?: string;
}

// Audit log types
export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
}

// Utility type helpers
export type Nullable<T> = T | null;
export type Optional<T> = T | undefined;
export type Maybe<T> = T | null | undefined;

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RecursivePartial<T> = {
  [P in keyof T]?: T[P] extends (infer U)[]
    ? RecursivePartial<U>[]
    : T[P] extends object
    ? RecursivePartial<T[P]>
    : T[P];
};

export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export type ValueOf<T> = T[keyof T];

export type PromiseType<T extends Promise<any>> = T extends Promise<infer U> ? U : never;

export type ArrayElement<ArrayType extends readonly unknown[]> = 
  ArrayType extends readonly (infer ElementType)[] ? ElementType : never;

// Environment types
export interface Environment {
  NODE_ENV: 'development' | 'production' | 'test';
  API_URL: string;
  APP_URL: string;
  VERSION: string;
  BUILD_ID?: string;
  FEATURES?: Record<string, boolean>;
}