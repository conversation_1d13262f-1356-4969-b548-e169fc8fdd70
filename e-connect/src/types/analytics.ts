// Email analytics and statistics types
export interface EmailStats {
  // Overall totals
  totals: {
    threads: number;
    messages: number;
    unread: number;
    starred: number;
    important: number;
    archived: number;
    deleted: number;
    sent: number;
    received: number;
    draft: number;
    spam: number;
  };

  // Time-based metrics
  metrics: {
    avgResponseTime: number; // hours
    avgThreadLength: number;
    avgMessageSize: number; // bytes
    avgAttachmentSize: number;
    emailsPerDay: number;
    peakHours: number[]; // 0-23
    busiestDays: string[]; // day names
    growthRate: number; // percentage
  };

  // Category breakdown
  categoryBreakdown: CategoryStats[];
  
  // Sender analysis
  topSenders: SenderStats[];
  
  // Time series data
  volumeData: VolumeData[];
  
  // Label usage
  labelStats: LabelStats[];
  
  // Attachment analysis
  attachmentStats: AttachmentStats;
  
  // Response patterns
  responsePatterns: ResponsePattern[];
  
  // Period for stats
  period: {
    start: Date;
    end: Date;
    timezone: string;
  };
}

export interface CategoryStats {
  category: string;
  count: number;
  percentage: number;
  unreadCount: number;
  avgProcessingTime?: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  trendPercentage: number;
  subcategories?: {
    name: string;
    count: number;
    percentage: number;
  }[];
}

export interface SenderStats {
  email: string;
  name?: string;
  domain: string;
  messageCount: number;
  threadCount: number;
  unreadCount: number;
  avgResponseTime?: number;
  lastMessageDate: Date;
  firstMessageDate: Date;
  categories: string[];
  sentiment?: 'positive' | 'negative' | 'neutral';
  importance: 'high' | 'medium' | 'low';
  isFrequent: boolean;
  isRecent: boolean;
  tags?: string[];
}

export interface VolumeData {
  date: Date;
  received: number;
  sent: number;
  archived: number;
  deleted: number;
  categories: Record<string, number>;
  hourlyDistribution?: number[]; // 24 hours
}

export interface LabelStats {
  label: string;
  count: number;
  percentage: number;
  color?: string;
  addedCount: number; // Recently added
  removedCount: number; // Recently removed
  avgTimeToLabel?: number; // hours
  automation?: {
    ruleId: string;
    autoLabeled: number;
    accuracy: number;
  };
}

export interface AttachmentStats {
  totalCount: number;
  totalSize: number;
  avgSize: number;
  types: {
    type: string;
    mimeType: string;
    count: number;
    totalSize: number;
    percentage: number;
  }[];
  largestAttachments: {
    filename: string;
    size: number;
    messageId: string;
    date: Date;
  }[];
  byMonth: {
    month: string;
    count: number;
    totalSize: number;
  }[];
}

export interface ResponsePattern {
  type: 'immediate' | 'same-day' | 'next-day' | 'within-week' | 'over-week' | 'no-response';
  count: number;
  percentage: number;
  avgTime?: number; // hours
  byCategory?: Record<string, number>;
  bySender?: {
    email: string;
    count: number;
    avgTime: number;
  }[];
}

// Chart-specific data types
export interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
  options?: ChartOptions;
}

export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor?: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  fill?: boolean;
  tension?: number;
  type?: 'line' | 'bar' | 'pie' | 'doughnut' | 'radar' | 'scatter';
}

export interface ChartOptions {
  responsive?: boolean;
  maintainAspectRatio?: boolean;
  plugins?: {
    legend?: {
      display?: boolean;
      position?: 'top' | 'bottom' | 'left' | 'right';
    };
    title?: {
      display?: boolean;
      text?: string;
    };
    tooltip?: {
      enabled?: boolean;
      mode?: 'index' | 'point' | 'nearest';
    };
  };
  scales?: {
    x?: ChartScale;
    y?: ChartScale;
  };
}

export interface ChartScale {
  display?: boolean;
  title?: {
    display?: boolean;
    text?: string;
  };
  ticks?: {
    beginAtZero?: boolean;
    stepSize?: number;
    max?: number;
    min?: number;
  };
  grid?: {
    display?: boolean;
    color?: string;
  };
}

// Dashboard widgets
export interface DashboardWidget {
  id: string;
  type: 'stats' | 'chart' | 'list' | 'heatmap' | 'timeline' | 'custom';
  title: string;
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  config: {
    metric?: string;
    chartType?: string;
    dataSource?: string;
    filters?: any;
    refreshInterval?: number; // seconds
    [key: string]: any;
  };
  data?: any;
  lastUpdated?: Date;
}

export interface DashboardLayout {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  isDefault?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Analytics events for tracking
export interface AnalyticsEvent {
  id: string;
  type: 'email_received' | 'email_sent' | 'email_archived' | 'email_deleted' | 
        'rule_triggered' | 'bulk_action' | 'ai_suggestion' | 'user_action';
  category?: string;
  action: string;
  label?: string;
  value?: number;
  metadata?: Record<string, any>;
  userId?: string;
  timestamp: Date;
  sessionId?: string;
}

// Performance metrics
export interface PerformanceMetrics {
  apiCalls: {
    endpoint: string;
    count: number;
    avgDuration: number;
    errors: number;
    successRate: number;
  }[];
  processingTimes: {
    task: string;
    avgTime: number;
    minTime: number;
    maxTime: number;
    count: number;
  }[];
  resourceUsage: {
    cpu: number;
    memory: number;
    storage: number;
    bandwidth: number;
  };
  aiUsage: {
    tokensUsed: number;
    apiCalls: number;
    avgResponseTime: number;
    costEstimate: number;
  };
}

// Advanced analytics types
export interface Anomaly {
  id: string;
  type: 'spike' | 'drop' | 'pattern-break' | 'threshold-breach';
  value: number;
  expectedValue: number;
  deviation: number;
  severity: 'low' | 'medium' | 'high';
  timestamp: Date;
  description: string;
  metric: string;
  confidence: number;
}

export interface Correlation {
  variables: [string, string];
  coefficient: number;
  strength: 'weak' | 'moderate' | 'strong';
  direction: 'positive' | 'negative';
  significance: number;
  sampleSize: number;
}

export interface Prediction {
  date: Date;
  predicted: number;
  confidence: number;
  lowerBound: number;
  upperBound: number;
  method: string;
}

export interface AdvancedInsights {
  correlations: {
    [key: string]: number;
  };
  anomalies: Anomaly[];
  predictions: {
    [metric: string]: Prediction[];
  };
  trends: {
    [metric: string]: {
      direction: 'up' | 'down' | 'stable';
      rate: number;
      confidence: number;
    };
  };
}

// Time analysis types
export interface TimeAnalysis {
  hourlyDistribution: {
    hour: number;
    count: number;
    avgResponseTime: number;
  }[];
  weeklyPattern: {
    day: string;
    count: number;
    avgResponseTime: number;
  }[];
  monthlyTrends: {
    month: string;
    count: number;
    growth: number;
  }[];
  busyHours: number[];
  quietHours: number[];
  peakDay: string;
  peakHour: number;
}

// Geographic analysis (if applicable)
export interface GeographicData {
  country: string;
  count: number;
  percentage: number;
  avgResponseTime: number;
  timezone: string;
}

// Rule performance analytics
export interface RulePerformance {
  rules: {
    id: string;
    name: string;
    executions: number;
    successes: number;
    failures: number;
    successRate: number;
    timeSaved: number;
    isActive: boolean;
    category: string;
    createdAt: Date;
    lastExecuted: Date;
    errorTypes?: {
      type: string;
      count: number;
      description: string;
    }[];
    efficiency: {
      timePerAction: number;
      falsePositives: number;
      falseNegatives: number;
      accuracy: number;
    };
  }[];
  totalTimeSaved: number;
  totalExecutions: number;
  overallSuccessRate: number;
  mostEffectiveRules: string[];
  leastEffectiveRules: string[];
  recentFailures: {
    ruleId: string;
    ruleName: string;
    failureCount: number;
    lastFailure: Date;
    errorMessage: string;
  }[];
  periodStart: Date;
  periodEnd: Date;
}

// Statistics utilities
export interface StatisticsResult {
  mean: number;
  median: number;
  mode: number[];
  standardDeviation: number;
  variance: number;
  min: number;
  max: number;
  range: number;
  quartiles: {
    q1: number;
    q2: number;
    q3: number;
  };
  percentiles: {
    p90: number;
    p95: number;
    p99: number;
  };
  skewness: number;
  kurtosis: number;
}

// Trend analysis
export interface TrendData {
  period: string;
  value: number;
  change: number;
  changePercent: number;
  trend: 'up' | 'down' | 'stable';
}

export interface TrendAnalysis {
  data: TrendData[];
  overallTrend: 'up' | 'down' | 'stable';
  growthRate: number;
  volatility: number;
  seasonality: boolean;
  cycles: {
    period: number;
    amplitude: number;
    confidence: number;
  }[];
}

// Export and reporting types
export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  sections: ReportSection[];
  format: 'pdf' | 'html' | 'csv' | 'json';
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;
    recipients: string[];
  };
}

export interface ReportSection {
  type: 'summary' | 'chart' | 'table' | 'insights' | 'recommendations';
  title: string;
  content: any;
  config?: any;
}

export interface Report {
  id: string;
  template: string;
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
  };
  data: any;
  insights: string[];
  recommendations: string[];
  metadata: {
    version: string;
    generator: string;
    size: number;
  };
}

// Cache and performance types
export interface CacheEntry {
  key: string;
  data: any;
  timestamp: Date;
  ttl: number;
  hits: number;
  size: number;
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  missRate: number;
  evictions: number;
  oldestEntry: Date;
  newestEntry: Date;
}