// Email rules and automation types
export interface Rule {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  conditions: RuleCondition[];
  conditionLogic: 'all' | 'any'; // AND/OR logic
  actions: RuleAction[];
  stats?: RuleStats;
  createdAt: Date;
  updatedAt: Date;
  lastTriggered?: Date;
  createdBy?: string;
  tags?: string[];
  schedule?: RuleSchedule;
  exceptions?: RuleException[];
}

export interface RuleCondition {
  id: string;
  type: RuleConditionType;
  field?: string; // For custom field conditions
  operator: RuleOperator;
  value: string | string[] | number | boolean | RegExp;
  caseSensitive?: boolean;
  negate?: boolean; // NOT condition
}

export type RuleConditionType = 
  | 'from'
  | 'to'
  | 'cc'
  | 'bcc'
  | 'subject'
  | 'body'
  | 'category'
  | 'label'
  | 'hasAttachment'
  | 'attachmentType'
  | 'attachmentSize'
  | 'isUnread'
  | 'isImportant'
  | 'isStarred'
  | 'age' // Email age in days
  | 'size'
  | 'header' // Custom header
  | 'listUnsubscribe'
  | 'domain'
  | 'keyword'
  | 'regex'
  | 'aiScore' // AI confidence score
  | 'custom';

export type RuleOperator = 
  | 'equals'
  | 'notEquals'
  | 'contains'
  | 'notContains'
  | 'startsWith'
  | 'endsWith'
  | 'matches' // Regex
  | 'in'
  | 'notIn'
  | 'greaterThan'
  | 'lessThan'
  | 'greaterThanOrEqual'
  | 'lessThanOrEqual'
  | 'between'
  | 'exists'
  | 'notExists';

export interface RuleAction {
  id: string;
  type: RuleActionType;
  value?: string | string[] | number | boolean;
  template?: string; // For reply/forward templates
  params?: {
    label?: string;
    folder?: string;
    address?: string;
    subject?: string;
    body?: string;
    duration?: number; // For snooze
    category?: string;
    priority?: 'high' | 'medium' | 'low';
    [key: string]: any;
  };
  order: number; // Action execution order
  condition?: RuleActionCondition; // Conditional action
}

export type RuleActionType = 
  | 'archive'
  | 'delete'
  | 'markRead'
  | 'markUnread'
  | 'star'
  | 'unstar'
  | 'label'
  | 'removeLabel'
  | 'categorize'
  | 'forward'
  | 'reply'
  | 'snooze'
  | 'moveToFolder'
  | 'markImportant'
  | 'markNotImportant'
  | 'addToList'
  | 'createTask'
  | 'webhook'
  | 'notification'
  | 'aiProcess'
  | 'custom';

export interface RuleActionCondition {
  type: 'probability' | 'confidence' | 'custom';
  threshold?: number;
  expression?: string;
}

export interface RuleStats {
  totalMatches: number;
  totalActions: number;
  successfulActions: number;
  failedActions: number;
  lastMatchDate?: Date;
  avgProcessingTime: number; // milliseconds
  matchesByDay: Record<string, number>;
  actionsByType: Record<RuleActionType, number>;
  errorRate: number;
  performance: {
    conditionEvalTime: number;
    actionExecutionTime: number;
    totalTime: number;
  };
}

export interface RuleSchedule {
  type: 'immediate' | 'delayed' | 'scheduled' | 'recurring';
  delay?: number; // minutes
  time?: string; // HH:MM format
  days?: number[]; // 0-6 (Sunday-Saturday)
  timezone?: string;
  nextRun?: Date;
}

export interface RuleException {
  id: string;
  type: 'sender' | 'domain' | 'subject' | 'custom';
  value: string;
  reason?: string;
}

export interface RuleTemplate {
  id: string;
  name: string;
  description: string;
  category: 'productivity' | 'organization' | 'security' | 'automation' | 'custom';
  rule: Partial<Rule>;
  popularity?: number;
  author?: string;
  tags: string[];
}

export interface RuleExecution {
  id: string;
  ruleId: string;
  threadId: string;
  messageId: string;
  timestamp: Date;
  conditions: {
    conditionId: string;
    matched: boolean;
    evaluationTime: number;
    details?: any;
  }[];
  actions: {
    actionId: string;
    executed: boolean;
    success: boolean;
    error?: string;
    executionTime: number;
    result?: any;
  }[];
  totalTime: number;
  success: boolean;
  error?: string;
}

export interface RuleTesting {
  ruleId: string;
  testCases: {
    id: string;
    name: string;
    email: Partial<{
      from: string;
      to: string[];
      subject: string;
      body: string;
      labels: string[];
      hasAttachment: boolean;
    }>;
    expectedMatch: boolean;
    expectedActions: RuleActionType[];
  }[];
  results?: {
    testCaseId: string;
    passed: boolean;
    matched: boolean;
    executedActions: RuleActionType[];
    error?: string;
  }[];
}

export interface RuleGroup {
  id: string;
  name: string;
  description?: string;
  rules: string[]; // Rule IDs
  enabled: boolean;
  logic: 'sequential' | 'parallel' | 'exclusive'; // How rules are processed
  priority: number;
}