// Bulk operations and batch processing types
export interface BulkOperation {
  id: string;
  type: BulkOperationType;
  status: BulkOperationStatus;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  createdBy?: string;
  
  // Selection criteria
  selection: {
    type: 'manual' | 'filter' | 'all';
    threadIds?: string[];
    messageIds?: string[];
    filter?: BulkFilter;
    totalCount: number;
  };
  
  // Progress tracking
  progress: {
    processed: number;
    succeeded: number;
    failed: number;
    skipped: number;
    percentage: number;
    estimatedTimeRemaining?: number; // seconds
    currentItem?: string;
  };
  
  // Operation details
  params?: BulkOperationParams;
  
  // Results
  results?: BulkOperationResult[];
  summary?: BulkOperationSummary;
  
  // Error handling
  errors: BulkOperationError[];
  retryCount: number;
  maxRetries: number;
  
  // Scheduling
  scheduled?: boolean;
  scheduledFor?: Date;
  recurring?: RecurringSchedule;
}

export type BulkOperationType = 
  | 'unsubscribe'
  | 'archive'
  | 'delete'
  | 'label'
  | 'removeLabel'
  | 'markRead'
  | 'markUnread'
  | 'star'
  | 'unstar'
  | 'categorize'
  | 'move'
  | 'forward'
  | 'export'
  | 'import'
  | 'analyze'
  | 'applyRule'
  | 'custom';

export type BulkOperationStatus = 
  | 'pending'
  | 'queued'
  | 'processing'
  | 'paused'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'scheduled';

export interface BulkFilter {
  category?: string[];
  labels?: string[];
  from?: string[];
  to?: string[];
  subject?: string;
  body?: string;
  hasAttachment?: boolean;
  dateRange?: {
    start?: Date;
    end?: Date;
  };
  size?: {
    min?: number;
    max?: number;
  };
  isUnread?: boolean;
  isImportant?: boolean;
  isStarred?: boolean;
  customQuery?: string;
}

export interface BulkOperationParams {
  // For label operations
  labels?: string[];
  
  // For move operations
  destination?: string;
  
  // For forward operations
  forwardTo?: string[];
  message?: string;
  
  // For categorize operations
  category?: string;
  
  // For export operations
  format?: 'json' | 'csv' | 'mbox' | 'pdf';
  includeAttachments?: boolean;
  
  // For import operations
  source?: 'file' | 'url' | 'api';
  mapping?: Record<string, string>;
  
  // For custom operations
  customParams?: Record<string, any>;
  
  // Processing options
  batchSize?: number;
  delayBetweenBatches?: number; // milliseconds
  skipErrors?: boolean;
  dryRun?: boolean;
}

export interface BulkOperationResult {
  itemId: string;
  itemType: 'thread' | 'message';
  success: boolean;
  error?: string;
  changes?: {
    field: string;
    oldValue?: any;
    newValue?: any;
  }[];
  processingTime: number;
  retries: number;
}

export interface BulkOperationSummary {
  totalItems: number;
  processedItems: number;
  successfulItems: number;
  failedItems: number;
  skippedItems: number;
  totalTime: number; // milliseconds
  avgTimePerItem: number;
  changes: {
    archived?: number;
    deleted?: number;
    labeled?: number;
    unlabeled?: number;
    markedRead?: number;
    markedUnread?: number;
    starred?: number;
    unstarred?: number;
    categorized?: number;
    moved?: number;
    forwarded?: number;
    unsubscribed?: number;
    [key: string]: number | undefined;
  };
  sizeReclaimed?: number; // bytes
  costSaved?: number; // estimated cost savings
}

export interface BulkOperationError {
  itemId?: string;
  error: string;
  code?: string;
  timestamp: Date;
  context?: any;
  stackTrace?: string;
  retryable: boolean;
}

export interface RecurringSchedule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'custom';
  interval?: number;
  daysOfWeek?: number[]; // 0-6
  dayOfMonth?: number;
  time?: string; // HH:MM
  timezone?: string;
  endDate?: Date;
  maxOccurrences?: number;
  nextRun?: Date;
}

// Unsubscribe specific types
export interface UnsubscribeOperation extends BulkOperation {
  type: 'unsubscribe';
  unsubscribeMethod: 'link' | 'email' | 'manual' | 'block';
  unsubscribeResults: {
    successful: UnsubscribeResult[];
    failed: UnsubscribeResult[];
    manual: UnsubscribeResult[];
  };
}

export interface UnsubscribeResult {
  messageId: string;
  from: string;
  subject: string;
  method: 'link' | 'email' | 'manual' | 'block';
  unsubscribeUrl?: string;
  unsubscribeEmail?: string;
  status: 'success' | 'failed' | 'pending' | 'manual';
  error?: string;
  confirmedAt?: Date;
  blockedDomain?: string;
}

// Newsletter sender types
export interface NewsletterSender {
  id: string;
  email: string;
  name: string;
  domain: string;
  category: 'newsletter' | 'marketing' | 'promotion' | 'notification' | 'transactional' | 'other';
  frequency: 'daily' | 'weekly' | 'biweekly' | 'monthly' | 'occasional' | 'unknown';
  volume: {
    total: number;
    lastMonth: number;
    lastWeek: number;
    trend: 'increasing' | 'stable' | 'decreasing';
  };
  lastReceived: Date;
  firstReceived: Date;
  unsubscribeMethod?: 'link' | 'email' | 'manual' | 'none';
  unsubscribeUrl?: string;
  unsubscribeEmail?: string;
  reputation: {
    score: number; // 0-100
    factors: {
      spamReports: number;
      userEngagement: number;
      domainAge: number;
      authentication: boolean;
    };
  };
  statistics: {
    openRate: number;
    clickRate: number;
    unsubscribeRate: number;
    spamRate: number;
  };
  isWhitelisted?: boolean;
  isBlocked?: boolean;
  recentEmails: Array<{
    id: string;
    subject: string;
    date: Date;
    hasUnsubscribeLink: boolean;
  }>;
}

// Whitelist management
export interface WhitelistEntry {
  id: string;
  type: 'email' | 'domain' | 'pattern';
  value: string;
  reason?: string;
  addedAt: Date;
  addedBy?: string;
  isVIP?: boolean;
  expiresAt?: Date;
}

// Unsubscribe detection
export interface UnsubscribeDetection {
  method: 'link' | 'email' | 'manual' | 'none';
  confidence: number; // 0-100
  details: {
    headerUnsubscribe?: string;
    headerUnsubscribePost?: string;
    linkUrls?: string[];
    emailAddresses?: string[];
    manualInstructions?: string;
  };
  validation: {
    isValid: boolean;
    lastChecked?: Date;
    errorMessage?: string;
  };
}

// Bulk unsubscribe operation
export interface BulkUnsubscribeOperation {
  id: string;
  status: 'preview' | 'processing' | 'completed' | 'failed' | 'cancelled';
  selection: {
    senderIds: string[];
    totalSenders: number;
    totalEmails: number;
    method: 'auto' | 'manual';
  };
  schedule?: {
    scheduledFor: Date;
    recurring?: boolean;
    frequency?: 'weekly' | 'monthly';
  };
  progress: {
    current: number;
    total: number;
    successful: number;
    failed: number;
    manual: number;
    percentage: number;
    currentSender?: string;
    estimatedTime?: number; // seconds
  };
  results: {
    successful: Array<{
      senderId: string;
      senderEmail: string;
      method: string;
      timestamp: Date;
    }>;
    failed: Array<{
      senderId: string;
      senderEmail: string;
      error: string;
      retryable: boolean;
      timestamp: Date;
    }>;
    manual: Array<{
      senderId: string;
      senderEmail: string;
      instructions: string;
      timestamp: Date;
    }>;
  };
  backup?: {
    id: string;
    createdAt: Date;
    restorable: boolean;
  };
  analytics: {
    emailsFreed: number;
    storageReclaimed: number; // bytes
    timeEstimatedSaved: number; // hours per month
    costSaved?: number;
  };
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  createdBy?: string;
}

// Archive specific types
export interface ArchiveOperation extends BulkOperation {
  type: 'archive';
  archiveOptions: {
    removeLabels?: boolean;
    markAsRead?: boolean;
    skipImportant?: boolean;
    preserveStarred?: boolean;
  };
}

// Delete specific types
export interface DeleteOperation extends BulkOperation {
  type: 'delete';
  deleteOptions: {
    permanent?: boolean;
    moveToTrash?: boolean;
    skipConfirmation?: boolean;
    includeAttachments?: boolean;
  };
}

// Label specific types
export interface LabelOperation extends BulkOperation {
  type: 'label' | 'removeLabel';
  labelOptions: {
    labels: string[];
    createIfNotExists?: boolean;
    exclusive?: boolean; // Remove other labels
    skipNested?: boolean;
  };
}

// Export specific types
export interface ExportOperation extends BulkOperation {
  type: 'export';
  exportOptions: {
    format: 'json' | 'csv' | 'mbox' | 'pdf';
    destination: 'download' | 'drive' | 's3' | 'email';
    includeAttachments: boolean;
    includeHeaders: boolean;
    dateFormat?: string;
    encoding?: string;
    compression?: 'none' | 'zip' | 'gzip';
    encryptionKey?: string;
  };
  exportResult?: {
    fileUrl?: string;
    fileSize?: number;
    checksum?: string;
    expiresAt?: Date;
  };
}

// Bulk operation queue management
export interface BulkOperationQueue {
  operations: BulkOperation[];
  maxConcurrent: number;
  currentlyProcessing: string[];
  priorityQueue: string[]; // Operation IDs in priority order
  isPaused: boolean;
  stats: {
    totalQueued: number;
    totalProcessing: number;
    totalCompleted: number;
    totalFailed: number;
    avgProcessingTime: number;
  };
}