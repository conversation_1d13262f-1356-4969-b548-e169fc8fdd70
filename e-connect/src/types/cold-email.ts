// Cold Email Blocker types

// Core detection types
export interface ColdEmailDetection {
  id: string;
  emailId: string;
  threadId: string;
  detectedAt: Date;
  confidence: number; // 0-100
  status: 'pending' | 'reviewed' | 'blocked' | 'allowed' | 'whitelisted';
  
  // Detection details
  detection: {
    method: DetectionMethod[];
    signals: DetectionSignal[];
    mlScore: number;
    ruleMatches: string[];
    explanation: string;
  };
  
  // Email metadata
  email: {
    from: string;
    senderName?: string;
    domain: string;
    subject: string;
    snippet: string;
    receivedDate: Date;
    hasAttachments: boolean;
    size: number;
  };
  
  // Sender analysis
  senderAnalysis: SenderAnalysis;
  
  // Content analysis
  contentAnalysis: ContentAnalysis;
  
  // Review details
  review?: {
    reviewedAt: Date;
    reviewedBy: string;
    decision: 'block' | 'allow' | 'whitelist';
    reason?: string;
    feedback?: ReviewFeedback;
  };
  
  // Actions taken
  actions: {
    blocked?: boolean;
    movedToSpam?: boolean;
    deletedAt?: Date;
    reportedAt?: Date;
    unsubscribed?: boolean;
  };
}

export type DetectionMethod = 
  | 'ml_model'
  | 'sender_reputation'
  | 'content_pattern'
  | 'behavioral_analysis'
  | 'domain_check'
  | 'spf_dkim_fail'
  | 'blacklist_match'
  | 'keyword_match'
  | 'link_analysis'
  | 'image_analysis'
  | 'mass_sending'
  | 'template_match';

export interface DetectionSignal {
  type: string;
  value: any;
  weight: number;
  confidence: number;
  description: string;
}

// Sender analysis types
export interface SenderAnalysis {
  email: string;
  domain: string;
  reputation: SenderReputation;
  authentication: {
    spf: AuthenticationResult;
    dkim: AuthenticationResult;
    dmarc: AuthenticationResult;
  };
  domainInfo: {
    age?: number; // days
    registrar?: string;
    country?: string;
    isDisposable: boolean;
    isFreemail: boolean;
    isDynamic: boolean;
  };
  statistics: {
    totalEmails: number;
    firstSeen: Date;
    lastSeen: Date;
    recipientCount: number;
    engagementRate: number;
    reportCount: number;
    blockCount: number;
  };
  patterns: {
    sendingFrequency: 'high' | 'medium' | 'low';
    timePattern: string[]; // e.g., ['business_hours', 'weekdays']
    massMailer: boolean;
    bulkSending: boolean;
  };
}

export interface SenderReputation {
  score: number; // 0-100
  level: 'trusted' | 'neutral' | 'suspicious' | 'malicious';
  factors: {
    domainAge: number;
    authenticationScore: number;
    engagementScore: number;
    reportScore: number;
    behaviorScore: number;
  };
  history: {
    totalSent: number;
    blocked: number;
    reported: number;
    engaged: number;
    unsubscribed: number;
  };
  blacklists: string[];
  whitelists: string[];
}

export interface AuthenticationResult {
  status: 'pass' | 'fail' | 'neutral' | 'none';
  details?: string;
}

// Content analysis types
export interface ContentAnalysis {
  language: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  tone: 'professional' | 'casual' | 'aggressive' | 'urgent';
  
  patterns: {
    isColdEmail: boolean;
    isSalesEmail: boolean;
    isMarketingEmail: boolean;
    isRecruitingEmail: boolean;
    isPhishing: boolean;
    isScam: boolean;
  };
  
  keywords: {
    sales: string[];
    urgency: string[];
    financial: string[];
    suspicious: string[];
    action: string[];
  };
  
  structure: {
    hasUnsubscribeLink: boolean;
    hasContactInfo: boolean;
    hasCompanyInfo: boolean;
    hasPrivacyPolicy: boolean;
    templateSimilarity: number; // 0-100
  };
  
  links: LinkAnalysis[];
  images: ImageAnalysis[];
  
  mlAnalysis: {
    coldEmailProbability: number;
    spamProbability: number;
    phishingProbability: number;
    categoryScores: Record<string, number>;
  };
}

export interface LinkAnalysis {
  url: string;
  domain: string;
  isShortened: boolean;
  isTracking: boolean;
  isSuspicious: boolean;
  reputation: 'safe' | 'unknown' | 'suspicious' | 'malicious';
  redirectChain?: string[];
}

export interface ImageAnalysis {
  url?: string;
  type: string;
  size: number;
  isTracking: boolean;
  isHidden: boolean;
  altText?: string;
}

// Review and training types
export interface ReviewFeedback {
  wasCorrect: boolean;
  actualType?: 'cold_email' | 'legitimate' | 'spam' | 'phishing';
  userComments?: string;
  trainingData?: {
    features: Record<string, any>;
    label: string;
    confidence: number;
  };
}

// Sender management types
export interface BlockedSender {
  id: string;
  email?: string;
  domain?: string;
  pattern?: string;
  type: 'email' | 'domain' | 'pattern';
  reason: string;
  blockedAt: Date;
  blockedBy: string;
  
  statistics: {
    emailsBlocked: number;
    lastBlocked?: Date;
    reportCount: number;
  };
  
  metadata?: {
    category?: string;
    tags?: string[];
    notes?: string;
  };
  
  expiry?: {
    type: 'permanent' | 'temporary';
    expiresAt?: Date;
  };
}

export interface WhitelistedSender {
  id: string;
  email?: string;
  domain?: string;
  pattern?: string;
  type: 'email' | 'domain' | 'pattern' | 'vip';
  reason: string;
  whitelistedAt: Date;
  whitelistedBy: string;
  
  vipSettings?: {
    priority: 'high' | 'medium' | 'low';
    notifications: boolean;
    bypassAllFilters: boolean;
  };
  
  statistics: {
    emailsAllowed: number;
    lastReceived?: Date;
  };
  
  expiry?: {
    type: 'permanent' | 'temporary';
    expiresAt?: Date;
  };
}

// Settings and configuration types
export interface ColdEmailSettings {
  enabled: boolean;
  sensitivity: 'conservative' | 'balanced' | 'aggressive';
  
  detection: {
    mlEnabled: boolean;
    mlThreshold: number;
    reputationEnabled: boolean;
    reputationThreshold: number;
    contentAnalysisEnabled: boolean;
    behavioralAnalysisEnabled: boolean;
    customRulesEnabled: boolean;
  };
  
  actions: {
    autoBlock: boolean;
    autoBlockThreshold: number;
    moveToSpam: boolean;
    deleteAfterDays?: number;
    notifyOnDetection: boolean;
    requireReview: boolean;
  };
  
  filters: {
    timeBasedFiltering: TimeBasedFilter[];
    geographicFiltering: GeographicFilter[];
    industryFilters: IndustryFilter[];
    customFilters: CustomFilter[];
  };
  
  learning: {
    enableLearning: boolean;
    requireMinSamples: number;
    autoUpdateModel: boolean;
    shareAnonymousData: boolean;
  };
  
  integration: {
    webhooksEnabled: boolean;
    webhookUrl?: string;
    crmIntegration?: CRMIntegration;
    apiAccess: boolean;
  };
  
  teamSettings?: {
    sharedBlocklist: boolean;
    sharedWhitelist: boolean;
    requireApproval: boolean;
    approvers: string[];
  };
}

export interface TimeBasedFilter {
  id: string;
  name: string;
  enabled: boolean;
  schedule: {
    days: number[]; // 0-6
    startTime: string; // HH:MM
    endTime: string; // HH:MM
    timezone: string;
  };
  action: 'block' | 'quarantine' | 'flag';
  sensitivity: 'low' | 'medium' | 'high';
}

export interface GeographicFilter {
  id: string;
  name: string;
  enabled: boolean;
  type: 'whitelist' | 'blacklist';
  countries?: string[];
  regions?: string[];
  languages?: string[];
  action: 'block' | 'quarantine' | 'flag';
}

export interface IndustryFilter {
  id: string;
  industry: string;
  enabled: boolean;
  keywords: string[];
  domains: string[];
  sensitivity: 'low' | 'medium' | 'high';
  exceptions: string[];
}

export interface CustomFilter {
  id: string;
  name: string;
  enabled: boolean;
  conditions: FilterCondition[];
  action: 'block' | 'quarantine' | 'flag' | 'whitelist';
  priority: number;
}

export interface FilterCondition {
  field: string;
  operator: 'contains' | 'equals' | 'starts_with' | 'ends_with' | 'matches' | 'greater_than' | 'less_than';
  value: any;
  caseSensitive?: boolean;
}

export interface CRMIntegration {
  provider: 'salesforce' | 'hubspot' | 'pipedrive' | 'custom';
  enabled: boolean;
  syncContacts: boolean;
  syncActivities: boolean;
  fieldMapping: Record<string, string>;
}

// Analytics types
export interface ColdEmailAnalytics {
  period: {
    start: Date;
    end: Date;
  };
  
  detection: {
    total: number;
    blocked: number;
    allowed: number;
    reviewed: number;
    falsePositives: number;
    falseNegatives: number;
    accuracy: number;
  };
  
  trends: {
    daily: TrendData[];
    weekly: TrendData[];
    monthly: TrendData[];
    byHour: HourlyData[];
    byDayOfWeek: DayData[];
  };
  
  senders: {
    topBlocked: SenderStat[];
    topReported: SenderStat[];
    topDomains: DomainStat[];
    newSenders: number;
    repeatOffenders: number;
  };
  
  effectiveness: {
    detectionRate: number;
    blockRate: number;
    reviewRate: number;
    userSatisfaction: number;
    timesSaved: number; // hours
    productivityGain: number; // percentage
  };
  
  mlPerformance: {
    accuracy: number;
    precision: number;
    recall: number;
    f1Score: number;
    improvements: ModelImprovement[];
  };
  
  categories: {
    sales: number;
    marketing: number;
    recruiting: number;
    phishing: number;
    other: number;
  };
}

export interface TrendData {
  date: Date;
  detected: number;
  blocked: number;
  allowed: number;
  reviewed: number;
}

export interface HourlyData {
  hour: number;
  count: number;
  avgConfidence: number;
}

export interface DayData {
  day: number;
  count: number;
  avgConfidence: number;
}

export interface SenderStat {
  sender: string;
  domain: string;
  count: number;
  lastSeen: Date;
  reputation: number;
}

export interface DomainStat {
  domain: string;
  count: number;
  senderCount: number;
  avgReputation: number;
  blocked: number;
}

export interface ModelImprovement {
  date: Date;
  version: string;
  accuracyBefore: number;
  accuracyAfter: number;
  samplesUsed: number;
}

// Training data types
export interface TrainingData {
  id: string;
  emailId: string;
  features: EmailFeatures;
  label: 'cold_email' | 'legitimate' | 'spam' | 'phishing';
  confidence: number;
  source: 'manual' | 'automated' | 'user_feedback';
  createdAt: Date;
  createdBy: string;
}

export interface EmailFeatures {
  // Sender features
  senderDomainAge: number;
  senderReputation: number;
  authenticationScore: number;
  sendingFrequency: number;
  recipientCount: number;
  
  // Content features
  subjectLength: number;
  bodyLength: number;
  linkCount: number;
  imageCount: number;
  attachmentCount: number;
  
  // Pattern features
  hasUnsubscribe: boolean;
  hasTracking: boolean;
  templateScore: number;
  personalizedScore: number;
  
  // Keyword features
  salesKeywords: number;
  urgencyKeywords: number;
  suspiciousKeywords: number;
  
  // Behavioral features
  timeSinceLast: number;
  engagementHistory: number;
  reportHistory: number;
  
  // Custom features
  customFeatures?: Record<string, any>;
}

// Queue and processing types
export interface ColdEmailQueue {
  pending: ColdEmailDetection[];
  processing: string[];
  processed: number;
  stats: {
    avgProcessingTime: number;
    queueLength: number;
    processingRate: number;
  };
}

// Export and compliance types
export interface ColdEmailExport {
  id: string;
  type: 'blocked' | 'allowed' | 'all';
  format: 'csv' | 'json' | 'pdf';
  dateRange: {
    start: Date;
    end: Date;
  };
  includeOptions: {
    detectionDetails: boolean;
    senderAnalysis: boolean;
    contentAnalysis: boolean;
    userDecisions: boolean;
  };
  createdAt: Date;
  createdBy: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  downloadUrl?: string;
  expiresAt?: Date;
}

// Audit log types
export interface ColdEmailAuditLog {
  id: string;
  timestamp: Date;
  action: AuditAction;
  entityType: 'detection' | 'sender' | 'setting' | 'filter' | 'export';
  entityId: string;
  userId: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

export type AuditAction = 
  | 'block_sender'
  | 'whitelist_sender'
  | 'review_email'
  | 'update_settings'
  | 'create_filter'
  | 'delete_filter'
  | 'export_data'
  | 'train_model'
  | 'manual_override';