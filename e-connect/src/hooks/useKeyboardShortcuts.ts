import { useEffect } from 'react'

export interface KeyboardShortcut {
  key: string
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  meta?: boolean
  handler: () => void
  description?: string
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[]) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      for (const shortcut of shortcuts) {
        const ctrlMatch = shortcut.ctrl ? (event.ctrlKey || event.metaKey) : !event.ctrlKey && !event.metaKey
        const shiftMatch = shortcut.shift ? event.shiftKey : !event.shiftKey
        const altMatch = shortcut.alt ? event.altKey : !event.altKey
        const metaMatch = shortcut.meta ? event.metaKey : !event.metaKey
        
        if (
          event.key.toLowerCase() === shortcut.key.toLowerCase() &&
          ctrlMatch &&
          shiftMatch &&
          altMatch &&
          metaMatch
        ) {
          event.preventDefault()
          shortcut.handler()
          break
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [shortcuts])
}

// Common shortcuts for bulk unsubscribe
export const bulkUnsubscribeShortcuts: KeyboardShortcut[] = [
  {
    key: 'a',
    ctrl: true,
    handler: () => {},
    description: 'Select all senders'
  },
  {
    key: 'd',
    ctrl: true,
    handler: () => {},
    description: 'Deselect all'
  },
  {
    key: '/',
    handler: () => {},
    description: 'Focus search'
  },
  {
    key: 'p',
    handler: () => {},
    description: 'Preview unsubscribe'
  },
  {
    key: 'Enter',
    handler: () => {},
    description: 'Start unsubscribe'
  },
  {
    key: 'Escape',
    handler: () => {},
    description: 'Cancel operation'
  },
  {
    key: 'w',
    handler: () => {},
    description: 'Switch to whitelist'
  },
  {
    key: 's',
    handler: () => {},
    description: 'Switch to senders'
  },
  {
    key: 'r',
    handler: () => {},
    description: 'Refresh data'
  }
]