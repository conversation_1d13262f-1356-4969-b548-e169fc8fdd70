import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { RouterProvider } from '@tanstack/react-router'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { ErrorBoundary } from 'react-error-boundary'
import './index.css'

// Import the router instance
import { router } from './router'

// Import stores for initialization
import { useSettingsStore } from './stores/settingsStore'
import { useMultiAccountStore } from './stores/multiAccountStore'
import { useAnalyticsStore } from './stores/analyticsStore'
import { useAuthStore } from './stores/authStore'
import { useEmailStore } from './stores/emailStore'
import { useAssistantStore } from './stores/assistantStore'
import { useRulesStore } from './stores/rulesStore'
import { useBulkStore } from './stores/bulkStore'
import { useColdEmailStore } from './stores/coldEmailStore'
import { useUIStore } from './stores/uiStore'

// Global error handler
function handleGlobalError(error: Error, errorInfo: any) {
  console.error('Global error caught:', error, errorInfo)
  
  // Track error in analytics if available
  try {
    const analyticsStore = useAnalyticsStore.getState()
    if (analyticsStore.trackEvent) {
      analyticsStore.trackEvent('error', 'global_error', error.message)
    }
  } catch (e) {
    // Ignore analytics errors
  }
  
  // You could send to external error tracking service here
  // e.g., Sentry, LogRocket, etc.
}

// Global error fallback component
function ErrorFallback({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 mx-4">
        <h2 className="text-2xl font-bold text-red-600 dark:text-red-400 mb-4">
          Application Error
        </h2>
        <p className="text-gray-700 dark:text-gray-300 mb-4">
          Something went wrong with the application. Please try refreshing the page.
        </p>
        {import.meta.env.DEV && (
          <details className="mb-4">
            <summary className="text-sm text-gray-500 dark:text-gray-400 cursor-pointer">
              Error Details
            </summary>
            <pre className="mt-2 text-xs bg-gray-100 dark:bg-gray-700 p-2 rounded overflow-auto max-h-40">
              {error.message}
            </pre>
          </details>
        )}
        <div className="flex gap-3">
          <button
            onClick={resetErrorBoundary}
            className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
          <button
            onClick={() => window.location.reload()}
            className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
          >
            Reload Page
          </button>
        </div>
      </div>
    </div>
  )
}

// Initialize all stores
function initializeStores() {
  // Initialize stores in the correct order
  // Some stores may depend on others being initialized first
  
  try {
    // Core stores first
    useSettingsStore.getState().initializeSettings?.()
    useAuthStore.getState().initialize?.()
    useUIStore.getState().initialize?.()
    
    // Feature stores
    useMultiAccountStore.getState().initializeMultiAccount?.()
    useEmailStore.getState().initialize?.()
    useAnalyticsStore.getState().initializeAnalytics?.()
    useAssistantStore.getState().initialize?.()
    useRulesStore.getState().initialize?.()
    useBulkStore.getState().initialize?.()
    useColdEmailStore.getState().initialize?.()
    
    // Make analytics store globally available for route tracking
    ;(window as any).__analyticsStore = useAnalyticsStore.getState()
    
    console.log('All stores initialized successfully')
  } catch (error) {
    console.error('Error initializing stores:', error)
  }
}

// Start MSW in development
async function enableMocking() {
  if (import.meta.env.DEV) {
    try {
      const { worker } = await import('./mocks/browser')
      await worker.start({
        onUnhandledRequest: 'bypass',
        serviceWorker: {
          url: '/mockServiceWorker.js',
        },
      })
      console.log('MSW initialized successfully')
    } catch (error) {
      console.error('Failed to start MSW:', error)
    }
  }
}

// Create a client for React Query with optimized defaults
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      gcTime: 1000 * 60 * 10, // 10 minutes
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors except 408, 429
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          if (error.response.status === 408 || error.response.status === 429) {
            return failureCount < 2
          }
          return false
        }
        return failureCount < 3
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      networkMode: 'online',
    },
    mutations: {
      retry: (failureCount, error: any) => {
        // Don't retry mutations on client errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false
        }
        return failureCount < 1
      },
      networkMode: 'online',
    },
  },
  mutationCache: {
    onError: (error: any) => {
      console.error('Mutation error:', error)
      // Track mutation errors
      try {
        const analyticsStore = useAnalyticsStore.getState()
        if (analyticsStore.trackEvent) {
          analyticsStore.trackEvent('error', 'mutation_error', error.message)
        }
      } catch (e) {
        // Ignore analytics errors
      }
    },
  },
  queryCache: {
    onError: (error: any) => {
      console.error('Query error:', error)
      // Track query errors
      try {
        const analyticsStore = useAnalyticsStore.getState()
        if (analyticsStore.trackEvent) {
          analyticsStore.trackEvent('error', 'query_error', error.message)
        }
      } catch (e) {
        // Ignore analytics errors
      }
    },
  },
})

// Initialize the app
async function initializeApp() {
  try {
    // Start MSW first
    await enableMocking()
    
    // Initialize stores
    initializeStores()
    
    // Render the app
    const root = createRoot(document.getElementById('root')!)
    root.render(
      <StrictMode>
        <ErrorBoundary
          FallbackComponent={ErrorFallback}
          onError={handleGlobalError}
          onReset={() => {
            // Reset stores or perform cleanup if needed
            window.location.reload()
          }}
        >
          <QueryClientProvider client={queryClient}>
            <RouterProvider router={router} />
            {import.meta.env.DEV && (
              <ReactQueryDevtools 
                initialIsOpen={false} 
                position="bottom-left"
              />
            )}
          </QueryClientProvider>
        </ErrorBoundary>
      </StrictMode>
    )
    
    console.log('Application initialized successfully')
  } catch (error) {
    console.error('Failed to initialize application:', error)
    
    // Fallback initialization without MSW
    const root = createRoot(document.getElementById('root')!)
    root.render(
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center p-6">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Initialization Error</h1>
          <p className="text-gray-700 mb-4">Failed to start the application properly.</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Reload Page
          </button>
        </div>
      </div>
    )
  }
}

// Start the application
initializeApp()
