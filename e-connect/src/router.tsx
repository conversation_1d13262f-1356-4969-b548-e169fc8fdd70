import { createRouter } from '@tanstack/react-router'
import { routeTree } from './routeTree.gen'
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/react/24/outline'

// Enhanced error boundary component
function ErrorBoundary({ error, reset }: { error: Error; reset: () => void }) {
  const isDev = import.meta.env.DEV
  
  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center w-12 h-12 mx-auto bg-red-100 dark:bg-red-900/20 rounded-full mb-4">
          <ExclamationTriangleIcon className="w-6 h-6 text-red-600 dark:text-red-400" />
        </div>
        
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-4">
          Something went wrong
        </h2>
        
        <p className="text-gray-600 dark:text-gray-300 text-center mb-6">
          {isDev && error.message ? error.message : 'An unexpected error occurred. Please try again.'}
        </p>
        
        {isDev && error.stack && (
          <details className="mb-6">
            <summary className="text-sm text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-200">
              Technical Details
            </summary>
            <pre className="mt-2 text-xs text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-auto max-h-40">
              {error.stack}
            </pre>
          </details>
        )}
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button
            onClick={reset}
            className="flex-1 flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            <ArrowPathIcon className="w-4 h-4" />
            Try Again
          </button>
          
          <button
            onClick={() => window.location.href = '/'}
            className="flex-1 bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            Go Home
          </button>
        </div>
        
        <p className="text-xs text-gray-500 dark:text-gray-400 text-center mt-4">
          If this problem persists, please contact support.
        </p>
      </div>
    </div>
  )
}

// Enhanced loading component
function LoadingComponent() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400"></div>
          <div className="animate-ping absolute top-0 left-0 h-12 w-12 border border-blue-300 dark:border-blue-600 rounded-full opacity-20"></div>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
          Loading...
        </p>
      </div>
    </div>
  )
}

// Route guard function for authentication
function createRouteGuard() {
  return (opts: any) => {
    // Enhanced route guards
    const { pathname } = window.location
    
    // Public routes that don't require authentication
    const publicRoutes = ['/about', '/chat']
    
    // Check if route is public
    if (publicRoutes.includes(pathname)) {
      return opts
    }
    
    // For now, we'll let individual routes handle specific auth requirements
    // This can be enhanced with actual authentication checks
    return opts
  }
}

// Create and export the router instance
export const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  defaultPreloadStaleTime: 1000 * 60 * 5, // 5 minutes
  defaultErrorComponent: ErrorBoundary,
  defaultPendingComponent: LoadingComponent,
  defaultGcTime: 1000 * 60 * 10, // 10 minutes
  onRouteChange: () => {
    // Track route changes for analytics
    try {
      const pathname = window.location.pathname
      console.log('Route changed to:', pathname)
      
      // Track page view in analytics if store is available
      // Using a timeout to ensure stores are initialized
      setTimeout(() => {
        try {
          const analyticsStore = (window as any).__analyticsStore
          if (analyticsStore?.trackPageView) {
            analyticsStore.trackPageView(pathname)
          }
        } catch (error) {
          // Ignore analytics errors
        }
      }, 100)
    } catch (error) {
      console.error('Route change tracking failed:', error)
    }
  },
  // Add route guards and other router-level configurations
  beforeLoad: createRouteGuard(),
})

// Register router for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

// Export router types for use in components
export type Router = typeof router