import { http } from 'msw'
import type { BulkOperation } from '../types/bulk'

// Mock operations store
const operations = new Map<string, BulkOperation>()

// WebSocket connections store (simulated)
const wsConnections = new Map<string, (data: any) => void>()

// Helper to create a mock operation
function createMockOperation(type: string, totalItems: number): BulkOperation {
  const id = `op-${Date.now()}`
  const operation: BulkOperation = {
    id,
    type: type as any,
    status: 'processing',
    createdAt: new Date(),
    startedAt: new Date(),
    selection: {
      type: 'filter',
      totalCount: totalItems,
      filter: {
        category: ['newsletter', 'marketing'],
        dateRange: {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          end: new Date()
        }
      }
    },
    progress: {
      processed: 0,
      succeeded: 0,
      failed: 0,
      skipped: 0,
      percentage: 0,
      estimatedTimeRemaining: totalItems * 0.5, // 0.5 seconds per item estimate
      currentItem: 'Starting...'
    },
    errors: [],
    retryCount: 0,
    maxRetries: 3
  }
  
  operations.set(id, operation)
  
  // Simulate progress updates
  simulateProgress(operation)
  
  return operation
}

// Simulate operation progress
function simulateProgress(operation: BulkOperation) {
  const interval = setInterval(() => {
    const op = operations.get(operation.id)
    if (!op || op.status !== 'processing') {
      clearInterval(interval)
      return
    }
    
    const totalProcessed = op.progress.processed + op.progress.succeeded + op.progress.failed + op.progress.skipped
    
    if (totalProcessed >= op.selection.totalCount) {
      // Operation complete
      op.status = 'completed'
      op.completedAt = new Date()
      op.summary = {
        totalItems: op.selection.totalCount,
        processedItems: op.selection.totalCount,
        successfulItems: op.progress.succeeded,
        failedItems: op.progress.failed,
        skippedItems: op.progress.skipped,
        totalTime: Date.now() - op.startedAt!.getTime(),
        avgTimePerItem: (Date.now() - op.startedAt!.getTime()) / op.selection.totalCount,
        changes: {
          [op.type === 'unsubscribe' ? 'unsubscribed' : op.type]: op.progress.succeeded
        },
        sizeReclaimed: op.progress.succeeded * 1024 * 50, // 50KB per item
        costSaved: op.progress.succeeded * 0.001 // $0.001 per item
      }
      
      // Send WebSocket notification
      const wsCallback = wsConnections.get(op.id)
      if (wsCallback) {
        wsCallback({
          type: 'operation-complete',
          operation: op,
          message: 'Operation completed successfully'
        })
      }
      
      clearInterval(interval)
      return
    }
    
    // Simulate processing with realistic distribution
    const random = Math.random()
    if (random < 0.85) {
      // Success
      op.progress.succeeded++
    } else if (random < 0.95) {
      // Failure
      op.progress.failed++
      
      // Add error
      if (op.errors.length < 50) {
        const errorTypes = [
          { error: 'Network timeout', code: 'NETWORK_TIMEOUT', retryable: true },
          { error: 'Invalid response from server', code: 'INVALID_RESPONSE', retryable: true },
          { error: 'Rate limit exceeded', code: 'RATE_LIMIT', retryable: true },
          { error: 'Authentication failed', code: 'AUTH_FAILED', retryable: false },
          { error: 'Resource not found', code: 'NOT_FOUND', retryable: false },
          { error: 'Permission denied', code: 'PERMISSION_DENIED', retryable: false }
        ]
        
        const errorType = errorTypes[Math.floor(Math.random() * errorTypes.length)]
        op.errors.push({
          itemId: `item-${totalProcessed}`,
          error: errorType.error,
          code: errorType.code,
          timestamp: new Date(),
          retryable: errorType.retryable,
          context: {
            attempt: 1,
            lastAttempt: new Date().toISOString()
          }
        })
      }
    } else {
      // Skip
      op.progress.skipped++
    }
    
    op.progress.processed = totalProcessed + 1
    op.progress.percentage = ((totalProcessed + 1) / op.selection.totalCount) * 100
    op.progress.currentItem = `item-${totalProcessed + 1}@example.com`
    
    const elapsed = (Date.now() - op.startedAt!.getTime()) / 1000
    const itemsPerSecond = (totalProcessed + 1) / elapsed
    op.progress.estimatedTimeRemaining = (op.selection.totalCount - totalProcessed - 1) / itemsPerSecond
    
    // Send WebSocket update
    const wsCallback = wsConnections.get(op.id)
    if (wsCallback) {
      wsCallback({
        type: 'progress-update',
        operation: op,
        message: `Processing ${op.progress.currentItem}`
      })
    }
    
    operations.set(op.id, op)
  }, 100) // Update every 100ms
}

export const progressTrackerHandlers = [
  // Start a new operation
  http.post('/api/operations/start', (req, res, ctx) => {
    const { type, totalItems = 1000 } = req.body as any
    const operation = createMockOperation(type, totalItems)
    
    return res(
      ctx.status(200),
      ctx.json({ operation })
    )
  }),
  
  // Get operation status
  http.get('/api/operations/:id', (req, res, ctx) => {
    const { id } = req.params
    const operation = operations.get(id as string)
    
    if (!operation) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Operation not found' })
      )
    }
    
    return res(
      ctx.status(200),
      ctx.json({ operation })
    )
  }),
  
  // Pause operation
  http.post('/api/operations/:id/pause', (req, res, ctx) => {
    const { id } = req.params
    const operation = operations.get(id as string)
    
    if (!operation) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Operation not found' })
      )
    }
    
    operation.status = 'paused'
    operations.set(id as string, operation)
    
    return res(
      ctx.status(200),
      ctx.json({ operation })
    )
  }),
  
  // Resume operation
  http.post('/api/operations/:id/resume', (req, res, ctx) => {
    const { id } = req.params
    const operation = operations.get(id as string)
    
    if (!operation) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Operation not found' })
      )
    }
    
    operation.status = 'processing'
    operations.set(id as string, operation)
    simulateProgress(operation)
    
    return res(
      ctx.status(200),
      ctx.json({ operation })
    )
  }),
  
  // Cancel operation
  http.post('/api/operations/:id/cancel', (req, res, ctx) => {
    const { id } = req.params
    const operation = operations.get(id as string)
    
    if (!operation) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Operation not found' })
      )
    }
    
    operation.status = 'cancelled'
    operation.completedAt = new Date()
    operations.set(id as string, operation)
    
    return res(
      ctx.status(200),
      ctx.json({ operation })
    )
  }),
  
  // Retry failed items
  http.post('/api/operations/:id/retry', (req, res, ctx) => {
    const { id } = req.params
    const { itemIds } = req.body as any
    const operation = operations.get(id as string)
    
    if (!operation) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Operation not found' })
      )
    }
    
    // Remove errors for retried items
    operation.errors = operation.errors.filter(e => !itemIds.includes(e.itemId))
    operations.set(id as string, operation)
    
    return res(
      ctx.status(200),
      ctx.json({ 
        message: `Retrying ${itemIds.length} items`,
        operation 
      })
    )
  }),
  
  // Export operation data
  http.get('/api/operations/:id/export', (req, res, ctx) => {
    const { id } = req.params
    const format = req.url.searchParams.get('format') || 'json'
    const operation = operations.get(id as string)
    
    if (!operation) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Operation not found' })
      )
    }
    
    // Create mock export data
    const exportData = {
      operation: {
        id: operation.id,
        type: operation.type,
        status: operation.status,
        startedAt: operation.startedAt,
        completedAt: operation.completedAt,
        summary: operation.summary
      },
      results: operation.results || [],
      errors: operation.errors
    }
    
    if (format === 'json') {
      return res(
        ctx.status(200),
        ctx.set('Content-Disposition', `attachment; filename="operation-${id}.json"`),
        ctx.json(exportData)
      )
    } else if (format === 'csv') {
      // Mock CSV data
      const csv = 'id,type,status,success,failed,skipped\n' +
        `${operation.id},${operation.type},${operation.status},${operation.progress.succeeded},${operation.progress.failed},${operation.progress.skipped}`
      
      return res(
        ctx.status(200),
        ctx.set('Content-Type', 'text/csv'),
        ctx.set('Content-Disposition', `attachment; filename="operation-${id}.csv"`),
        ctx.text(csv)
      )
    } else if (format === 'pdf') {
      // Mock PDF response
      return res(
        ctx.status(200),
        ctx.set('Content-Type', 'application/pdf'),
        ctx.set('Content-Disposition', `attachment; filename="operation-${id}.pdf"`),
        ctx.body(new ArrayBuffer(1024)) // Mock PDF data
      )
    }
    
    return res(
      ctx.status(400),
      ctx.json({ error: 'Invalid format' })
    )
  }),
  
  // WebSocket simulation endpoint
  http.post('/api/operations/:id/subscribe', (req, res, ctx) => {
    const { id } = req.params
    const { callback } = req.body as any
    
    // Store callback for WebSocket simulation
    if (callback) {
      wsConnections.set(id as string, callback)
    }
    
    return res(
      ctx.status(200),
      ctx.json({ message: 'Subscribed to operation updates' })
    )
  }),
  
  // Get all operations
  http.get('/api/operations', (req, res, ctx) => {
    const status = req.url.searchParams.get('status')
    const type = req.url.searchParams.get('type')
    
    let filteredOps = Array.from(operations.values())
    
    if (status) {
      filteredOps = filteredOps.filter(op => op.status === status)
    }
    
    if (type) {
      filteredOps = filteredOps.filter(op => op.type === type)
    }
    
    return res(
      ctx.status(200),
      ctx.json({ 
        operations: filteredOps,
        total: filteredOps.length 
      })
    )
  })
]