import { http } from 'msw'
import type { 
  EmailAccount, 
  AccountProvider, 
  AccountStatus,
  UnifiedThread,
  UnifiedInboxFilter,
  AccountMigration,
  AccountBackup,
  AccountAnalytics,
  MultiAccountSettings 
} from '../../types/multi-account'
import type { Thread } from '../../types/email'
import { mockThreads } from '../data/emails'

// Mock accounts data
let mockAccounts: EmailAccount[] = [
  {
    id: 'gmail-personal',
    name: 'Personal Gmail',
    email: '<EMAIL>',
    provider: 'gmail',
    status: 'active',
    isDefault: true,
    isPrimary: true,
    color: '#EA4335',
    displayOrder: 0,
    config: {
      provider: 'gmail',
      oauth: {
        clientId: 'gmail-client-id',
        scopes: ['https://www.googleapis.com/auth/gmail.readonly'],
        redirectUri: 'http://localhost:3000/auth/gmail/callback'
      }
    },
    auth: {
      type: 'oauth',
      oauth: {
        accessToken: 'mock-gmail-token',
        refreshToken: 'mock-gmail-refresh',
        expiresAt: new Date(Date.now() + 3600000),
        tokenType: 'Bearer',
        scope: 'gmail.readonly'
      },
      isValid: true,
      lastValidated: new Date()
    },
    sync: {
      enabled: true,
      syncInterval: 15,
      fullSyncInterval: 24,
      emails: true,
      contacts: true,
      calendar: false,
      syncFolders: ['INBOX', 'SENT', 'DRAFTS'],
      excludeFolders: ['SPAM', 'TRASH'],
      syncLabels: true,
      syncAttachments: true,
      maxAttachmentSize: 25,
      daysToSync: 30,
      maxEmailsPerSync: 100,
      conflictResolution: 'server_wins',
      backgroundSync: true,
      wifiOnly: false,
      lowBatteryMode: false
    },
    stats: {
      totalEmails: 1247,
      unreadEmails: 23,
      syncedEmails: 1247,
      lastSyncDuration: 2340,
      averageSyncDuration: 2100,
      syncErrors: 0,
      consecutiveFailures: 0,
      apiCalls: 1567,
      rateLimitHits: 2,
      bandwidthUsed: 45623789,
      storageUsed: 234567890,
      attachmentStorage: 123456789,
      emailsSent: 89,
      emailsReceived: 1158,
      threadsProcessed: 1247
    },
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date(),
    lastSyncAt: new Date(Date.now() - 300000),
    lastAccessAt: new Date(Date.now() - 60000)
  },
  {
    id: 'outlook-work',
    name: 'Work Outlook',
    email: '<EMAIL>',
    provider: 'outlook',
    status: 'active',
    isDefault: false,
    isPrimary: false,
    color: '#0078D4',
    displayOrder: 1,
    config: {
      provider: 'outlook',
      oauth: {
        clientId: 'outlook-client-id',
        scopes: ['https://graph.microsoft.com/mail.read'],
        redirectUri: 'http://localhost:3000/auth/outlook/callback'
      }
    },
    auth: {
      type: 'oauth',
      oauth: {
        accessToken: 'mock-outlook-token',
        refreshToken: 'mock-outlook-refresh',
        expiresAt: new Date(Date.now() + 3600000),
        tokenType: 'Bearer',
        scope: 'mail.read'
      },
      isValid: true,
      lastValidated: new Date()
    },
    sync: {
      enabled: true,
      syncInterval: 10,
      fullSyncInterval: 12,
      emails: true,
      contacts: true,
      calendar: true,
      syncFolders: ['Inbox', 'Sent Items', 'Drafts'],
      excludeFolders: ['Junk Email', 'Deleted Items'],
      syncLabels: false,
      syncAttachments: true,
      maxAttachmentSize: 50,
      daysToSync: 90,
      maxEmailsPerSync: 200,
      conflictResolution: 'server_wins',
      backgroundSync: true,
      wifiOnly: false,
      lowBatteryMode: false
    },
    stats: {
      totalEmails: 2834,
      unreadEmails: 47,
      syncedEmails: 2834,
      lastSyncDuration: 4567,
      averageSyncDuration: 4200,
      syncErrors: 1,
      lastSyncError: 'Rate limit exceeded',
      consecutiveFailures: 0,
      apiCalls: 3245,
      rateLimitHits: 5,
      bandwidthUsed: 123456789,
      storageUsed: 567890123,
      attachmentStorage: 234567890,
      emailsSent: 234,
      emailsReceived: 2600,
      threadsProcessed: 2834
    },
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date(),
    lastSyncAt: new Date(Date.now() - 180000),
    lastAccessAt: new Date(Date.now() - 120000)
  },
  {
    id: 'imap-custom',
    name: 'Custom IMAP',
    email: '<EMAIL>',
    provider: 'imap',
    status: 'error',
    isDefault: false,
    isPrimary: false,
    color: '#10B981',
    displayOrder: 2,
    config: {
      provider: 'imap',
      imap: {
        host: 'mail.mycompany.com',
        port: 993,
        secure: true,
        auth: {
          user: '<EMAIL>',
          pass: '*** encrypted ***'
        }
      },
      smtp: {
        host: 'mail.mycompany.com',
        port: 587,
        secure: true,
        auth: {
          user: '<EMAIL>',
          pass: '*** encrypted ***'
        }
      }
    },
    auth: {
      type: 'password',
      credentials: {
        username: '<EMAIL>',
        password: '*** encrypted ***'
      },
      isValid: false,
      lastValidated: new Date(Date.now() - 86400000),
      validationErrors: ['Authentication failed: Invalid credentials']
    },
    sync: {
      enabled: false,
      syncInterval: 30,
      fullSyncInterval: 24,
      emails: true,
      contacts: false,
      calendar: false,
      syncFolders: ['INBOX'],
      excludeFolders: [],
      syncLabels: false,
      syncAttachments: true,
      maxAttachmentSize: 10,
      daysToSync: 14,
      maxEmailsPerSync: 50,
      conflictResolution: 'server_wins',
      backgroundSync: false,
      wifiOnly: true,
      lowBatteryMode: true
    },
    stats: {
      totalEmails: 0,
      unreadEmails: 0,
      syncedEmails: 0,
      lastSyncDuration: 0,
      averageSyncDuration: 0,
      syncErrors: 12,
      lastSyncError: 'Authentication failed',
      consecutiveFailures: 12,
      apiCalls: 0,
      rateLimitHits: 0,
      bandwidthUsed: 0,
      storageUsed: 0,
      attachmentStorage: 0,
      emailsSent: 0,
      emailsReceived: 0,
      threadsProcessed: 0
    },
    createdAt: new Date('2024-02-15'),
    updatedAt: new Date(),
    lastSyncAt: undefined,
    lastAccessAt: new Date(Date.now() - 600000)
  }
]

let mockSettings: MultiAccountSettings = {
  autoSwitchOnEmail: true,
  showAccountInEmailList: true,
  groupByAccount: false,
  unifiedInboxEnabled: true,
  unifiedInboxAccounts: ['gmail-personal', 'outlook-work'],
  unifiedSorting: 'chronological',
  consolidateNotifications: true,
  notifyAllAccounts: true,
  accountSpecificSounds: false,
  searchAllAccountsByDefault: true,
  searchTimeout: 30,
  maxSearchResults: 100,
  maxConcurrentSyncs: 3,
  syncThrottling: true,
  offlineMode: false,
  dataIsolation: true,
  sharedContacts: false,
  sharedRules: false,
  lockOnAccountSwitch: false,
  sessionTimeout: 60,
  encryptAccountData: true
}

// Create unified threads from mock data
const createUnifiedThreads = (accountIds: string[] = mockAccounts.map(acc => acc.id)): UnifiedThread[] => {
  const unifiedThreads: UnifiedThread[] = []
  
  accountIds.forEach((accountId, accountIndex) => {
    const account = mockAccounts.find(acc => acc.id === accountId)
    if (!account || account.status !== 'active') return
    
    // Take a subset of mock threads for each account
    const accountThreads = mockThreads.slice(accountIndex * 5, (accountIndex + 1) * 5 + 10)
    
    accountThreads.forEach((thread, threadIndex) => {
      unifiedThreads.push({
        ...thread,
        id: `${accountId}-${thread.id}`,
        accountId,
        accountName: account.name,
        accountColor: account.color,
        accountProvider: account.provider
      })
    })
  })
  
  return unifiedThreads.sort((a, b) => 
    new Date(b.lastMessageDate).getTime() - new Date(a.lastMessageDate).getTime()
  )
}

export const multiAccountHandlers = [
  // Get all accounts
  http.get('/api/accounts', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json({
        data: mockAccounts,
        total: mockAccounts.length
      })
    )
  }),

  // Add new account
  http.post('/api/accounts', async (req, res, ctx) => {
    const accountData = await req.json()
    
    const newAccount: EmailAccount = {
      ...accountData,
      id: `account-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      stats: {
        totalEmails: 0,
        unreadEmails: 0,
        syncedEmails: 0,
        lastSyncDuration: 0,
        averageSyncDuration: 0,
        syncErrors: 0,
        consecutiveFailures: 0,
        apiCalls: 0,
        rateLimitHits: 0,
        bandwidthUsed: 0,
        storageUsed: 0,
        attachmentStorage: 0,
        emailsSent: 0,
        emailsReceived: 0,
        threadsProcessed: 0
      }
    }
    
    mockAccounts.push(newAccount)
    
    return res(
      ctx.status(201),
      ctx.json(newAccount)
    )
  }),

  // Update account
  http.patch('/api/accounts/:accountId', async (req, res, ctx) => {
    const { accountId } = req.params
    const updates = await req.json()
    
    const accountIndex = mockAccounts.findIndex(acc => acc.id === accountId)
    if (accountIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Account not found' })
      )
    }
    
    mockAccounts[accountIndex] = {
      ...mockAccounts[accountIndex],
      ...updates,
      updatedAt: new Date()
    }
    
    return res(
      ctx.status(200),
      ctx.json(mockAccounts[accountIndex])
    )
  }),

  // Delete account
  http.delete('/api/accounts/:accountId', (req, res, ctx) => {
    const { accountId } = req.params
    
    const accountIndex = mockAccounts.findIndex(acc => acc.id === accountId)
    if (accountIndex === -1) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Account not found' })
      )
    }
    
    mockAccounts.splice(accountIndex, 1)
    
    return res(
      ctx.status(204)
    )
  }),

  // Refresh account
  http.post('/api/accounts/:accountId/refresh', (req, res, ctx) => {
    const { accountId } = req.params
    
    const account = mockAccounts.find(acc => acc.id === accountId)
    if (!account) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Account not found' })
      )
    }
    
    // Simulate refresh
    account.lastSyncAt = new Date()
    account.updatedAt = new Date()
    
    return res(
      ctx.status(200),
      ctx.json(account)
    )
  }),

  // Authenticate account
  http.post('/api/accounts/:accountId/authenticate', async (req, res, ctx) => {
    const { accountId } = req.params
    const credentials = await req.json()
    
    const account = mockAccounts.find(acc => acc.id === accountId)
    if (!account) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Account not found' })
      )
    }
    
    // Simulate authentication success
    account.auth.isValid = true
    account.auth.lastValidated = new Date()
    account.status = 'active'
    account.updatedAt = new Date()
    
    return res(
      ctx.status(200),
      ctx.json({
        auth: account.auth,
        status: account.status
      })
    )
  }),

  // Sync account
  http.post('/api/accounts/:accountId/sync', async (req, res, ctx) => {
    const { accountId } = req.params
    const { force } = await req.json()
    
    const account = mockAccounts.find(acc => acc.id === accountId)
    if (!account) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Account not found' })
      )
    }
    
    // Simulate sync delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Update sync stats
    account.lastSyncAt = new Date()
    account.stats.lastSyncDuration = Math.floor(Math.random() * 5000) + 1000
    account.stats.averageSyncDuration = (account.stats.averageSyncDuration + account.stats.lastSyncDuration) / 2
    account.updatedAt = new Date()
    
    return res(
      ctx.status(200),
      ctx.json({
        success: true,
        duration: account.stats.lastSyncDuration,
        itemsSynced: Math.floor(Math.random() * 50) + 10,
        stats: account.stats
      })
    )
  }),

  // Get unified threads
  http.post('/api/unified/threads', async (req, res, ctx) => {
    const { accountIds, filter, sorting, page = 1, pageSize = 20 } = await req.json()
    
    let threads = createUnifiedThreads(accountIds)
    
    // Apply filters
    if (filter) {
      if (filter.isUnread !== undefined) {
        threads = threads.filter(thread => thread.status.isUnread === filter.isUnread)
      }
      if (filter.query) {
        threads = threads.filter(thread => 
          thread.subject.toLowerCase().includes(filter.query.toLowerCase()) ||
          thread.snippet.toLowerCase().includes(filter.query.toLowerCase())
        )
      }
    }
    
    // Apply sorting
    switch (sorting) {
      case 'by_account':
        threads.sort((a, b) => a.accountName.localeCompare(b.accountName))
        break
      case 'by_importance':
        threads.sort((a, b) => {
          if (a.status.isImportant && !b.status.isImportant) return -1
          if (!a.status.isImportant && b.status.isImportant) return 1
          return new Date(b.lastMessageDate).getTime() - new Date(a.lastMessageDate).getTime()
        })
        break
      default: // chronological
        threads.sort((a, b) => 
          new Date(b.lastMessageDate).getTime() - new Date(a.lastMessageDate).getTime()
        )
    }
    
    // Pagination
    const start = (page - 1) * pageSize
    const paginatedThreads = threads.slice(start, start + pageSize)
    
    // Account breakdown
    const accountBreakdown = mockAccounts
      .filter(acc => accountIds?.includes(acc.id) || !accountIds)
      .map(account => ({
        accountId: account.id,
        accountName: account.name,
        threads: threads.filter(t => t.accountId === account.id).length,
        unread: threads.filter(t => t.accountId === account.id && t.status.isUnread).length
      }))
    
    return res(
      ctx.status(200),
      ctx.json({
        data: paginatedThreads,
        pagination: {
          page,
          pageSize,
          totalPages: Math.ceil(threads.length / pageSize),
          totalItems: threads.length,
          hasNext: start + pageSize < threads.length,
          hasPrevious: page > 1
        },
        summary: {
          totalUnread: threads.filter(t => t.status.isUnread).length,
          totalImportant: threads.filter(t => t.status.isImportant).length,
          categories: {}
        },
        accountBreakdown
      })
    )
  }),

  // Search across accounts
  http.post('/api/unified/search', async (req, res, ctx) => {
    const { query, accountIds, options = {} } = await req.json()
    
    const targetAccounts = accountIds || mockAccounts.filter(acc => acc.status === 'active').map(acc => acc.id)
    
    const results = targetAccounts.map(accountId => {
      const account = mockAccounts.find(acc => acc.id === accountId)
      if (!account) return null
      
      const threads = createUnifiedThreads([accountId])
      const matchingThreads = threads.filter(thread =>
        thread.subject.toLowerCase().includes(query.toLowerCase()) ||
        thread.snippet.toLowerCase().includes(query.toLowerCase())
      ).slice(0, options.maxResults || 10)
      
      return {
        accountId,
        accountName: account.name,
        accountColor: account.color,
        threads: matchingThreads,
        totalMatches: matchingThreads.length,
        searchDuration: Math.floor(Math.random() * 500) + 100
      }
    }).filter(Boolean)
    
    return res(
      ctx.status(200),
      ctx.json(results)
    )
  }),

  // Move threads between accounts
  http.post('/api/accounts/move', async (req, res, ctx) => {
    const { threadIds, fromAccountId, toAccountId } = await req.json()
    
    // Simulate operation
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    return res(
      ctx.status(200),
      ctx.json({
        operationId: `move-${Date.now()}`,
        status: 'completed',
        affectedCount: threadIds.length,
        successCount: threadIds.length,
        failedCount: 0
      })
    )
  }),

  // Copy threads between accounts
  http.post('/api/accounts/copy', async (req, res, ctx) => {
    const { threadIds, fromAccountId, toAccountId } = await req.json()
    
    // Simulate operation
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    return res(
      ctx.status(200),
      ctx.json({
        operationId: `copy-${Date.now()}`,
        status: 'completed',
        affectedCount: threadIds.length,
        successCount: threadIds.length,
        failedCount: 0
      })
    )
  }),

  // Account analytics
  http.get('/api/accounts/:accountId/analytics', (req, res, ctx) => {
    const { accountId } = req.params
    const url = new URL(req.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    const account = mockAccounts.find(acc => acc.id === accountId)
    if (!account) {
      return res(
        ctx.status(404),
        ctx.json({ error: 'Account not found' })
      )
    }
    
    const analytics: AccountAnalytics = {
      accountId,
      period: {
        start: start ? new Date(start) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: end ? new Date(end) : new Date()
      },
      emailMetrics: {
        sent: account.stats.emailsSent,
        received: account.stats.emailsReceived,
        replied: Math.floor(account.stats.emailsSent * 0.6),
        forwarded: Math.floor(account.stats.emailsSent * 0.1),
        deleted: Math.floor(account.stats.emailsReceived * 0.2),
        archived: Math.floor(account.stats.emailsReceived * 0.3)
      },
      activityByHour: Object.fromEntries(
        Array.from({ length: 24 }, (_, i) => [
          i.toString(),
          Math.floor(Math.random() * 20)
        ])
      ),
      activityByDay: Object.fromEntries(
        Array.from({ length: 7 }, (_, i) => [
          ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][i],
          Math.floor(Math.random() * 50) + 10
        ])
      ),
      activityByWeek: Object.fromEntries(
        Array.from({ length: 4 }, (_, i) => [
          `Week ${i + 1}`,
          Math.floor(Math.random() * 200) + 50
        ])
      ),
      topSenders: [
        { email: '<EMAIL>', name: 'Boss', count: 45, percentage: 15.2 },
        { email: '<EMAIL>', name: 'Important Client', count: 32, percentage: 10.8 },
        { email: '<EMAIL>', count: 28, percentage: 9.5 }
      ],
      topRecipients: [
        { email: '<EMAIL>', name: 'Team', count: 67, percentage: 22.6 },
        { email: '<EMAIL>', count: 23, percentage: 7.8 },
        { email: '<EMAIL>', name: 'Important Client', count: 19, percentage: 6.4 }
      ],
      categories: {
        Work: 60,
        Personal: 25,
        Newsletter: 15
      },
      averageResponseTime: 3.2, // hours
      responseRate: 0.85,
      syncPerformance: {
        averageSyncTime: account.stats.averageSyncDuration,
        syncSuccess: account.stats.apiCalls - account.stats.syncErrors,
        syncFailures: account.stats.syncErrors,
        uptime: 98.5
      }
    }
    
    return res(
      ctx.status(200),
      ctx.json(analytics)
    )
  }),

  // Multi-account settings
  http.get('/api/multi-account/settings', (req, res, ctx) => {
    return res(
      ctx.status(200),
      ctx.json(mockSettings)
    )
  }),

  http.patch('/api/multi-account/settings', async (req, res, ctx) => {
    const updates = await req.json()
    mockSettings = { ...mockSettings, ...updates }
    
    return res(
      ctx.status(200),
      ctx.json(mockSettings)
    )
  }),

  // Account backup
  http.post('/api/accounts/:accountId/backup', async (req, res, ctx) => {
    const { accountId } = req.params
    const config = await req.json()
    
    // Simulate backup creation
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    return res(
      ctx.status(200),
      ctx.json({
        backupId: `backup-${accountId}-${Date.now()}`,
        size: Math.floor(Math.random() * *********0) + *********,
        status: 'completed'
      })
    )
  }),

  // Account migration
  http.post('/api/accounts/migrate', async (req, res, ctx) => {
    const migration = await req.json()
    
    // Simulate migration start
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    return res(
      ctx.status(200),
      ctx.json({
        migrationId: `migration-${Date.now()}`,
        status: 'pending'
      })
    )
  })
]