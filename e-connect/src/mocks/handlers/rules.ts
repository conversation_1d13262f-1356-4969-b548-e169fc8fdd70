import { http, HttpResponse } from 'msw';
import { Rule, RuleExecution, RuleTemplate, RuleGroup, RuleTesting } from '@/types/rules';
import { 
  mockRules, 
  mockRuleExecutions, 
  mockRuleTemplates, 
  mockRuleGroups 
} from '../data/rules';

// In-memory storage for mock data
let rules: Rule[] = [...mockRules];
let executions: RuleExecution[] = [...mockRuleExecutions];
let templates: RuleTemplate[] = [...mockRuleTemplates];
let groups: RuleGroup[] = [...mockRuleGroups];

// Helper function to generate IDs
const generateId = () => Math.random().toString(36).substr(2, 9);

// Helper function to simulate network delay
const delay = (ms: number = 300) => new Promise(resolve => setTimeout(resolve, ms));

export const rulesHandlers = [
  // Get all rules
  http.get('/api/rules', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const enabled = url.searchParams.get('enabled');
    const tags = url.searchParams.get('tags')?.split(',');
    const search = url.searchParams.get('search');
    
    let filteredRules = [...rules];
    
    if (enabled !== null) {
      filteredRules = filteredRules.filter(rule => 
        rule.enabled === (enabled === 'true')
      );
    }
    
    if (tags && tags.length > 0) {
      filteredRules = filteredRules.filter(rule => 
        rule.tags?.some(tag => tags.includes(tag))
      );
    }
    
    if (search) {
      const query = search.toLowerCase();
      filteredRules = filteredRules.filter(rule => 
        rule.name.toLowerCase().includes(query) ||
        rule.description?.toLowerCase().includes(query) ||
        rule.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    }
    
    return HttpResponse.json(filteredRules);
  }),

  // Get single rule
  http.get('/api/rules/:id', async ({ params }) => {
    await delay();
    
    const rule = rules.find(r => r.id === params.id);
    if (!rule) {
      return new HttpResponse('Rule not found', { status: 404 });
    }
    
    return HttpResponse.json(rule);
  }),

  // Create rule
  http.post('/api/rules', async ({ request }) => {
    await delay();
    
    const ruleData = await request.json() as Omit<Rule, 'id' | 'createdAt' | 'updatedAt'>;
    
    const newRule: Rule = {
      ...ruleData,
      id: `rule-${generateId()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      stats: {
        totalMatches: 0,
        totalActions: 0,
        successfulActions: 0,
        failedActions: 0,
        avgProcessingTime: 0,
        matchesByDay: {},
        actionsByType: {
          archive: 0, delete: 0, markRead: 0, markUnread: 0, star: 0, unstar: 0,
          label: 0, removeLabel: 0, categorize: 0, forward: 0, reply: 0, snooze: 0,
          moveToFolder: 0, markImportant: 0, markNotImportant: 0, addToList: 0,
          createTask: 0, webhook: 0, notification: 0, aiProcess: 0, custom: 0
        },
        errorRate: 0,
        performance: {
          conditionEvalTime: 0,
          actionExecutionTime: 0,
          totalTime: 0
        }
      }
    };
    
    rules.push(newRule);
    return HttpResponse.json(newRule, { status: 201 });
  }),

  // Update rule
  http.put('/api/rules/:id', async ({ params, request }) => {
    await delay();
    
    const ruleIndex = rules.findIndex(r => r.id === params.id);
    if (ruleIndex === -1) {
      return new HttpResponse('Rule not found', { status: 404 });
    }
    
    const updates = await request.json() as Partial<Rule>;
    rules[ruleIndex] = {
      ...rules[ruleIndex],
      ...updates,
      updatedAt: new Date()
    };
    
    return HttpResponse.json(rules[ruleIndex]);
  }),

  // Delete rule
  http.delete('/api/rules/:id', async ({ params }) => {
    await delay();
    
    const ruleIndex = rules.findIndex(r => r.id === params.id);
    if (ruleIndex === -1) {
      return new HttpResponse('Rule not found', { status: 404 });
    }
    
    rules.splice(ruleIndex, 1);
    
    // Also remove from groups
    groups.forEach(group => {
      group.rules = group.rules.filter(ruleId => ruleId !== params.id);
    });
    
    return new HttpResponse(null, { status: 204 });
  }),

  // Test rule
  http.post('/api/rules/:id/test', async ({ params, request }) => {
    await delay(1000); // Simulate longer processing for testing
    
    const rule = rules.find(r => r.id === params.id);
    if (!rule) {
      return new HttpResponse('Rule not found', { status: 404 });
    }
    
    const { testCases } = await request.json() as { testCases: any[] };
    
    // Simulate test results
    const results = testCases.map((testCase, index) => {
      const shouldMatch = Math.random() > 0.3; // 70% match rate
      const executionTime = Math.random() * 500 + 50; // 50-550ms
      
      return {
        testCaseId: testCase.id || `test-${index}`,
        passed: shouldMatch === testCase.expectedMatch,
        matched: shouldMatch,
        executedActions: shouldMatch ? rule.actions.map(a => a.type) : [],
        executionTime,
        error: Math.random() > 0.95 ? 'Simulated error' : undefined
      };
    });
    
    return HttpResponse.json({
      ruleId: params.id,
      testCases,
      results,
      summary: {
        totalTests: testCases.length,
        passed: results.filter(r => r.passed).length,
        failed: results.filter(r => !r.passed).length,
        avgExecutionTime: results.reduce((sum, r) => sum + r.executionTime, 0) / results.length
      }
    });
  }),

  // Batch test rules
  http.post('/api/rules/batch-test', async ({ request }) => {
    await delay(2000); // Simulate longer processing for batch testing
    
    const { ruleIds, emailSample } = await request.json() as { 
      ruleIds: string[];
      emailSample: any[];
    };
    
    const testRules = rules.filter(r => ruleIds.includes(r.id));
    
    const results = testRules.map(rule => {
      const emailResults = emailSample.map((email, index) => {
        const shouldMatch = Math.random() > 0.4; // 60% match rate
        const executionTime = Math.random() * 300 + 25;
        
        return {
          emailId: email.id || `email-${index}`,
          matched: shouldMatch,
          executedActions: shouldMatch ? rule.actions.map(a => a.type) : [],
          executionTime,
          conditions: rule.conditions.map(condition => ({
            conditionId: condition.id,
            matched: shouldMatch,
            evaluationTime: Math.random() * 50 + 5
          }))
        };
      });
      
      return {
        ruleId: rule.id,
        ruleName: rule.name,
        emailResults,
        summary: {
          totalEmails: emailSample.length,
          matched: emailResults.filter(r => r.matched).length,
          avgExecutionTime: emailResults.reduce((sum, r) => sum + r.executionTime, 0) / emailResults.length
        }
      };
    });
    
    return HttpResponse.json({
      results,
      summary: {
        totalRules: testRules.length,
        totalEmails: emailSample.length,
        overallMatchRate: results.reduce((sum, r) => sum + r.summary.matched, 0) / (testRules.length * emailSample.length),
        totalExecutionTime: results.reduce((sum, r) => 
          sum + r.emailResults.reduce((rSum, e) => rSum + e.executionTime, 0), 0
        )
      }
    });
  }),

  // Get rule execution stats
  http.get('/api/rules/:id/stats', async ({ params, request }) => {
    await delay();
    
    const url = new URL(request.url);
    const days = parseInt(url.searchParams.get('days') || '30');
    
    const rule = rules.find(r => r.id === params.id);
    if (!rule) {
      return new HttpResponse('Rule not found', { status: 404 });
    }
    
    // Generate mock stats for the requested period
    const stats = {
      ...rule.stats,
      period: {
        days,
        startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
        endDate: new Date()
      },
      trends: {
        matchesChange: Math.random() * 20 - 10, // -10% to +10%
        errorRateChange: Math.random() * 5 - 2.5,
        performanceChange: Math.random() * 30 - 15
      }
    };
    
    return HttpResponse.json(stats);
  }),

  // Get rule executions
  http.get('/api/rules/:id/executions', async ({ params, request }) => {
    await delay();
    
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    
    const ruleExecutions = executions
      .filter(exec => exec.ruleId === params.id)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(offset, offset + limit);
    
    return HttpResponse.json({
      executions: ruleExecutions,
      total: executions.filter(exec => exec.ruleId === params.id).length,
      limit,
      offset
    });
  }),

  // Get all executions
  http.get('/api/executions', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '100');
    const offset = parseInt(url.searchParams.get('offset') || '0');
    
    const sortedExecutions = executions
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(offset, offset + limit);
    
    return HttpResponse.json({
      executions: sortedExecutions,
      total: executions.length,
      limit,
      offset
    });
  }),

  // Rollback execution
  http.post('/api/executions/:id/rollback', async ({ params }) => {
    await delay(800);
    
    const execution = executions.find(exec => exec.id === params.id);
    if (!execution) {
      return new HttpResponse('Execution not found', { status: 404 });
    }
    
    // Simulate rollback (in real implementation, this would reverse the actions)
    const rollbackResult = {
      executionId: params.id,
      success: Math.random() > 0.1, // 90% success rate
      actionsReversed: execution.actions.filter(a => a.executed && a.success).length,
      timestamp: new Date()
    };
    
    return HttpResponse.json(rollbackResult);
  }),

  // Get rule templates
  http.get('/api/rule-templates', async () => {
    await delay();
    return HttpResponse.json(templates);
  }),

  // Create rule template
  http.post('/api/rule-templates', async ({ request }) => {
    await delay();
    
    const templateData = await request.json() as Omit<RuleTemplate, 'id'>;
    
    const newTemplate: RuleTemplate = {
      ...templateData,
      id: `template-${generateId()}`,
      popularity: 0
    };
    
    templates.push(newTemplate);
    return HttpResponse.json(newTemplate, { status: 201 });
  }),

  // Get rule groups
  http.get('/api/rule-groups', async () => {
    await delay();
    return HttpResponse.json(groups);
  }),

  // Create rule group
  http.post('/api/rule-groups', async ({ request }) => {
    await delay();
    
    const groupData = await request.json() as Omit<RuleGroup, 'id'>;
    
    const newGroup: RuleGroup = {
      ...groupData,
      id: `group-${generateId()}`
    };
    
    groups.push(newGroup);
    return HttpResponse.json(newGroup, { status: 201 });
  }),

  // Update rule group
  http.put('/api/rule-groups/:id', async ({ params, request }) => {
    await delay();
    
    const groupIndex = groups.findIndex(g => g.id === params.id);
    if (groupIndex === -1) {
      return new HttpResponse('Group not found', { status: 404 });
    }
    
    const updates = await request.json() as Partial<RuleGroup>;
    groups[groupIndex] = {
      ...groups[groupIndex],
      ...updates
    };
    
    return HttpResponse.json(groups[groupIndex]);
  }),

  // Delete rule group
  http.delete('/api/rule-groups/:id', async ({ params }) => {
    await delay();
    
    const groupIndex = groups.findIndex(g => g.id === params.id);
    if (groupIndex === -1) {
      return new HttpResponse('Group not found', { status: 404 });
    }
    
    groups.splice(groupIndex, 1);
    return new HttpResponse(null, { status: 204 });
  }),

  // Bulk operations
  http.post('/api/rules/bulk', async ({ request }) => {
    await delay();
    
    const { operation, ruleIds, data } = await request.json() as {
      operation: 'enable' | 'disable' | 'delete' | 'updatePriority';
      ruleIds: string[];
      data?: any;
    };
    
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };
    
    for (const ruleId of ruleIds) {
      const ruleIndex = rules.findIndex(r => r.id === ruleId);
      
      if (ruleIndex === -1) {
        results.failed++;
        results.errors.push(`Rule ${ruleId} not found`);
        continue;
      }
      
      try {
        switch (operation) {
          case 'enable':
            rules[ruleIndex].enabled = true;
            break;
          case 'disable':
            rules[ruleIndex].enabled = false;
            break;
          case 'delete':
            rules.splice(ruleIndex, 1);
            break;
          case 'updatePriority':
            rules[ruleIndex].priority = data.priority;
            break;
        }
        
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`Failed to ${operation} rule ${ruleId}: ${error}`);
      }
    }
    
    return HttpResponse.json(results);
  }),

  // Rule conflict detection
  http.get('/api/rules/conflicts', async () => {
    await delay();
    
    const enabledRules = rules.filter(rule => rule.enabled);
    const conflicts = [];
    
    // Check for priority conflicts
    const priorityGroups = enabledRules.reduce((groups, rule) => {
      if (!groups[rule.priority]) groups[rule.priority] = [];
      groups[rule.priority].push(rule);
      return groups;
    }, {} as Record<number, Rule[]>);
    
    for (const [priority, rulesInPriority] of Object.entries(priorityGroups)) {
      if (rulesInPriority.length > 1) {
        for (let i = 0; i < rulesInPriority.length; i++) {
          for (let j = i + 1; j < rulesInPriority.length; j++) {
            conflicts.push({
              type: 'priority',
              severity: 'warning',
              ruleId1: rulesInPriority[i].id,
              ruleName1: rulesInPriority[i].name,
              ruleId2: rulesInPriority[j].id,
              ruleName2: rulesInPriority[j].name,
              description: `Rules have the same priority (${priority})`,
              recommendation: 'Consider adjusting priorities to ensure deterministic execution order'
            });
          }
        }
      }
    }
    
    // Check for potential action conflicts
    for (let i = 0; i < enabledRules.length; i++) {
      for (let j = i + 1; j < enabledRules.length; j++) {
        const rule1 = enabledRules[i];
        const rule2 = enabledRules[j];
        
        // Check if rules have conflicting actions (e.g., one archives, another deletes)
        const hasArchive1 = rule1.actions.some(a => a.type === 'archive');
        const hasDelete2 = rule2.actions.some(a => a.type === 'delete');
        const hasArchive2 = rule2.actions.some(a => a.type === 'archive');
        const hasDelete1 = rule1.actions.some(a => a.type === 'delete');
        
        if ((hasArchive1 && hasDelete2) || (hasArchive2 && hasDelete1)) {
          conflicts.push({
            type: 'action',
            severity: 'error',
            ruleId1: rule1.id,
            ruleName1: rule1.name,
            ruleId2: rule2.id,
            ruleName2: rule2.name,
            description: 'Conflicting actions: one rule archives while another deletes',
            recommendation: 'Review rule priorities and conditions to prevent conflicts'
          });
        }
      }
    }
    
    return HttpResponse.json(conflicts);
  }),

  // Performance analytics
  http.get('/api/rules/analytics', async ({ request }) => {
    await delay();
    
    const url = new URL(request.url);
    const period = url.searchParams.get('period') || '7d';
    
    // Generate mock analytics data
    const analytics = {
      period,
      totalRules: rules.length,
      activeRules: rules.filter(r => r.enabled).length,
      totalExecutions: executions.length,
      successRate: executions.length > 0 
        ? executions.filter(e => e.success).length / executions.length 
        : 0,
      avgExecutionTime: executions.length > 0
        ? executions.reduce((sum, e) => sum + e.totalTime, 0) / executions.length
        : 0,
      topPerformingRules: rules
        .filter(r => r.stats)
        .sort((a, b) => (b.stats!.successfulActions / Math.max(b.stats!.totalActions, 1)) - 
                       (a.stats!.successfulActions / Math.max(a.stats!.totalActions, 1)))
        .slice(0, 5)
        .map(r => ({
          id: r.id,
          name: r.name,
          successRate: r.stats!.successfulActions / Math.max(r.stats!.totalActions, 1),
          totalActions: r.stats!.totalActions
        })),
      executionTrends: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        executions: Math.floor(Math.random() * 100) + 20,
        errors: Math.floor(Math.random() * 5)
      }))
    };
    
    return HttpResponse.json(analytics);
  })
];

export default rulesHandlers;