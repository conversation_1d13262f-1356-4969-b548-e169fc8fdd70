import { http, HttpResponse, delay } from 'msw'
import type { 
  NewsletterSender, 
  BulkUnsubscribeOperation, 
  WhitelistEntry,
  UnsubscribeDetection 
} from '../../types/bulk'
import { mockNewsletterSenders, mockWhitelist } from '../data/senders'

// Simulate unsubscribe detection
function detectUnsubscribeMethod(senderEmail: string): UnsubscribeDetection {
  const random = Math.random()
  
  if (random < 0.7) {
    // 70% have unsubscribe links
    return {
      method: 'link',
      confidence: 95,
      details: {
        headerUnsubscribe: `<https://unsubscribe.${senderEmail.split('@')[1]}/unsub?token=abc123>`,
        linkUrls: [
          `https://unsubscribe.${senderEmail.split('@')[1]}/unsub?token=abc123`,
          `https://${senderEmail.split('@')[1]}/preferences/unsubscribe`
        ]
      },
      validation: {
        isValid: true,
        lastChecked: new Date()
      }
    }
  } else if (random < 0.85) {
    // 15% have email-based unsubscribe
    return {
      method: 'email',
      confidence: 90,
      details: {
        emailAddresses: [`unsubscribe@${senderEmail.split('@')[1]}`, '<EMAIL>'],
        manualInstructions: 'Reply with "UNSUBSCRIBE" in subject line'
      },
      validation: {
        isValid: true,
        lastChecked: new Date()
      }
    }
  } else if (random < 0.95) {
    // 10% require manual action
    return {
      method: 'manual',
      confidence: 80,
      details: {
        manualInstructions: 'Log into your account and update email preferences'
      },
      validation: {
        isValid: false,
        errorMessage: 'Manual intervention required'
      }
    }
  } else {
    // 5% have no unsubscribe method
    return {
      method: 'none',
      confidence: 100,
      details: {},
      validation: {
        isValid: false,
        errorMessage: 'No unsubscribe method found'
      }
    }
  }
}

// Simulate unsubscribe processing
async function processUnsubscribe(senderId: string, method: string): Promise<{ success: boolean; error?: string }> {
  await delay(Math.random() * 2000 + 500) // 0.5-2.5s delay
  
  const random = Math.random()
  
  if (method === 'link' && random < 0.9) {
    // 90% success rate for link-based unsubscribe
    return { success: true }
  } else if (method === 'email' && random < 0.8) {
    // 80% success rate for email-based
    return { success: true }
  } else if (method === 'manual') {
    // Manual always requires user action
    return { success: false, error: 'Manual action required' }
  } else if (random < 0.1) {
    // 10% random failure
    const errors = [
      'Network timeout',
      'Invalid unsubscribe token',
      'Server error (500)',
      'Rate limit exceeded',
      'Authentication failed'
    ]
    return { success: false, error: errors[Math.floor(Math.random() * errors.length)] }
  }
  
  return { success: true }
}

export const bulkHandlers = [
  // Get newsletter senders
  http.get('/api/bulk/newsletter-senders', ({ request }) => {
    const url = new URL(request.url)
    const category = url.searchParams.get('category')
    const sort = url.searchParams.get('sort') || 'volume'
    const order = url.searchParams.get('order') || 'desc'
    const search = url.searchParams.get('search')
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '50')
    
    let senders = [...mockNewsletterSenders]
    
    // Filter by category
    if (category && category !== 'all') {
      senders = senders.filter(s => s.category === category)
    }
    
    // Search
    if (search) {
      const searchLower = search.toLowerCase()
      senders = senders.filter(s => 
        s.email.toLowerCase().includes(searchLower) ||
        s.name.toLowerCase().includes(searchLower) ||
        s.domain.toLowerCase().includes(searchLower)
      )
    }
    
    // Sort
    senders.sort((a, b) => {
      let compareValue = 0
      
      switch (sort) {
        case 'volume':
          compareValue = a.volume.total - b.volume.total
          break
        case 'frequency':
          const freqOrder = ['daily', 'weekly', 'biweekly', 'monthly', 'occasional', 'unknown']
          compareValue = freqOrder.indexOf(a.frequency) - freqOrder.indexOf(b.frequency)
          break
        case 'lastReceived':
          compareValue = a.lastReceived.getTime() - b.lastReceived.getTime()
          break
        case 'reputation':
          compareValue = a.reputation.score - b.reputation.score
          break
        case 'name':
          compareValue = a.name.localeCompare(b.name)
          break
      }
      
      return order === 'desc' ? -compareValue : compareValue
    })
    
    // Paginate
    const start = (page - 1) * limit
    const paginatedSenders = senders.slice(start, start + limit)
    
    return HttpResponse.json({
      senders: paginatedSenders,
      pagination: {
        total: senders.length,
        page,
        limit,
        pages: Math.ceil(senders.length / limit)
      },
      stats: {
        totalSenders: senders.length,
        byCategory: senders.reduce((acc, s) => {
          acc[s.category] = (acc[s.category] || 0) + 1
          return acc
        }, {} as Record<string, number>),
        totalEmails: senders.reduce((sum, s) => sum + s.volume.total, 0),
        averageFrequency: {
          daily: senders.filter(s => s.frequency === 'daily').length,
          weekly: senders.filter(s => s.frequency === 'weekly').length,
          monthly: senders.filter(s => s.frequency === 'monthly').length
        }
      }
    })
  }),

  // Get single sender details
  http.get('/api/bulk/newsletter-senders/:senderId', ({ params }) => {
    const sender = mockNewsletterSenders.find(s => s.id === params.senderId)
    
    if (!sender) {
      return new HttpResponse(null, { status: 404 })
    }
    
    // Add unsubscribe detection
    const detection = detectUnsubscribeMethod(sender.email)
    
    return HttpResponse.json({
      ...sender,
      unsubscribeDetection: detection
    })
  }),

  // Detect unsubscribe methods for multiple senders
  http.post('/api/bulk/detect-unsubscribe', async ({ request }) => {
    const { senderIds } = await request.json() as { senderIds: string[] }
    
    const results = senderIds.map(senderId => {
      const sender = mockNewsletterSenders.find(s => s.id === senderId)
      if (!sender) return null
      
      const detection = detectUnsubscribeMethod(sender.email)
      return {
        senderId,
        senderEmail: sender.email,
        detection
      }
    }).filter(Boolean)
    
    return HttpResponse.json({ results })
  }),

  // Create bulk unsubscribe operation
  http.post('/api/bulk/unsubscribe', async ({ request }) => {
    const { 
      senderIds, 
      schedule,
      preview = false 
    } = await request.json() as {
      senderIds: string[]
      schedule?: { scheduledFor: Date; recurring?: boolean; frequency?: string }
      preview?: boolean
    }
    
    const selectedSenders = mockNewsletterSenders.filter(s => senderIds.includes(s.id))
    const totalEmails = selectedSenders.reduce((sum, s) => sum + s.volume.total, 0)
    
    const operation: BulkUnsubscribeOperation = {
      id: `unsubscribe-${Date.now()}`,
      status: preview ? 'preview' : 'processing',
      selection: {
        senderIds,
        totalSenders: selectedSenders.length,
        totalEmails,
        method: 'auto'
      },
      schedule,
      progress: {
        current: 0,
        total: selectedSenders.length,
        successful: 0,
        failed: 0,
        manual: 0,
        percentage: 0
      },
      results: {
        successful: [],
        failed: [],
        manual: []
      },
      analytics: {
        emailsFreed: 0,
        storageReclaimed: 0,
        timeEstimatedSaved: 0
      },
      createdAt: new Date(),
      backup: {
        id: `backup-${Date.now()}`,
        createdAt: new Date(),
        restorable: true
      }
    }
    
    if (preview) {
      // Return preview data
      return HttpResponse.json({
        operation,
        preview: {
          senders: selectedSenders.map(s => ({
            id: s.id,
            email: s.email,
            name: s.name,
            volume: s.volume.total,
            method: detectUnsubscribeMethod(s.email).method
          })),
          estimatedTime: Math.ceil(selectedSenders.length * 1.5), // 1.5s per sender
          potentialSavings: {
            emails: totalEmails,
            storage: totalEmails * 50 * 1024, // 50KB per email average
            timePerMonth: totalEmails * 0.1 // 0.1 hours per 100 emails
          }
        }
      })
    }
    
    // Start processing in background
    return HttpResponse.json({ operationId: operation.id })
  }),

  // Get operation status
  http.get('/api/bulk/unsubscribe/:operationId', ({ params }) => {
    // Simulate operation progress
    const operation: BulkUnsubscribeOperation = {
      id: params.operationId as string,
      status: 'processing',
      selection: {
        senderIds: mockNewsletterSenders.slice(0, 10).map(s => s.id),
        totalSenders: 10,
        totalEmails: 1250,
        method: 'auto'
      },
      progress: {
        current: 7,
        total: 10,
        successful: 5,
        failed: 1,
        manual: 1,
        percentage: 70,
        currentSender: '<EMAIL>',
        estimatedTime: 4.5
      },
      results: {
        successful: [
          {
            senderId: 'sender-1',
            senderEmail: '<EMAIL>',
            method: 'link',
            timestamp: new Date()
          }
        ],
        failed: [
          {
            senderId: 'sender-2',
            senderEmail: '<EMAIL>',
            error: 'Network timeout',
            retryable: true,
            timestamp: new Date()
          }
        ],
        manual: [
          {
            senderId: 'sender-3',
            senderEmail: '<EMAIL>',
            instructions: 'Log into LinkedIn and update email preferences',
            timestamp: new Date()
          }
        ]
      },
      analytics: {
        emailsFreed: 875,
        storageReclaimed: 43750000, // ~43MB
        timeEstimatedSaved: 8.75
      },
      createdAt: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      startedAt: new Date(Date.now() - 4 * 60 * 1000),
      backup: {
        id: 'backup-123',
        createdAt: new Date(Date.now() - 5 * 60 * 1000),
        restorable: true
      }
    }
    
    return HttpResponse.json(operation)
  }),

  // Process individual unsubscribe
  http.post('/api/bulk/unsubscribe/:operationId/process/:senderId', async ({ params }) => {
    const sender = mockNewsletterSenders.find(s => s.id === params.senderId)
    
    if (!sender) {
      return new HttpResponse(null, { status: 404 })
    }
    
    const detection = detectUnsubscribeMethod(sender.email)
    const result = await processUnsubscribe(sender.id, detection.method)
    
    return HttpResponse.json({
      senderId: sender.id,
      senderEmail: sender.email,
      method: detection.method,
      ...result
    })
  }),

  // Cancel operation
  http.post('/api/bulk/unsubscribe/:operationId/cancel', () => {
    return HttpResponse.json({ 
      success: true,
      message: 'Operation cancelled'
    })
  }),

  // Restore from backup
  http.post('/api/bulk/unsubscribe/:operationId/restore', async () => {
    await delay(2000)
    
    return HttpResponse.json({
      success: true,
      message: 'Successfully restored from backup',
      restoredCount: 5
    })
  }),

  // Whitelist management
  http.get('/api/bulk/whitelist', () => {
    return HttpResponse.json({
      entries: mockWhitelist,
      stats: {
        total: mockWhitelist.length,
        byType: {
          email: mockWhitelist.filter(w => w.type === 'email').length,
          domain: mockWhitelist.filter(w => w.type === 'domain').length,
          pattern: mockWhitelist.filter(w => w.type === 'pattern').length
        },
        vipCount: mockWhitelist.filter(w => w.isVIP).length
      }
    })
  }),

  // Add to whitelist
  http.post('/api/bulk/whitelist', async ({ request }) => {
    const entry = await request.json() as Omit<WhitelistEntry, 'id' | 'addedAt'>
    
    const newEntry: WhitelistEntry = {
      ...entry,
      id: `whitelist-${Date.now()}`,
      addedAt: new Date()
    }
    
    mockWhitelist.push(newEntry)
    
    return HttpResponse.json(newEntry, { status: 201 })
  }),

  // Remove from whitelist
  http.delete('/api/bulk/whitelist/:entryId', ({ params }) => {
    const index = mockWhitelist.findIndex(w => w.id === params.entryId)
    
    if (index === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    
    mockWhitelist.splice(index, 1)
    
    return HttpResponse.json({ success: true })
  }),

  // Import whitelist
  http.post('/api/bulk/whitelist/import', async ({ request }) => {
    const { format, data } = await request.json() as { format: 'csv' | 'json'; data: string }
    
    // Simulate import processing
    await delay(1000)
    
    return HttpResponse.json({
      success: true,
      imported: 12,
      skipped: 3,
      errors: []
    })
  }),

  // Export whitelist
  http.get('/api/bulk/whitelist/export', ({ request }) => {
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'json'
    
    if (format === 'csv') {
      const csv = 'type,value,reason,isVIP\n' + 
        mockWhitelist.map(w => `${w.type},${w.value},${w.reason || ''},${w.isVIP || false}`).join('\n')
      
      return new HttpResponse(csv, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': 'attachment; filename=whitelist.csv'
        }
      })
    }
    
    return HttpResponse.json(mockWhitelist)
  }),

  // Get unsubscribe analytics
  http.get('/api/bulk/analytics/unsubscribe', () => {
    return HttpResponse.json({
      overview: {
        totalUnsubscribed: 156,
        lastMonth: 42,
        lastWeek: 8,
        successRate: 87.5,
        averageTimePerUnsubscribe: 1.8 // seconds
      },
      byMethod: {
        link: { count: 109, successRate: 92 },
        email: { count: 31, successRate: 84 },
        manual: { count: 16, successRate: 0 },
        blocked: { count: 0, successRate: 100 }
      },
      byCategory: {
        newsletter: 89,
        marketing: 42,
        promotion: 18,
        notification: 7
      },
      topUnsubscribed: mockNewsletterSenders
        .sort((a, b) => b.statistics.unsubscribeRate - a.statistics.unsubscribeRate)
        .slice(0, 5)
        .map(s => ({
          email: s.email,
          name: s.name,
          unsubscribeRate: s.statistics.unsubscribeRate
        })),
      savings: {
        emailsFreed: 12450,
        storageReclaimed: 622500000, // ~622MB
        timeEstimatedSaved: 124.5, // hours per month
        costSaved: 15.50 // estimated
      }
    })
  }),

  // Clean Inbox handlers
  
  // Get cleaning analytics
  http.get('/api/cleaning/analytics', async () => {
    await delay(500)
    
    return HttpResponse.json({
      totalEmails: 24567,
      totalSize: 13245678900, // ~13.2 GB
      categoriesFound: 12,
      potentialSavings: {
        emails: 8234,
        storage: **********, // ~4.5 GB
        percentage: 34
      },
      topSenders: [
        { email: '<EMAIL>', name: 'Medium Daily Digest', count: 1234, size: 234567890 },
        { email: '<EMAIL>', name: 'LinkedIn', count: 987, size: 187654321 },
        { email: '<EMAIL>', name: 'Amazon', count: 765, size: 145678901 },
        { email: '<EMAIL>', name: 'GitHub', count: 543, size: 103456789 },
        { email: '<EMAIL>', name: 'Product Hunt', count: 432, size: 82345678 }
      ],
      emailsByAge: [
        { label: '< 7 days', count: 2345, size: 1234567890 },
        { label: '7-30 days', count: 4567, size: 2345678901 },
        { label: '30-90 days', count: 6789, size: 3456789012 },
        { label: '90-365 days', count: 8901, size: 4567890123 },
        { label: '> 365 days', count: 1965, size: 1234567890 }
      ],
      growthRate: {
        daily: 45,
        weekly: 315,
        monthly: 1350
      }
    })
  }),

  // Start cleaning session
  http.post('/api/cleaning/start', async ({ request }) => {
    const { workflow, settings } = await request.json() as any
    
    const sessionId = `clean-${Date.now()}`
    
    return HttpResponse.json({
      id: sessionId,
      startedAt: new Date(),
      workflow,
      status: 'analyzing',
      categories: [],
      selectedCategories: new Set(),
      selectedEmails: new Set(),
      progress: {
        current: 0,
        total: 24567,
        percentage: 0
      }
    })
  }),

  // Analyze inbox
  http.post('/api/cleaning/:sessionId/analyze', async ({ params }) => {
    await delay(3000) // Simulate analysis time
    
    const categories = [
      {
        id: 'marketing',
        name: 'Marketing Emails',
        description: 'Promotional emails and advertisements',
        icon: 'tag',
        count: 3456,
        sizeInBytes: 1234567890,
        suggestedAction: 'delete',
        confidence: 92,
        examples: [
          '50% off everything - Limited time offer!',
          'Flash Sale: Today Only',
          'Your exclusive discount code inside'
        ],
        criteria: {
          labels: ['promotions', 'marketing'],
          ageInDays: 30,
          senderPatterns: ['*@*.marketing', '*@campaign.*', '*@promo.*']
        }
      },
      {
        id: 'notifications',
        name: 'Old Notifications',
        description: 'System notifications and alerts older than 7 days',
        icon: 'bell',
        count: 2345,
        sizeInBytes: 567890123,
        suggestedAction: 'archive',
        confidence: 88,
        examples: [
          'You have a new follower',
          'Someone liked your post',
          'New comment on your article'
        ],
        criteria: {
          labels: ['notifications', 'social'],
          ageInDays: 7,
          senders: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      },
      {
        id: 'newsletters',
        name: 'Unread Newsletters',
        description: 'Newsletter subscriptions you haven\'t read in 30+ days',
        icon: 'newspaper',
        count: 1234,
        sizeInBytes: 987654321,
        suggestedAction: 'archive',
        confidence: 85,
        examples: [
          'Your weekly digest from Medium',
          'The Morning Brew - Today\'s top stories',
          'TechCrunch Daily Newsletter'
        ],
        criteria: {
          labels: ['newsletters'],
          ageInDays: 30,
          isUnread: true
        }
      },
      {
        id: 'receipts',
        name: 'Old Receipts',
        description: 'Purchase receipts and invoices older than 1 year',
        icon: 'receipt',
        count: 876,
        sizeInBytes: 234567890,
        suggestedAction: 'archive',
        confidence: 78,
        examples: [
          'Your Amazon order has been delivered',
          'Receipt for your purchase',
          'Invoice #12345'
        ],
        criteria: {
          labels: ['receipts', 'purchases'],
          ageInDays: 365,
          subjects: ['receipt', 'invoice', 'order', 'purchase']
        }
      },
      {
        id: 'large-attachments',
        name: 'Large Attachments',
        description: 'Emails with attachments over 10MB',
        icon: 'paperclip',
        count: 234,
        sizeInBytes: 1876543210,
        suggestedAction: 'review',
        confidence: 95,
        examples: [
          'Project files attached',
          'Photos from last week',
          'Presentation slides'
        ],
        criteria: {
          hasAttachments: true,
          sizeThreshold: 10485760 // 10MB
        }
      },
      {
        id: 'duplicates',
        name: 'Duplicate Emails',
        description: 'Multiple copies of the same email',
        icon: 'copy',
        count: 543,
        sizeInBytes: 123456789,
        suggestedAction: 'delete',
        confidence: 97,
        examples: [
          'Re: Meeting tomorrow (3 copies)',
          'Fwd: Important update (2 copies)',
          'Newsletter subscription (5 copies)'
        ],
        criteria: {
          customRules: [{
            id: 'dup-1',
            name: 'Duplicate detection',
            condition: 'and',
            filters: [],
            action: 'delete'
          }]
        }
      }
    ]
    
    return HttpResponse.json({
      id: params.sessionId,
      startedAt: new Date(),
      status: 'preview',
      categories,
      selectedCategories: new Set(),
      selectedEmails: new Set(),
      progress: {
        current: 24567,
        total: 24567,
        percentage: 100
      }
    })
  }),

  // Execute cleaning
  http.post('/api/cleaning/:sessionId/execute', async ({ params, request }) => {
    const { categories, emails, createBackup } = await request.json() as any
    
    return HttpResponse.json({
      id: params.sessionId,
      status: 'cleaning',
      backup: createBackup ? {
        id: `backup-${Date.now()}`,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        size: 234567890,
        canRestore: true
      } : undefined
    })
  }),

  // Get cleaning status
  http.get('/api/cleaning/:sessionId/status', async ({ params }) => {
    // Simulate progress
    const progress = Math.min(100, Math.random() * 20 + 80)
    
    return HttpResponse.json({
      id: params.sessionId,
      status: progress === 100 ? 'completed' : 'cleaning',
      progress: {
        current: Math.floor(progress * 245.67),
        total: 24567,
        percentage: progress,
        currentStep: progress < 100 ? 'Cleaning marketing emails...' : 'Completed'
      },
      results: progress === 100 ? {
        archived: 3456,
        deleted: 2345,
        kept: 18766,
        errors: 0,
        storageFreed: **********,
        timeElapsed: 127,
        recommendations: [
          'Set up automatic rules to archive marketing emails older than 30 days',
          'Unsubscribe from 15 newsletters you haven\'t read in the past 3 months',
          'Enable smart categorization to keep your inbox organized',
          'Schedule weekly cleaning to maintain inbox health'
        ]
      } : undefined,
      analytics: {
        totalSize: 13245678900,
        totalEmails: 24567
      }
    })
  }),

  // Restore backup
  http.post('/api/cleaning/backup/:backupId/restore', async ({ params }) => {
    await delay(2000)
    
    return HttpResponse.json({
      success: true,
      message: 'Backup restored successfully',
      restoredCount: 5801
    })
  }),

  // Get/Save workflows
  http.get('/api/cleaning/workflows', () => {
    return HttpResponse.json({
      workflows: [
        {
          id: 'custom-1',
          name: 'My Custom Workflow',
          description: 'Clean up work emails',
          steps: [],
          lastUsed: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          successRate: 94
        }
      ]
    })
  }),

  http.post('/api/cleaning/workflows', async ({ request }) => {
    const workflow = await request.json()
    
    return HttpResponse.json({
      ...workflow,
      id: `workflow-${Date.now()}`,
      createdAt: new Date()
    })
  })
]