import { http, HttpResponse } from 'msw'
import { 
  mockEmails, 
  mockThreads, 
  generateThread, 
  searchThreads, 
  getThreadStats,
  getThreadsByCategory,
  getUnreadThreads,
  getImportantThreads,
  getStarredThreads,
  getThreadsByLabel,
  simulateNewEmail,
  simulateReply
} from '../data/emails'
import type { ThreadListResponse, MessageRequest, PaginatedResponse } from '../../types/api'
import type { Thread, ParsedMessage, EmailCategory } from '../../types/email'

// Gmail-style search query parser
function parseGmailSearchQuery(query: string) {
  const operators: Record<string, string | string[]> = {}
  
  // Extract operators like "from:<EMAIL>", "has:attachment", etc.
  const operatorRegex = /(\w+):([^\s]+|"[^"]*")/g
  let match
  
  while ((match = operatorRegex.exec(query)) !== null) {
    const [fullMatch, operator, value] = match
    const cleanValue = value.replace(/"/g, '')
    
    if (operators[operator]) {
      if (Array.isArray(operators[operator])) {
        (operators[operator] as string[]).push(cleanValue)
      } else {
        operators[operator] = [operators[operator] as string, cleanValue]
      }
    } else {
      operators[operator] = cleanValue
    }
    
    query = query.replace(fullMatch, '').trim()
  }
  
  // Remaining text becomes general search
  if (query.trim()) {
    operators.q = query.trim()
  }
  
  return operators
}

// Advanced thread filtering function
function filterThreads(threads: Thread[], filters: any): Thread[] {
  let filtered = [...threads]
  
  // Text search
  if (filters.q) {
    const searchTerm = filters.q.toLowerCase()
    filtered = filtered.filter(thread => 
      thread.subject.toLowerCase().includes(searchTerm) ||
      thread.snippet.toLowerCase().includes(searchTerm) ||
      thread.participants.some(p => 
        p.email.toLowerCase().includes(searchTerm) ||
        p.name?.toLowerCase().includes(searchTerm)
      ) ||
      thread.messages.some(m => 
        m.body.text?.toLowerCase().includes(searchTerm) ||
        m.body.plain?.toLowerCase().includes(searchTerm)
      )
    )
  }
  
  // From filter
  if (filters.from) {
    const fromFilter = Array.isArray(filters.from) ? filters.from : [filters.from]
    filtered = filtered.filter(thread =>
      thread.participants.some(p => 
        fromFilter.some((f: string) => p.email.toLowerCase().includes(f.toLowerCase()))
      )
    )
  }
  
  // To filter
  if (filters.to) {
    const toFilter = Array.isArray(filters.to) ? filters.to : [filters.to]
    filtered = filtered.filter(thread =>
      thread.messages.some(m =>
        m.to.some(t => 
          toFilter.some((f: string) => t.email.toLowerCase().includes(f.toLowerCase()))
        )
      )
    )
  }
  
  // Subject filter
  if (filters.subject) {
    filtered = filtered.filter(thread =>
      thread.subject.toLowerCase().includes(filters.subject.toLowerCase())
    )
  }
  
  // Has attachments
  if (filters.has === 'attachment') {
    filtered = filtered.filter(thread =>
      thread.messages.some(m => m.attachments.length > 0)
    )
  }
  
  // Status filters
  if (filters.is) {
    const statusFilter = Array.isArray(filters.is) ? filters.is : [filters.is]
    filtered = filtered.filter(thread => {
      return statusFilter.every((status: string) => {
        switch (status) {
          case 'unread': return thread.status.isUnread
          case 'read': return !thread.status.isUnread
          case 'starred': return thread.status.isStarred
          case 'important': return thread.status.isImportant
          case 'spam': return thread.status.isSpam
          case 'trash': return thread.status.isTrash
          default: return true
        }
      })
    })
  }
  
  // Label filter
  if (filters.label) {
    const labelFilter = Array.isArray(filters.label) ? filters.label : [filters.label]
    filtered = filtered.filter(thread =>
      labelFilter.some((label: string) => 
        thread.labels.some(l => l.toLowerCase() === label.toLowerCase())
      )
    )
  }
  
  // Category filter  
  if (filters.category) {
    const categoryFilter = Array.isArray(filters.category) ? filters.category : [filters.category]
    filtered = filtered.filter(thread =>
      categoryFilter.includes(thread.category)
    )
  }
  
  // Date filters
  if (filters.after || filters.before) {
    filtered = filtered.filter(thread => {
      const threadDate = thread.lastMessageDate
      
      if (filters.after) {
        const afterDate = parseDate(filters.after)
        if (afterDate && threadDate <= afterDate) return false
      }
      
      if (filters.before) {
        const beforeDate = parseDate(filters.before)
        if (beforeDate && threadDate >= beforeDate) return false
      }
      
      return true
    })
  }
  
  return filtered
}

// Parse date strings for filtering
function parseDate(dateStr: string): Date | null {
  // Handle relative dates
  const now = new Date()
  switch (dateStr.toLowerCase()) {
    case 'today':
      return new Date(now.getFullYear(), now.getMonth(), now.getDate())
    case 'yesterday':
      return new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
    case 'week':
      return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
    case 'month':
      return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    default:
      // Try to parse as date
      const parsed = new Date(dateStr)
      return isNaN(parsed.getTime()) ? null : parsed
  }
}

// Generate pagination token
function generatePageToken(page: number, filters: any): string {
  return Buffer.from(JSON.stringify({ page, filters, timestamp: Date.now() })).toString('base64')
}

// Parse pagination token
function parsePageToken(token: string): { page: number; filters: any; timestamp: number } | null {
  try {
    return JSON.parse(Buffer.from(token, 'base64').toString())
  } catch {
    return null
  }
}

export const emailHandlers = [
  // Gmail-style Threads API - Advanced search and filtering
  http.get('/api/google/threads', ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    const labelIds = url.searchParams.getAll('labelId')
    const type = url.searchParams.get('type') // inbox, sent, draft, spam, trash
    const pageToken = url.searchParams.get('pageToken')
    const maxResults = parseInt(url.searchParams.get('maxResults') || '50')
    const includeSpamTrash = url.searchParams.get('includeSpamTrash') === 'true'
    
    // Parse search query for Gmail-style operators
    const searchFilters = parseGmailSearchQuery(query)
    
    // Add type filtering
    if (type) {
      switch (type) {
        case 'inbox':
          searchFilters.label = searchFilters.label ? [searchFilters.label, 'INBOX'].flat() : 'INBOX'
          break
        case 'sent':
          searchFilters.label = searchFilters.label ? [searchFilters.label, 'SENT'].flat() : 'SENT'
          break
        case 'draft':
          searchFilters.label = searchFilters.label ? [searchFilters.label, 'DRAFT'].flat() : 'DRAFT'
          break
        case 'spam':
          searchFilters.label = searchFilters.label ? [searchFilters.label, 'SPAM'].flat() : 'SPAM'
          break
        case 'trash':
          searchFilters.label = searchFilters.label ? [searchFilters.label, 'TRASH'].flat() : 'TRASH'
          break
      }
    }
    
    // Add label filtering
    if (labelIds.length > 0) {
      searchFilters.label = searchFilters.label ? [searchFilters.label, ...labelIds].flat() : labelIds
    }
    
    // Parse page token for pagination
    let startPage = 1
    if (pageToken) {
      const parsed = parsePageToken(pageToken)
      if (parsed) {
        startPage = parsed.page
        // Could validate timestamp for security
      }
    }
    
    // Apply filters
    let threads = filterThreads(mockThreads, searchFilters)
    
    // Filter spam/trash unless explicitly requested
    if (!includeSpamTrash && !type) {
      threads = threads.filter(t => !t.status.isSpam && !t.status.isTrash)
    }
    
    // Sort by last message date (most recent first)
    threads.sort((a, b) => b.lastMessageDate.getTime() - a.lastMessageDate.getTime())
    
    // Pagination
    const startIndex = (startPage - 1) * maxResults
    const endIndex = startIndex + maxResults
    const paginatedThreads = threads.slice(startIndex, endIndex)
    
    // Generate next page token
    const nextPageToken = endIndex < threads.length ? 
      generatePageToken(startPage + 1, searchFilters) : undefined
    
    const response = {
      threads: paginatedThreads.map(thread => ({
        id: thread.id,
        snippet: thread.snippet,
        historyId: thread.messages[0]?.historyId || `history-${thread.id}`
      })),
      nextPageToken,
      resultSizeEstimate: threads.length
    }
    
    return HttpResponse.json(response)
  }),

  // Get single thread with full details
  http.get('/api/google/threads/:threadId', ({ params, request }) => {
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'full'
    const metadataHeaders = url.searchParams.getAll('metadataHeaders')
    
    const thread = mockThreads.find(t => t.id === params.threadId)
    
    if (!thread) {
      return new HttpResponse(null, { status: 404 })
    }
    
    // Format response based on requested format
    let formattedThread
    switch (format) {
      case 'minimal':
        formattedThread = {
          id: thread.id,
          messages: thread.messages.map(m => ({
            id: m.id,
            threadId: m.threadId,
            labelIds: m.labels,
            snippet: m.snippet,
            historyId: m.historyId,
            internalDate: m.internalDate,
            sizeEstimate: m.size
          }))
        }
        break
      case 'metadata':
        formattedThread = {
          id: thread.id,
          messages: thread.messages.map(m => ({
            id: m.id,
            threadId: m.threadId,
            labelIds: m.labels,
            snippet: m.snippet,
            historyId: m.historyId,
            internalDate: m.internalDate,
            sizeEstimate: m.size,
            payload: {
              headers: metadataHeaders.length > 0 ? 
                Object.entries(m.headers)
                  .filter(([key]) => metadataHeaders.includes(key))
                  .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}) :
                m.headers
            }
          }))
        }
        break
      default: // full
        formattedThread = {
          id: thread.id,
          messages: thread.messages.map(m => ({
            id: m.id,
            threadId: m.threadId,
            labelIds: m.labels,
            snippet: m.snippet,
            historyId: m.historyId,
            internalDate: m.internalDate,
            sizeEstimate: m.size,
            payload: {
              headers: m.headers,
              body: {
                data: Buffer.from(m.body.html || m.body.text || '').toString('base64')
              },
              parts: m.attachments.map(att => ({
                partId: att.id,
                mimeType: att.mimeType,
                filename: att.filename,
                headers: {
                  'Content-Type': att.mimeType,
                  'Content-Disposition': `attachment; filename="${att.filename}"`
                },
                body: {
                  attachmentId: att.id,
                  size: att.size
                }
              }))
            }
          }))
        }
    }
    
    return HttpResponse.json(formattedThread)
  }),

  // Modify thread (add/remove labels, archive, etc.)
  http.post('/api/google/threads/:threadId/modify', async ({ params, request }) => {
    const { addLabelIds = [], removeLabelIds = [] } = await request.json() as {
      addLabelIds?: string[]
      removeLabelIds?: string[]
    }
    
    const thread = mockThreads.find(t => t.id === params.threadId)
    if (!thread) {
      return new HttpResponse(null, { status: 404 })
    }
    
    // Remove labels
    removeLabelIds.forEach(labelId => {
      const index = thread.labels.indexOf(labelId)
      if (index > -1) {
        thread.labels.splice(index, 1)
      }
      
      // Update status based on label changes
      if (labelId === 'UNREAD') thread.status.isUnread = false
      if (labelId === 'STARRED') thread.status.isStarred = false
      if (labelId === 'IMPORTANT') thread.status.isImportant = false
      if (labelId === 'TRASH') thread.status.isTrash = false
      if (labelId === 'SPAM') thread.status.isSpam = false
    })
    
    // Add labels
    addLabelIds.forEach(labelId => {
      if (!thread.labels.includes(labelId)) {
        thread.labels.push(labelId)
      }
      
      // Update status based on label changes
      if (labelId === 'UNREAD') thread.status.isUnread = true
      if (labelId === 'STARRED') thread.status.isStarred = true
      if (labelId === 'IMPORTANT') thread.status.isImportant = true
      if (labelId === 'TRASH') thread.status.isTrash = true
      if (labelId === 'SPAM') thread.status.isSpam = true
    })
    
    // Update message labels as well
    thread.messages.forEach(message => {
      removeLabelIds.forEach(labelId => {
        const index = message.labels.indexOf(labelId)
        if (index > -1) {
          message.labels.splice(index, 1)
        }
      })
      
      addLabelIds.forEach(labelId => {
        if (!message.labels.includes(labelId)) {
          message.labels.push(labelId)
        }
      })
    })
    
    return HttpResponse.json({
      id: thread.id,
      messages: thread.messages.map(m => ({
        id: m.id,
        threadId: m.threadId,
        labelIds: m.labels
      }))
    })
  }),

  // Trash thread
  http.post('/api/google/threads/:threadId/trash', ({ params }) => {
    const thread = mockThreads.find(t => t.id === params.threadId)
    if (!thread) {
      return new HttpResponse(null, { status: 404 })
    }
    
    thread.status.isTrash = true
    thread.labels = thread.labels.filter(l => l !== 'INBOX')
    if (!thread.labels.includes('TRASH')) {
      thread.labels.push('TRASH')
    }
    
    return HttpResponse.json({
      id: thread.id,
      messages: thread.messages.map(m => ({ id: m.id, threadId: m.threadId, labelIds: ['TRASH'] }))
    })
  }),

  // Untrash thread
  http.post('/api/google/threads/:threadId/untrash', ({ params }) => {
    const thread = mockThreads.find(t => t.id === params.threadId)
    if (!thread) {
      return new HttpResponse(null, { status: 404 })
    }
    
    thread.status.isTrash = false
    thread.labels = thread.labels.filter(l => l !== 'TRASH')
    if (!thread.labels.includes('INBOX')) {
      thread.labels.push('INBOX')
    }
    
    return HttpResponse.json({
      id: thread.id,
      messages: thread.messages.map(m => ({ id: m.id, threadId: m.threadId, labelIds: ['INBOX'] }))
    })
  }),

  // Delete thread permanently
  http.delete('/api/google/threads/:threadId', ({ params }) => {
    const threadIndex = mockThreads.findIndex(t => t.id === params.threadId)
    if (threadIndex === -1) {
      return new HttpResponse(null, { status: 404 })
    }
    
    // Remove thread and its messages
    const thread = mockThreads[threadIndex]
    thread.messages.forEach(message => {
      const messageIndex = mockEmails.findIndex(e => e.id === message.id)
      if (messageIndex > -1) {
        mockEmails.splice(messageIndex, 1)
      }
    })
    
    mockThreads.splice(threadIndex, 1)
    
    return HttpResponse.json({ success: true })
  }),

  // Get single message
  http.get('/api/google/messages/:messageId', ({ params, request }) => {
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'full'
    const metadataHeaders = url.searchParams.getAll('metadataHeaders')
    
    const message = mockEmails.find(m => m.id === params.messageId)
    if (!message) {
      return new HttpResponse(null, { status: 404 })
    }
    
    let formattedMessage
    switch (format) {
      case 'minimal':
        formattedMessage = {
          id: message.id,
          threadId: message.threadId,
          labelIds: message.labels,
          snippet: message.snippet,
          historyId: message.historyId,
          internalDate: message.internalDate,
          sizeEstimate: message.size
        }
        break
      case 'metadata':
        formattedMessage = {
          id: message.id,
          threadId: message.threadId,
          labelIds: message.labels,
          snippet: message.snippet,
          historyId: message.historyId,
          internalDate: message.internalDate,
          sizeEstimate: message.size,
          payload: {
            headers: metadataHeaders.length > 0 ? 
              Object.entries(message.headers)
                .filter(([key]) => metadataHeaders.includes(key))
                .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {}) :
              message.headers
          }
        }
        break
      case 'raw':
        formattedMessage = {
          id: message.id,
          threadId: message.threadId,
          raw: Buffer.from(`Subject: ${message.subject}\nFrom: ${message.headers.from}\nTo: ${message.headers.to}\n\n${message.body.text}`).toString('base64')
        }
        break
      default: // full
        formattedMessage = {
          id: message.id,
          threadId: message.threadId,
          labelIds: message.labels,
          snippet: message.snippet,
          historyId: message.historyId,
          internalDate: message.internalDate,
          sizeEstimate: message.size,
          payload: {
            headers: message.headers,
            body: {
              data: Buffer.from(message.body.html || message.body.text || '').toString('base64')
            },
            parts: message.attachments.map(att => ({
              partId: att.id,
              mimeType: att.mimeType,
              filename: att.filename,
              headers: {
                'Content-Type': att.mimeType,
                'Content-Disposition': `attachment; filename="${att.filename}"`
              },
              body: {
                attachmentId: att.id,
                size: att.size
              }
            }))
          }
        }
    }
    
    return HttpResponse.json(formattedMessage)
  }),

  // Send message
  http.post('/api/google/messages/send', async ({ request }) => {
    const body = await request.json() as {
      raw?: string
      threadId?: string
      payload?: any
    }
    
    let messageData
    if (body.raw) {
      // Decode raw message
      const decoded = Buffer.from(body.raw, 'base64').toString()
      const lines = decoded.split('\n')
      const headers: Record<string, string> = {}
      let bodyStart = 0
      
      // Parse headers
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i]
        if (line.trim() === '') {
          bodyStart = i + 1
          break
        }
        const [key, ...valueParts] = line.split(':')
        if (key && valueParts.length > 0) {
          headers[key.toLowerCase()] = valueParts.join(':').trim()
        }
      }
      
      const messageBody = lines.slice(bodyStart).join('\n')
      messageData = {
        subject: headers.subject || 'No Subject',
        to: headers.to || '',
        body: messageBody,
        threadId: body.threadId
      }
    } else if (body.payload) {
      // Parse payload format
      messageData = {
        subject: body.payload.headers?.subject || 'No Subject',
        to: body.payload.headers?.to || '',
        body: body.payload.body?.data ? Buffer.from(body.payload.body.data, 'base64').toString() : '',
        threadId: body.threadId
      }
    } else {
      return new HttpResponse(null, { status: 400 })
    }
    
    // Create new message
    const newMessage: ParsedMessage = {
      id: `msg-sent-${Date.now()}`,
      threadId: messageData.threadId || `thread-sent-${Date.now()}`,
      messageId: `<sent-${Date.now()}@gmail.com>`,
      subject: messageData.subject,
      snippet: messageData.body.substring(0, 100),
      body: {
        text: messageData.body,
        html: `<div>${messageData.body.replace(/\n/g, '<br>')}</div>`,
        plain: messageData.body,
      },
      headers: {
        'message-id': `<sent-${Date.now()}@gmail.com>`,
        subject: messageData.subject,
        from: '<EMAIL>',
        to: messageData.to,
        date: new Date().toISOString(),
        'content-type': 'text/html; charset=UTF-8',
      },
      from: { email: '<EMAIL>', name: 'You', type: 'personal' },
      to: messageData.to.split(',').map((email: string) => ({ 
        email: email.trim(), 
        name: email.trim().split('@')[0] 
      })),
      date: new Date(),
      receivedDate: new Date(),
      attachments: [],
      labels: ['SENT'],
      flags: {
        isUnread: false,
        isImportant: false,
        isStarred: false,
        isDraft: false,
        isSpam: false,
        isTrash: false,
      },
      size: messageData.body.length,
      rawSize: messageData.body.length,
      historyId: `${Math.floor(Date.now() / 1000)}.${Math.random().toString(36).substr(2, 8)}`,
      internalDate: new Date().toISOString(),
    }
    
    mockEmails.push(newMessage)
    
    // If reply to existing thread, add to that thread
    if (messageData.threadId) {
      const thread = mockThreads.find(t => t.id === messageData.threadId)
      if (thread) {
        thread.messages.push(newMessage)
        thread.lastMessageDate = new Date()
        thread.messageCount++
      }
    } else {
      // Create new thread
      const newThread: Thread = {
        id: newMessage.threadId,
        messages: [newMessage],
        snippet: newMessage.snippet,
        subject: newMessage.subject,
        labels: ['SENT'],
        participants: [
          {
            email: '<EMAIL>',
            name: 'You',
            type: 'personal',
            role: 'sender',
            messageCount: 1,
            lastMessageDate: new Date(),
          },
          ...newMessage.to.map(t => ({
            ...t,
            role: 'recipient' as any,
            messageCount: 1,
            lastMessageDate: new Date(),
          }))
        ],
        lastMessageDate: new Date(),
        firstMessageDate: new Date(),
        messageCount: 1,
        unreadCount: 0,
        status: {
          isUnread: false,
          isImportant: false,
          isStarred: false,
          isDraft: false,
          isSpam: false,
          isTrash: false,
          isSnoozed: false,
        },
      }
      mockThreads.unshift(newThread)
    }
    
    return HttpResponse.json({
      id: newMessage.id,
      threadId: newMessage.threadId,
      labelIds: newMessage.labels
    }, { status: 201 })
  }),

  // Modify message (add/remove labels)
  http.post('/api/google/messages/:messageId/modify', async ({ params, request }) => {
    const { addLabelIds = [], removeLabelIds = [] } = await request.json() as {
      addLabelIds?: string[]
      removeLabelIds?: string[]
    }
    
    const message = mockEmails.find(m => m.id === params.messageId)
    if (!message) {
      return new HttpResponse(null, { status: 404 })
    }
    
    // Remove labels
    removeLabelIds.forEach(labelId => {
      const index = message.labels.indexOf(labelId)
      if (index > -1) {
        message.labels.splice(index, 1)
      }
      
      // Update flags based on label changes
      if (labelId === 'UNREAD') message.flags.isUnread = false
      if (labelId === 'STARRED') message.flags.isStarred = false
      if (labelId === 'IMPORTANT') message.flags.isImportant = false
      if (labelId === 'TRASH') message.flags.isTrash = false
      if (labelId === 'SPAM') message.flags.isSpam = false
    })
    
    // Add labels
    addLabelIds.forEach(labelId => {
      if (!message.labels.includes(labelId)) {
        message.labels.push(labelId)
      }
      
      // Update flags based on label changes
      if (labelId === 'UNREAD') message.flags.isUnread = true
      if (labelId === 'STARRED') message.flags.isStarred = true
      if (labelId === 'IMPORTANT') message.flags.isImportant = true
      if (labelId === 'TRASH') message.flags.isTrash = true
      if (labelId === 'SPAM') message.flags.isSpam = true
    })
    
    return HttpResponse.json({
      id: message.id,
      threadId: message.threadId,
      labelIds: message.labels
    })
  }),

  // Trash message
  http.post('/api/google/messages/:messageId/trash', ({ params }) => {
    const message = mockEmails.find(m => m.id === params.messageId)
    if (!message) {
      return new HttpResponse(null, { status: 404 })
    }
    
    message.flags.isTrash = true
    message.labels = message.labels.filter(l => l !== 'INBOX')
    if (!message.labels.includes('TRASH')) {
      message.labels.push('TRASH')
    }
    
    return HttpResponse.json({
      id: message.id,
      threadId: message.threadId,
      labelIds: message.labels
    })
  }),

  // Untrash message
  http.post('/api/google/messages/:messageId/untrash', ({ params }) => {
    const message = mockEmails.find(m => m.id === params.messageId)
    if (!message) {
      return new HttpResponse(null, { status: 404 })
    }
    
    message.flags.isTrash = false
    message.labels = message.labels.filter(l => l !== 'TRASH')
    if (!message.labels.includes('INBOX')) {
      message.labels.push('INBOX')
    }
    
    return HttpResponse.json({
      id: message.id,
      threadId: message.threadId,
      labelIds: message.labels
    })
  }),

  // Batch operations for threads
  http.post('/api/google/threads/batch', async ({ request }) => {
    const { 
      operation, 
      threadIds = [], 
      addLabelIds = [], 
      removeLabelIds = [] 
    } = await request.json() as {
      operation: 'archive' | 'delete' | 'markRead' | 'markUnread' | 'addLabel' | 'removeLabel' | 'trash' | 'untrash'
      threadIds: string[]
      addLabelIds?: string[]
      removeLabelIds?: string[]
    }
    
    const results = {
      operationId: `batch-${Date.now()}`,
      status: 'completed',
      affectedCount: 0,
      successCount: 0,
      failedCount: 0,
      errors: [] as any[]
    }
    
    for (const threadId of threadIds) {
      const thread = mockThreads.find(t => t.id === threadId)
      if (!thread) {
        results.failedCount++
        results.errors.push({
          threadId,
          error: 'Thread not found',
          code: 'NOT_FOUND'
        })
        continue
      }
      
      try {
        switch (operation) {
          case 'archive':
            thread.labels = thread.labels.filter(l => l !== 'INBOX')
            if (!thread.labels.includes('ARCHIVE')) {
              thread.labels.push('ARCHIVE')
            }
            break
          case 'delete':
            const threadIndex = mockThreads.findIndex(t => t.id === threadId)
            if (threadIndex > -1) {
              mockThreads.splice(threadIndex, 1)
            }
            break
          case 'markRead':
            thread.status.isUnread = false
            thread.unreadCount = 0
            thread.messages.forEach(m => m.flags.isUnread = false)
            break
          case 'markUnread':
            thread.status.isUnread = true
            thread.unreadCount = thread.messages.filter(m => !m.flags.isUnread).length + 1
            thread.messages[thread.messages.length - 1].flags.isUnread = true
            break
          case 'addLabel':
            addLabelIds.forEach(labelId => {
              if (!thread.labels.includes(labelId)) {
                thread.labels.push(labelId)
              }
            })
            break
          case 'removeLabel':
            removeLabelIds.forEach(labelId => {
              thread.labels = thread.labels.filter(l => l !== labelId)
            })
            break
          case 'trash':
            thread.status.isTrash = true
            thread.labels = thread.labels.filter(l => l !== 'INBOX')
            if (!thread.labels.includes('TRASH')) {
              thread.labels.push('TRASH')
            }
            break
          case 'untrash':
            thread.status.isTrash = false
            thread.labels = thread.labels.filter(l => l !== 'TRASH')
            if (!thread.labels.includes('INBOX')) {
              thread.labels.push('INBOX')
            }
            break
        }
        
        results.successCount++
      } catch (error) {
        results.failedCount++
        results.errors.push({
          threadId,
          error: error instanceof Error ? error.message : 'Unknown error',
          code: 'OPERATION_FAILED'
        })
      }
    }
    
    results.affectedCount = results.successCount
    
    return HttpResponse.json({ data: results })
  }),

  // Real-time simulation endpoints
  http.post('/api/simulate/new-email', async ({ request }) => {
    const { category } = await request.json() as { category?: EmailCategory }
    
    const newEmail = simulateNewEmail(category)
    
    return HttpResponse.json({
      message: 'New email simulated',
      email: {
        id: newEmail.id,
        threadId: newEmail.threadId,
        subject: newEmail.subject,
        from: newEmail.from,
        snippet: newEmail.snippet,
        category: newEmail.category
      }
    }, { status: 201 })
  }),

  http.post('/api/simulate/reply/:threadId', ({ params }) => {
    const reply = simulateReply(params.threadId as string)
    
    if (!reply) {
      return new HttpResponse(JSON.stringify({ error: 'Cannot reply to this thread' }), { 
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      })
    }
    
    return HttpResponse.json({
      message: 'Reply simulated',
      reply: {
        id: reply.id,
        threadId: reply.threadId,
        subject: reply.subject,
        from: reply.from,
        snippet: reply.snippet
      }
    }, { status: 201 })
  }),

  // Enhanced statistics endpoint
  http.get('/api/email-stats', () => {
    const stats = getThreadStats()
    
    return HttpResponse.json({
      ...stats,
      lastUpdated: new Date().toISOString(),
      performance: {
        totalMessages: mockEmails.length,
        avgResponseTime: Math.floor(Math.random() * 100) + 50, // Simulated
        uptime: '99.9%'
      }
    })
  }),

  // Legacy endpoints for backward compatibility
  http.get('/api/threads', ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '20')
    const category = url.searchParams.get('category')
    const unread = url.searchParams.get('unread') === 'true'
    const search = url.searchParams.get('search')
    
    let threads = [...mockThreads]
    
    // Apply legacy filters
    if (category) {
      threads = threads.filter(t => t.category === category)
    }
    if (unread) {
      threads = threads.filter(t => t.status.isUnread)
    }
    if (search) {
      threads = searchThreads(search)
    }
    
    // Filter out trash and spam for legacy API
    threads = threads.filter(t => !t.status.isTrash && !t.status.isSpam)
    
    // Sort by date
    threads.sort((a, b) => b.lastMessageDate.getTime() - a.lastMessageDate.getTime())
    
    // Paginate
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedThreads = threads.slice(startIndex, endIndex)
    
    const response: ThreadListResponse = {
      data: paginatedThreads,
      pagination: {
        page,
        pageSize,
        totalPages: Math.ceil(threads.length / pageSize),
        totalItems: threads.length,
        hasNext: endIndex < threads.length,
        hasPrevious: page > 1,
        nextPage: endIndex < threads.length ? page + 1 : undefined,
        previousPage: page > 1 ? page - 1 : undefined,
      },
      total: threads.length,
      hasMore: endIndex < threads.length,
      summary: {
        totalUnread: threads.filter(t => t.status.isUnread).length,
        totalImportant: threads.filter(t => t.status.isImportant).length,
        categories: threads.reduce((acc, t) => {
          const cat = t.category || 'Other'
          acc[cat] = (acc[cat] || 0) + 1
          return acc
        }, {} as Record<string, number>),
      },
    }
    
    return HttpResponse.json(response)
  }),

  // Other legacy endpoints...
  http.get('/api/threads/:threadId', ({ params }) => {
    const thread = mockThreads.find(t => t.id === params.threadId)
    
    if (!thread) {
      return new HttpResponse(null, { status: 404 })
    }
    
    return HttpResponse.json(thread)
  }),

  http.get('/api/messages/:messageId', ({ params, request }) => {
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'full'
    
    const message = mockEmails.find(m => m.id === params.messageId)
    
    if (!message) {
      return new HttpResponse(null, { status: 404 })
    }
    
    if (format === 'minimal') {
      const { body, attachments, ...minimal } = message
      return HttpResponse.json(minimal)
    }
    
    return HttpResponse.json(message)
  }),
]