import { http, HttpResponse } from 'msw'
import { mockAnalyticsData } from '../data/analytics'

export const analyticsHandlers = [
  // Get email statistics
  http.get('/api/analytics/email-stats', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    // In a real app, you'd filter data based on date range
    console.log(`Fetching email stats from ${start} to ${end}`)
    
    return HttpResponse.json(mockAnalyticsData.emailStats)
  }),

  // Get sender analytics
  http.get('/api/analytics/senders', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    const limit = url.searchParams.get('limit') || '20'
    
    console.log(`Fetching sender analytics from ${start} to ${end}, limit: ${limit}`)
    
    // Return top senders based on limit
    const topSenders = mockAnalyticsData.senderAnalytics
      .sort((a, b) => b.messageCount - a.messageCount)
      .slice(0, parseInt(limit))
    
    return HttpResponse.json({
      topSenders,
      totalSenders: mockAnalyticsData.senderAnalytics.length,
      period: {
        start,
        end
      }
    })
  }),

  // Get rule performance
  http.get('/api/analytics/rules', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Fetching rule performance from ${start} to ${end}`)
    
    return HttpResponse.json(mockAnalyticsData.rulePerformance)
  }),

  // Get time patterns analysis
  http.get('/api/analytics/time-patterns', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Fetching time patterns from ${start} to ${end}`)
    
    return HttpResponse.json(mockAnalyticsData.timeAnalysis)
  }),

  // Export analytics data
  http.get('/api/analytics/export', ({ request }) => {
    const url = new URL(request.url)
    const format = url.searchParams.get('format') || 'csv'
    const type = url.searchParams.get('type') || 'email-stats'
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Exporting ${type} as ${format} from ${start} to ${end}`)
    
    let data: any
    let filename: string
    
    switch (type) {
      case 'email-stats':
        data = mockAnalyticsData.emailStats
        filename = `email-stats.${format}`
        break
      case 'senders':
        data = mockAnalyticsData.senderAnalytics
        filename = `sender-analytics.${format}`
        break
      case 'rules':
        data = mockAnalyticsData.rulePerformance
        filename = `rule-performance.${format}`
        break
      default:
        data = mockAnalyticsData.emailStats
        filename = `analytics.${format}`
    }
    
    if (format === 'csv') {
      // Convert to CSV format
      const csvContent = convertToCSV(data, type)
      return new HttpResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      })
    } else {
      // Return as JSON
      return HttpResponse.json(data, {
        headers: {
          'Content-Disposition': `attachment; filename="${filename}"`
        }
      })
    }
  }),

  // Get category breakdown
  http.get('/api/analytics/categories', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Fetching category breakdown from ${start} to ${end}`)
    
    return HttpResponse.json({
      categories: mockAnalyticsData.emailStats.categoryBreakdown,
      total: mockAnalyticsData.emailStats.totals.threads,
      period: { start, end }
    })
  }),

  // Get volume trends
  http.get('/api/analytics/volume', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    const granularity = url.searchParams.get('granularity') || 'daily'
    
    console.log(`Fetching volume data from ${start} to ${end}, granularity: ${granularity}`)
    
    return HttpResponse.json({
      volumeData: mockAnalyticsData.emailStats.volumeData,
      granularity,
      period: { start, end }
    })
  }),

  // Get response patterns
  http.get('/api/analytics/response-patterns', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Fetching response patterns from ${start} to ${end}`)
    
    return HttpResponse.json({
      patterns: mockAnalyticsData.emailStats.responsePatterns,
      period: { start, end }
    })
  }),

  // Get label statistics
  http.get('/api/analytics/labels', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Fetching label statistics from ${start} to ${end}`)
    
    return HttpResponse.json({
      labelStats: mockAnalyticsData.emailStats.labelStats,
      period: { start, end }
    })
  }),

  // Get attachment statistics
  http.get('/api/analytics/attachments', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Fetching attachment statistics from ${start} to ${end}`)
    
    return HttpResponse.json({
      attachmentStats: mockAnalyticsData.emailStats.attachmentStats,
      period: { start, end }
    })
  }),

  // Get productivity metrics
  http.get('/api/analytics/productivity', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Fetching productivity metrics from ${start} to ${end}`)
    
    const emailStats = mockAnalyticsData.emailStats
    const rulePerformance = mockAnalyticsData.rulePerformance
    
    return HttpResponse.json({
      inboxZeroRate: ((emailStats.totals.threads - emailStats.totals.unread) / emailStats.totals.threads * 100),
      emailsProcessedToday: Math.floor(emailStats.metrics.emailsPerDay),
      timeSavedByAutomation: rulePerformance.totalTimeSaved,
      responseRate: (emailStats.totals.sent / emailStats.totals.received * 100),
      averageResponseTime: emailStats.metrics.avgResponseTime,
      coldEmailsBlocked: 234, // Mock value
      unsubscribeSuccessRate: 87.5, // Mock value
      period: { start, end }
    })
  }),

  // Real-time analytics updates (WebSocket simulation)
  http.get('/api/analytics/realtime', ({ request }) => {
    const url = new URL(request.url)
    const type = url.searchParams.get('type') || 'all'
    
    console.log(`Setting up real-time analytics for type: ${type}`)
    
    // In a real app, this would establish a WebSocket connection
    // For now, return current stats with a timestamp
    return HttpResponse.json({
      timestamp: new Date().toISOString(),
      type,
      data: {
        newEmails: Math.floor(Math.random() * 5),
        rulesTriggered: Math.floor(Math.random() * 3),
        timeSaved: Math.floor(Math.random() * 10),
        activeUsers: Math.floor(Math.random() * 50) + 100,
        systemLoad: Math.random() * 100
      }
    })
  }),

  // Get performance metrics
  http.get('/api/analytics/performance', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    
    console.log(`Fetching performance metrics from ${start} to ${end}`)
    
    return HttpResponse.json(mockAnalyticsData.performanceMetrics)
  }),

  // Get analytics events
  http.get('/api/analytics/events', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    const type = url.searchParams.get('type')
    const limit = parseInt(url.searchParams.get('limit') || '100')
    
    console.log(`Fetching analytics events from ${start} to ${end}, type: ${type}, limit: ${limit}`)
    
    let events = mockAnalyticsData.analyticsEvents
    
    // Filter by type if specified
    if (type && type !== 'all') {
      events = events.filter(event => event.type === type)
    }
    
    // Apply limit
    events = events.slice(0, limit)
    
    return HttpResponse.json({
      events,
      total: mockAnalyticsData.analyticsEvents.length,
      hasMore: events.length < mockAnalyticsData.analyticsEvents.length,
      period: { start, end }
    })
  }),

  // Get advanced insights
  http.get('/api/analytics/insights', ({ request }) => {
    const url = new URL(request.url)
    const start = url.searchParams.get('start')
    const end = url.searchParams.get('end')
    const types = url.searchParams.get('types')?.split(',') || ['correlations', 'anomalies', 'predictions']
    
    console.log(`Fetching advanced insights from ${start} to ${end}, types: ${types.join(', ')}`)
    
    const insights = mockAnalyticsData.advancedInsights
    const filteredInsights: any = {}
    
    types.forEach(type => {
      if (insights[type as keyof typeof insights]) {
        filteredInsights[type] = insights[type as keyof typeof insights]
      }
    })
    
    return HttpResponse.json({
      insights: filteredInsights,
      period: { start, end },
      generatedAt: new Date().toISOString()
    })
  }),

  // Generate reports
  http.post('/api/analytics/reports', async ({ request }) => {
    const { template, data } = await request.json() as any
    
    console.log(`Generating report with template: ${template}`)
    
    // Simulate report generation delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000))
    
    const reportId = `report-${Date.now()}`
    const report = {
      id: reportId,
      template,
      status: 'completed',
      generatedAt: new Date().toISOString(),
      summary: {
        totalDataPoints: data?.data?.volumeData?.length || 0,
        analysisType: template,
        keyFindings: [
          'Email volume increased by 12% compared to previous period',
          'Response time improved by 8% on average',
          'Automation rules saved approximately 4.2 hours of manual work'
        ]
      },
      downloadUrl: `/api/analytics/reports/${reportId}/download`,
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
    }
    
    return HttpResponse.json(report)
  }),

  // Anomaly detection
  http.post('/api/analytics/anomalies/detect', async ({ request }) => {
    const { data: inputData, sensitivity = 3, methods = ['zscore'] } = await request.json() as any
    
    console.log(`Detecting anomalies with sensitivity: ${sensitivity}, methods: ${methods.join(', ')}`)
    
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const anomalies = mockAnalyticsData.advancedInsights.anomalies
    
    return HttpResponse.json({
      anomalies,
      summary: {
        total: anomalies.length,
        high: anomalies.filter(a => a.severity === 'high').length,
        medium: anomalies.filter(a => a.severity === 'medium').length,
        low: anomalies.filter(a => a.severity === 'low').length
      },
      parameters: { sensitivity, methods },
      processedAt: new Date().toISOString()
    })
  }),

  // Generate predictions
  http.post('/api/analytics/predictions/generate', async ({ request }) => {
    const { data: inputData, days = 30, method = 'ensemble' } = await request.json() as any
    
    console.log(`Generating predictions for ${days} days using ${method} method`)
    
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const predictions = {
      method,
      accuracy: 0.82,
      predictions: Array.from({ length: days }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() + i + 1)
        
        const baseValue = 50 + Math.sin(i * 0.2) * 10
        const predicted = baseValue + Math.random() * 20 - 10
        
        return {
          date: date.toISOString(),
          predicted: Math.max(0, Math.round(predicted)),
          confidence: 0.7 + Math.random() * 0.2,
          lowerBound: Math.max(0, Math.round(predicted * 0.8)),
          upperBound: Math.round(predicted * 1.2)
        }
      }),
      insights: [
        'Volume expected to increase by 8% over the next month',
        'Peak activity predicted for days 15-20',
        'Model confidence is good but monitor for trend changes'
      ]
    }
    
    return HttpResponse.json(predictions)
  })
]

// Utility function to convert data to CSV
function convertToCSV(data: any, type: string): string {
  switch (type) {
    case 'email-stats':
      return convertEmailStatsToCSV(data)
    case 'senders':
      return convertSendersToCSV(data)
    case 'rules':
      return convertRulesToCSV(data)
    default:
      return JSON.stringify(data)
  }
}

function convertEmailStatsToCSV(emailStats: any): string {
  const headers = [
    'Date',
    'Received',
    'Sent',
    'Archived',
    'Deleted',
    'Unread',
    'Category_Work',
    'Category_Personal',
    'Category_Newsletters',
    'Category_Promotions',
    'Category_Spam'
  ]
  
  const rows = emailStats.volumeData.map((day: any) => [
    new Date(day.date).toISOString().split('T')[0],
    day.received,
    day.sent,
    day.archived,
    day.deleted,
    day.categories.Work || 0,
    day.categories.Personal || 0,
    day.categories.Newsletters || 0,
    day.categories.Promotions || 0,
    day.categories.Spam || 0
  ])
  
  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
}

function convertSendersToCSV(senders: any[]): string {
  const headers = [
    'Email',
    'Name',
    'Domain',
    'Message_Count',
    'Thread_Count',
    'Unread_Count',
    'Avg_Response_Time',
    'Importance',
    'Categories'
  ]
  
  const rows = senders.map(sender => [
    sender.email,
    sender.name || '',
    sender.domain,
    sender.messageCount,
    sender.threadCount,
    sender.unreadCount,
    sender.avgResponseTime || '',
    sender.importance,
    sender.categories.join(';')
  ])
  
  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
}

function convertRulesToCSV(ruleData: any): string {
  const headers = [
    'Rule_Name',
    'Executions',
    'Successes',
    'Failures',
    'Success_Rate',
    'Time_Saved_Minutes',
    'Is_Active',
    'Category',
    'Created_Date',
    'Last_Executed'
  ]
  
  const rows = ruleData.rules.map((rule: any) => [
    rule.name,
    rule.executions,
    rule.successes,
    rule.failures,
    rule.successRate,
    rule.timeSaved,
    rule.isActive,
    rule.category,
    rule.createdAt ? new Date(rule.createdAt).toISOString().split('T')[0] : '',
    rule.lastExecuted ? new Date(rule.lastExecuted).toISOString().split('T')[0] : ''
  ])
  
  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
}

// Enhanced CSV conversion with better error handling
function convertAdvancedDataToCSV(data: any, type: string): string {
  try {
    switch (type) {
      case 'performance':
        return convertPerformanceToCSV(data)
      case 'events':
        return convertEventsToCSV(data)
      case 'insights':
        return convertInsightsToCSV(data)
      default:
        return JSON.stringify(data, null, 2)
    }
  } catch (error) {
    console.error('Error converting to CSV:', error)
    return 'Error generating CSV data'
  }
}

function convertPerformanceToCSV(metrics: any): string {
  const headers = ['Metric_Type', 'Name', 'Value', 'Unit']
  const rows: string[] = []
  
  // API calls
  metrics.apiCalls?.forEach((api: any) => {
    rows.push(`API,${api.endpoint},${api.count},calls`)
    rows.push(`API,${api.endpoint}_duration,${api.avgDuration},ms`)
    rows.push(`API,${api.endpoint}_success_rate,${api.successRate},%`)
  })
  
  // Resource usage
  if (metrics.resourceUsage) {
    Object.entries(metrics.resourceUsage).forEach(([key, value]) => {
      rows.push(`Resource,${key},${value},%`)
    })
  }
  
  return [headers.join(','), ...rows].join('\n')
}

function convertEventsToCSV(events: any[]): string {
  const headers = ['Timestamp', 'Type', 'Category', 'Action', 'Label', 'Value', 'User_ID']
  
  const rows = events.map(event => [
    new Date(event.timestamp).toISOString(),
    event.type,
    event.category || '',
    event.action,
    event.label || '',
    event.value || '',
    event.userId || ''
  ])
  
  return [headers.join(','), ...rows.map(row => row.join(','))].join('\n')
}

function convertInsightsToCSV(insights: any): string {
  const headers = ['Type', 'Key', 'Value', 'Description']
  const rows: string[] = []
  
  // Correlations
  if (insights.correlations) {
    Object.entries(insights.correlations).forEach(([key, value]) => {
      rows.push(`Correlation,${key},${value},Correlation coefficient`)
    })
  }
  
  // Anomalies
  if (insights.anomalies) {
    insights.anomalies.forEach((anomaly: any, index: number) => {
      rows.push(`Anomaly,anomaly_${index},${anomaly.value},${anomaly.description}`)
    })
  }
  
  return [headers.join(','), ...rows].join('\n')
}