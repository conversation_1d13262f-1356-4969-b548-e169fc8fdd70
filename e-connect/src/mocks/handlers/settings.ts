import { http, HttpResponse } from 'msw';
import { 
  UserSettings, 
  OAuthConnection, 
  WebhookConfiguration, 
  ApiKeyConfiguration,
  SettingsHealthReport,
  SettingsBackup,
  SettingsImportResult
} from '../../types/settings';

// Mock data storage
let mockSettings: UserSettings = {
  profile: {
    id: 'user_1',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=John',
    bio: 'Senior Software Engineer passionate about email productivity and automation.',
    company: 'TechCorp Inc.',
    jobTitle: 'Senior Software Engineer',
    location: 'San Francisco, CA',
    timezone: 'America/Los_Angeles',
    locale: 'en-US',
  },
  security: {
    twoFactorEnabled: false,
    twoFactorMethod: 'authenticator',
    backupCodes: ['123456', '789012', '345678'],
    sessionTimeout: 480,
    maxSessions: 5,
    passwordLastChanged: new Date('2024-01-15'),
    securityNotifications: true,
    suspiciousActivityAlerts: true,
    loginHistory: [
      {
        id: 'session_1',
        deviceName: 'MacBook Pro',
        location: 'San Francisco, CA',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)',
        lastAccessed: new Date('2024-01-20T09:30:00'),
        isCurrentSession: true,
      },
      {
        id: 'session_2',
        deviceName: 'iPhone 15',
        location: 'San Francisco, CA',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X)',
        lastAccessed: new Date('2024-01-19T15:20:00'),
        isCurrentSession: false,
      },
    ],
  },
  privacy: {
    dataProcessingConsent: true,
    marketingEmailsConsent: false,
    analyticsConsent: true,
    thirdPartyIntegrationsConsent: true,
    dataRetentionPeriod: 365,
    allowDataExport: true,
    allowDataDeletion: true,
    gdprCompliant: true,
    cookiePreferences: {
      essential: true,
      analytics: true,
      marketing: false,
      personalization: true,
      thirdParty: false,
    },
  },
  notifications: {
    email: {
      enabled: true,
      newEmails: true,
      importantEmails: true,
      mentions: true,
      rules: false,
      bulkOperations: true,
      securityAlerts: true,
      systemUpdates: false,
      frequency: 'immediate',
    },
    push: {
      enabled: false,
      newEmails: false,
      importantEmails: false,
      mentions: false,
      sound: true,
      vibration: true,
      showPreview: false,
    },
    inApp: {
      enabled: true,
      position: 'top-right',
      duration: 5,
      showIcons: true,
      groupSimilar: true,
    },
    digest: {
      enabled: false,
      frequency: 'daily',
      time: '09:00',
      includeStats: true,
      includeTopSenders: true,
      includeRuleActivity: false,
    },
    quiet: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00',
      timezone: 'America/Los_Angeles',
      weekendsOnly: false,
      exceptions: [],
    },
  },
  theme: {
    mode: 'auto',
    accentColor: '#3b82f6',
    colorScheme: 'blue',
    density: 'comfortable',
    fontSize: 'medium',
    fontFamily: 'system-ui, -apple-system, sans-serif',
    reducedMotion: false,
    highContrast: false,
  },
  email: {
    signature: {
      id: 'default',
      name: 'Default Signature',
      content: 'Best regards,<br/>John Doe<br/>Senior Software Engineer<br/>TechCorp Inc.',
      isDefault: true,
      useForReplies: true,
      useForForwards: false,
    },
    autoReply: {
      enabled: false,
      subject: 'Auto-reply: Out of Office',
      message: 'Thank you for your email. I am currently out of office and will respond to your message when I return.',
      sendToKnownContactsOnly: true,
      sendOnce: true,
      excludeDomains: ['internal.company.com'],
    },
    threading: {
      enabled: true,
      groupBySubject: true,
      maxThreadDepth: 10,
      collapseRead: false,
      showParticipants: true,
      sortOrder: 'chronological',
    },
    reading: {
      markAsReadDelay: 2,
      autoAdvance: false,
      showImages: 'known-senders',
      showExternalContent: false,
      previewLength: 200,
      openInNewTab: false,
      keyboardShortcuts: true,
    },
    compose: {
      defaultFormat: 'html',
      autoSave: true,
      autoSaveInterval: 30,
      spellCheck: true,
      suggestContacts: true,
      sendDelay: 5,
      requireSubject: true,
      warnLargeAttachments: 25,
    },
    forwarding: {
      enabled: false,
      forwardTo: [],
      keepCopy: true,
      forwardFiltered: false,
      includeOriginalHeaders: false,
    },
  },
  integrations: {
    oauth: [
      {
        id: 'oauth_google_1',
        provider: 'google',
        email: '<EMAIL>',
        displayName: 'John Doe',
        scopes: ['email', 'calendar', 'drive'],
        connectedAt: new Date('2024-01-10'),
        lastUsed: new Date('2024-01-20'),
        isActive: true,
      },
    ],
    webhooks: [
      {
        id: 'webhook_1',
        name: 'Slack Notifications',
        url: 'https://hooks.slack.com/services/T123/B456/xyz789',
        events: ['email.received', 'rule.executed'],
        secret: 'webhook_secret_123',
        isActive: true,
        createdAt: new Date('2024-01-15'),
        lastTriggered: new Date('2024-01-20T08:30:00'),
        failureCount: 0,
        headers: {
          'Content-Type': 'application/json',
        },
      },
    ],
    apiKeys: [
      {
        id: 'api_key_1',
        name: 'OpenAI API',
        service: 'OpenAI',
        keyPreview: '***sk-4321',
        scopes: ['completions', 'embeddings'],
        createdAt: new Date('2024-01-12'),
        lastUsed: new Date('2024-01-19'),
        isActive: true,
        expiresAt: new Date('2025-01-12'),
      },
    ],
    calendar: {
      enabled: true,
      provider: 'google',
      syncMeetingInvites: true,
      createEventsFromEmails: false,
      defaultCalendar: 'primary',
      reminderSettings: {
        enabled: true,
        minutesBefore: 15,
      },
    },
    crm: {
      enabled: false,
      provider: 'salesforce',
      syncContacts: false,
      createLeadsFromEmails: false,
      trackEmailActivity: false,
      customFields: {},
    },
    productivity: [],
  },
  ai: {
    model: {
      provider: 'openai',
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 1000,
    },
    features: {
      autoCategories: true,
      smartReplies: true,
      summaries: false,
      priorityScoring: true,
      sentimentAnalysis: false,
      languageDetection: true,
      spamDetection: true,
      phishingDetection: true,
    },
    training: {
      personalizeResponses: false,
      learnFromCorrections: false,
      shareAnonymizedData: false,
      retainTrainingData: 30,
    },
    privacy: {
      processLocally: false,
      encryptData: true,
      anonymizeBeforeProcessing: true,
      optOutOfImprovement: false,
    },
  },
  performance: {
    cache: {
      enabled: true,
      maxSize: 100,
      ttl: 3600,
      preloadNextPage: false,
      cacheImages: true,
      cacheAttachments: false,
    },
    sync: {
      enabled: true,
      interval: 30,
      batchSize: 50,
      conflictResolution: 'server-wins',
      offlineMode: false,
      backgroundSync: true,
    },
    debug: {
      enabled: false,
      logLevel: 'warn',
      includeNetworkLogs: false,
      includePerformanceLogs: false,
      maxLogSize: 10,
      uploadLogs: false,
    },
    experimental: {
      enabled: false,
      features: {},
      betaOptIn: false,
      feedbackEnabled: false,
    },
  },
  shortcuts: {
    enabled: true,
    shortcuts: {
      'compose': {
        id: 'compose',
        name: 'Compose Email',
        description: 'Open compose dialog',
        keys: ['c'],
        action: 'compose',
        enabled: true,
      },
      'search': {
        id: 'search',
        name: 'Search',
        description: 'Focus search bar',
        keys: ['/', 'cmd+k'],
        action: 'search',
        enabled: true,
      },
    },
    customShortcuts: [],
  },
  metadata: {
    version: '1.0.0',
    lastModified: new Date('2024-01-20T10:00:00'),
    modifiedBy: '<EMAIL>',
    checksum: 'abc123def456',
    conflicts: [],
  },
};

let mockBackups: SettingsBackup[] = [
  {
    id: 'backup_1',
    name: 'Daily Backup',
    description: 'Automatic daily backup',
    settings: mockSettings,
    createdAt: new Date('2024-01-19'),
    size: 5432,
    checksum: 'backup_checksum_123',
  },
];

export const settingsHandlers = [
  // Get settings
  http.get('/api/settings', () => {
    return HttpResponse.json(mockSettings);
  }),

  // Update settings
  http.put('/api/settings', async ({ request }) => {
    const updatedSettings = await request.json() as UserSettings;
    mockSettings = {
      ...mockSettings,
      ...updatedSettings,
      metadata: {
        ...mockSettings.metadata,
        lastModified: new Date(),
        version: mockSettings.metadata.version,
      },
    };
    return HttpResponse.json(mockSettings);
  }),

  // Reset settings
  http.post('/api/settings/reset', () => {
    // Reset to default settings
    return HttpResponse.json({ success: true });
  }),

  // OAuth Connections
  http.post('/api/settings/oauth/connect', async ({ request }) => {
    const { provider, credentials } = await request.json() as any;
    
    const connection: OAuthConnection = {
      id: `oauth_${provider}_${Date.now()}`,
      provider,
      email: `user@${provider}.com`,
      displayName: 'User Account',
      scopes: ['email', 'profile'],
      connectedAt: new Date(),
      lastUsed: new Date(),
      isActive: true,
    };

    mockSettings.integrations.oauth.push(connection);
    return HttpResponse.json(connection);
  }),

  http.delete('/api/settings/oauth/:connectionId', ({ params }) => {
    const { connectionId } = params;
    mockSettings.integrations.oauth = mockSettings.integrations.oauth.filter(
      conn => conn.id !== connectionId
    );
    return HttpResponse.json({ success: true });
  }),

  // Webhooks
  http.get('/api/settings/webhooks', () => {
    return HttpResponse.json(mockSettings.integrations.webhooks);
  }),

  http.post('/api/settings/webhooks', async ({ request }) => {
    const webhookData = await request.json() as Partial<WebhookConfiguration>;
    
    const webhook: WebhookConfiguration = {
      id: `webhook_${Date.now()}`,
      name: webhookData.name!,
      url: webhookData.url!,
      events: webhookData.events!,
      secret: webhookData.secret,
      isActive: true,
      createdAt: new Date(),
      failureCount: 0,
      headers: webhookData.headers || {},
    };

    mockSettings.integrations.webhooks.push(webhook);
    return HttpResponse.json(webhook);
  }),

  http.put('/api/settings/webhooks/:webhookId', async ({ params, request }) => {
    const { webhookId } = params;
    const updates = await request.json() as Partial<WebhookConfiguration>;
    
    const webhookIndex = mockSettings.integrations.webhooks.findIndex(w => w.id === webhookId);
    if (webhookIndex === -1) {
      return HttpResponse.json({ error: 'Webhook not found' }, { status: 404 });
    }

    mockSettings.integrations.webhooks[webhookIndex] = {
      ...mockSettings.integrations.webhooks[webhookIndex],
      ...updates,
    };

    return HttpResponse.json(mockSettings.integrations.webhooks[webhookIndex]);
  }),

  http.delete('/api/settings/webhooks/:webhookId', ({ params }) => {
    const { webhookId } = params;
    mockSettings.integrations.webhooks = mockSettings.integrations.webhooks.filter(
      w => w.id !== webhookId
    );
    return HttpResponse.json({ success: true });
  }),

  http.post('/api/settings/webhooks/:webhookId/test', ({ params }) => {
    const { webhookId } = params;
    const webhook = mockSettings.integrations.webhooks.find(w => w.id === webhookId);
    
    if (!webhook) {
      return HttpResponse.json({ error: 'Webhook not found' }, { status: 404 });
    }

    // Simulate webhook test
    webhook.lastTriggered = new Date();
    return HttpResponse.json({ success: true, status: 200 });
  }),

  // API Keys
  http.get('/api/settings/api-keys', () => {
    return HttpResponse.json(mockSettings.integrations.apiKeys);
  }),

  http.post('/api/settings/api-keys', async ({ request }) => {
    const apiKeyData = await request.json() as Partial<ApiKeyConfiguration>;
    
    const apiKey: ApiKeyConfiguration = {
      id: `api_key_${Date.now()}`,
      name: apiKeyData.name!,
      service: apiKeyData.service!,
      keyPreview: apiKeyData.keyPreview!,
      scopes: apiKeyData.scopes || [],
      createdAt: new Date(),
      isActive: true,
    };

    mockSettings.integrations.apiKeys.push(apiKey);
    return HttpResponse.json(apiKey);
  }),

  http.put('/api/settings/api-keys/:apiKeyId', async ({ params, request }) => {
    const { apiKeyId } = params;
    const updates = await request.json() as Partial<ApiKeyConfiguration>;
    
    const apiKeyIndex = mockSettings.integrations.apiKeys.findIndex(k => k.id === apiKeyId);
    if (apiKeyIndex === -1) {
      return HttpResponse.json({ error: 'API key not found' }, { status: 404 });
    }

    mockSettings.integrations.apiKeys[apiKeyIndex] = {
      ...mockSettings.integrations.apiKeys[apiKeyIndex],
      ...updates,
    };

    return HttpResponse.json(mockSettings.integrations.apiKeys[apiKeyIndex]);
  }),

  http.delete('/api/settings/api-keys/:apiKeyId', ({ params }) => {
    const { apiKeyId } = params;
    mockSettings.integrations.apiKeys = mockSettings.integrations.apiKeys.filter(
      k => k.id !== apiKeyId
    );
    return HttpResponse.json({ success: true });
  }),

  // Health Check
  http.get('/api/settings/health', () => {
    const healthReport: SettingsHealthReport = {
      overall: 'healthy',
      issues: [
        {
          id: 'no-2fa',
          severity: 'medium',
          category: 'security',
          title: 'Two-Factor Authentication Disabled',
          description: 'Enable 2FA to improve account security',
          fix: 'Go to Security settings and enable Two-Factor Authentication',
          autoFixable: false,
        },
      ],
      recommendations: [
        'Enable two-factor authentication for better security',
        'Review your privacy settings regularly',
        'Keep your notification preferences up to date',
      ],
      score: 85,
    };

    return HttpResponse.json(healthReport);
  }),

  // Backups
  http.get('/api/settings/backups', () => {
    return HttpResponse.json(mockBackups);
  }),

  http.post('/api/settings/backups', async ({ request }) => {
    const { name, description } = await request.json() as { name: string; description?: string };
    
    const backup: SettingsBackup = {
      id: `backup_${Date.now()}`,
      name,
      description,
      settings: mockSettings,
      createdAt: new Date(),
      size: JSON.stringify(mockSettings).length,
      checksum: `checksum_${Date.now()}`,
    };

    mockBackups.push(backup);
    return HttpResponse.json(backup);
  }),

  http.post('/api/settings/backups/:backupId/restore', ({ params }) => {
    const { backupId } = params;
    const backup = mockBackups.find(b => b.id === backupId);
    
    if (!backup) {
      return HttpResponse.json({ error: 'Backup not found' }, { status: 404 });
    }

    mockSettings = backup.settings;
    return HttpResponse.json({ success: true });
  }),

  http.delete('/api/settings/backups/:backupId', ({ params }) => {
    const { backupId } = params;
    mockBackups = mockBackups.filter(b => b.id !== backupId);
    return HttpResponse.json({ success: true });
  }),

  // Import/Export
  http.get('/api/settings/export', () => {
    const data = JSON.stringify(mockSettings, null, 2);
    return HttpResponse.json({ data, format: 'json' });
  }),

  http.post('/api/settings/import', async ({ request }) => {
    const { data, format } = await request.json() as { data: string; format: string };
    
    try {
      const importedSettings = JSON.parse(data) as Partial<UserSettings>;
      
      // Validate and merge settings
      mockSettings = {
        ...mockSettings,
        ...importedSettings,
        metadata: {
          ...mockSettings.metadata,
          lastModified: new Date(),
        },
      };

      const result: SettingsImportResult = {
        success: true,
        imported: Object.keys(importedSettings),
        skipped: [],
        errors: [],
        warnings: [],
      };

      return HttpResponse.json(result);
    } catch (error) {
      const result: SettingsImportResult = {
        success: false,
        imported: [],
        skipped: [],
        errors: ['Invalid JSON format'],
        warnings: [],
      };

      return HttpResponse.json(result, { status: 400 });
    }
  }),

  // Validation
  http.post('/api/settings/validate', async ({ request }) => {
    const settings = await request.json() as Partial<UserSettings>;
    const errors: string[] = [];

    // Basic validation
    if (settings.profile?.email && !settings.profile.email.includes('@')) {
      errors.push('Invalid email format');
    }

    if (settings.security?.sessionTimeout && (settings.security.sessionTimeout < 5 || settings.security.sessionTimeout > 1440)) {
      errors.push('Session timeout must be between 5 minutes and 24 hours');
    }

    return HttpResponse.json(errors);
  }),

  // Data Export
  http.post('/api/settings/export-data', () => {
    // Simulate data export
    const exportData = {
      profile: mockSettings.profile,
      emails: [], // Would include user's emails
      rules: [], // Would include user's rules
      analytics: {}, // Would include analytics data
    };

    return HttpResponse.json({
      downloadUrl: '/api/downloads/user-data-export.json',
      size: JSON.stringify(exportData).length,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    });
  }),

  // Account Deletion
  http.post('/api/settings/delete-account', async ({ request }) => {
    const { email, confirmation } = await request.json() as { email: string; confirmation: string };
    
    if (email !== mockSettings.profile.email) {
      return HttpResponse.json({ error: 'Email does not match' }, { status: 400 });
    }

    if (confirmation !== 'DELETE') {
      return HttpResponse.json({ error: 'Invalid confirmation' }, { status: 400 });
    }

    // Simulate account deletion
    return HttpResponse.json({ 
      success: true, 
      message: 'Account deletion scheduled. You will receive a confirmation email.',
      scheduledFor: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });
  }),
];