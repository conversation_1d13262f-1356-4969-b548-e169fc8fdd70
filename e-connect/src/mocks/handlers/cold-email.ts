import { http, HttpResponse, delay } from 'msw';
import type { 
  ColdEmailDetection, 
  BlockedSender, 
  WhitelistedSender,
  ColdEmailAnalytics,
  ColdEmailSettings,
  SenderAnalysis,
  ContentAnalysis,
  TrainingData,
  ColdEmailExport
} from '../../types/cold-email';

// Mock data generators
const generateSenderAnalysis = (email: string): SenderAnalysis => {
  const domain = email.split('@')[1] || 'unknown.com';
  const isKnownSpammer = ['spammy.com', 'coldmail.net', 'salespro.io'].includes(domain);
  const domainAge = Math.floor(Math.random() * 3650) + 30; // 30 days to 10 years
  
  return {
    email,
    domain,
    reputation: {
      score: isKnownSpammer ? Math.floor(Math.random() * 30) : Math.floor(Math.random() * 70) + 30,
      level: isKnownSpammer ? 'malicious' : Math.random() > 0.7 ? 'trusted' : Math.random() > 0.5 ? 'neutral' : 'suspicious',
      factors: {
        domainAge: Math.min(100, (domainAge / 365) * 10),
        authenticationScore: Math.random() > 0.3 ? 100 : 0,
        engagementScore: Math.floor(Math.random() * 100),
        reportScore: isKnownSpammer ? 20 : 80 + Math.floor(Math.random() * 20),
        behaviorScore: Math.floor(Math.random() * 100)
      },
      history: {
        totalSent: Math.floor(Math.random() * 1000) + 10,
        blocked: isKnownSpammer ? Math.floor(Math.random() * 500) : Math.floor(Math.random() * 10),
        reported: isKnownSpammer ? Math.floor(Math.random() * 100) : Math.floor(Math.random() * 5),
        engaged: Math.floor(Math.random() * 100),
        unsubscribed: Math.floor(Math.random() * 50)
      },
      blacklists: isKnownSpammer ? ['SPAMHAUS', 'BARRACUDA'] : [],
      whitelists: isKnownSpammer ? [] : Math.random() > 0.7 ? ['DNSWL'] : []
    },
    authentication: {
      spf: { status: Math.random() > 0.2 ? 'pass' : 'fail' },
      dkim: { status: Math.random() > 0.3 ? 'pass' : 'fail' },
      dmarc: { status: Math.random() > 0.5 ? 'pass' : 'none' }
    },
    domainInfo: {
      age: domainAge,
      registrar: 'GoDaddy',
      country: ['US', 'UK', 'CA', 'AU', 'IN'][Math.floor(Math.random() * 5)],
      isDisposable: ['tempmail.com', 'guerrillamail.com'].includes(domain),
      isFreemail: ['gmail.com', 'yahoo.com', 'hotmail.com'].includes(domain),
      isDynamic: false
    },
    statistics: {
      totalEmails: Math.floor(Math.random() * 100) + 1,
      firstSeen: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      lastSeen: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      recipientCount: Math.floor(Math.random() * 1000) + 10,
      engagementRate: Math.random() * 30,
      reportCount: isKnownSpammer ? Math.floor(Math.random() * 50) : Math.floor(Math.random() * 5),
      blockCount: isKnownSpammer ? Math.floor(Math.random() * 100) : Math.floor(Math.random() * 10)
    },
    patterns: {
      sendingFrequency: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
      timePattern: ['business_hours', 'weekdays'],
      massMailer: isKnownSpammer || Math.random() > 0.7,
      bulkSending: isKnownSpammer || Math.random() > 0.8
    }
  };
};

const generateContentAnalysis = (subject: string, snippet: string): ContentAnalysis => {
  const salesKeywords = ['offer', 'discount', 'limited time', 'exclusive', 'deal', 'save', 'buy now'];
  const urgencyKeywords = ['urgent', 'immediate', 'act now', 'expires', 'last chance', 'hurry'];
  const suspiciousKeywords = ['guaranteed', 'risk-free', 'no obligation', 'winner', 'congratulations'];
  
  const foundSalesKeywords = salesKeywords.filter(k => 
    subject.toLowerCase().includes(k) || snippet.toLowerCase().includes(k)
  );
  const foundUrgencyKeywords = urgencyKeywords.filter(k => 
    subject.toLowerCase().includes(k) || snippet.toLowerCase().includes(k)
  );
  const foundSuspiciousKeywords = suspiciousKeywords.filter(k => 
    subject.toLowerCase().includes(k) || snippet.toLowerCase().includes(k)
  );
  
  const isColdEmail = foundSalesKeywords.length > 0 || Math.random() > 0.6;
  const isSalesEmail = foundSalesKeywords.length > 1;
  const isPhishing = foundSuspiciousKeywords.length > 1;
  
  return {
    language: 'en',
    sentiment: isSalesEmail ? 'positive' : 'neutral',
    tone: isSalesEmail ? 'aggressive' : 'professional',
    patterns: {
      isColdEmail,
      isSalesEmail,
      isMarketingEmail: foundSalesKeywords.length > 0,
      isRecruitingEmail: subject.toLowerCase().includes('opportunity') || subject.toLowerCase().includes('position'),
      isPhishing,
      isScam: isPhishing && foundSuspiciousKeywords.length > 2
    },
    keywords: {
      sales: foundSalesKeywords,
      urgency: foundUrgencyKeywords,
      financial: [],
      suspicious: foundSuspiciousKeywords,
      action: ['click here', 'sign up', 'register'].filter(k => snippet.toLowerCase().includes(k))
    },
    structure: {
      hasUnsubscribeLink: Math.random() > 0.3,
      hasContactInfo: Math.random() > 0.5,
      hasCompanyInfo: Math.random() > 0.6,
      hasPrivacyPolicy: Math.random() > 0.7,
      templateSimilarity: Math.floor(Math.random() * 100)
    },
    links: [
      {
        url: 'https://example.com/unsubscribe',
        domain: 'example.com',
        isShortened: false,
        isTracking: true,
        isSuspicious: false,
        reputation: 'safe'
      }
    ],
    images: [
      {
        type: 'image/png',
        size: 1234,
        isTracking: true,
        isHidden: false,
        altText: 'Company Logo'
      }
    ],
    mlAnalysis: {
      coldEmailProbability: isColdEmail ? 70 + Math.floor(Math.random() * 30) : Math.floor(Math.random() * 40),
      spamProbability: isPhishing ? 60 + Math.floor(Math.random() * 40) : Math.floor(Math.random() * 30),
      phishingProbability: isPhishing ? 50 + Math.floor(Math.random() * 50) : Math.floor(Math.random() * 20),
      categoryScores: {
        sales: isSalesEmail ? 80 + Math.floor(Math.random() * 20) : Math.floor(Math.random() * 30),
        marketing: foundSalesKeywords.length > 0 ? 70 + Math.floor(Math.random() * 30) : Math.floor(Math.random() * 40),
        recruiting: subject.toLowerCase().includes('opportunity') ? 90 : Math.floor(Math.random() * 20),
        legitimate: isColdEmail ? Math.floor(Math.random() * 30) : 70 + Math.floor(Math.random() * 30)
      }
    }
  };
};

// Generate mock cold email detections
const generateColdEmailDetections = (count: number): ColdEmailDetection[] => {
  const senders = [
    { email: '<EMAIL>', name: 'TechPro Sales', subject: 'Exclusive offer for your business' },
    { email: '<EMAIL>', name: 'John Smith', subject: 'Boost your ROI by 300%' },
    { email: '<EMAIL>', name: 'Best Deals', subject: 'Limited time offer - Act now!' },
    { email: '<EMAIL>', name: 'Sarah Johnson', subject: 'Exciting opportunity at Fortune 500' },
    { email: '<EMAIL>', name: 'Web Services Inc', subject: 'Free website audit for your company' },
    { email: '<EMAIL>', name: 'Cloud Platform', subject: 'Migrate to the cloud - Special pricing' },
    { email: '<EMAIL>', name: 'Startup Accelerator', subject: 'Investment opportunity discussion' },
    { email: '<EMAIL>', name: 'SEO Experts', subject: 'First page Google ranking guaranteed' }
  ];
  
  return Array.from({ length: count }, (_, i) => {
    const sender = senders[i % senders.length];
    const senderAnalysis = generateSenderAnalysis(sender.email);
    const contentAnalysis = generateContentAnalysis(sender.subject, 'This is a sample email snippet...');
    
    const confidence = Math.min(100, Math.max(0,
      (senderAnalysis.reputation.score < 30 ? 50 : 0) +
      (contentAnalysis.patterns.isColdEmail ? 30 : 0) +
      (contentAnalysis.mlAnalysis.coldEmailProbability / 5) +
      Math.floor(Math.random() * 20)
    ));
    
    return {
      id: `detection-${i + 1}`,
      emailId: `email-${1000 + i}`,
      threadId: `thread-${500 + i}`,
      detectedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      confidence,
      status: 'pending',
      detection: {
        method: [
          'ml_model',
          confidence > 70 ? 'content_pattern' : null,
          senderAnalysis.reputation.score < 30 ? 'sender_reputation' : null,
          senderAnalysis.patterns.massMailer ? 'mass_sending' : null
        ].filter(Boolean) as any[],
        signals: [
          {
            type: 'ml_confidence',
            value: contentAnalysis.mlAnalysis.coldEmailProbability,
            weight: 0.4,
            confidence: contentAnalysis.mlAnalysis.coldEmailProbability,
            description: 'Machine learning model confidence'
          },
          {
            type: 'sender_reputation',
            value: senderAnalysis.reputation.score,
            weight: 0.3,
            confidence: 100 - senderAnalysis.reputation.score,
            description: 'Sender reputation score'
          }
        ],
        mlScore: contentAnalysis.mlAnalysis.coldEmailProbability,
        ruleMatches: contentAnalysis.keywords.sales.length > 0 ? ['sales_keywords'] : [],
        explanation: `This email appears to be a cold email based on ${
          confidence > 80 ? 'strong indicators including sender reputation, content patterns, and ML analysis' :
          confidence > 60 ? 'moderate indicators from content analysis and sending patterns' :
          'weak indicators that suggest possible cold email characteristics'
        }.`
      },
      email: {
        from: sender.email,
        senderName: sender.name,
        domain: sender.email.split('@')[1],
        subject: sender.subject,
        snippet: 'Hi there, I noticed your company and wanted to reach out about our services...',
        receivedDate: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
        hasAttachments: Math.random() > 0.7,
        size: Math.floor(Math.random() * 50000) + 5000
      },
      senderAnalysis,
      contentAnalysis
    };
  });
};

// Generate mock blocked senders
const generateBlockedSenders = (): BlockedSender[] => [
  {
    id: 'blocked-1',
    email: '<EMAIL>',
    type: 'email',
    reason: 'Persistent cold emails despite unsubscribe requests',
    blockedAt: new Date('2024-01-15'),
    blockedBy: '<EMAIL>',
    statistics: {
      emailsBlocked: 47,
      lastBlocked: new Date('2024-02-20'),
      reportCount: 12
    },
    metadata: {
      category: 'sales',
      tags: ['aggressive', 'spam'],
      notes: 'Multiple users reported as spam'
    }
  },
  {
    id: 'blocked-2',
    domain: 'coldmail.net',
    type: 'domain',
    reason: 'Known cold email service provider',
    blockedAt: new Date('2024-01-20'),
    blockedBy: '<EMAIL>',
    statistics: {
      emailsBlocked: 234,
      lastBlocked: new Date('2024-02-22'),
      reportCount: 45
    }
  },
  {
    id: 'blocked-3',
    pattern: '.*@.*\\.biz',
    type: 'pattern',
    reason: 'Blocking all .biz domains due to high spam rate',
    blockedAt: new Date('2024-02-01'),
    blockedBy: '<EMAIL>',
    statistics: {
      emailsBlocked: 89,
      lastBlocked: new Date('2024-02-21'),
      reportCount: 23
    },
    expiry: {
      type: 'temporary',
      expiresAt: new Date('2024-12-31')
    }
  }
];

// Generate mock whitelisted senders
const generateWhitelistedSenders = (): WhitelistedSender[] => [
  {
    id: 'whitelist-1',
    email: '<EMAIL>',
    type: 'email',
    reason: 'Valuable industry newsletter',
    whitelistedAt: new Date('2024-01-10'),
    whitelistedBy: '<EMAIL>',
    statistics: {
      emailsAllowed: 52,
      lastReceived: new Date('2024-02-22')
    }
  },
  {
    id: 'whitelist-2',
    domain: 'partner.com',
    type: 'domain',
    reason: 'Business partner communications',
    whitelistedAt: new Date('2024-01-05'),
    whitelistedBy: '<EMAIL>',
    vipSettings: {
      priority: 'high',
      notifications: true,
      bypassAllFilters: true
    },
    statistics: {
      emailsAllowed: 128,
      lastReceived: new Date('2024-02-23')
    }
  },
  {
    id: 'whitelist-3',
    email: '<EMAIL>',
    type: 'vip',
    reason: 'VIP client - CEO direct communications',
    whitelistedAt: new Date('2024-01-01'),
    whitelistedBy: '<EMAIL>',
    vipSettings: {
      priority: 'high',
      notifications: true,
      bypassAllFilters: true
    },
    statistics: {
      emailsAllowed: 15,
      lastReceived: new Date('2024-02-20')
    }
  }
];

// Generate analytics data
const generateAnalytics = (): ColdEmailAnalytics => {
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  
  return {
    period: {
      start: thirtyDaysAgo,
      end: now
    },
    detection: {
      total: 2847,
      blocked: 1923,
      allowed: 734,
      reviewed: 890,
      falsePositives: 45,
      falseNegatives: 12,
      accuracy: 94.3
    },
    trends: {
      daily: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(thirtyDaysAgo.getTime() + i * 24 * 60 * 60 * 1000),
        detected: Math.floor(Math.random() * 100) + 50,
        blocked: Math.floor(Math.random() * 70) + 30,
        allowed: Math.floor(Math.random() * 30) + 10,
        reviewed: Math.floor(Math.random() * 40) + 20
      })),
      weekly: Array.from({ length: 4 }, (_, i) => ({
        date: new Date(thirtyDaysAgo.getTime() + i * 7 * 24 * 60 * 60 * 1000),
        detected: Math.floor(Math.random() * 700) + 350,
        blocked: Math.floor(Math.random() * 500) + 250,
        allowed: Math.floor(Math.random() * 200) + 100,
        reviewed: Math.floor(Math.random() * 300) + 150
      })),
      monthly: Array.from({ length: 1 }, () => ({
        date: thirtyDaysAgo,
        detected: 2847,
        blocked: 1923,
        allowed: 734,
        reviewed: 890
      })),
      byHour: Array.from({ length: 24 }, (_, hour) => ({
        hour,
        count: hour >= 9 && hour <= 17 ? Math.floor(Math.random() * 50) + 100 : Math.floor(Math.random() * 20) + 10,
        avgConfidence: Math.floor(Math.random() * 20) + 60
      })),
      byDayOfWeek: Array.from({ length: 7 }, (_, day) => ({
        day,
        count: day === 0 || day === 6 ? Math.floor(Math.random() * 100) + 50 : Math.floor(Math.random() * 200) + 150,
        avgConfidence: Math.floor(Math.random() * 15) + 65
      }))
    },
    senders: {
      topBlocked: [
        { sender: '<EMAIL>', domain: 'techpro.com', count: 234, lastSeen: new Date(), reputation: 25 },
        { sender: '<EMAIL>', domain: 'webservices.biz', count: 189, lastSeen: new Date(), reputation: 18 },
        { sender: '<EMAIL>', domain: 'spammy.com', count: 156, lastSeen: new Date(), reputation: 12 },
        { sender: '<EMAIL>', domain: 'coldmail.net', count: 134, lastSeen: new Date(), reputation: 20 },
        { sender: '<EMAIL>', domain: 'salespro.io', count: 98, lastSeen: new Date(), reputation: 15 }
      ],
      topReported: [
        { sender: '<EMAIL>', domain: 'marketer.com', count: 67, lastSeen: new Date(), reputation: 10 },
        { sender: '<EMAIL>', domain: 'sales.net', count: 45, lastSeen: new Date(), reputation: 22 },
        { sender: '<EMAIL>', domain: 'emailblast.io', count: 34, lastSeen: new Date(), reputation: 8 }
      ],
      topDomains: [
        { domain: 'spammy.com', count: 456, senderCount: 23, avgReputation: 15, blocked: 412 },
        { domain: 'coldmail.net', count: 389, senderCount: 18, avgReputation: 20, blocked: 350 },
        { domain: 'salespro.io', count: 267, senderCount: 12, avgReputation: 18, blocked: 245 }
      ],
      newSenders: 145,
      repeatOffenders: 67
    },
    effectiveness: {
      detectionRate: 92.5,
      blockRate: 67.6,
      reviewRate: 31.3,
      userSatisfaction: 4.7,
      timesSaved: 47.5,
      productivityGain: 23.4
    },
    mlPerformance: {
      accuracy: 94.3,
      precision: 91.2,
      recall: 88.7,
      f1Score: 89.9,
      improvements: [
        {
          date: new Date('2024-02-15'),
          version: 'v2.3.1',
          accuracyBefore: 92.1,
          accuracyAfter: 94.3,
          samplesUsed: 5000
        },
        {
          date: new Date('2024-01-28'),
          version: 'v2.3.0',
          accuracyBefore: 89.5,
          accuracyAfter: 92.1,
          samplesUsed: 3500
        }
      ]
    },
    categories: {
      sales: 987,
      marketing: 654,
      recruiting: 234,
      phishing: 45,
      other: 927
    }
  };
};

// Mock data storage
let detections = generateColdEmailDetections(8);
let blockedSenders = generateBlockedSenders();
let whitelistedSenders = generateWhitelistedSenders();
let analytics = generateAnalytics();
let settings: ColdEmailSettings = {
  enabled: true,
  sensitivity: 'balanced',
  detection: {
    mlEnabled: true,
    mlThreshold: 60,
    reputationEnabled: true,
    reputationThreshold: 30,
    contentAnalysisEnabled: true,
    behavioralAnalysisEnabled: true,
    customRulesEnabled: true
  },
  actions: {
    autoBlock: false,
    autoBlockThreshold: 80,
    moveToSpam: true,
    deleteAfterDays: 30,
    notifyOnDetection: true,
    requireReview: true
  },
  filters: {
    timeBasedFiltering: [],
    geographicFiltering: [],
    industryFilters: [],
    customFilters: []
  },
  learning: {
    enableLearning: true,
    requireMinSamples: 100,
    autoUpdateModel: true,
    shareAnonymousData: false
  },
  integration: {
    webhooksEnabled: false,
    apiAccess: true
  }
};

// Handlers
export const coldEmailHandlers = [
  // Get detections
  http.get('/api/cold-email/detections', async () => {
    await delay(500);
    
    return HttpResponse.json({
      detections: detections.filter(d => d.status === 'pending'),
      total: detections.length,
      pending: detections.filter(d => d.status === 'pending').length,
      reviewed: detections.filter(d => d.status === 'reviewed').length
    });
  }),
  
  // Review detection
  http.post('/api/cold-email/detections/:id/review', async ({ params, request }) => {
    await delay(300);
    
    const { id } = params;
    const body = await request.json() as { decision: string; feedback?: any };
    
    const detectionIndex = detections.findIndex(d => d.id === id);
    if (detectionIndex !== -1) {
      detections[detectionIndex] = {
        ...detections[detectionIndex],
        status: body.decision === 'block' ? 'blocked' : body.decision === 'whitelist' ? 'whitelisted' : 'allowed',
        review: {
          reviewedAt: new Date(),
          reviewedBy: '<EMAIL>',
          decision: body.decision as any,
          feedback: body.feedback
        }
      };
      
      // Update analytics
      analytics.detection.reviewed++;
      if (body.decision === 'block') {
        analytics.detection.blocked++;
      } else {
        analytics.detection.allowed++;
      }
      
      // If whitelisting, add to whitelist
      if (body.decision === 'whitelist') {
        const detection = detections[detectionIndex];
        whitelistedSenders.push({
          id: `whitelist-${Date.now()}`,
          email: detection.email.from,
          type: 'email',
          reason: 'Whitelisted from review queue',
          whitelistedAt: new Date(),
          whitelistedBy: '<EMAIL>',
          statistics: {
            emailsAllowed: 1,
            lastReceived: new Date()
          }
        });
      }
    }
    
    return HttpResponse.json({ success: true });
  }),
  
  // Bulk review
  http.post('/api/cold-email/detections/bulk-review', async ({ request }) => {
    await delay(500);
    
    const body = await request.json() as { ids: string[]; decision: string };
    
    body.ids.forEach(id => {
      const detectionIndex = detections.findIndex(d => d.id === id);
      if (detectionIndex !== -1) {
        detections[detectionIndex] = {
          ...detections[detectionIndex],
          status: body.decision === 'block' ? 'blocked' : 'allowed',
          review: {
            reviewedAt: new Date(),
            reviewedBy: '<EMAIL>',
            decision: body.decision as any
          }
        };
      }
    });
    
    // Update analytics
    analytics.detection.reviewed += body.ids.length;
    if (body.decision === 'block') {
      analytics.detection.blocked += body.ids.length;
    } else {
      analytics.detection.allowed += body.ids.length;
    }
    
    return HttpResponse.json({ 
      success: true,
      processed: body.ids.length
    });
  }),
  
  // Get blocked senders
  http.get('/api/cold-email/blocklist', async () => {
    await delay(300);
    
    return HttpResponse.json({
      senders: blockedSenders,
      total: blockedSenders.length
    });
  }),
  
  // Add blocked sender
  http.post('/api/cold-email/blocklist', async ({ request }) => {
    await delay(300);
    
    const body = await request.json() as Partial<BlockedSender>;
    
    const newSender: BlockedSender = {
      id: `blocked-${Date.now()}`,
      ...body as any,
      blockedAt: new Date(),
      blockedBy: '<EMAIL>',
      statistics: {
        emailsBlocked: 0,
        reportCount: 0
      }
    };
    
    blockedSenders.push(newSender);
    
    return HttpResponse.json(newSender);
  }),
  
  // Remove blocked sender
  http.delete('/api/cold-email/blocklist/:id', async ({ params }) => {
    await delay(300);
    
    const { id } = params;
    blockedSenders = blockedSenders.filter(s => s.id !== id);
    
    return HttpResponse.json({ success: true });
  }),
  
  // Get whitelisted senders
  http.get('/api/cold-email/whitelist', async () => {
    await delay(300);
    
    return HttpResponse.json({
      senders: whitelistedSenders,
      total: whitelistedSenders.length
    });
  }),
  
  // Add whitelisted sender
  http.post('/api/cold-email/whitelist', async ({ request }) => {
    await delay(300);
    
    const body = await request.json() as Partial<WhitelistedSender>;
    
    const newSender: WhitelistedSender = {
      id: `whitelist-${Date.now()}`,
      ...body as any,
      whitelistedAt: new Date(),
      whitelistedBy: '<EMAIL>',
      statistics: {
        emailsAllowed: 0,
        lastReceived: new Date()
      }
    };
    
    whitelistedSenders.push(newSender);
    
    return HttpResponse.json(newSender);
  }),
  
  // Remove whitelisted sender
  http.delete('/api/cold-email/whitelist/:id', async ({ params }) => {
    await delay(300);
    
    const { id } = params;
    whitelistedSenders = whitelistedSenders.filter(s => s.id !== id);
    
    return HttpResponse.json({ success: true });
  }),
  
  // Get analytics
  http.get('/api/cold-email/analytics', async () => {
    await delay(500);
    
    return HttpResponse.json(analytics);
  }),
  
  // Get settings
  http.get('/api/cold-email/settings', async () => {
    await delay(200);
    
    return HttpResponse.json(settings);
  }),
  
  // Update settings
  http.put('/api/cold-email/settings', async ({ request }) => {
    await delay(300);
    
    const body = await request.json() as ColdEmailSettings;
    settings = body;
    
    return HttpResponse.json({ success: true, settings });
  }),
  
  // Import senders
  http.post('/api/cold-email/import', async ({ request }) => {
    await delay(1000);
    
    // Simulate file upload processing
    const formData = await request.formData();
    const type = formData.get('type') as string;
    
    // Add some mock imported senders
    if (type === 'blocked') {
      blockedSenders.push({
        id: `blocked-import-${Date.now()}`,
        email: '<EMAIL>',
        type: 'email',
        reason: 'Imported from CSV',
        blockedAt: new Date(),
        blockedBy: '<EMAIL>',
        statistics: {
          emailsBlocked: 0,
          reportCount: 0
        }
      });
    } else {
      whitelistedSenders.push({
        id: `whitelist-import-${Date.now()}`,
        email: '<EMAIL>',
        type: 'email',
        reason: 'Imported from CSV',
        whitelistedAt: new Date(),
        whitelistedBy: '<EMAIL>',
        statistics: {
          emailsAllowed: 0,
          lastReceived: new Date()
        }
      });
    }
    
    return HttpResponse.json({ 
      success: true,
      imported: 1
    });
  }),
  
  // Export senders
  http.get('/api/cold-email/export', async ({ request }) => {
    await delay(500);
    
    const url = new URL(request.url);
    const type = url.searchParams.get('type');
    
    // Generate CSV content
    const senders = type === 'blocked' ? blockedSenders : whitelistedSenders;
    const headers = ['Type', 'Value', 'Reason', 'Date'];
    const rows = senders.map(s => [
      s.type,
      s.email || s.domain || s.pattern || '',
      s.reason,
      type === 'blocked' ? s.blockedAt.toISOString() : (s as any).whitelistedAt.toISOString()
    ]);
    
    const csv = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');
    
    return new HttpResponse(csv, {
      headers: {
        'Content-Type': 'text/csv',
        'Content-Disposition': `attachment; filename="${type}-senders.csv"`
      }
    });
  }),
  
  // Train model with feedback
  http.post('/api/cold-email/train', async ({ request }) => {
    await delay(1000);
    
    const body = await request.json() as TrainingData;
    
    // Simulate model training
    analytics.mlPerformance.accuracy += 0.1;
    analytics.mlPerformance.improvements.push({
      date: new Date(),
      version: `v2.3.${analytics.mlPerformance.improvements.length + 2}`,
      accuracyBefore: analytics.mlPerformance.accuracy - 0.1,
      accuracyAfter: analytics.mlPerformance.accuracy,
      samplesUsed: 100
    });
    
    return HttpResponse.json({ 
      success: true,
      newAccuracy: analytics.mlPerformance.accuracy
    });
  }),
  
  // Real-time detection simulation
  http.post('/api/cold-email/detect', async ({ request }) => {
    await delay(200);
    
    const body = await request.json() as { emailId: string };
    
    // Simulate real-time detection
    const newDetection = generateColdEmailDetections(1)[0];
    newDetection.emailId = body.emailId;
    newDetection.detectedAt = new Date();
    
    detections.unshift(newDetection);
    analytics.detection.total++;
    
    return HttpResponse.json({
      detection: newDetection,
      shouldBlock: newDetection.confidence > settings.actions.autoBlockThreshold && settings.actions.autoBlock
    });
  }),
  
  // Get audit logs
  http.get('/api/cold-email/audit', async ({ request }) => {
    await delay(300);
    
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '50');
    
    const logs = Array.from({ length: limit }, (_, i) => ({
      id: `audit-${i + 1}`,
      timestamp: new Date(Date.now() - i * 60 * 60 * 1000),
      action: ['block_sender', 'whitelist_sender', 'review_email', 'update_settings'][i % 4],
      entityType: ['detection', 'sender', 'setting'][i % 3],
      entityId: `entity-${i}`,
      userId: '<EMAIL>',
      details: {
        before: { status: 'pending' },
        after: { status: 'blocked' }
      }
    }));
    
    return HttpResponse.json({
      logs,
      total: logs.length
    });
  })
];