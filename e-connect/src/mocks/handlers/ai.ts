import { http, HttpResponse, delay } from 'msw';
import { 
  generateMockCategorizationResult,
  generateMockRuleSuggestion,
  generateMockChatResponse,
  getProcessingDelay,
  generateMockPerformanceMetrics,
  MOCK_RULE_SUGGESTIONS,
  MOCK_ANALYSIS_INSIGHTS,
  getRandomInsight,
  getRandomSuggestion,
  shouldSimulateError,
  simulateProgressiveResponse
} from '../../utils/ai/mock-responses';
import { EmailCategory, ParsedMessage } from '../../types/email';
import { Rule } from '../../types/rules';
import { AIResponse, ChatMessage, Suggestion } from '../../types/assistant';

// In-memory storage for learning and feedback
let categorizationFeedback: Array<{
  messageId: string;
  originalCategory: EmailCategory;
  correctedCategory: EmailCategory;
  timestamp: Date;
}> = [];

let ruleSuggestionFeedback: Array<{
  suggestionId: string;
  feedback: 'accepted' | 'rejected' | 'modified';
  modifications?: any;
  timestamp: Date;
}> = [];

let conversationHistory: Map<string, ChatMessage[]> = new Map();

/**
 * AI Categorization Endpoints
 */

// POST /api/ai/categorize - Categorize emails
export const categorizeSingleEmail = http.post('/api/ai/categorize', async ({ request }) => {
  const body = await request.json() as {
    email: ParsedMessage;
    options?: {
      model?: string;
      contextEmails?: ParsedMessage[];
      confidenceThreshold?: number;
    };
  };

  // Simulate processing delay
  await delay(getProcessingDelay('categorization'));

  // Simulate error occasionally
  if (shouldSimulateError(body.options?.model)) {
    return HttpResponse.json(
      { 
        error: 'AI_SERVICE_ERROR',
        message: 'Temporary categorization service unavailable. Please try again.',
        code: 'CATEGORIZATION_FAILED'
      },
      { status: 503 }
    );
  }

  const result = generateMockCategorizationResult(body.email);
  
  return HttpResponse.json({
    success: true,
    result,
    metadata: {
      model: body.options?.model || 'gpt-4-turbo',
      processingTime: result.processingTime,
      cost: 0.002,
      requestId: `req_${Date.now()}`
    }
  });
});

// POST /api/ai/categorize/batch - Batch categorize emails
export const categorizeBatchEmails = http.post('/api/ai/categorize/batch', async ({ request }) => {
  const body = await request.json() as {
    emails: ParsedMessage[];
    options?: {
      model?: string;
      batchSize?: number;
      enableLearning?: boolean;
    };
  };

  // Simulate processing delay based on batch size
  const baseDelay = getProcessingDelay('categorization');
  const batchDelay = Math.min(body.emails.length * 50, 5000); // Max 5 seconds
  await delay(baseDelay + batchDelay);

  const results = new Map<string, any>();
  const errors = [];

  for (const email of body.emails) {
    try {
      const result = generateMockCategorizationResult(email);
      results.set(email.id, result);
    } catch (error) {
      errors.push({
        emailId: email.id,
        error: 'CATEGORIZATION_FAILED',
        message: 'Failed to categorize this specific email'
      });
    }
  }

  return HttpResponse.json({
    success: true,
    results: Object.fromEntries(results),
    errors,
    summary: {
      total: body.emails.length,
      successful: results.size,
      failed: errors.length,
      processingTime: baseDelay + batchDelay
    },
    metadata: {
      model: body.options?.model || 'gpt-4-turbo',
      batchSize: body.options?.batchSize || 20,
      cost: results.size * 0.001
    }
  });
});

/**
 * AI Rule Suggestion Endpoints
 */

// POST /api/ai/suggest-rules - Get rule suggestions based on email patterns
export const suggestRules = http.post('/api/ai/suggest-rules', async ({ request }) => {
  const body = await request.json() as {
    emails: ParsedMessage[];
    context: {
      timeframe: { start: Date; end: Date };
      userBehavior?: any;
      existingRules?: Rule[];
      focusAreas?: string[];
    };
    options?: {
      model?: string;
      suggestionLimit?: number;
      confidenceThreshold?: number;
      includeComplexRules?: boolean;
    };
  };

  // Simulate processing delay
  await delay(getProcessingDelay('rule_suggestion'));

  // Generate suggestions based on email patterns
  const suggestions = [];
  const suggestionLimit = body.options?.suggestionLimit || 5;
  const confidenceThreshold = body.options?.confidenceThreshold || 0.6;

  // Use predefined suggestions and customize them
  for (let i = 0; i < Math.min(suggestionLimit, MOCK_RULE_SUGGESTIONS.length); i++) {
    const baseSuggestion = MOCK_RULE_SUGGESTIONS[i];
    const customizedSuggestion = {
      ...baseSuggestion,
      id: `suggestion_${Date.now()}_${i}`,
      impact: {
        ...baseSuggestion.impact,
        emailsAffected: Math.floor(body.emails.length * (Math.random() * 0.3 + 0.1)), // 10-40% of emails
        timesSaved: Math.floor(Math.random() * 25) + 5 // 5-30 minutes
      }
    };

    if (customizedSuggestion.confidence >= confidenceThreshold) {
      suggestions.push(customizedSuggestion);
    }
  }

  return HttpResponse.json({
    success: true,
    suggestions,
    analysis: {
      patternsFound: suggestions.length + Math.floor(Math.random() * 5),
      totalEmails: body.emails.length,
      automationPotential: Math.random() * 0.4 + 0.3, // 30-70%
      estimatedTimesSavings: suggestions.reduce((sum, s) => sum + s.impact.timesSaved, 0)
    },
    insights: [
      getRandomInsight('volume'),
      getRandomInsight('senders'),
      getRandomInsight('productivity')
    ],
    metadata: {
      model: body.options?.model || 'gpt-4-turbo',
      processingTime: getProcessingDelay('rule_suggestion'),
      cost: 0.008
    }
  });
});

// POST /api/ai/analyze-rule-opportunity - Analyze a specific rule opportunity
export const analyzeRuleOpportunity = http.post('/api/ai/analyze-rule-opportunity', async ({ request }) => {
  const body = await request.json() as {
    pattern: any;
    emails: ParsedMessage[];
    context: any;
  };

  await delay(800);

  const viability = Math.random() * 0.4 + 0.6; // 60-100%
  const affectedEmails = Math.floor(body.emails.length * (Math.random() * 0.3 + 0.1));

  return HttpResponse.json({
    success: true,
    analysis: {
      viability,
      impact: {
        emailsAffected: affectedEmails,
        timesSaved: Math.floor(affectedEmails * 0.5), // 30 seconds per email
        riskLevel: viability > 0.8 ? 'low' : viability > 0.6 ? 'medium' : 'high'
      },
      suggestedConditions: [
        {
          id: 'cond_1',
          type: 'from',
          operator: 'contains',
          value: body.pattern.sender || 'example.com'
        }
      ],
      suggestedActions: [
        {
          id: 'action_1',
          type: 'label',
          params: { label: 'Auto-organized' },
          order: 1
        }
      ],
      risks: viability < 0.7 ? ['May catch unintended emails', 'Consider testing with smaller scope'] : [],
      benefits: [
        'Reduces manual email sorting',
        'Consistent organization',
        'Time savings'
      ],
      implementation: {
        difficulty: viability > 0.8 ? 'easy' : 'medium',
        timeRequired: Math.floor(Math.random() * 10) + 5, // 5-15 minutes
        testingSteps: [
          'Create rule with test conditions',
          'Apply to small email sample',
          'Verify accuracy',
          'Enable for all emails'
        ]
      }
    }
  });
});

/**
 * Conversational AI Endpoints
 */

// POST /api/ai/chat - Conversational AI interactions
export const chatWithAI = http.post('/api/ai/chat', async ({ request }) => {
  const body = await request.json() as {
    message: string;
    conversationId?: string;
    context?: {
      selectedEmails?: string[];
      currentScreen?: string;
      userIntent?: string;
    };
    options?: {
      model?: string;
      temperature?: number;
      stream?: boolean;
    };
  };

  const conversationId = body.conversationId || `conv_${Date.now()}`;
  
  // Get conversation history
  const history = conversationHistory.get(conversationId) || [];
  
  // Simulate processing delay
  await delay(getProcessingDelay('chat_response'));

  // Generate response
  const aiResponse = generateMockChatResponse(body.message, body.context);
  
  // Create chat messages
  const userMessage: ChatMessage = {
    id: `msg_${Date.now()}`,
    role: 'user',
    content: body.message,
    timestamp: new Date()
  };

  const assistantMessage: ChatMessage = {
    id: `msg_${Date.now() + 1}`,
    role: 'assistant',
    content: aiResponse.response,
    timestamp: new Date(),
    metadata: {
      model: body.options?.model || 'gpt-4-turbo',
      tokens: aiResponse.tokens.total,
      processingTime: aiResponse.latency,
      confidence: aiResponse.confidence
    }
  };

  // Update conversation history
  history.push(userMessage, assistantMessage);
  conversationHistory.set(conversationId, history.slice(-20)); // Keep last 20 messages

  // Generate contextual suggestions
  const suggestions: Suggestion[] = [];
  if (body.message.toLowerCase().includes('rule')) {
    const ruleSuggestion = getRandomSuggestion('automation');
    if (ruleSuggestion) suggestions.push(ruleSuggestion);
  }
  if (body.message.toLowerCase().includes('organize')) {
    const orgSuggestion = getRandomSuggestion('organization');
    if (orgSuggestion) suggestions.push(orgSuggestion);
  }

  const response = {
    success: true,
    response: aiResponse,
    conversationId,
    suggestions,
    metadata: {
      messageCount: history.length,
      model: body.options?.model || 'gpt-4-turbo',
      cost: aiResponse.cost
    }
  };

  // Handle streaming response
  if (body.options?.stream) {
    // For streaming, we would return a ReadableStream
    // For now, just return the full response
    return HttpResponse.json(response);
  }

  return HttpResponse.json(response);
});

// POST /api/ai/chat/create-rule - Create rule from natural language
export const createRuleFromChat = http.post('/api/ai/chat/create-rule', async ({ request }) => {
  const body = await request.json() as {
    description: string;
    context?: {
      exampleEmails?: ParsedMessage[];
      userPreferences?: any;
    };
    options?: {
      model?: string;
      confidenceThreshold?: number;
    };
  };

  await delay(1200);

  // Parse rule from description (simplified)
  const rule: Partial<Rule> = {
    name: `Auto-rule: ${body.description}`,
    description: body.description,
    enabled: false, // Start disabled for safety
    priority: 1,
    conditions: [
      {
        id: 'cond_1',
        type: 'subject',
        operator: 'contains',
        value: 'newsletter' // Simplified parsing
      }
    ],
    conditionLogic: 'all',
    actions: [
      {
        id: 'action_1',
        type: 'archive',
        order: 1
      }
    ],
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const confidence = Math.random() * 0.3 + 0.6; // 60-90%
  const clarifications = confidence < 0.7 ? [
    'Which senders should this rule apply to?',
    'Should this apply to emails from the last month only?'
  ] : [];

  return HttpResponse.json({
    success: true,
    rule,
    confidence,
    clarifications,
    preview: {
      matchingEmails: Math.floor(Math.random() * 50) + 10,
      potentialIssues: confidence < 0.8 ? ['Rule may be too broad'] : [],
      suggestedImprovements: [
        'Consider adding sender filter',
        'Test with small sample first'
      ]
    },
    metadata: {
      model: body.options?.model || 'gpt-4-turbo',
      processingTime: 1200,
      cost: 0.005
    }
  });
});

/**
 * Email Analysis Endpoints
 */

// POST /api/ai/analyze - Email analysis and insights
export const analyzeEmails = http.post('/api/ai/analyze', async ({ request }) => {
  const body = await request.json() as {
    emails: ParsedMessage[];
    analysisType: 'summary' | 'patterns' | 'insights' | 'optimization' | 'custom';
    query?: string;
    options?: {
      model?: string;
      depth?: 'quick' | 'standard' | 'comprehensive';
    };
  };

  await delay(getProcessingDelay('email_analysis'));

  const analysis = {
    summary: '',
    insights: [] as string[],
    suggestions: [] as Suggestion[],
    actionableItems: [] as any[],
    visualizations: [] as any[]
  };

  switch (body.analysisType) {
    case 'summary':
      analysis.summary = `Analyzed ${body.emails.length} emails. Found ${Math.floor(body.emails.length * 0.3)} actionable items and ${Math.floor(body.emails.length * 0.15)} automation opportunities.`;
      break;

    case 'patterns':
      analysis.insights = [
        getRandomInsight('senders'),
        getRandomInsight('categories'),
        getRandomInsight('behavior')
      ];
      break;

    case 'insights':
      analysis.insights = [
        getRandomInsight('volume'),
        getRandomInsight('productivity'),
        getRandomInsight('behavior')
      ];
      analysis.suggestions = [
        getRandomSuggestion('automation'),
        getRandomSuggestion('organization')
      ].filter(Boolean) as Suggestion[];
      break;

    case 'optimization':
      analysis.summary = 'Found several optimization opportunities based on your email patterns.';
      analysis.suggestions = [
        getRandomSuggestion('automation'),
        getRandomSuggestion('unsubscribe'),
        getRandomSuggestion('organization')
      ].filter(Boolean) as Suggestion[];
      break;

    case 'custom':
      analysis.summary = `Custom analysis for query: "${body.query}"`;
      analysis.insights = [getRandomInsight('volume')];
      break;
  }

  return HttpResponse.json({
    success: true,
    analysis,
    metadata: {
      analysisType: body.analysisType,
      emailsAnalyzed: body.emails.length,
      model: body.options?.model || 'gpt-4-turbo',
      processingTime: getProcessingDelay('email_analysis'),
      cost: 0.006
    }
  });
});

// POST /api/ai/extract - Extract data from emails
export const extractDataFromEmails = http.post('/api/ai/extract', async ({ request }) => {
  const body = await request.json() as {
    emails: ParsedMessage[];
    extractionType: 'contacts' | 'events' | 'tasks' | 'orders' | 'custom';
    schema?: any;
    options?: {
      model?: string;
      confidence?: number;
    };
  };

  await delay(1500);

  const extractedData: any = {};
  const confidence: any = {};

  switch (body.extractionType) {
    case 'contacts':
      body.emails.forEach((email, index) => {
        extractedData[`contact_${index}`] = {
          name: email.from.name || 'Unknown',
          email: email.from.email,
          domain: email.from.email.split('@')[1],
          frequency: Math.floor(Math.random() * 10) + 1
        };
        confidence[`contact_${index}`] = Math.random() * 0.3 + 0.7;
      });
      break;

    case 'events':
      const eventEmails = body.emails.filter(email => 
        email.subject.toLowerCase().includes('meeting') || 
        email.subject.toLowerCase().includes('event')
      );
      eventEmails.forEach((email, index) => {
        extractedData[`event_${index}`] = {
          title: email.subject,
          date: new Date(email.date.getTime() + 24 * 60 * 60 * 1000), // Tomorrow
          organizer: email.from.email,
          type: 'meeting'
        };
        confidence[`event_${index}`] = 0.85;
      });
      break;

    case 'orders':
      const orderEmails = body.emails.filter(email =>
        email.subject.toLowerCase().includes('order') ||
        email.subject.toLowerCase().includes('receipt')
      );
      orderEmails.forEach((email, index) => {
        extractedData[`order_${index}`] = {
          orderId: `ORD-${Math.random().toString(36).substr(2, 9)}`,
          vendor: email.from.email.split('@')[1],
          amount: `$${(Math.random() * 200 + 10).toFixed(2)}`,
          date: email.date
        };
        confidence[`order_${index}`] = 0.9;
      });
      break;
  }

  return HttpResponse.json({
    success: true,
    extracted: extractedData,
    confidence,
    summary: {
      totalItems: Object.keys(extractedData).length,
      averageConfidence: Object.values(confidence).reduce((a: any, b: any) => a + b, 0) / Object.keys(confidence).length,
      extractionType: body.extractionType
    },
    metadata: {
      model: body.options?.model || 'gpt-4-turbo',
      processingTime: 1500,
      cost: 0.004
    }
  });
});

/**
 * Learning and Feedback Endpoints
 */

// POST /api/ai/feedback - Submit feedback for AI learning
export const submitFeedback = http.post('/api/ai/feedback', async ({ request }) => {
  const body = await request.json() as {
    type: 'categorization' | 'rule_suggestion' | 'chat_response' | 'analysis';
    itemId: string;
    feedback: 'helpful' | 'not_helpful' | 'incorrect' | 'correct';
    corrections?: any;
    comment?: string;
  };

  await delay(200);

  // Store feedback for learning
  switch (body.type) {
    case 'categorization':
      if (body.corrections) {
        categorizationFeedback.push({
          messageId: body.itemId,
          originalCategory: body.corrections.original,
          correctedCategory: body.corrections.corrected,
          timestamp: new Date()
        });
      }
      break;

    case 'rule_suggestion':
      ruleSuggestionFeedback.push({
        suggestionId: body.itemId,
        feedback: body.feedback === 'helpful' ? 'accepted' : 'rejected',
        modifications: body.corrections,
        timestamp: new Date()
      });
      break;
  }

  return HttpResponse.json({
    success: true,
    message: 'Feedback recorded successfully',
    learningStats: {
      totalFeedback: categorizationFeedback.length + ruleSuggestionFeedback.length,
      improvementRate: Math.random() * 0.1 + 0.05, // 5-15% improvement
      lastUpdate: new Date()
    }
  });
});

// GET /api/ai/performance - Get AI performance metrics
export const getPerformanceMetrics = http.get('/api/ai/performance', async () => {
  await delay(300);

  const metrics = generateMockPerformanceMetrics();

  return HttpResponse.json({
    success: true,
    metrics: {
      ...metrics,
      categorization: {
        accuracy: 0.89,
        totalClassifications: categorizationFeedback.length + 1247,
        feedbackReceived: categorizationFeedback.length,
        lastImprovement: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
      },
      ruleSuggestions: {
        acceptanceRate: 0.73,
        totalSuggestions: ruleSuggestionFeedback.length + 156,
        feedbackReceived: ruleSuggestionFeedback.length,
        averageImpact: 18.5 // minutes saved per week
      },
      chat: {
        responseQuality: 0.86,
        averageResponseTime: 1200,
        conversationsHandled: conversationHistory.size + 89,
        userSatisfaction: 0.82
      }
    },
    trends: {
      accuracy: [0.85, 0.87, 0.89], // Last 3 months
      performance: [850, 820, 800], // Response times
      usage: [120, 145, 167] // Daily requests
    }
  });
});

// GET /api/ai/models - Get available AI models
export const getAvailableModels = http.get('/api/ai/models', async () => {
  await delay(150);

  return HttpResponse.json({
    success: true,
    models: [
      {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        provider: 'OpenAI',
        capabilities: ['chat', 'analysis', 'classification', 'function-calling'],
        pricing: { input: 0.01, output: 0.03 },
        limits: { maxTokens: 4096, contextLength: 128000 },
        available: true,
        recommended: true
      },
      {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        provider: 'OpenAI',
        capabilities: ['chat', 'classification'],
        pricing: { input: 0.0015, output: 0.002 },
        limits: { maxTokens: 4096, contextLength: 16385 },
        available: true,
        recommended: false
      },
      {
        id: 'claude-3-opus',
        name: 'Claude 3 Opus',
        provider: 'Anthropic',
        capabilities: ['chat', 'analysis', 'reasoning', 'long-context'],
        pricing: { input: 0.015, output: 0.075 },
        limits: { maxTokens: 4096, contextLength: 200000 },
        available: true,
        recommended: true
      },
      {
        id: 'claude-3-haiku',
        name: 'Claude 3 Haiku',
        provider: 'Anthropic',
        capabilities: ['chat', 'classification', 'speed'],
        pricing: { input: 0.00025, output: 0.00125 },
        limits: { maxTokens: 4096, contextLength: 200000 },
        available: true,
        recommended: false
      }
    ]
  });
});

/**
 * Bulk Operations with AI
 */

// POST /api/ai/bulk/categorize - Bulk categorize large email sets
export const bulkCategorize = http.post('/api/ai/bulk/categorize', async ({ request }) => {
  const body = await request.json() as {
    emailIds: string[];
    options?: {
      model?: string;
      batchSize?: number;
      priority?: 'low' | 'normal' | 'high';
    };
  };

  // Simulate long-running operation
  await delay(Math.min(body.emailIds.length * 100, 8000));

  const results = body.emailIds.map(id => ({
    emailId: id,
    category: ['Newsletter', 'Marketing', 'Work', 'Personal', 'Receipt'][Math.floor(Math.random() * 5)] as EmailCategory,
    confidence: Math.random() * 0.3 + 0.7,
    processingTime: Math.random() * 200 + 100
  }));

  return HttpResponse.json({
    success: true,
    jobId: `job_${Date.now()}`,
    results,
    summary: {
      total: body.emailIds.length,
      processed: results.length,
      averageConfidence: results.reduce((sum, r) => sum + r.confidence, 0) / results.length,
      totalProcessingTime: results.reduce((sum, r) => sum + r.processingTime, 0)
    },
    metadata: {
      model: body.options?.model || 'gpt-4-turbo',
      batchSize: body.options?.batchSize || 50,
      cost: results.length * 0.001
    }
  });
});

// GET /api/ai/bulk/status/:jobId - Check bulk operation status
export const getBulkOperationStatus = http.get('/api/ai/bulk/status/:jobId', async ({ params }) => {
  await delay(100);

  const progress = Math.random();
  const isComplete = progress > 0.9;

  return HttpResponse.json({
    success: true,
    jobId: params.jobId,
    status: isComplete ? 'completed' : 'in_progress',
    progress: Math.round(progress * 100),
    estimatedTimeRemaining: isComplete ? 0 : Math.round((1 - progress) * 30000), // milliseconds
    results: isComplete ? {
      processed: 150,
      successful: 147,
      failed: 3,
      errors: []
    } : null
  });
});

export const aiHandlers = [
  categorizeSingleEmail,
  categorizeBatchEmails,
  suggestRules,
  analyzeRuleOpportunity,
  chatWithAI,
  createRuleFromChat,
  analyzeEmails,
  extractDataFromEmails,
  submitFeedback,
  getPerformanceMetrics,
  getAvailableModels,
  bulkCategorize,
  getBulkOperationStatus
];