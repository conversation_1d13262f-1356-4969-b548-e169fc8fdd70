import { emailHandlers } from './emails'
import { authHandlers } from './auth'
import { analyticsHandlers } from './analytics'
import { assistantHandlers } from './assistant'
import { bulkHandlers } from './bulk'
import { rulesHandlers } from './rules'
import { settingsHandlers } from './settings'
import { aiHandlers } from './ai'
import { coldEmailHandlers } from './cold-email'
import { progressTrackerHandlers } from './progress-tracker'
import { multiAccountHandlers } from './multi-account'

export const handlers = [
  ...authHandlers,
  ...emailHandlers,
  ...analyticsHandlers,
  ...assistantHandlers,
  ...bulkHandlers,
  ...rulesHandlers,
  ...settingsHandlers,
  ...aiHandlers,
  ...coldEmailHandlers,
  ...progressTrackerHandlers,
  ...multiAccountHandlers,
]