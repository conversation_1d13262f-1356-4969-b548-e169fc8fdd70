import { http, HttpResponse } from 'msw'
import { mockUser } from '../data/users'

export const authHandlers = [
  // Get current session
  http.get('/api/auth/session', () => {
    const isAuthenticated = localStorage.getItem('auth-token') !== null
    
    if (!isAuthenticated) {
      return HttpResponse.json(null, { status: 401 })
    }
    
    return HttpResponse.json({
      user: mockUser,
      expires: new Date(Date.now() + ********).toISOString(), // 24 hours
    })
  }),

  // Login
  http.post('/api/auth/login', async ({ request }) => {
    const { email, password } = await request.json() as { email: string; password: string }
    
    // Mock validation
    if (email === '<EMAIL>' && password === 'password') {
      const token = `mock-token-${Date.now()}`
      
      // Store token in localStorage for demo
      localStorage.setItem('auth-token', token)
      
      return HttpResponse.json({
        user: mockUser,
        token,
        expires: new Date(Date.now() + ********).toISOString(),
      })
    }
    
    return HttpResponse.json(
      { error: 'Invalid credentials' },
      { status: 401 }
    )
  }),

  // Logout
  http.post('/api/auth/logout', () => {
    localStorage.removeItem('auth-token')
    
    return HttpResponse.json({ success: true })
  }),

  // Register
  http.post('/api/auth/register', async ({ request }) => {
    const body = await request.json() as any
    
    // Mock validation
    if (!body.email || !body.password || !body.name) {
      return HttpResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    const newUser = {
      ...mockUser,
      id: `user-${Date.now()}`,
      email: body.email,
      name: body.name,
    }
    
    const token = `mock-token-${Date.now()}`
    localStorage.setItem('auth-token', token)
    
    return HttpResponse.json({
      user: newUser,
      token,
      expires: new Date(Date.now() + ********).toISOString(),
    }, { status: 201 })
  }),

  // Get current user
  http.get('/api/auth/me', () => {
    const isAuthenticated = localStorage.getItem('auth-token') !== null
    
    if (!isAuthenticated) {
      return HttpResponse.json(null, { status: 401 })
    }
    
    return HttpResponse.json(mockUser)
  }),

  // Update user profile
  http.patch('/api/auth/me', async ({ request }) => {
    const isAuthenticated = localStorage.getItem('auth-token') !== null
    
    if (!isAuthenticated) {
      return HttpResponse.json(null, { status: 401 })
    }
    
    const updates = await request.json() as any
    const updatedUser = { ...mockUser, ...updates }
    
    return HttpResponse.json(updatedUser)
  }),

  // OAuth endpoints
  http.get('/api/auth/google', () => {
    // Mock Google OAuth redirect
    return HttpResponse.json({
      authUrl: 'https://accounts.google.com/oauth/authorize?client_id=mock&redirect_uri=http://localhost:5173/auth/callback',
    })
  }),

  http.post('/api/auth/google/callback', async ({ request }) => {
    const { code } = await request.json() as { code: string }
    
    if (!code) {
      return HttpResponse.json(
        { error: 'Missing authorization code' },
        { status: 400 }
      )
    }
    
    // Mock successful OAuth
    const token = `google-token-${Date.now()}`
    localStorage.setItem('auth-token', token)
    
    return HttpResponse.json({
      user: mockUser,
      token,
      expires: new Date(Date.now() + ********).toISOString(),
    })
  }),

  // Refresh token
  http.post('/api/auth/refresh', async ({ request }) => {
    const { refreshToken } = await request.json() as { refreshToken: string }
    
    if (!refreshToken) {
      return HttpResponse.json(
        { error: 'Missing refresh token' },
        { status: 400 }
      )
    }
    
    const newToken = `refreshed-token-${Date.now()}`
    
    return HttpResponse.json({
      token: newToken,
      refreshToken: `refresh-${Date.now()}`,
      expires: new Date(Date.now() + ********).toISOString(),
    })
  }),

  // Request password reset
  http.post('/api/auth/forgot-password', async ({ request }) => {
    const { email } = await request.json() as { email: string }
    
    if (!email) {
      return HttpResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }
    
    return HttpResponse.json({
      message: 'Password reset email sent',
      success: true,
    })
  }),

  // Reset password
  http.post('/api/auth/reset-password', async ({ request }) => {
    const { token, password } = await request.json() as { token: string; password: string }
    
    if (!token || !password) {
      return HttpResponse.json(
        { error: 'Token and password are required' },
        { status: 400 }
      )
    }
    
    return HttpResponse.json({
      message: 'Password reset successful',
      success: true,
    })
  }),
]