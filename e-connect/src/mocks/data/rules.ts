import { Rule, RuleExecution, RuleTemplate, RuleGroup } from '@/types/rules';

export const mockRules: Rule[] = [
  {
    id: 'rule-1',
    name: 'Auto-archive newsletters',
    description: 'Automatically archive newsletter emails to keep inbox clean',
    enabled: true,
    priority: 1,
    conditions: [
      {
        id: 'cond-1',
        type: 'subject',
        operator: 'contains',
        value: ['newsletter', 'weekly digest', 'unsubscribe'],
        caseSensitive: false
      },
      {
        id: 'cond-2',
        type: 'hasAttachment',
        operator: 'equals',
        value: false
      }
    ],
    conditionLogic: 'any',
    actions: [
      {
        id: 'action-1',
        type: 'archive',
        order: 1
      },
      {
        id: 'action-2',
        type: 'label',
        value: 'newsletter',
        order: 2
      }
    ],
    stats: {
      totalMatches: 1250,
      totalActions: 2500,
      successfulActions: 2475,
      failedActions: 25,
      lastMatchDate: new Date('2024-12-15T10:30:00Z'),
      avgProcessingTime: 45,
      matchesByDay: {
        '2024-12-15': 15,
        '2024-12-14': 22,
        '2024-12-13': 18,
        '2024-12-12': 12,
        '2024-12-11': 20
      },
      actionsByType: {
        archive: 1250,
        label: 1225,
        delete: 0,
        markRead: 0,
        markUnread: 0,
        star: 0,
        unstar: 0,
        removeLabel: 0,
        categorize: 0,
        forward: 0,
        reply: 0,
        snooze: 0,
        moveToFolder: 0,
        markImportant: 0,
        markNotImportant: 0,
        addToList: 0,
        createTask: 0,
        webhook: 0,
        notification: 0,
        aiProcess: 0,
        custom: 0
      },
      errorRate: 0.01,
      performance: {
        conditionEvalTime: 12,
        actionExecutionTime: 33,
        totalTime: 45
      }
    },
    createdAt: new Date('2024-11-01T09:00:00Z'),
    updatedAt: new Date('2024-12-01T14:30:00Z'),
    lastTriggered: new Date('2024-12-15T10:30:00Z'),
    createdBy: 'user-1',
    tags: ['productivity', 'email-management'],
    schedule: {
      type: 'immediate'
    }
  },
  {
    id: 'rule-2',
    name: 'VIP sender priority',
    description: 'Mark emails from VIP senders as important and notify immediately',
    enabled: true,
    priority: 0,
    conditions: [
      {
        id: 'cond-3',
        type: 'from',
        operator: 'in',
        value: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
        caseSensitive: false
      }
    ],
    conditionLogic: 'any',
    actions: [
      {
        id: 'action-3',
        type: 'markImportant',
        order: 1
      },
      {
        id: 'action-4',
        type: 'star',
        order: 2
      },
      {
        id: 'action-5',
        type: 'notification',
        params: {
          priority: 'high'
        },
        order: 3
      }
    ],
    stats: {
      totalMatches: 89,
      totalActions: 267,
      successfulActions: 267,
      failedActions: 0,
      lastMatchDate: new Date('2024-12-15T16:45:00Z'),
      avgProcessingTime: 23,
      matchesByDay: {
        '2024-12-15': 3,
        '2024-12-14': 2,
        '2024-12-13': 4,
        '2024-12-12': 1,
        '2024-12-11': 2
      },
      actionsByType: {
        markImportant: 89,
        star: 89,
        notification: 89,
        archive: 0,
        delete: 0,
        markRead: 0,
        markUnread: 0,
        unstar: 0,
        label: 0,
        removeLabel: 0,
        categorize: 0,
        forward: 0,
        reply: 0,
        snooze: 0,
        moveToFolder: 0,
        markNotImportant: 0,
        addToList: 0,
        createTask: 0,
        webhook: 0,
        aiProcess: 0,
        custom: 0
      },
      errorRate: 0.0,
      performance: {
        conditionEvalTime: 8,
        actionExecutionTime: 15,
        totalTime: 23
      }
    },
    createdAt: new Date('2024-10-15T11:20:00Z'),
    updatedAt: new Date('2024-11-15T09:10:00Z'),
    lastTriggered: new Date('2024-12-15T16:45:00Z'),
    createdBy: 'user-1',
    tags: ['vip', 'priority'],
    schedule: {
      type: 'immediate'
    }
  },
  {
    id: 'rule-3',
    name: 'Spam filter enhanced',
    description: 'Advanced spam detection using multiple criteria',
    enabled: true,
    priority: 2,
    conditions: [
      {
        id: 'cond-4',
        type: 'subject',
        operator: 'contains',
        value: ['URGENT', 'WINNER', 'CONGRATULATIONS', '!!!'],
        caseSensitive: false
      },
      {
        id: 'cond-5',
        type: 'body',
        operator: 'contains',
        value: ['click here now', 'limited time', 'act fast'],
        caseSensitive: false
      },
      {
        id: 'cond-6',
        type: 'from',
        operator: 'notContains',
        value: '@company.com'
      }
    ],
    conditionLogic: 'any',
    actions: [
      {
        id: 'action-6',
        type: 'delete',
        order: 1
      },
      {
        id: 'action-7',
        type: 'webhook',
        params: {
          url: 'https://api.security.company.com/spam-report',
          method: 'POST'
        },
        order: 2
      }
    ],
    stats: {
      totalMatches: 2156,
      totalActions: 4312,
      successfulActions: 4298,
      failedActions: 14,
      lastMatchDate: new Date('2024-12-15T18:22:00Z'),
      avgProcessingTime: 67,
      matchesByDay: {
        '2024-12-15': 28,
        '2024-12-14': 31,
        '2024-12-13': 25,
        '2024-12-12': 22,
        '2024-12-11': 27
      },
      actionsByType: {
        delete: 2156,
        webhook: 2142,
        archive: 0,
        markRead: 0,
        markUnread: 0,
        star: 0,
        unstar: 0,
        label: 0,
        removeLabel: 0,
        categorize: 0,
        forward: 0,
        reply: 0,
        snooze: 0,
        moveToFolder: 0,
        markImportant: 0,
        markNotImportant: 0,
        addToList: 0,
        createTask: 0,
        notification: 0,
        aiProcess: 0,
        custom: 0
      },
      errorRate: 0.003,
      performance: {
        conditionEvalTime: 42,
        actionExecutionTime: 25,
        totalTime: 67
      }
    },
    createdAt: new Date('2024-09-20T13:45:00Z'),
    updatedAt: new Date('2024-12-10T10:15:00Z'),
    lastTriggered: new Date('2024-12-15T18:22:00Z'),
    createdBy: 'user-1',
    tags: ['security', 'spam-protection'],
    schedule: {
      type: 'immediate'
    },
    exceptions: [
      {
        id: 'exc-1',
        type: 'sender',
        value: '<EMAIL>',
        reason: 'Legitimate vendor support'
      }
    ]
  },
  {
    id: 'rule-4',
    name: 'Auto-categorize receipts',
    description: 'Automatically categorize and label receipt emails',
    enabled: true,
    priority: 3,
    conditions: [
      {
        id: 'cond-7',
        type: 'subject',
        operator: 'contains',
        value: ['receipt', 'invoice', 'payment confirmation', 'purchase'],
        caseSensitive: false
      },
      {
        id: 'cond-8',
        type: 'hasAttachment',
        operator: 'equals',
        value: true
      },
      {
        id: 'cond-9',
        type: 'attachmentType',
        operator: 'in',
        value: ['pdf', 'png', 'jpg']
      }
    ],
    conditionLogic: 'all',
    actions: [
      {
        id: 'action-8',
        type: 'categorize',
        value: 'financial',
        order: 1
      },
      {
        id: 'action-9',
        type: 'label',
        value: 'receipt',
        order: 2
      },
      {
        id: 'action-10',
        type: 'moveToFolder',
        params: {
          folder: 'Financial/Receipts'
        },
        order: 3
      }
    ],
    stats: {
      totalMatches: 342,
      totalActions: 1026,
      successfulActions: 1019,
      failedActions: 7,
      lastMatchDate: new Date('2024-12-15T14:18:00Z'),
      avgProcessingTime: 156,
      matchesByDay: {
        '2024-12-15': 4,
        '2024-12-14': 6,
        '2024-12-13': 3,
        '2024-12-12': 8,
        '2024-12-11': 5
      },
      actionsByType: {
        categorize: 342,
        label: 335,
        moveToFolder: 342,
        archive: 0,
        delete: 0,
        markRead: 0,
        markUnread: 0,
        star: 0,
        unstar: 0,
        removeLabel: 0,
        forward: 0,
        reply: 0,
        snooze: 0,
        markImportant: 0,
        markNotImportant: 0,
        addToList: 0,
        createTask: 0,
        webhook: 0,
        notification: 0,
        aiProcess: 0,
        custom: 0
      },
      errorRate: 0.007,
      performance: {
        conditionEvalTime: 89,
        actionExecutionTime: 67,
        totalTime: 156
      }
    },
    createdAt: new Date('2024-11-10T08:30:00Z'),
    updatedAt: new Date('2024-12-05T16:20:00Z'),
    lastTriggered: new Date('2024-12-15T14:18:00Z'),
    createdBy: 'user-1',
    tags: ['finance', 'organization'],
    schedule: {
      type: 'immediate'
    }
  },
  {
    id: 'rule-5',
    name: 'Meeting reminder processing',
    description: 'Process meeting invitations and reminders',
    enabled: false,
    priority: 4,
    conditions: [
      {
        id: 'cond-10',
        type: 'subject',
        operator: 'contains',
        value: ['meeting', 'calendar', 'reminder', 'invitation'],
        caseSensitive: false
      },
      {
        id: 'cond-11',
        type: 'body',
        operator: 'contains',
        value: ['zoom', 'teams', 'meet.google.com', 'calendar event']
      }
    ],
    conditionLogic: 'all',
    actions: [
      {
        id: 'action-11',
        type: 'createTask',
        params: {
          category: 'meetings'
        },
        order: 1
      },
      {
        id: 'action-12',
        type: 'label',
        value: 'meeting',
        order: 2
      }
    ],
    stats: {
      totalMatches: 156,
      totalActions: 312,
      successfulActions: 298,
      failedActions: 14,
      lastMatchDate: new Date('2024-12-10T09:45:00Z'),
      avgProcessingTime: 234,
      matchesByDay: {
        '2024-12-10': 8,
        '2024-12-09': 12,
        '2024-12-08': 6,
        '2024-12-07': 4,
        '2024-12-06': 9
      },
      actionsByType: {
        createTask: 142,
        label: 156,
        archive: 0,
        delete: 0,
        markRead: 0,
        markUnread: 0,
        star: 0,
        unstar: 0,
        removeLabel: 0,
        categorize: 0,
        forward: 0,
        reply: 0,
        snooze: 0,
        moveToFolder: 0,
        markImportant: 0,
        markNotImportant: 0,
        addToList: 0,
        webhook: 0,
        notification: 0,
        aiProcess: 0,
        custom: 0
      },
      errorRate: 0.045,
      performance: {
        conditionEvalTime: 67,
        actionExecutionTime: 167,
        totalTime: 234
      }
    },
    createdAt: new Date('2024-10-25T15:10:00Z'),
    updatedAt: new Date('2024-11-30T11:40:00Z'),
    lastTriggered: new Date('2024-12-10T09:45:00Z'),
    createdBy: 'user-1',
    tags: ['meetings', 'calendar'],
    schedule: {
      type: 'scheduled',
      time: '09:00',
      days: [1, 2, 3, 4, 5], // Weekdays
      timezone: 'America/New_York'
    }
  },
  {
    id: 'rule-6',
    name: 'AI content analysis',
    description: 'Use AI to analyze and categorize complex emails',
    enabled: true,
    priority: 5,
    conditions: [
      {
        id: 'cond-12',
        type: 'aiScore',
        operator: 'greaterThan',
        value: 0.8
      },
      {
        id: 'cond-13',
        type: 'size',
        operator: 'greaterThan',
        value: 10000 // bytes
      }
    ],
    conditionLogic: 'all',
    actions: [
      {
        id: 'action-13',
        type: 'aiProcess',
        params: {
          model: 'content-analyzer-v2',
          confidence: 0.85
        },
        order: 1,
        condition: {
          type: 'confidence',
          threshold: 0.85
        }
      },
      {
        id: 'action-14',
        type: 'categorize',
        value: 'ai-processed',
        order: 2
      }
    ],
    stats: {
      totalMatches: 45,
      totalActions: 78,
      successfulActions: 73,
      failedActions: 5,
      lastMatchDate: new Date('2024-12-14T20:15:00Z'),
      avgProcessingTime: 890,
      matchesByDay: {
        '2024-12-14': 2,
        '2024-12-13': 1,
        '2024-12-12': 3,
        '2024-12-11': 1,
        '2024-12-10': 2
      },
      actionsByType: {
        aiProcess: 38,
        categorize: 40,
        archive: 0,
        delete: 0,
        markRead: 0,
        markUnread: 0,
        star: 0,
        unstar: 0,
        label: 0,
        removeLabel: 0,
        forward: 0,
        reply: 0,
        snooze: 0,
        moveToFolder: 0,
        markImportant: 0,
        markNotImportant: 0,
        addToList: 0,
        createTask: 0,
        webhook: 0,
        notification: 0,
        custom: 0
      },
      errorRate: 0.064,
      performance: {
        conditionEvalTime: 123,
        actionExecutionTime: 767,
        totalTime: 890
      }
    },
    createdAt: new Date('2024-12-01T12:00:00Z'),
    updatedAt: new Date('2024-12-14T08:30:00Z'),
    lastTriggered: new Date('2024-12-14T20:15:00Z'),
    createdBy: 'user-1',
    tags: ['ai', 'experimental'],
    schedule: {
      type: 'delayed',
      delay: 30 // 30 minutes
    }
  }
];

export const mockRuleExecutions: RuleExecution[] = [
  {
    id: 'exec-1',
    ruleId: 'rule-1',
    threadId: 'thread-1',
    messageId: 'msg-1',
    timestamp: new Date('2024-12-15T10:30:00Z'),
    conditions: [
      {
        conditionId: 'cond-1',
        matched: true,
        evaluationTime: 8,
        details: { matchedValue: 'newsletter' }
      },
      {
        conditionId: 'cond-2',
        matched: true,
        evaluationTime: 4,
        details: { hasAttachment: false }
      }
    ],
    actions: [
      {
        actionId: 'action-1',
        executed: true,
        success: true,
        executionTime: 25,
        result: { archived: true }
      },
      {
        actionId: 'action-2',
        executed: true,
        success: true,
        executionTime: 12,
        result: { labelAdded: 'newsletter' }
      }
    ],
    totalTime: 49,
    success: true
  },
  {
    id: 'exec-2',
    ruleId: 'rule-2',
    threadId: 'thread-2',
    messageId: 'msg-2',
    timestamp: new Date('2024-12-15T16:45:00Z'),
    conditions: [
      {
        conditionId: 'cond-3',
        matched: true,
        evaluationTime: 6,
        details: { matchedSender: '<EMAIL>' }
      }
    ],
    actions: [
      {
        actionId: 'action-3',
        executed: true,
        success: true,
        executionTime: 8,
        result: { markedImportant: true }
      },
      {
        actionId: 'action-4',
        executed: true,
        success: true,
        executionTime: 5,
        result: { starred: true }
      },
      {
        actionId: 'action-5',
        executed: true,
        success: true,
        executionTime: 15,
        result: { notificationSent: true }
      }
    ],
    totalTime: 34,
    success: true
  },
  {
    id: 'exec-3',
    ruleId: 'rule-3',
    threadId: 'thread-3',
    messageId: 'msg-3',
    timestamp: new Date('2024-12-15T18:22:00Z'),
    conditions: [
      {
        conditionId: 'cond-4',
        matched: true,
        evaluationTime: 15,
        details: { matchedValue: 'URGENT!!!' }
      },
      {
        conditionId: 'cond-5',
        matched: false,
        evaluationTime: 12,
        details: { noMatch: true }
      },
      {
        conditionId: 'cond-6',
        matched: true,
        evaluationTime: 8,
        details: { externalSender: true }
      }
    ],
    actions: [
      {
        actionId: 'action-6',
        executed: true,
        success: true,
        executionTime: 18,
        result: { deleted: true }
      },
      {
        actionId: 'action-7',
        executed: true,
        success: false,
        error: 'Webhook timeout',
        executionTime: 5000,
        result: null
      }
    ],
    totalTime: 5053,
    success: false,
    error: 'Action action-7 failed: Webhook timeout'
  }
];

export const mockRuleTemplates: RuleTemplate[] = [
  {
    id: 'template-1',
    name: 'Newsletter Auto-Archive',
    description: 'Automatically archive newsletters and promotional emails',
    category: 'productivity',
    rule: {
      name: 'Newsletter Auto-Archive',
      description: 'Automatically archive newsletters and promotional emails',
      enabled: true,
      priority: 5,
      conditions: [
        {
          id: 'temp-cond-1',
          type: 'subject',
          operator: 'contains',
          value: ['newsletter', 'unsubscribe', 'promotional'],
          caseSensitive: false
        }
      ],
      conditionLogic: 'any',
      actions: [
        {
          id: 'temp-action-1',
          type: 'archive',
          order: 1
        },
        {
          id: 'temp-action-2',
          type: 'label',
          value: 'newsletter',
          order: 2
        }
      ]
    },
    popularity: 95,
    author: 'E-Connect Team',
    tags: ['newsletter', 'archive', 'productivity']
  },
  {
    id: 'template-2',
    name: 'VIP Sender Priority',
    description: 'Mark emails from important contacts as high priority',
    category: 'organization',
    rule: {
      name: 'VIP Sender Priority',
      description: 'Mark emails from important contacts as high priority',
      enabled: true,
      priority: 1,
      conditions: [
        {
          id: 'temp-cond-2',
          type: 'from',
          operator: 'in',
          value: [], // User will fill this
          caseSensitive: false
        }
      ],
      conditionLogic: 'any',
      actions: [
        {
          id: 'temp-action-3',
          type: 'markImportant',
          order: 1
        },
        {
          id: 'temp-action-4',
          type: 'star',
          order: 2
        }
      ]
    },
    popularity: 88,
    author: 'E-Connect Team',
    tags: ['vip', 'priority', 'important']
  },
  {
    id: 'template-3',
    name: 'Receipt Organization',
    description: 'Automatically organize receipts and financial documents',
    category: 'organization',
    rule: {
      name: 'Receipt Organization',
      description: 'Automatically organize receipts and financial documents',
      enabled: true,
      priority: 3,
      conditions: [
        {
          id: 'temp-cond-3',
          type: 'subject',
          operator: 'contains',
          value: ['receipt', 'invoice', 'payment'],
          caseSensitive: false
        },
        {
          id: 'temp-cond-4',
          type: 'hasAttachment',
          operator: 'equals',
          value: true
        }
      ],
      conditionLogic: 'all',
      actions: [
        {
          id: 'temp-action-5',
          type: 'label',
          value: 'receipt',
          order: 1
        },
        {
          id: 'temp-action-6',
          type: 'moveToFolder',
          params: { folder: 'Financial' },
          order: 2
        }
      ]
    },
    popularity: 76,
    author: 'E-Connect Team',
    tags: ['receipt', 'financial', 'organization']
  },
  {
    id: 'template-4',
    name: 'Spam Blocker',
    description: 'Advanced spam detection and blocking',
    category: 'security',
    rule: {
      name: 'Spam Blocker',
      description: 'Advanced spam detection and blocking',
      enabled: true,
      priority: 2,
      conditions: [
        {
          id: 'temp-cond-5',
          type: 'subject',
          operator: 'contains',
          value: ['URGENT', 'WINNER', 'FREE', '!!!'],
          caseSensitive: false
        },
        {
          id: 'temp-cond-6',
          type: 'from',
          operator: 'notContains',
          value: '@your-domain.com'
        }
      ],
      conditionLogic: 'any',
      actions: [
        {
          id: 'temp-action-7',
          type: 'delete',
          order: 1
        }
      ]
    },
    popularity: 92,
    author: 'E-Connect Team',
    tags: ['spam', 'security', 'filter']
  },
  {
    id: 'template-5',
    name: 'Meeting Auto-Processor',
    description: 'Automatically process meeting invitations and calendar events',
    category: 'automation',
    rule: {
      name: 'Meeting Auto-Processor',
      description: 'Automatically process meeting invitations and calendar events',
      enabled: true,
      priority: 4,
      conditions: [
        {
          id: 'temp-cond-7',
          type: 'subject',
          operator: 'contains',
          value: ['meeting', 'invitation', 'calendar'],
          caseSensitive: false
        }
      ],
      conditionLogic: 'any',
      actions: [
        {
          id: 'temp-action-8',
          type: 'label',
          value: 'meeting',
          order: 1
        },
        {
          id: 'temp-action-9',
          type: 'createTask',
          params: { category: 'meetings' },
          order: 2
        }
      ]
    },
    popularity: 67,
    author: 'E-Connect Team',
    tags: ['meeting', 'calendar', 'automation']
  }
];

export const mockRuleGroups: RuleGroup[] = [
  {
    id: 'group-1',
    name: 'Email Organization',
    description: 'Rules for organizing and categorizing emails',
    rules: ['rule-1', 'rule-4'],
    enabled: true,
    logic: 'parallel',
    priority: 1
  },
  {
    id: 'group-2',
    name: 'Security & Filtering',
    description: 'Rules for spam detection and security',
    rules: ['rule-3'],
    enabled: true,
    logic: 'sequential',
    priority: 0
  },
  {
    id: 'group-3',
    name: 'VIP Processing',
    description: 'Special handling for important contacts',
    rules: ['rule-2'],
    enabled: true,
    logic: 'exclusive',
    priority: 0
  }
];