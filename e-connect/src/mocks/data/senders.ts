import type { NewsletterSender, WhitelistEntry } from '../../types/bulk'

// Generate realistic newsletter senders
export const mockNewsletterSenders: NewsletterSender[] = [
  {
    id: 'sender-1',
    email: '<EMAIL>',
    name: 'TechCrunch',
    domain: 'techcrunch.com',
    category: 'newsletter',
    frequency: 'daily',
    volume: {
      total: 365,
      lastMonth: 30,
      lastWeek: 7,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    firstReceived: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000), // 1 year ago
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://techcrunch.com/unsubscribe',
    reputation: {
      score: 85,
      factors: {
        spamReports: 2,
        userEngagement: 75,
        domainAge: 95,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.42,
      clickRate: 0.18,
      unsubscribeRate: 0.02,
      spamRate: 0.001
    },
    recentEmails: [
      {
        id: 'msg-tc-1',
        subject: 'Daily Crunch: AI funding reaches new heights',
        date: new Date(Date.now() - 2 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      },
      {
        id: 'msg-tc-2',
        subject: 'Daily Crunch: Major tech layoffs continue',
        date: new Date(Date.now() - 26 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-2',
    email: '<EMAIL>',
    name: 'Medium Daily Digest',
    domain: 'medium.com',
    category: 'newsletter',
    frequency: 'daily',
    volume: {
      total: 280,
      lastMonth: 28,
      lastWeek: 7,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 8 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 280 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://medium.com/me/settings',
    reputation: {
      score: 92,
      factors: {
        spamReports: 1,
        userEngagement: 88,
        domainAge: 98,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.38,
      clickRate: 0.22,
      unsubscribeRate: 0.015,
      spamRate: 0.0005
    },
    recentEmails: [
      {
        id: 'msg-med-1',
        subject: 'Your daily reading recommendations',
        date: new Date(Date.now() - 8 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-3',
    email: '<EMAIL>',
    name: 'LinkedIn',
    domain: 'linkedin.com',
    category: 'notification',
    frequency: 'weekly',
    volume: {
      total: 156,
      lastMonth: 12,
      lastWeek: 3,
      trend: 'decreasing'
    },
    lastReceived: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 156 * 7 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'manual',
    reputation: {
      score: 88,
      factors: {
        spamReports: 5,
        userEngagement: 65,
        domainAge: 100,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.28,
      clickRate: 0.12,
      unsubscribeRate: 0.03,
      spamRate: 0.002
    },
    recentEmails: [
      {
        id: 'msg-li-1',
        subject: 'You have 3 new profile views',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        hasUnsubscribeLink: false
      }
    ]
  },
  {
    id: 'sender-4',
    email: '<EMAIL>',
    name: 'Amazon Deals',
    domain: 'amazon.com',
    category: 'promotion',
    frequency: 'daily',
    volume: {
      total: 520,
      lastMonth: 45,
      lastWeek: 12,
      trend: 'increasing'
    },
    lastReceived: new Date(Date.now() - 4 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 520 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://www.amazon.com/gp/prefs/email',
    reputation: {
      score: 78,
      factors: {
        spamReports: 12,
        userEngagement: 45,
        domainAge: 100,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.22,
      clickRate: 0.08,
      unsubscribeRate: 0.05,
      spamRate: 0.003
    },
    recentEmails: [
      {
        id: 'msg-amz-1',
        subject: 'Lightning Deal: 50% off Electronics',
        date: new Date(Date.now() - 4 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-5',
    email: '<EMAIL>',
    name: 'GitHub',
    domain: 'github.com',
    category: 'notification',
    frequency: 'daily',
    volume: {
      total: 890,
      lastMonth: 85,
      lastWeek: 21,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 1 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 890 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://github.com/settings/notifications',
    reputation: {
      score: 95,
      factors: {
        spamReports: 0,
        userEngagement: 92,
        domainAge: 95,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.68,
      clickRate: 0.45,
      unsubscribeRate: 0.005,
      spamRate: 0
    },
    isWhitelisted: true,
    recentEmails: [
      {
        id: 'msg-gh-1',
        subject: '[vercel/next.js] New issue: Performance regression',
        date: new Date(Date.now() - 1 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-6',
    email: '<EMAIL>',
    name: 'Shopify Marketing',
    domain: 'shopify.com',
    category: 'marketing',
    frequency: 'weekly',
    volume: {
      total: 52,
      lastMonth: 4,
      lastWeek: 1,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 52 * 7 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://www.shopify.com/unsubscribe',
    reputation: {
      score: 82,
      factors: {
        spamReports: 3,
        userEngagement: 68,
        domainAge: 90,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.35,
      clickRate: 0.15,
      unsubscribeRate: 0.025,
      spamRate: 0.001
    },
    recentEmails: [
      {
        id: 'msg-shop-1',
        subject: 'New e-commerce trends for 2024',
        date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-7',
    email: '<EMAIL>',
    name: 'Product Hunt Daily',
    domain: 'producthunt.com',
    category: 'newsletter',
    frequency: 'daily',
    volume: {
      total: 180,
      lastMonth: 30,
      lastWeek: 7,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 6 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://www.producthunt.com/settings/email',
    reputation: {
      score: 90,
      factors: {
        spamReports: 1,
        userEngagement: 82,
        domainAge: 85,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.48,
      clickRate: 0.28,
      unsubscribeRate: 0.018,
      spamRate: 0.0003
    },
    recentEmails: [
      {
        id: 'msg-ph-1',
        subject: 'Top 5 products launching today',
        date: new Date(Date.now() - 6 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-8',
    email: '<EMAIL>',
    name: 'Notion Updates',
    domain: 'notion.so',
    category: 'notification',
    frequency: 'monthly',
    volume: {
      total: 24,
      lastMonth: 2,
      lastWeek: 0,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 24 * 30 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'email',
    unsubscribeEmail: '<EMAIL>',
    reputation: {
      score: 93,
      factors: {
        spamReports: 0,
        userEngagement: 85,
        domainAge: 80,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.72,
      clickRate: 0.35,
      unsubscribeRate: 0.008,
      spamRate: 0
    },
    recentEmails: [
      {
        id: 'msg-not-1',
        subject: 'Notion AI: Now with custom prompts',
        date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
        hasUnsubscribeLink: false
      }
    ]
  },
  {
    id: 'sender-9',
    email: '<EMAIL>',
    name: 'Spotify',
    domain: 'spotify.com',
    category: 'marketing',
    frequency: 'weekly',
    volume: {
      total: 104,
      lastMonth: 8,
      lastWeek: 2,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 104 * 7 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://www.spotify.com/account/notifications',
    reputation: {
      score: 87,
      factors: {
        spamReports: 4,
        userEngagement: 78,
        domainAge: 95,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.45,
      clickRate: 0.22,
      unsubscribeRate: 0.02,
      spamRate: 0.001
    },
    recentEmails: [
      {
        id: 'msg-spot-1',
        subject: 'Your Weekly Discovery: New releases',
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-10',
    email: '<EMAIL>',
    name: 'Uber Promotions',
    domain: 'uber.com',
    category: 'promotion',
    frequency: 'biweekly',
    volume: {
      total: 78,
      lastMonth: 6,
      lastWeek: 1,
      trend: 'decreasing'
    },
    lastReceived: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 78 * 14 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://www.uber.com/unsubscribe',
    reputation: {
      score: 75,
      factors: {
        spamReports: 8,
        userEngagement: 55,
        domainAge: 90,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.28,
      clickRate: 0.12,
      unsubscribeRate: 0.04,
      spamRate: 0.002
    },
    recentEmails: [
      {
        id: 'msg-uber-1',
        subject: '30% off your next 5 rides',
        date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-11',
    email: '<EMAIL>',
    name: 'Substack Weekly',
    domain: 'substack.com',
    category: 'newsletter',
    frequency: 'weekly',
    volume: {
      total: 45,
      lastMonth: 4,
      lastWeek: 1,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 45 * 7 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://substack.com/unsubscribe',
    reputation: {
      score: 91,
      factors: {
        spamReports: 1,
        userEngagement: 86,
        domainAge: 75,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.52,
      clickRate: 0.31,
      unsubscribeRate: 0.012,
      spamRate: 0.0002
    },
    recentEmails: [
      {
        id: 'msg-sub-1',
        subject: 'Top stories from your subscriptions',
        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  },
  {
    id: 'sender-12',
    email: '<EMAIL>',
    name: 'Indeed Job Alerts',
    domain: 'indeed.com',
    category: 'notification',
    frequency: 'daily',
    volume: {
      total: 245,
      lastMonth: 30,
      lastWeek: 7,
      trend: 'stable'
    },
    lastReceived: new Date(Date.now() - 3 * 60 * 60 * 1000),
    firstReceived: new Date(Date.now() - 245 * 24 * 60 * 60 * 1000),
    unsubscribeMethod: 'link',
    unsubscribeUrl: 'https://www.indeed.com/alerts',
    reputation: {
      score: 80,
      factors: {
        spamReports: 6,
        userEngagement: 60,
        domainAge: 98,
        authentication: true
      }
    },
    statistics: {
      openRate: 0.32,
      clickRate: 0.15,
      unsubscribeRate: 0.035,
      spamRate: 0.002
    },
    recentEmails: [
      {
        id: 'msg-indeed-1',
        subject: '15 new jobs match your search',
        date: new Date(Date.now() - 3 * 60 * 60 * 1000),
        hasUnsubscribeLink: true
      }
    ]
  }
]

// Mock whitelist entries
export const mockWhitelist: WhitelistEntry[] = [
  {
    id: 'whitelist-1',
    type: 'email',
    value: '<EMAIL>',
    reason: 'Important development notifications',
    addedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    isVIP: true
  },
  {
    id: 'whitelist-2',
    type: 'domain',
    value: 'company.com',
    reason: 'Work emails',
    addedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
    isVIP: true
  },
  {
    id: 'whitelist-3',
    type: 'pattern',
    value: '*.edu',
    reason: 'Educational institutions',
    addedAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'whitelist-4',
    type: 'email',
    value: '<EMAIL>',
    reason: 'Banking notifications',
    addedAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
    isVIP: true
  },
  {
    id: 'whitelist-5',
    type: 'domain',
    value: 'healthcare.org',
    reason: 'Medical appointments and updates',
    addedAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000)
  }
]