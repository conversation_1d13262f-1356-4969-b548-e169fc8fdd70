import { 
  EmailStats, 
  SenderStats, 
  CategoryStats, 
  VolumeData,
  ResponsePattern,
  LabelStats,
  AttachmentStats,
  PerformanceMetrics,
  AnalyticsEvent,
  AdvancedInsights,
  TimeAnalysis,
  RulePerformance
} from '@/types/analytics'

// Helper function to generate dates
const daysAgo = (days: number) => new Date(Date.now() - days * 24 * 60 * 60 * 1000)
const hoursAgo = (hours: number) => new Date(Date.now() - hours * 60 * 60 * 1000)

// Generate volume data for the last 30 days
export const generateVolumeData = (): VolumeData[] => {
  return Array.from({ length: 30 }, (_, i) => {
    const date = daysAgo(29 - i)
    const dayOfWeek = date.getDay()
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
    
    // Reduce volume on weekends
    const weekendMultiplier = isWeekend ? 0.3 : 1
    const baseReceived = (50 + Math.random() * 50) * weekendMultiplier
    const baseSent = baseReceived * (0.3 + Math.random() * 0.4)
    
    const received = Math.floor(baseReceived)
    const sent = Math.floor(baseSent)
    
    return {
      date,
      received,
      sent,
      archived: Math.floor(received * 0.7),
      deleted: Math.floor(received * 0.1),
      categories: {
        Work: Math.floor(received * (isWeekend ? 0.1 : 0.5)),
        Personal: Math.floor(received * (isWeekend ? 0.6 : 0.25)),
        Newsletters: Math.floor(received * 0.2),
        Promotions: Math.floor(received * 0.15),
        Spam: Math.floor(received * 0.05),
      },
      hourlyDistribution: Array.from({ length: 24 }, (_, hour) => {
        // Use seasonal patterns for more realistic data
        const isPeakMorning = hour >= 9 && hour <= 11
        const isPeakAfternoon = hour >= 14 && hour <= 16
        const isWorkHours = hour >= 8 && hour <= 18
        
        let multiplier = 0.1 // Base for non-work hours
        if (isWorkHours) multiplier = 0.5
        if (isPeakMorning || isPeakAfternoon) multiplier = 1
        
        // Add weekend adjustment
        if (isWeekend) multiplier *= 0.3
        
        return Math.floor(received * multiplier * (0.8 + Math.random() * 0.4))
      })
    }
  })
}

// Generate category breakdown
export const generateCategoryStats = (): CategoryStats[] => [
  {
    category: 'Work',
    count: 645,
    percentage: 42.3,
    unreadCount: 23,
    avgProcessingTime: 3.2,
    trend: 'increasing',
    trendPercentage: 12.5,
    subcategories: [
      { name: 'Meetings', count: 234, percentage: 36.3 },
      { name: 'Projects', count: 189, percentage: 29.3 },
      { name: 'Internal', count: 156, percentage: 24.2 },
      { name: 'External', count: 66, percentage: 10.2 },
    ]
  },
  {
    category: 'Personal',
    count: 387,
    percentage: 25.4,
    unreadCount: 8,
    avgProcessingTime: 1.8,
    trend: 'stable',
    trendPercentage: 2.1,
    subcategories: [
      { name: 'Family', count: 134, percentage: 34.6 },
      { name: 'Friends', count: 98, percentage: 25.3 },
      { name: 'Finance', count: 87, percentage: 22.5 },
      { name: 'Health', count: 68, percentage: 17.6 },
    ]
  },
  {
    category: 'Newsletters',
    count: 298,
    percentage: 19.6,
    unreadCount: 156,
    avgProcessingTime: 0.5,
    trend: 'decreasing',
    trendPercentage: -8.3,
    subcategories: [
      { name: 'Tech News', count: 89, percentage: 29.9 },
      { name: 'Business', count: 67, percentage: 22.5 },
      { name: 'Marketing', count: 78, percentage: 26.2 },
      { name: 'Other', count: 64, percentage: 21.5 },
    ]
  },
  {
    category: 'Promotions',
    count: 142,
    percentage: 9.3,
    unreadCount: 34,
    avgProcessingTime: 0.3,
    trend: 'stable',
    trendPercentage: -1.2,
    subcategories: [
      { name: 'Retail', count: 56, percentage: 39.4 },
      { name: 'Travel', count: 34, percentage: 23.9 },
      { name: 'Services', count: 28, percentage: 19.7 },
      { name: 'Other', count: 24, percentage: 16.9 },
    ]
  },
  {
    category: 'Spam',
    count: 51,
    percentage: 3.4,
    unreadCount: 0,
    avgProcessingTime: 0.1,
    trend: 'decreasing',
    trendPercentage: -45.6,
    subcategories: [
      { name: 'Phishing', count: 23, percentage: 45.1 },
      { name: 'Scam', count: 16, percentage: 31.4 },
      { name: 'Adult', count: 8, percentage: 15.7 },
      { name: 'Other', count: 4, percentage: 7.8 },
    ]
  }
]

// Generate sender statistics
export const generateSenderStats = (): SenderStats[] => [
  {
    email: '<EMAIL>',
    name: 'Company Notifications',
    domain: 'company.com',
    messageCount: 156,
    threadCount: 48,
    unreadCount: 3,
    avgResponseTime: 2.5,
    lastMessageDate: hoursAgo(2),
    firstMessageDate: daysAgo(120),
    categories: ['Work'],
    sentiment: 'neutral',
    importance: 'high',
    isFrequent: true,
    isRecent: true,
    tags: ['automated', 'notifications']
  },
  {
    email: '<EMAIL>',
    name: 'Customer Support',
    domain: 'service.com',
    messageCount: 89,
    threadCount: 34,
    unreadCount: 1,
    avgResponseTime: 4.2,
    lastMessageDate: daysAgo(1),
    firstMessageDate: daysAgo(180),
    categories: ['Work'],
    sentiment: 'positive',
    importance: 'high',
    isFrequent: true,
    isRecent: true,
    tags: ['support', 'service']
  },
  {
    email: '<EMAIL>',
    name: 'Tech Blog Newsletter',
    domain: 'techblog.com',
    messageCount: 67,
    threadCount: 67,
    unreadCount: 23,
    avgResponseTime: undefined,
    lastMessageDate: hoursAgo(12),
    firstMessageDate: daysAgo(365),
    categories: ['Newsletters'],
    sentiment: 'neutral',
    importance: 'medium',
    isFrequent: true,
    isRecent: true,
    tags: ['newsletter', 'tech']
  },
  {
    email: '<EMAIL>',
    name: 'John Doe',
    domain: 'client.com',
    messageCount: 54,
    threadCount: 18,
    unreadCount: 2,
    avgResponseTime: 6.8,
    lastMessageDate: hoursAgo(6),
    firstMessageDate: daysAgo(45),
    categories: ['Work'],
    sentiment: 'positive',
    importance: 'high',
    isFrequent: true,
    isRecent: true,
    tags: ['client', 'important']
  },
  {
    email: '<EMAIL>',
    name: 'Retail Store Marketing',
    domain: 'retailstore.com',
    messageCount: 43,
    threadCount: 43,
    unreadCount: 12,
    avgResponseTime: undefined,
    lastMessageDate: daysAgo(2),
    firstMessageDate: daysAgo(200),
    categories: ['Promotions'],
    sentiment: 'neutral',
    importance: 'low',
    isFrequent: true,
    isRecent: true,
    tags: ['marketing', 'promotions']
  },
  {
    email: '<EMAIL>',
    name: 'Mom',
    domain: 'family.com',
    messageCount: 38,
    threadCount: 15,
    unreadCount: 0,
    avgResponseTime: 1.2,
    lastMessageDate: hoursAgo(18),
    firstMessageDate: daysAgo(730),
    categories: ['Personal'],
    sentiment: 'positive',
    importance: 'high',
    isFrequent: true,
    isRecent: true,
    tags: ['family', 'personal']
  },
  {
    email: '<EMAIL>',
    name: 'Billing Department',
    domain: 'services.com',
    messageCount: 32,
    threadCount: 28,
    unreadCount: 1,
    avgResponseTime: 24.5,
    lastMessageDate: daysAgo(3),
    firstMessageDate: daysAgo(180),
    categories: ['Personal'],
    sentiment: 'neutral',
    importance: 'medium',
    isFrequent: false,
    isRecent: true,
    tags: ['billing', 'financial']
  },
  {
    email: '<EMAIL>',
    name: 'Local Meetup Events',
    domain: 'meetup.com',
    messageCount: 29,
    threadCount: 29,
    unreadCount: 8,
    avgResponseTime: undefined,
    lastMessageDate: daysAgo(5),
    firstMessageDate: daysAgo(300),
    categories: ['Personal'],
    sentiment: 'positive',
    importance: 'medium',
    isFrequent: false,
    isRecent: false,
    tags: ['events', 'social']
  },
  {
    email: '<EMAIL>',
    name: 'Bank Security',
    domain: 'bank.com',
    messageCount: 18,
    threadCount: 18,
    unreadCount: 0,
    avgResponseTime: undefined,
    lastMessageDate: daysAgo(7),
    firstMessageDate: daysAgo(365),
    categories: ['Personal'],
    sentiment: 'neutral',
    importance: 'high',
    isFrequent: false,
    isRecent: false,
    tags: ['security', 'financial', 'important']
  },
  {
    email: '<EMAIL>',
    name: 'Social Media Notifications',
    domain: 'socialmedia.com',
    messageCount: 156,
    threadCount: 156,
    unreadCount: 45,
    avgResponseTime: undefined,
    lastMessageDate: hoursAgo(4),
    firstMessageDate: daysAgo(500),
    categories: ['Newsletters'],
    sentiment: 'neutral',
    importance: 'low',
    isFrequent: true,
    isRecent: true,
    tags: ['social', 'notifications', 'automated']
  }
]

// Generate response patterns
export const generateResponsePatterns = (): ResponsePattern[] => [
  {
    type: 'immediate',
    count: 45,
    percentage: 12.5,
    avgTime: 0.25,
    byCategory: {
      Work: 32,
      Personal: 13,
    },
    bySender: [
      { email: '<EMAIL>', count: 15, avgTime: 0.2 },
      { email: '<EMAIL>', count: 8, avgTime: 0.3 },
    ]
  },
  {
    type: 'same-day',
    count: 89,
    percentage: 24.7,
    avgTime: 4.2,
    byCategory: {
      Work: 67,
      Personal: 22,
    },
    bySender: [
      { email: '<EMAIL>', count: 23, avgTime: 3.8 },
      { email: '<EMAIL>', count: 18, avgTime: 4.5 },
    ]
  },
  {
    type: 'next-day',
    count: 67,
    percentage: 18.6,
    avgTime: 18.5,
    byCategory: {
      Work: 45,
      Personal: 22,
    }
  },
  {
    type: 'within-week',
    count: 78,
    percentage: 21.7,
    avgTime: 72,
    byCategory: {
      Work: 34,
      Personal: 44,
    }
  },
  {
    type: 'over-week',
    count: 34,
    percentage: 9.4,
    avgTime: 240,
    byCategory: {
      Work: 12,
      Personal: 22,
    }
  },
  {
    type: 'no-response',
    count: 47,
    percentage: 13.1,
    byCategory: {
      Newsletters: 34,
      Promotions: 13,
    }
  }
]

// Generate label statistics
export const generateLabelStats = (): LabelStats[] => [
  {
    label: 'Important',
    count: 67,
    percentage: 4.4,
    color: '#ef4444',
    addedCount: 12,
    removedCount: 3,
    avgTimeToLabel: 2.3,
    automation: {
      ruleId: 'rule-001',
      autoLabeled: 45,
      accuracy: 89.5
    }
  },
  {
    label: 'Follow Up',
    count: 43,
    percentage: 2.8,
    color: '#f59e0b',
    addedCount: 8,
    removedCount: 5,
    avgTimeToLabel: 1.8
  },
  {
    label: 'Waiting',
    count: 28,
    percentage: 1.8,
    color: '#8b5cf6',
    addedCount: 6,
    removedCount: 2,
    avgTimeToLabel: 4.2
  },
  {
    label: 'Archive',
    count: 234,
    percentage: 15.4,
    color: '#6b7280',
    addedCount: 45,
    removedCount: 12,
    avgTimeToLabel: 0.5,
    automation: {
      ruleId: 'rule-003',
      autoLabeled: 198,
      accuracy: 94.2
    }
  }
]

// Generate attachment statistics
export const generateAttachmentStats = (): AttachmentStats => ({
  totalCount: 234,
  totalSize: 567890123, // bytes
  avgSize: 2427008, // bytes
  types: [
    {
      type: 'PDF',
      mimeType: 'application/pdf',
      count: 89,
      totalSize: 234567890,
      percentage: 38.0
    },
    {
      type: 'Image',
      mimeType: 'image/*',
      count: 67,
      totalSize: 123456789,
      percentage: 28.6
    },
    {
      type: 'Document',
      mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      count: 45,
      totalSize: 98765432,
      percentage: 19.2
    },
    {
      type: 'Spreadsheet',
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      count: 23,
      totalSize: 67890123,
      percentage: 9.8
    },
    {
      type: 'Other',
      mimeType: 'application/octet-stream',
      count: 10,
      totalSize: 43210987,
      percentage: 4.3
    }
  ],
  largestAttachments: [
    {
      filename: 'presentation_final_v3.pptx',
      size: 45678901,
      messageId: 'msg-001',
      date: daysAgo(2)
    },
    {
      filename: 'project_data.xlsx',
      size: 34567890,
      messageId: 'msg-002',
      date: daysAgo(5)
    },
    {
      filename: 'design_mockups.zip',
      size: 23456789,
      messageId: 'msg-003',
      date: daysAgo(8)
    }
  ],
  byMonth: Array.from({ length: 6 }, (_, i) => {
    const month = new Date()
    month.setMonth(month.getMonth() - i)
    return {
      month: month.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
      count: Math.floor(20 + Math.random() * 40),
      totalSize: Math.floor(50000000 + Math.random() * 100000000)
    }
  }).reverse()
})

// Generate complete email statistics
export const generateEmailStats = (): EmailStats => {
  const volumeData = generateVolumeData()
  const categoryBreakdown = generateCategoryStats()
  const topSenders = generateSenderStats()
  
  const totalReceived = volumeData.reduce((sum, day) => sum + day.received, 0)
  const totalSent = volumeData.reduce((sum, day) => sum + day.sent, 0)
  const totalThreads = Math.floor(totalReceived * 0.7) // Assume threading reduces count
  const totalUnread = categoryBreakdown.reduce((sum, cat) => sum + cat.unreadCount, 0)
  
  return {
    totals: {
      threads: totalThreads,
      messages: totalReceived + totalSent,
      unread: totalUnread,
      starred: 45,
      important: 67,
      archived: Math.floor(totalReceived * 0.8),
      deleted: Math.floor(totalReceived * 0.05),
      sent: totalSent,
      received: totalReceived,
      draft: 12,
      spam: categoryBreakdown.find(c => c.category === 'Spam')?.count || 0
    },
    metrics: {
      avgResponseTime: 3.8,
      avgThreadLength: 2.4,
      avgMessageSize: 15420,
      avgAttachmentSize: 2427008,
      emailsPerDay: totalReceived / 30,
      peakHours: [9, 10, 14, 15, 16],
      busiestDays: ['Tuesday', 'Wednesday', 'Thursday'],
      growthRate: 8.5
    },
    categoryBreakdown,
    topSenders,
    volumeData,
    labelStats: generateLabelStats(),
    attachmentStats: generateAttachmentStats(),
    responsePatterns: generateResponsePatterns(),
    period: {
      start: daysAgo(30),
      end: new Date(),
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
    }
  }
}

// Generate rule performance data
export const generateRulePerformance = () => ({
  rules: [
    {
      id: 'rule-001',
      name: 'Auto-archive newsletters',
      executions: 234,
      successes: 229,
      failures: 5,
      successRate: 97.9,
      timeSaved: 2340, // minutes
      isActive: true,
      category: 'Auto-filing',
      createdAt: daysAgo(120),
      lastExecuted: hoursAgo(2)
    },
    {
      id: 'rule-002',
      name: 'Flag important clients',
      executions: 156,
      successes: 142,
      failures: 14,
      successRate: 91.0,
      timeSaved: 780,
      isActive: true,
      category: 'Priority',
      createdAt: daysAgo(90),
      lastExecuted: hoursAgo(6)
    },
    {
      id: 'rule-003',
      name: 'Block spam domains',
      executions: 89,
      successes: 89,
      failures: 0,
      successRate: 100.0,
      timeSaved: 445,
      isActive: true,
      category: 'Filtering',
      createdAt: daysAgo(200),
      lastExecuted: hoursAgo(1)
    },
    {
      id: 'rule-004',
      name: 'Forward billing emails',
      executions: 67,
      successes: 59,
      failures: 8,
      successRate: 88.1,
      timeSaved: 335,
      isActive: true,
      category: 'Forwarding',
      createdAt: daysAgo(60),
      lastExecuted: daysAgo(3)
    },
    {
      id: 'rule-005',
      name: 'Old promotion cleanup',
      executions: 45,
      successes: 23,
      failures: 22,
      successRate: 51.1,
      timeSaved: 115,
      isActive: false,
      category: 'Cleanup',
      createdAt: daysAgo(180),
      lastExecuted: daysAgo(30)
    }
  ],
  totalTimeSaved: 4015,
  totalExecutions: 591,
  averageSuccessRate: 85.6,
  periodStart: daysAgo(30),
  periodEnd: new Date()
})

// Generate time analysis data
export const generateTimeAnalysis = () => ({
  hourlyDistribution: Array.from({ length: 24 }, (_, hour) => {
    // Simulate email patterns throughout the day
    const isWorkHours = hour >= 8 && hour <= 18
    const isPeakMorning = hour >= 9 && hour <= 11
    const isPeakAfternoon = hour >= 14 && hour <= 16
    
    let baseLevel = 5
    if (isWorkHours) baseLevel = 15
    if (isPeakMorning || isPeakAfternoon) baseLevel = 35
    
    return Array.from({ length: 7 }, (_, day) => {
      const isWeekend = day === 0 || day === 6
      const weekendMultiplier = isWeekend ? 0.3 : 1
      return Math.floor(baseLevel * weekendMultiplier * (0.8 + Math.random() * 0.4))
    })
  }),
  weeklyPattern: [
    { day: 'Monday', emails: 67, avgResponseTime: 4.2 },
    { day: 'Tuesday', emails: 89, avgResponseTime: 3.8 },
    { day: 'Wednesday', emails: 94, avgResponseTime: 3.5 },
    { day: 'Thursday', emails: 78, avgResponseTime: 4.1 },
    { day: 'Friday', emails: 56, avgResponseTime: 5.2 },
    { day: 'Saturday', emails: 23, avgResponseTime: 8.5 },
    { day: 'Sunday', emails: 18, avgResponseTime: 12.3 }
  ],
  responsePatterns: generateResponsePatterns(),
  peakTimes: {
    mostActiveHour: 10,
    mostActiveDay: 'Wednesday',
    leastActiveHour: 3,
    leastActiveDay: 'Sunday'
  }
})

// Generate performance metrics
export const generatePerformanceMetrics = (): PerformanceMetrics => ({
  apiCalls: [
    {
      endpoint: '/api/emails',
      count: 1234,
      avgDuration: 145,
      errors: 12,
      successRate: 99.0
    },
    {
      endpoint: '/api/analytics',
      count: 567,
      avgDuration: 89,
      errors: 3,
      successRate: 99.5
    },
    {
      endpoint: '/api/rules',
      count: 890,
      avgDuration: 67,
      errors: 8,
      successRate: 99.1
    }
  ],
  processingTimes: [
    {
      task: 'email_classification',
      avgTime: 23,
      minTime: 12,
      maxTime: 156,
      count: 2345
    },
    {
      task: 'rule_execution',
      avgTime: 45,
      minTime: 8,
      maxTime: 234,
      count: 891
    }
  ],
  resourceUsage: {
    cpu: 45.2,
    memory: 67.8,
    storage: 23.4,
    bandwidth: 12.6
  },
  aiUsage: {
    tokensUsed: 123456,
    apiCalls: 789,
    avgResponseTime: 234,
    costEstimate: 12.45
  }
})

// Generate analytics events
export const generateAnalyticsEvents = (): AnalyticsEvent[] => {
  const events: AnalyticsEvent[] = []
  const eventTypes = ['email_received', 'email_sent', 'email_archived', 'rule_triggered', 'ai_suggestion'] as const
  
  for (let i = 0; i < 100; i++) {
    const timestamp = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
    const type = eventTypes[Math.floor(Math.random() * eventTypes.length)]
    
    events.push({
      id: `event-${i + 1}`,
      type,
      category: 'email_management',
      action: type.replace('_', ' '),
      label: `Event ${i + 1}`,
      value: Math.floor(Math.random() * 100),
      metadata: {
        source: 'system',
        version: '1.0.0'
      },
      userId: `user-${Math.floor(Math.random() * 10) + 1}`,
      timestamp,
      sessionId: `session-${Math.floor(Math.random() * 50) + 1}`
    })
  }
  
  return events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
}

// Generate advanced insights
export const generateAdvancedInsights = (): AdvancedInsights => ({
  correlations: {
    'email_volume_vs_response_time': 0.75,
    'rule_usage_vs_productivity': 0.68,
    'sender_frequency_vs_importance': 0.45,
    'day_of_week_vs_email_count': 0.82
  },
  anomalies: [
    {
      id: 'anomaly-1',
      type: 'spike',
      value: 156,
      expectedValue: 89,
      deviation: 75.3,
      severity: 'high',
      timestamp: hoursAgo(6),
      description: 'Unusual spike in email volume detected',
      metric: 'email_volume',
      confidence: 0.95
    },
    {
      id: 'anomaly-2',
      type: 'drop',
      value: 12,
      expectedValue: 45,
      deviation: -73.3,
      severity: 'medium',
      timestamp: hoursAgo(18),
      description: 'Significant drop in rule executions',
      metric: 'rule_executions',
      confidence: 0.87
    }
  ],
  predictions: {
    email_volume: Array.from({ length: 7 }, (_, i) => ({
      date: new Date(Date.now() + (i + 1) * 24 * 60 * 60 * 1000),
      predicted: 85 + Math.sin(i * 0.5) * 15 + Math.random() * 10,
      confidence: 0.8 + Math.random() * 0.15,
      lowerBound: 60,
      upperBound: 120,
      method: 'ensemble'
    }))
  },
  trends: {
    email_volume: {
      direction: 'up',
      rate: 12.5,
      confidence: 0.89
    },
    response_time: {
      direction: 'down',
      rate: -8.3,
      confidence: 0.76
    },
    automation_usage: {
      direction: 'up',
      rate: 15.7,
      confidence: 0.93
    }
  }
})

// Export all mock data
export const mockAnalyticsData = {
  emailStats: generateEmailStats(),
  senderAnalytics: generateSenderStats(),
  rulePerformance: generateRulePerformance(),
  timeAnalysis: generateTimeAnalysis(),
  performanceMetrics: generatePerformanceMetrics(),
  analyticsEvents: generateAnalyticsEvents(),
  advancedInsights: generateAdvancedInsights()
}