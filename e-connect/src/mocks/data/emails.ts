import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../types/email'

// Enhanced data for realistic email generation
const FIRST_NAMES = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
]

const LAST_NAMES = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
]

const DOMAINS = [
  'gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'aol.com',
  'company.com', 'corp.com', 'enterprise.org', 'business.net', 'startup.io',
  'tech.com', 'consulting.com', 'university.edu', 'government.gov', 'nonprofit.org'
]

const COMPANY_NAMES = [
  'TechCorp', 'InnovateLabs', 'DataSystems', 'CloudWorks', 'NextGen Solutions',
  'DigitalFirst', 'SmartTech', 'FutureSoft', 'GlobalTech', 'PrimeSolutions',
  'EdgeComputing', 'StreamlineInc', 'OptimalSystems', 'VelocityTech', 'ApexSolutions'
]

// Helper to generate random dates with more realistic patterns
function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()))
}

// Helper to generate realistic business hours (9 AM - 6 PM)
function randomBusinessHourDate(daysAgo: number = 0): Date {
  const now = new Date()
  const date = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000)
  const hour = 9 + Math.floor(Math.random() * 9) // 9 AM to 6 PM
  const minute = Math.floor(Math.random() * 60)
  
  date.setHours(hour, minute, 0, 0)
  return date
}

// Helper to generate random email addresses with more variety
function randomEmail(type: 'personal' | 'work' | 'service' = 'personal'): EmailAddress {
  const firstName = FIRST_NAMES[Math.floor(Math.random() * FIRST_NAMES.length)]
  const lastName = LAST_NAMES[Math.floor(Math.random() * LAST_NAMES.length)]
  
  let domain: string
  let email: string
  
  switch (type) {
    case 'work':
      domain = DOMAINS.filter(d => d.includes('com') || d.includes('org')).slice(5)[Math.floor(Math.random() * 10)]
      email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`
      break
    case 'service':
      const company = COMPANY_NAMES[Math.floor(Math.random() * COMPANY_NAMES.length)]
      const serviceTypes = ['noreply', 'support', 'info', 'notifications', 'updates', 'newsletter']
      const serviceType = serviceTypes[Math.floor(Math.random() * serviceTypes.length)]
      email = `${serviceType}@${company.toLowerCase()}.com`
      break
    default:
      domain = DOMAINS[Math.floor(Math.random() * 5)] // First 5 are personal domains
      email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`
  }
  
  return {
    name: type === 'service' ? email.split('@')[1].replace('.com', '').replace(/\b\w/g, l => l.toUpperCase()) : `${firstName} ${lastName}`,
    email,
    type
  }
}

// Helper to generate attachment data
function generateAttachments(count: number = 0): any[] {
  if (count === 0) return []
  
  const attachmentTypes = [
    { ext: 'pdf', mime: 'application/pdf', names: ['document', 'report', 'invoice', 'contract', 'presentation'] },
    { ext: 'docx', mime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', names: ['document', 'letter', 'proposal', 'memo'] },
    { ext: 'xlsx', mime: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', names: ['spreadsheet', 'data', 'budget', 'report'] },
    { ext: 'jpg', mime: 'image/jpeg', names: ['photo', 'image', 'screenshot', 'diagram'] },
    { ext: 'png', mime: 'image/png', names: ['screenshot', 'logo', 'chart', 'graphic'] },
    { ext: 'zip', mime: 'application/zip', names: ['archive', 'files', 'backup', 'data'] }
  ]
  
  const attachments = []
  for (let i = 0; i < count; i++) {
    const type = attachmentTypes[Math.floor(Math.random() * attachmentTypes.length)]
    const baseName = type.names[Math.floor(Math.random() * type.names.length)]
    const filename = `${baseName}-${Math.floor(Math.random() * 1000)}.${type.ext}`
    
    attachments.push({
      id: `attach-${Date.now()}-${i}`,
      filename,
      mimeType: type.mime,
      size: Math.floor(Math.random() * 5000000) + 10000, // 10KB to 5MB
      inline: type.ext.includes('jpg') || type.ext.includes('png') ? Math.random() > 0.7 : false
    })
  }
  
  return attachments
}

// Enhanced email templates with more realistic content and metadata
const emailTemplates: Record<EmailCategory, Array<{
  subject: string
  body: string
  html?: string
  from: EmailAddress
  attachmentCount?: number
  priority?: 'high' | 'medium' | 'low'
  replyChance?: number // Probability this email will get replies
}>> = {
  Newsletter: [
    {
      subject: 'Your Weekly Tech Digest - AI Breakthroughs & Cloud Updates',
      body: `Hi there,\n\nHere are the top tech stories of the week:\n\n🚀 Major AI breakthrough in natural language processing\n☁️ New cloud computing standards released\n🔒 Latest cybersecurity threats and how to protect yourself\n📱 Mobile development trends for 2024\n\nRead more in our full newsletter.\n\nBest regards,\nThe TechNews Team`,
      html: `<h2>Weekly Tech Digest</h2><p>Here are the top tech stories...</p><ul><li>🚀 Major AI breakthrough</li><li>☁️ New cloud standards</li></ul>`,
      from: { name: 'TechNews Daily', email: '<EMAIL>', type: 'service' },
      attachmentCount: 0,
      priority: 'medium',
      replyChance: 0.1
    },
    {
      subject: 'Monthly Product Updates - New Features Released!',
      body: `Hello valued customer,\n\nWe're excited to share our latest product updates:\n\n✨ New dashboard with improved analytics\n🔧 Enhanced API performance (30% faster)\n🎨 Updated UI with dark mode support\n🔐 Advanced security features\n\nTry out these features today and let us know what you think!\n\nThe Product Team`,
      from: { name: 'Product Team', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      priority: 'medium',
      replyChance: 0.2
    },
    {
      subject: '🎯 Industry Report: Digital Transformation Trends 2024',
      body: 'Our comprehensive annual report on digital transformation trends is now available. Download your free copy and discover key insights that are shaping the future of business.',
      from: { name: 'Research Insights', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      replyChance: 0.05
    }
  ],
  Receipt: [
    {
      subject: `Your Amazon Order #${Math.random().toString().substr(2, 9)} has shipped`,
      body: `Hello,\n\nGreat news! Your recent order has been shipped and is on its way.\n\nOrder Details:\n• Wireless Bluetooth Headphones - $89.99\n• USB-C Cable (2-pack) - $15.99\n• Phone Case - $24.99\n\nTracking Number: 1Z999AA1234567890\nExpected Delivery: ${new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toLocaleDateString()}\n\nThank you for shopping with Amazon!`,
      from: { name: 'Amazon', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      priority: 'medium',
      replyChance: 0.02
    },
    {
      subject: `Payment Receipt - $${(Math.random() * 200 + 10).toFixed(2)}`,
      body: `Thank you for your payment.\n\nTransaction Details:\nAmount: $${(Math.random() * 200 + 10).toFixed(2)}\nTransaction ID: TXN${Math.random().toString().substr(2, 8)}\nDate: ${new Date().toLocaleDateString()}\nMethod: **** **** **** 4523\n\nIf you have any questions, please contact our support team.\n\nPayPal Customer Service`,
      from: { name: 'PayPal', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      priority: 'low',
      replyChance: 0.01
    },
    {
      subject: 'Receipt for Adobe Creative Cloud Subscription',
      body: 'Your monthly subscription has been renewed successfully. Amount charged: $52.99. Your subscription is active until next month.',
      from: { name: 'Adobe', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      replyChance: 0.01
    }
  ],
  Marketing: [
    {
      subject: '🔥 50% Off Everything - Limited Time Flash Sale!',
      body: `Don't miss out on our biggest sale of the year!\n\n🎯 50% off all items\n⏰ Sale ends in 24 hours\n🚚 Free shipping on orders over $50\n\nUse code: FLASH50\n\nShop now before it's too late!\n\nFashion Forward Team`,
      from: { name: 'Fashion Forward', email: '<EMAIL>', type: 'service' },
      priority: 'high',
      replyChance: 0.05
    },
    {
      subject: '⭐ Exclusive VIP Offer - Early Access Inside',
      body: `Hi VIP Customer,\n\nAs one of our valued customers, you get exclusive early access to our new collection - 24 hours before anyone else!\n\n🎁 Complimentary gift with purchase\n💎 VIP pricing on select items\n🎯 Personal styling consultation available\n\nYour exclusive access code: VIP2024\n\nHappy shopping!`,
      from: { name: 'VIP Services', email: '<EMAIL>', type: 'service' },
      priority: 'medium',
      replyChance: 0.08
    },
    {
      subject: 'Your Wish List Items Are Now On Sale! 💝',
      body: 'Great news! Several items from your wish list are now on sale. Don\'t wait - these deals won\'t last long.',
      from: { name: 'WishList Alerts', email: '<EMAIL>', type: 'service' },
      replyChance: 0.15
    }
  ],
  Social: [
    {
      subject: `${randomEmail('personal').name} wants to connect with you on LinkedIn`,
      body: `Hi there,\n\n${randomEmail('personal').name} wants to add you to their professional network on LinkedIn.\n\nView their profile and accept or decline their invitation.\n\nThe LinkedIn Team`,
      from: { name: 'LinkedIn', email: '<EMAIL>', type: 'service' },
      priority: 'medium',
      replyChance: 0.3
    },
    {
      subject: `🎉 You have ${Math.floor(Math.random() * 10) + 1} new followers`,
      body: `Your content is getting noticed!\n\nCheck out who's been following you:\n• ${randomEmail('personal').name}\n• ${randomEmail('personal').name}\n• ${randomEmail('personal').name}\n\nKeep up the great work!\n\nThe Twitter Team`,
      from: { name: 'Twitter', email: '<EMAIL>', type: 'service' },
      priority: 'low',
      replyChance: 0.02
    },
    {
      subject: 'Your post got 50+ likes! 📈',
      body: 'Your recent post is performing well! It has received over 50 likes and several comments. Keep engaging with your audience.',
      from: { name: 'Instagram', email: '<EMAIL>', type: 'service' },
      replyChance: 0.01
    }
  ],
  Updates: [
    {
      subject: '🔒 Security Alert: New sign-in from new device',
      body: `We noticed a new sign-in to your account from a device we don't recognize.\n\nDevice: Chrome on Windows\nLocation: San Francisco, CA\nTime: ${new Date().toLocaleString()}\n\nIf this was you, you can ignore this email. If not, please secure your account immediately.\n\nGoogle Security Team`,
      from: { name: 'Google Security', email: '<EMAIL>', type: 'service' },
      priority: 'high',
      replyChance: 0.05
    },
    {
      subject: '⏰ Your premium subscription expires in 7 days',
      body: `Hi there,\n\nJust a friendly reminder that your premium subscription will expire on ${new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}.\n\nTo continue enjoying premium features:\n✅ Unlimited access\n✅ Priority support\n✅ Advanced analytics\n\nRenew now to avoid interruption.\n\nSubscription Team`,
      from: { name: 'Premium Services', email: '<EMAIL>', type: 'service' },
      priority: 'medium',
      replyChance: 0.1
    },
    {
      subject: 'Software Update Available - Version 2.1.4',
      body: 'A new software update is available with bug fixes and performance improvements. Update now for the best experience.',
      from: { name: 'Software Updates', email: '<EMAIL>', type: 'service' },
      replyChance: 0.02
    }
  ],
  Personal: [
    {
      subject: 'Lunch tomorrow? 🍕',
      body: `Hey!\n\nAre you free for lunch tomorrow around 12:30? There's this new Italian place downtown that got amazing reviews. I thought we could check it out together!\n\nLet me know if you're interested.\n\nTalk soon!`,
      from: randomEmail('personal'),
      priority: 'medium',
      replyChance: 0.8
    },
    {
      subject: '🎉 Happy Birthday! Hope you have an amazing day!',
      body: `Happy Birthday!\n\nI hope you have the most wonderful day filled with love, laughter, and all your favorite things. You deserve all the happiness in the world!\n\nLet's definitely celebrate this weekend. I was thinking we could do dinner at that place you've been wanting to try?\n\nHave the best day ever!\n\nLots of love! ❤️`,
      from: randomEmail('personal'),
      priority: 'high',
      replyChance: 0.9
    },
    {
      subject: 'Thanks for yesterday!',
      body: 'Had such a great time catching up yesterday! Thanks for listening and for all the great advice. You\'re the best!',
      from: randomEmail('personal'),
      replyChance: 0.7
    },
    {
      subject: 'Weekend plans?',
      body: 'Any fun plans for the weekend? I was thinking of going hiking if the weather is nice. Want to join?',
      from: randomEmail('personal'),
      replyChance: 0.6
    }
  ],
  Work: [
    {
      subject: 'Meeting Notes - Project Alpha Update (Action Items)',
      body: `Hi team,\n\nHere are the key points from today's Project Alpha meeting:\n\n📋 Action Items:\n• Sarah: Complete user research by Friday\n• Mike: Update wireframes based on feedback\n• David: Prepare technical requirements doc\n\n🎯 Key Decisions:\n• Moving forward with mobile-first approach\n• Launch date pushed to end of Q1\n• Budget approved for additional testing\n\n📅 Next meeting: Thursday 2PM\n\nLet me know if I missed anything!\n\nBest,\nProject Manager`,
      from: randomEmail('work'),
      attachmentCount: 1,
      priority: 'high',
      replyChance: 0.6
    },
    {
      subject: 'Q4 Budget Review - Please Review by EOD Friday',
      body: `Team,\n\nPlease review the attached Q4 budget proposal and provide your feedback by end of day Friday.\n\nKey areas to focus on:\n💰 Marketing spend allocation\n📊 Technology infrastructure costs\n👥 Headcount planning\n🎯 Performance bonus structure\n\nScheduling a review meeting for next Monday to discuss any concerns.\n\nThanks,\nFinance Team`,
      from: randomEmail('work'),
      attachmentCount: 2,
      priority: 'high',
      replyChance: 0.4
    },
    {
      subject: 'All-Hands Meeting: Company Updates - Friday 3PM',
      body: 'Join us for the monthly all-hands meeting this Friday. We\'ll be sharing Q3 results and discussing exciting developments for Q4.',
      from: randomEmail('work'),
      priority: 'medium',
      replyChance: 0.2
    },
    {
      subject: 'Performance Review Reminder',
      body: 'Reminder: Performance reviews are due next week. Please complete your self-assessment and schedule your 1:1 with your manager.',
      from: randomEmail('work'),
      priority: 'medium',
      replyChance: 0.3
    }
  ],
  Finance: [
    {
      subject: `Your ${new Date().toLocaleDateString('en-US', { month: 'long', year: 'numeric' })} Statement is Ready`,
      body: `Your monthly statement is now available for download.\n\nAccount Summary:\n💳 Total Transactions: ${Math.floor(Math.random() * 50) + 10}\n💰 Current Balance: $${(Math.random() * 5000 + 1000).toFixed(2)}\n📊 Total Spent: $${(Math.random() * 2000 + 500).toFixed(2)}\n\nView your complete statement in online banking.\n\nChase Customer Service`,
      from: { name: 'Chase Bank', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      priority: 'medium',
      replyChance: 0.02
    },
    {
      subject: `📈 Investment Portfolio Update - ${(Math.random() > 0.5 ? '+' : '-')}${(Math.random() * 10).toFixed(1)}% This Month`,
      body: `Portfolio Performance Update\n\nYour investment portfolio has ${Math.random() > 0.5 ? 'grown' : 'decreased'} by ${(Math.random() * 10).toFixed(1)}% this month.\n\n📊 Key Highlights:\n• Total Value: $${(Math.random() * 50000 + 10000).toFixed(2)}\n• Monthly Change: ${(Math.random() > 0.5 ? '+' : '-')}$${(Math.random() * 2000).toFixed(2)}\n• Top Performer: Tech Growth Fund\n\nSchedule a consultation to discuss your strategy.\n\nYour Investment Team`,
      from: { name: 'Investment Advisory', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      priority: 'medium',
      replyChance: 0.15
    },
    {
      subject: 'Credit Score Update - Score Improved!',
      body: 'Good news! Your credit score has improved by 12 points this month. Keep up the great work with your financial habits.',
      from: { name: 'Credit Monitoring', email: '<EMAIL>', type: 'service' },
      replyChance: 0.05
    }
  ],
  Travel: [
    {
      subject: `✈️ Flight Confirmation - ${['NYC', 'LAX', 'CHI', 'MIA', 'SEA'][Math.floor(Math.random() * 5)]} to ${['LAX', 'NYC', 'DEN', 'ATL', 'SFO'][Math.floor(Math.random() * 5)]}`,
      body: `Flight Confirmation Details\n\n✅ Confirmation Code: ${Math.random().toString(36).substr(2, 6).toUpperCase()}\n📅 Departure: ${new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()} at ${Math.floor(Math.random() * 12) + 1}:${Math.random() > 0.5 ? '30' : '00'} ${Math.random() > 0.5 ? 'AM' : 'PM'}\n🎫 Seat: ${Math.floor(Math.random() * 30) + 1}${['A', 'B', 'C', 'D', 'E', 'F'][Math.floor(Math.random() * 6)]}\n🧳 Baggage: 1 carry-on included\n\nCheck in online 24 hours before departure.\n\nUnited Airlines`,
      from: { name: 'United Airlines', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      priority: 'high',
      replyChance: 0.05
    },
    {
      subject: `🏨 Hotel Booking Confirmed - ${['Hilton', 'Marriott', 'Hyatt', 'Sheraton', 'Westin'][Math.floor(Math.random() * 5)]} ${['Los Angeles', 'New York', 'Chicago', 'Miami', 'Seattle'][Math.floor(Math.random() * 5)]}`,
      body: `Hotel Reservation Confirmed\n\n🏨 Hotel: ${['Hilton', 'Marriott', 'Hyatt'][Math.floor(Math.random() * 3)]} Downtown\n📅 Check-in: ${new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString()}\n📅 Check-out: ${new Date(Date.now() + (Math.random() * 30 + 2) * 24 * 60 * 60 * 1000).toLocaleDateString()}\n🛏️ Room: Deluxe King Room\n💰 Total: $${(Math.random() * 300 + 150).toFixed(2)}/night\n\n🅿️ Parking: Available for $25/day\n📶 WiFi: Complimentary\n🏊 Pool & Gym: Available 24/7\n\nCancellation is free until 24 hours before check-in.\n\nBooking.com Team`,
      from: { name: 'Booking.com', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      priority: 'high',
      replyChance: 0.1
    },
    {
      subject: '🚗 Car Rental Confirmation - Pick up Tomorrow',
      body: 'Your car rental is confirmed for tomorrow. Pick up location: Airport Terminal 1. Confirmation number: CAR123456.',
      from: { name: 'Enterprise Rent-A-Car', email: '<EMAIL>', type: 'service' },
      attachmentCount: 1,
      replyChance: 0.05
    }
  ],
  Security: [
    {
      subject: '🚨 Unusual Activity Detected on Your Account',
      body: `Security Alert\n\nWe detected unusual activity on your account that requires your immediate attention.\n\n🔍 Activity Details:\n• Multiple login attempts from unknown location\n• Time: ${new Date().toLocaleString()}\n• Location: ${['Unknown Location', 'Russia', 'China', 'Nigeria'][Math.floor(Math.random() * 4)]}\n• Device: Unknown Device\n\n🔒 Immediate Actions Taken:\n• Account temporarily locked\n• All active sessions terminated\n\n👉 What you should do:\n1. Change your password immediately\n2. Review recent account activity\n3. Enable two-factor authentication\n\nSecurity Team`,
      from: { name: 'Security Alert', email: '<EMAIL>', type: 'service' },
      priority: 'high',
      replyChance: 0.8
    },
    {
      subject: '✅ Two-Factor Authentication Successfully Enabled',
      body: `Great news! You have successfully enabled two-factor authentication on your account.\n\n🔐 Your account is now more secure with:\n• SMS verification codes\n• Email backup codes\n• Recovery options configured\n\n📱 Next time you log in, you'll receive a verification code on your phone.\n\n💡 Pro tip: Save your backup codes in a secure location.\n\nAccount Security Team`,
      from: { name: 'Account Security', email: '<EMAIL>', type: 'service' },
      priority: 'medium',
      replyChance: 0.1
    },
    {
      subject: 'Password Expiry Reminder - Update Required',
      body: 'Your password will expire in 7 days. Please update your password to maintain account security.',
      from: { name: 'IT Security', email: '<EMAIL>', type: 'service' },
      priority: 'medium',
      replyChance: 0.05
    }
  ],
  Notification: [
    {
      subject: '📦 Package Delivered - Signed for by Resident',
      body: `Delivery Confirmation\n\n✅ Your package has been successfully delivered!\n\n📍 Delivery Details:\n• Time: ${new Date().toLocaleTimeString()}\n• Location: Front door\n• Signed by: Resident\n• Tracking: ${Math.random().toString().substr(2, 12)}\n\n📱 Rate your delivery experience in our app.\n\nFedEx Delivery Team`,
      from: { name: 'FedEx Delivery', email: '<EMAIL>', type: 'service' },
      priority: 'medium',
      replyChance: 0.05
    },
    {
      subject: `⏰ Reminder: Appointment Tomorrow at ${Math.floor(Math.random() * 5) + 1}:${Math.random() > 0.5 ? '00' : '30'} PM`,
      body: `Appointment Reminder\n\nThis is a friendly reminder about your upcoming appointment:\n\n🏥 Dr. Smith's Office\n📅 Date: ${new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleDateString()}\n⏰ Time: ${Math.floor(Math.random() * 5) + 1}:${Math.random() > 0.5 ? '00' : '30'} PM\n📍 123 Medical Center Dr, Suite 200\n\n📝 Please bring:\n• Insurance card\n• Photo ID\n• List of current medications\n\n📞 Need to reschedule? Call (555) 123-4567\n\nDr. Smith's Office`,
      from: { name: 'Dr. Smith Office', email: '<EMAIL>', type: 'service' },
      priority: 'high',
      replyChance: 0.2
    },
    {
      subject: '🔔 Calendar Reminder: Team Meeting in 1 Hour',
      body: 'Your meeting "Weekly Team Sync" starts in 1 hour. Join the video call or dial in using the details in your calendar.',
      from: { name: 'Calendar', email: '<EMAIL>', type: 'service' },
      priority: 'high',
      replyChance: 0.1
    }
  ],
  Important: [
    {
      subject: '🚨 URGENT: Action Required - Account Verification',
      body: `IMMEDIATE ACTION REQUIRED\n\nYour account requires verification within 24 hours to prevent suspension.\n\n⚠️ What's needed:\n• Verify your identity\n• Update contact information\n• Confirm recent transactions\n\n🔗 Click here to verify now: [Verification Link]\n\n⏰ Deadline: ${new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleDateString()} 11:59 PM\n\nIf you don't complete verification, your account will be temporarily suspended for security reasons.\n\nAccount Verification Team`,
      from: randomEmail('work'),
      priority: 'high',
      replyChance: 0.9
    },
    {
      subject: '📋 Contract Ready for Signature - Review Required',
      body: `Contract Ready for Review\n\nThe service agreement contract is ready for your review and signature.\n\n📄 Contract Details:\n• Service Agreement 2024\n• Value: $${(Math.random() * 100000 + 10000).toFixed(2)}\n• Duration: 12 months\n• Start Date: ${new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString()}\n\n✅ Next Steps:\n1. Review all terms and conditions\n2. Sign electronically\n3. Return by ${new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString()}\n\nLegal Team`,
      from: randomEmail('work'),
      attachmentCount: 2,
      priority: 'high',
      replyChance: 0.7
    },
    {
      subject: 'FINAL NOTICE: Payment Overdue',
      body: 'This is a final notice that your payment is overdue. Please remit payment immediately to avoid service interruption.',
      from: { name: 'Billing Department', email: '<EMAIL>', type: 'service' },
      priority: 'high',
      replyChance: 0.8
    }
  ],
  Spam: [
    {
      subject: '🎉 CONGRATULATIONS! You\'ve Won $1,000,000 - Claim Now!!!',
      body: `URGENT: CLAIM YOUR PRIZE NOW\n\nCongratulations! You are our lucky winner of the International Lottery!\n\n💰 Prize Amount: $1,000,000 USD\n🎫 Winning Number: ${Math.random().toString().substr(2, 8)}\n📧 Confirmation Code: WIN${Math.random().toString().substr(2, 6)}\n\nTo claim your prize, reply with:\n• Full name\n• Address\n• Phone number\n• Social Security Number\n\nClaim within 48 hours or prize will be forfeited!\n\nPrize Committee International`,
      from: { name: 'Prize Committee', email: `winner${Math.floor(Math.random() * 999)}@totally-not-spam.com`, type: 'service' },
      priority: 'high',
      replyChance: 0.01
    },
    {
      subject: '💕 Hot Singles in Your Area - Meet Tonight!',
      body: `Meet Amazing People Tonight!\n\n🔥 Over 1000 singles near you\n💬 Chat and meet instantly\n📱 No strings attached\n🆓 100% Free to join\n\nClick here to see who's online now!\n\n[SUSPICIOUS LINK]\n\nDating Connection Network`,
      from: { name: 'Dating Connection', email: `singles${Math.floor(Math.random() * 999)}@suspicious-dating.com`, type: 'service' },
      replyChance: 0.005
    },
    {
      subject: 'MAKE $5000 FROM HOME - NO EXPERIENCE NEEDED!!!',
      body: 'Work from home opportunity! Make thousands of dollars with no experience required. Limited time offer!',
      from: { name: 'Work From Home', email: '<EMAIL>', type: 'service' },
      replyChance: 0.002
    }
  ],
  Other: [
    {
      subject: 'Community Newsletter - Local Events This Week',
      body: `Community Updates\n\nHere's what's happening in your neighborhood this week:\n\n🎨 Art Fair at Central Park - Saturday 10 AM\n🍕 Pizza Night at Community Center - Tuesday 6 PM\n📚 Book Club Meeting - Thursday 7 PM\n🏃 Morning Jog Group - Daily 7 AM\n\nJoin us for any or all events!\n\nCommunity Board`,
      from: { name: 'Community Board', email: '<EMAIL>', type: 'service' },
      replyChance: 0.1
    },
    {
      subject: 'Weather Alert: Severe Storm Warning',
      body: 'Severe thunderstorm warning in effect for your area. Please stay indoors and avoid unnecessary travel.',
      from: { name: 'Weather Service', email: '<EMAIL>', type: 'service' },
      priority: 'high',
      replyChance: 0.02
    },
    {
      subject: 'Library Books Due Soon',
      body: 'Reminder: You have 3 library books due on Friday. Renew online or return to avoid late fees.',
      from: { name: 'Public Library', email: '<EMAIL>', type: 'service' },
      replyChance: 0.05
    }
  ]
}

// Generate a single email message with enhanced realism
export function generateMessage(
  id: string,
  threadId: string,
  category: EmailCategory,
  date: Date,
  isUnread: boolean = true,
  isReply: boolean = false
): ParsedMessage {
  const templates = emailTemplates[category]
  const template = templates[Math.floor(Math.random() * templates.length)]
  
  // Generate attachments based on template or random chance
  const attachmentCount = template.attachmentCount || (Math.random() > 0.8 ? Math.floor(Math.random() * 3) + 1 : 0)
  const attachments = generateAttachments(attachmentCount)
  
  // Generate more realistic headers
  const messageId = `<${id}.${Date.now()}@${template.from.email.split('@')[1]}>`
  const references = isReply ? `<${threadId}@${template.from.email.split('@')[1]}>` : undefined
  
  // Create snippet from body text
  const bodyText = template.body.replace(/\\n/g, ' ').replace(/\\s+/g, ' ')
  const snippet = bodyText.length > 150 ? bodyText.substring(0, 147) + '...' : bodyText
  
  // Enhanced HTML body
  const htmlBody = template.html || template.body
    .replace(/\\n\\n/g, '</p><p>')
    .replace(/\\n/g, '<br>')
    .replace(/^/, '<p>')
    .replace(/$/, '</p>')
    .replace(/•/g, '&bull;')
  
  const message: ParsedMessage = {
    id,
    threadId,
    messageId,
    subject: isReply && !template.subject.toLowerCase().startsWith('re:') ? `Re: ${template.subject}` : template.subject,
    snippet,
    body: {
      text: template.body,
      html: htmlBody,
      plain: template.body.replace(/[^\x00-\x7F]/g, '').replace(/\s+/g, ' '),
    },
    headers: {
      'message-id': messageId,
      'in-reply-to': references,
      references,
      subject: template.subject,
      from: `${template.from.name} <${template.from.email}>`,
      to: '<EMAIL>',
      date: date.toISOString(),
      'content-type': 'multipart/alternative',
      'x-mailer': ['Gmail', 'Outlook', 'Apple Mail', 'Thunderbird'][Math.floor(Math.random() * 4)],
      'x-priority': template.priority === 'high' ? '1' : template.priority === 'low' ? '5' : '3',
    },
    from: template.from,
    to: [{ email: '<EMAIL>', name: 'You', type: 'personal' }],
    date,
    receivedDate: new Date(date.getTime() + Math.random() * 60000), // Slight delay for realism
    attachments,
    labels: category === 'Spam' ? ['SPAM'] : category === 'Important' ? ['INBOX', 'IMPORTANT'] : ['INBOX'],
    flags: {
      isUnread,
      isImportant: category === 'Important' || template.priority === 'high' || Math.random() > 0.85,
      isStarred: Math.random() > 0.92,
      isDraft: false,
      isSpam: category === 'Spam',
      isTrash: false,
    },
    size: template.body.length + (attachments.reduce((sum, att) => sum + att.size, 0)),
    rawSize: template.body.length * 1.3, // Account for headers and encoding
    historyId: `${Math.floor(Date.now() / 1000)}.${Math.random().toString(36).substr(2, 8)}`,
    internalDate: date.toISOString(),
    category,
    aiAnalysis: {
      sentiment: category === 'Spam' ? 'negative' : 
                 category === 'Personal' ? 'positive' : 
                 ['positive', 'neutral', 'negative'][Math.floor(Math.random() * 3)] as any,
      actionRequired: category === 'Important' || category === 'Work' || template.priority === 'high',
      suggestedActions: generateSuggestedActions(category, template.priority),
    },
  }
  
  return message
}

// Generate AI suggested actions based on email content
function generateSuggestedActions(category: EmailCategory, priority?: string): string[] {
  const baseActions = {
    'Work': ['Reply', 'Forward to team', 'Add to calendar', 'Archive'],
    'Personal': ['Reply', 'Call', 'Add to contacts'],
    'Important': ['Reply urgently', 'Forward', 'Add reminder'],
    'Marketing': ['Unsubscribe', 'Archive', 'Mark as spam'],
    'Newsletter': ['Archive', 'Unsubscribe', 'Save for later'],
    'Receipt': ['Archive', 'Forward to accounting', 'Save to records'],
    'Travel': ['Add to calendar', 'Save confirmation', 'Forward'],
    'Finance': ['Review', 'Archive', 'Forward to advisor'],
    'Security': ['Verify account', 'Update password', 'Contact support'],
    'Notification': ['Archive', 'Mark as read'],
    'Updates': ['Archive', 'Unsubscribe'],
    'Social': ['Reply', 'Archive', 'Connect'],
    'Spam': ['Delete', 'Mark as spam', 'Block sender'],
    'Other': ['Archive', 'Reply']
  }
  
  const actions = baseActions[category] || ['Archive', 'Reply']
  return actions.slice(0, Math.floor(Math.random() * 3) + 1)
}

// Generate a thread with multiple messages
export function generateThread(
  id: string,
  messageCount: number = 1,
  category?: EmailCategory
): Thread {
  const threadCategory = category || Object.keys(emailTemplates)[
    Math.floor(Math.random() * Object.keys(emailTemplates).length)
  ] as EmailCategory
  
  const now = new Date()
  const messages: ParsedMessage[] = []
  
  // Generate messages in reverse chronological order
  for (let i = 0; i < messageCount; i++) {
    const messageDate = new Date(now.getTime() - i * 3600000) // 1 hour apart
    const isUnread = i === 0 && Math.random() > 0.3 // 70% chance first message is unread
    
    messages.push(
      generateMessage(
        `msg-${id}-${i}`,
        id,
        threadCategory,
        messageDate,
        isUnread
      )
    )
  }
  
  const firstMessage = messages[messages.length - 1]
  const lastMessage = messages[0]
  const unreadCount = messages.filter(m => m.flags.isUnread).length
  
  const thread: Thread = {
    id,
    messages,
    snippet: lastMessage.snippet,
    subject: firstMessage.subject,
    category: threadCategory,
    labels: ['INBOX'],
    participants: [
      {
        ...firstMessage.from,
        role: 'sender',
        messageCount: messages.filter(m => m.from.email === firstMessage.from.email).length,
        lastMessageDate: lastMessage.date,
      },
      {
        email: '<EMAIL>',
        name: 'John Doe',
        role: 'recipient',
        messageCount: messages.length,
        lastMessageDate: lastMessage.date,
      },
    ],
    lastMessageDate: lastMessage.date,
    firstMessageDate: firstMessage.date,
    messageCount: messages.length,
    unreadCount,
    status: {
      isUnread: unreadCount > 0,
      isImportant: messages.some(m => m.flags.isImportant),
      isStarred: messages.some(m => m.flags.isStarred),
      isDraft: false,
      isSpam: threadCategory === 'Spam',
      isTrash: false,
      isSnoozed: false,
    },
  }
  
  return thread
}

// Generate multiple threads
export function generateThreads(count: number): Thread[] {
  const threads: Thread[] = []
  const categories = Object.keys(emailTemplates) as EmailCategory[]
  
  for (let i = 0; i < count; i++) {
    const messageCount = Math.floor(Math.random() * 5) + 1 // 1-5 messages per thread
    const category = categories[Math.floor(Math.random() * categories.length)]
    
    threads.push(generateThread(`thread-${i}`, messageCount, category))
  }
  
  return threads.sort((a, b) => b.lastMessageDate.getTime() - a.lastMessageDate.getTime())
}

// Mock data exports
export const mockThreads = generateThreads(50)
export const mockEmails = mockThreads.flatMap(thread => thread.messages)

// Helper to get threads by category
export function getThreadsByCategory(category: EmailCategory): Thread[] {
  return mockThreads.filter(thread => thread.category === category)
}

// Helper to get unread threads
export function getUnreadThreads(): Thread[] {
  return mockThreads.filter(thread => thread.status.isUnread)
}

// Helper to search threads
export function searchThreads(query: string): Thread[] {
  const searchTerm = query.toLowerCase()
  
  return mockThreads.filter(thread => 
    thread.subject.toLowerCase().includes(searchTerm) ||
    thread.snippet.toLowerCase().includes(searchTerm) ||
    thread.participants.some(p => 
      p.email.toLowerCase().includes(searchTerm) ||
      p.name?.toLowerCase().includes(searchTerm)
    )
  )
}

// Helper to get thread statistics
export function getThreadStats() {
  const stats = {
    total: mockThreads.length,
    unread: mockThreads.filter(t => t.status.isUnread).length,
    important: mockThreads.filter(t => t.status.isImportant).length,
    starred: mockThreads.filter(t => t.status.isStarred).length,
    byCategory: {} as Record<EmailCategory, number>,
  }
  
  // Count by category
  mockThreads.forEach(thread => {
    const category = thread.category || 'Other'
    stats.byCategory[category] = (stats.byCategory[category] || 0) + 1
  })
  
  return stats
}

// Additional helper functions needed by handlers
export function getImportantThreads(): Thread[] {
  return mockThreads.filter(thread => thread.status.isImportant && !thread.status.isTrash)
}

export function getStarredThreads(): Thread[] {
  return mockThreads.filter(thread => thread.status.isStarred && !thread.status.isTrash)
}

export function getThreadsByLabel(label: string): Thread[] {
  return mockThreads.filter(thread => thread.labels.includes(label) && !thread.status.isTrash)
}

// Simulation functions for real-time features
let emailIdCounter = mockEmails.length
let threadIdCounter = mockThreads.length

export function simulateNewEmail(category?: EmailCategory): ParsedMessage {
  const categories = Object.keys(emailTemplates) as EmailCategory[]
  const selectedCategory = category || categories[Math.floor(Math.random() * categories.length)]
  
  const newEmailId = `msg-sim-${emailIdCounter++}`
  const newThreadId = `thread-sim-${threadIdCounter++}`
  
  const newMessage = generateMessage(
    newEmailId,
    newThreadId,
    selectedCategory,
    new Date(),
    true
  )
  
  // Create thread for the new message
  const newThread = {
    id: newThreadId,
    messages: [newMessage],
    snippet: newMessage.snippet,
    subject: newMessage.subject,
    category: selectedCategory,
    labels: newMessage.labels,
    participants: [
      {
        ...newMessage.from,
        role: 'sender' as any,
        messageCount: 1,
        lastMessageDate: newMessage.date,
      },
      {
        email: '<EMAIL>',
        name: 'You',
        type: 'personal' as any,
        role: 'recipient' as any,
        messageCount: 1,
        lastMessageDate: newMessage.date,
      },
    ],
    lastMessageDate: newMessage.date,
    firstMessageDate: newMessage.date,
    messageCount: 1,
    unreadCount: 1,
    status: {
      isUnread: true,
      isImportant: newMessage.flags.isImportant,
      isStarred: false,
      isDraft: false,
      isSpam: selectedCategory === 'Spam',
      isTrash: false,
      isSnoozed: false,
    },
    priority: 'medium' as any,
    aiSummary: `New ${selectedCategory.toLowerCase()} email from ${newMessage.from.name}`,
  }
  
  // Add to collections
  mockEmails.unshift(newMessage)
  mockThreads.unshift(newThread)
  
  return newMessage
}

export function simulateReply(threadId: string): ParsedMessage | null {
  const thread = mockThreads.find(t => t.id === threadId)
  if (!thread || thread.status.isSpam) return null
  
  const replyId = `msg-reply-${emailIdCounter++}`
  const lastMessage = thread.messages[thread.messages.length - 1]
  
  // Generate reply with 5 minute to 2 hour delay
  const replyDelay = 5 * 60 * 1000 + Math.random() * (2 * 60 * 60 * 1000 - 5 * 60 * 1000)
  const replyDate = new Date(Date.now() + replyDelay)
  
  const reply = generateMessage(
    replyId,
    threadId,
    thread.category!,
    replyDate,
    true,
    true
  )
  
  // Update thread
  thread.messages.push(reply)
  thread.lastMessageDate = reply.date
  thread.messageCount++
  thread.unreadCount++
  thread.snippet = reply.snippet
  thread.status.isUnread = true
  
  // Add to emails collection
  mockEmails.push(reply)
  
  // Re-sort threads by last message date
  mockThreads.sort((a, b) => b.lastMessageDate.getTime() - a.lastMessageDate.getTime())
  
  return reply
}