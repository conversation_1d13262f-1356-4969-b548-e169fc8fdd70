// Mock user data for MSW handlers

export interface MockUser {
  id: string
  email: string
  name: string
  avatar?: string
  isVerified: boolean
  createdAt: Date
  lastLoginAt: Date
  subscription: {
    plan: 'free' | 'pro' | 'enterprise'
    isActive: boolean
    expiresAt?: Date
  }
  preferences: {
    theme: 'light' | 'dark' | 'system'
    notifications: boolean
    language: string
  }
}

export const mockUser: MockUser = {
  id: 'user_123456789',
  email: '<EMAIL>',
  name: 'Demo User',
  avatar: 'https://ui-avatars.com/api/?name=Demo+User&background=3b82f6&color=ffffff',
  isVerified: true,
  createdAt: new Date('2024-01-01'),
  lastLoginAt: new Date(),
  subscription: {
    plan: 'pro',
    isActive: true,
    expiresAt: new Date('2025-01-01')
  },
  preferences: {
    theme: 'system',
    notifications: true,
    language: 'en'
  }
}

export const mockUsers: MockUser[] = [
  mockUser,
  {
    id: 'user_987654321',
    email: '<EMAIL>',
    name: 'Admin User',
    avatar: 'https://ui-avatars.com/api/?name=Admin+User&background=059669&color=ffffff',
    isVerified: true,
    createdAt: new Date('2023-06-01'),
    lastLoginAt: new Date(),
    subscription: {
      plan: 'enterprise',
      isActive: true
    },
    preferences: {
      theme: 'dark',
      notifications: true,
      language: 'en'
    }
  }
]