import React from 'react';
import { clsx } from 'clsx';

interface SettingsSliderProps {
  value: number;
  onChange: (value: number) => void;
  min: number;
  max: number;
  step?: number;
  disabled?: boolean;
  className?: string;
  showValue?: boolean;
  formatValue?: (value: number) => string;
  marks?: { value: number; label: string }[];
  size?: 'sm' | 'md' | 'lg';
}

export function SettingsSlider({
  value,
  onChange,
  min,
  max,
  step = 1,
  disabled = false,
  className,
  showValue = true,
  formatValue = (val) => val.toString(),
  marks = [],
  size = 'md',
}: SettingsSliderProps) {
  const percentage = ((value - min) / (max - min)) * 100;

  const sizes = {
    sm: {
      track: 'h-1',
      thumb: 'w-3 h-3',
      value: 'text-xs',
    },
    md: {
      track: 'h-2',
      thumb: 'w-4 h-4',
      value: 'text-sm',
    },
    lg: {
      track: 'h-3',
      thumb: 'w-5 h-5',
      value: 'text-base',
    },
  };

  const sizeConfig = sizes[size];

  return (
    <div className={clsx('space-y-2', className)}>
      <div className="flex items-center justify-between">
        {showValue && (
          <span className={clsx(
            'font-medium text-gray-900',
            sizeConfig.value,
            disabled && 'text-gray-500'
          )}>
            {formatValue(value)}
          </span>
        )}
      </div>

      <div className="relative">
        {/* Track */}
        <div className={clsx(
          'relative bg-gray-200 rounded-full',
          sizeConfig.track,
          disabled && 'opacity-50'
        )}>
          {/* Fill */}
          <div
            className={clsx(
              'absolute top-0 left-0 bg-blue-600 rounded-full transition-all duration-150',
              sizeConfig.track
            )}
            style={{ width: `${percentage}%` }}
          />
          
          {/* Marks */}
          {marks.map((mark) => {
            const markPercentage = ((mark.value - min) / (max - min)) * 100;
            return (
              <div
                key={mark.value}
                className="absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2"
                style={{ left: `${markPercentage}%` }}
              >
                <div className="w-1 h-1 bg-gray-400 rounded-full" />
              </div>
            );
          })}
        </div>

        {/* Slider input */}
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          disabled={disabled}
          className={clsx(
            'absolute top-0 left-0 w-full opacity-0 cursor-pointer',
            sizeConfig.track,
            disabled && 'cursor-not-allowed'
          )}
        />

        {/* Thumb */}
        <div
          className={clsx(
            'absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2',
            'bg-white border-2 border-blue-600 rounded-full shadow-sm',
            'transition-all duration-150 pointer-events-none',
            sizeConfig.thumb,
            disabled && 'border-gray-400'
          )}
          style={{ left: `${percentage}%` }}
        />
      </div>

      {/* Mark labels */}
      {marks.length > 0 && (
        <div className="relative">
          {marks.map((mark) => {
            const markPercentage = ((mark.value - min) / (max - min)) * 100;
            return (
              <div
                key={mark.value}
                className="absolute transform -translate-x-1/2"
                style={{ left: `${markPercentage}%` }}
              >
                <span className={clsx(
                  'text-xs text-gray-500',
                  disabled && 'text-gray-400'
                )}>
                  {mark.label}
                </span>
              </div>
            );
          })}
        </div>
      )}

      {/* Min/Max labels */}
      <div className="flex justify-between">
        <span className={clsx(
          'text-xs text-gray-500',
          disabled && 'text-gray-400'
        )}>
          {formatValue(min)}
        </span>
        <span className={clsx(
          'text-xs text-gray-500',
          disabled && 'text-gray-400'
        )}>
          {formatValue(max)}
        </span>
      </div>
    </div>
  );
}