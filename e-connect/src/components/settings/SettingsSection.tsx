import React from 'react';
import { clsx } from 'clsx';

interface SettingsSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  badge?: React.ReactNode;
  actions?: React.ReactNode;
}

export function SettingsSection({
  title,
  description,
  children,
  className,
  collapsible = false,
  defaultExpanded = true,
  badge,
  actions,
}: SettingsSectionProps) {
  const [expanded, setExpanded] = React.useState(defaultExpanded);

  return (
    <div className={clsx('border-b border-gray-200 pb-8 mb-8 last:border-b-0 last:pb-0 last:mb-0', className)}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          {collapsible ? (
            <button
              onClick={() => setExpanded(!expanded)}
              className="flex items-center gap-2 text-left group"
            >
              <svg
                className={clsx(
                  'w-4 h-4 text-gray-400 transition-transform',
                  expanded ? 'rotate-90' : 'rotate-0'
                )}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
              <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600">
                {title}
              </h3>
            </button>
          ) : (
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          )}
          {badge && <div className="flex-shrink-0">{badge}</div>}
        </div>
        {actions && <div className="flex items-center gap-2">{actions}</div>}
      </div>
      
      {description && (
        <p className="text-sm text-gray-600 mb-6">{description}</p>
      )}
      
      {(!collapsible || expanded) && (
        <div className="space-y-6">{children}</div>
      )}
    </div>
  );
}