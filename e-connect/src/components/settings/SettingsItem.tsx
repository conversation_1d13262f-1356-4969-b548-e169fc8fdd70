import React from 'react';
import { clsx } from 'clsx';
import { Tooltip } from '../ui/Tooltip';

interface SettingsItemProps {
  label: string;
  description?: string;
  help?: string;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  required?: boolean;
  error?: string;
  warning?: string;
  orientation?: 'horizontal' | 'vertical';
}

export function SettingsItem({
  label,
  description,
  help,
  children,
  className,
  disabled = false,
  required = false,
  error,
  warning,
  orientation = 'horizontal',
}: SettingsItemProps) {
  const isHorizontal = orientation === 'horizontal';

  const labelContent = (
    <div className={clsx(
      isHorizontal ? 'flex-1' : 'mb-2',
      disabled && 'opacity-50'
    )}>
      <div className="flex items-center gap-1">
        <label className={clsx(
          'text-sm font-medium text-gray-900',
          disabled && 'text-gray-500'
        )}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        {help && (
          <Tooltip content={help}>
            <button type="button" className="text-gray-400 hover:text-gray-600">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </button>
          </Tooltip>
        )}
      </div>
      {description && (
        <p className={clsx(
          'text-xs text-gray-500 mt-1',
          disabled && 'text-gray-400'
        )}>
          {description}
        </p>
      )}
    </div>
  );

  const controlContent = (
    <div className={clsx(
      isHorizontal ? 'flex-shrink-0' : '',
      disabled && 'opacity-50 pointer-events-none'
    )}>
      {children}
    </div>
  );

  return (
    <div className={clsx(
      'space-y-1',
      className
    )}>
      <div className={clsx(
        isHorizontal ? 'flex items-start justify-between gap-4' : 'block'
      )}>
        {labelContent}
        {controlContent}
      </div>
      
      {(error || warning) && (
        <div className="mt-1">
          {error && (
            <p className="text-xs text-red-600 flex items-center gap-1">
              <svg className="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {error}
            </p>
          )}
          {warning && !error && (
            <p className="text-xs text-amber-600 flex items-center gap-1">
              <svg className="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              {warning}
            </p>
          )}
        </div>
      )}
    </div>
  );
}