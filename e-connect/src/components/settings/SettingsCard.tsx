import React from 'react';
import { clsx } from 'clsx';

interface SettingsCardProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'outlined' | 'filled';
  padding?: 'sm' | 'md' | 'lg';
  header?: React.ReactNode;
  footer?: React.ReactNode;
}

export function SettingsCard({
  title,
  description,
  children,
  className,
  variant = 'default',
  padding = 'md',
  header,
  footer,
}: SettingsCardProps) {
  const variants = {
    default: 'bg-white border border-gray-200 shadow-sm',
    outlined: 'bg-white border-2 border-gray-300',
    filled: 'bg-gray-50 border border-gray-200',
  };

  const paddings = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  };

  return (
    <div className={clsx(
      'rounded-lg',
      variants[variant],
      className
    )}>
      {(header || title || description) && (
        <div className={clsx(
          'border-b border-gray-200',
          paddings[padding],
          'pb-4'
        )}>
          {header || (
            <div>
              {title && (
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {title}
                </h3>
              )}
              {description && (
                <p className="text-sm text-gray-600">
                  {description}
                </p>
              )}
            </div>
          )}
        </div>
      )}

      <div className={clsx(
        paddings[padding],
        (header || title || description) && 'pt-6'
      )}>
        {children}
      </div>

      {footer && (
        <div className={clsx(
          'border-t border-gray-200',
          paddings[padding],
          'pt-4'
        )}>
          {footer}
        </div>
      )}
    </div>
  );
}

interface SettingsCardGroupProps {
  children: React.ReactNode;
  className?: string;
  spacing?: 'sm' | 'md' | 'lg';
}

export function SettingsCardGroup({
  children,
  className,
  spacing = 'md',
}: SettingsCardGroupProps) {
  const spacings = {
    sm: 'space-y-3',
    md: 'space-y-6',
    lg: 'space-y-8',
  };

  return (
    <div className={clsx(spacings[spacing], className)}>
      {children}
    </div>
  );
}