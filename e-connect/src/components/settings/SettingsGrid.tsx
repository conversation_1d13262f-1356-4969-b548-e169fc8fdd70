import React from 'react';
import { clsx } from 'clsx';

interface SettingsGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function SettingsGrid({
  children,
  columns = 1,
  gap = 'md',
  className,
}: SettingsGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  };

  const gridGap = {
    sm: 'gap-3',
    md: 'gap-6',
    lg: 'gap-8',
  };

  return (
    <div className={clsx(
      'grid',
      gridCols[columns],
      gridGap[gap],
      className
    )}>
      {children}
    </div>
  );
}

interface SettingsGridItemProps {
  children: React.ReactNode;
  className?: string;
  span?: 1 | 2 | 3;
}

export function SettingsGridItem({
  children,
  className,
  span = 1,
}: SettingsGridItemProps) {
  const spanClasses = {
    1: 'col-span-1',
    2: 'col-span-1 md:col-span-2',
    3: 'col-span-1 md:col-span-2 lg:col-span-3',
  };

  return (
    <div className={clsx(spanClasses[span], className)}>
      {children}
    </div>
  );
}