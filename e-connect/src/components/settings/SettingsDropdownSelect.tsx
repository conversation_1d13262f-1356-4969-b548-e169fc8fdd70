import React from 'react';
import { clsx } from 'clsx';

interface Option {
  value: string;
  label: string;
  description?: string;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface SettingsDropdownSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: Option[];
  placeholder?: string;
  disabled?: boolean;
  className?: string;
  showDescription?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function SettingsDropdownSelect({
  value,
  onChange,
  options,
  placeholder = 'Select an option',
  disabled = false,
  className,
  showDescription = false,
  size = 'md',
}: SettingsDropdownSelectProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const selectedOption = options.find(option => option.value === value);

  const sizes = {
    sm: 'px-2 py-1 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className={clsx('relative', className)}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={clsx(
          'w-full bg-white border border-gray-300 rounded-md shadow-sm',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
          'flex items-center justify-between',
          sizes[size],
          disabled && 'opacity-50 cursor-not-allowed bg-gray-50'
        )}
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          {selectedOption?.icon && (
            <div className="flex-shrink-0 text-gray-400">
              {selectedOption.icon}
            </div>
          )}
          <div className="min-w-0 flex-1 text-left">
            <div className={clsx(
              'truncate',
              selectedOption ? 'text-gray-900' : 'text-gray-500'
            )}>
              {selectedOption?.label || placeholder}
            </div>
            {showDescription && selectedOption?.description && (
              <div className="text-xs text-gray-500 truncate">
                {selectedOption.description}
              </div>
            )}
          </div>
        </div>
        <div className="flex-shrink-0 ml-2">
          <svg
            className={clsx(
              'w-4 h-4 text-gray-400 transition-transform',
              isOpen ? 'rotate-180' : 'rotate-0'
            )}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              onClick={() => handleSelect(option.value)}
              disabled={option.disabled}
              className={clsx(
                'w-full px-3 py-2 text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none',
                'flex items-center gap-2',
                option.value === value && 'bg-blue-50 text-blue-700',
                option.disabled && 'opacity-50 cursor-not-allowed'
              )}
            >
              {option.icon && (
                <div className="flex-shrink-0 text-gray-400">
                  {option.icon}
                </div>
              )}
              <div className="min-w-0 flex-1">
                <div className="text-sm font-medium">{option.label}</div>
                {option.description && (
                  <div className="text-xs text-gray-500">{option.description}</div>
                )}
              </div>
              {option.value === value && (
                <div className="flex-shrink-0">
                  <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              )}
            </button>
          ))}
        </div>
      )}

      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}