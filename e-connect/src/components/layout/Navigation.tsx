import React from 'react'
import { <PERSON> } from '@tanstack/react-router'
import { 
  EnvelopeIcon, 
  Cog6ToothIcon,
  SparklesIcon,
  InboxIcon,
  FunnelIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  BoltIcon,
  UserCircleIcon,
  ChevronDownIcon,
  BellIcon,
  KeyIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline'
import { AccountSwitcher } from '../account-switcher'
import { useMultiAccountStore } from '../../stores/multiAccountStore'
import { useSettingsStore } from '../../stores/settingsStore'
import { useEmailStore } from '../../stores/emailStore'
import { cn } from '../../lib/utils'

interface NavigationProps {
  className?: string
}

const navigation = [
  { 
    name: 'Inbox', 
    href: '/', 
    icon: InboxIcon,
    description: 'View your emails',
    badge: 'unread'
  },
  { 
    name: 'Mail', 
    href: '/mail', 
    icon: EnvelopeIcon,
    description: 'Email management',
    badge: null
  },
  { 
    name: 'Rules', 
    href: '/automation', 
    icon: FunnelIcon,
    description: 'Email automation rules',
    badge: 'active'
  },
  { 
    name: 'Assistant', 
    href: '/assistant', 
    icon: SparklesIcon,
    description: 'AI email assistant',
    badge: null
  },
  { 
    name: 'Analytics', 
    href: '/stats', 
    icon: ChartBarIcon,
    description: 'Email insights & stats',
    badge: null
  },
  { 
    name: 'Clean', 
    href: '/clean', 
    icon: BoltIcon,
    description: 'Bulk clean & organize',
    badge: null
  },
  { 
    name: 'Bulk Actions', 
    href: '/bulk-unsubscribe', 
    icon: ShieldCheckIcon,
    description: 'Mass unsubscribe & block',
    badge: null
  },
  { 
    name: 'Cold Blocker', 
    href: '/cold-email-blocker', 
    icon: ShieldCheckIcon,
    description: 'Block cold emails',
    badge: null
  },
]

const secondaryNavigation = [
  { 
    name: 'Settings', 
    href: '/settings', 
    icon: Cog6ToothIcon,
    description: 'App preferences & config'
  },
]

const quickActions = [
  { name: 'Shortcuts', icon: KeyIcon, action: 'toggleShortcuts' },
  { name: 'Notifications', icon: BellIcon, action: 'toggleNotifications' },
]

export function Navigation({ className }: NavigationProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = React.useState(false)
  const [showAccountMenu, setShowAccountMenu] = React.useState(false)
  
  const { currentAccount, accounts } = useMultiAccountStore()
  const { settings } = useSettingsStore()
  const { threads } = useEmailStore()
  
  // Calculate unread count from threads
  const unreadCount = Array.isArray(threads) 
    ? threads.reduce((count, thread) => {
        // Handle both Thread and UnifiedThread types
        const threadUnreadCount = typeof thread === 'object' && thread && 'unreadCount' in thread 
          ? (thread.unreadCount as number) || 0 
          : 0
        return count + threadUnreadCount
      }, 0)
    : 0
  
  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'toggleShortcuts':
        // toggleKeyboardShortcuts() - Method not available, implement later
        break
      case 'toggleNotifications':
        // Toggle notifications (would need notification store)
        break
    }
  }

  const getBadgeCount = (badge: string | null) => {
    switch (badge) {
      case 'unread':
        return unreadCount
      case 'active':
        // Return active rules count if available
        return null
      default:
        return null
    }
  }

  return (
    <>
      {/* Desktop Sidebar */}
      <div className={cn(
        "hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0",
        "bg-white border-r border-gray-200 dark:bg-gray-900 dark:border-gray-700",
        className
      )}>
        <div className="flex flex-col flex-grow pt-5 overflow-y-auto">
          {/* Logo */}
          <div className="flex items-center flex-shrink-0 px-4">
            <Link to="/" className="flex items-center group">
              <EnvelopeIcon className="h-8 w-8 text-blue-600 group-hover:text-blue-700 transition-colors" />
              <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                E-Connect 2
              </span>
            </Link>
          </div>

          {/* Account Switcher */}
          <div className="mt-6 px-4">
            <AccountSwitcher />
          </div>

          {/* Main Navigation */}
          <div className="mt-8 flex-grow flex flex-col">
            <nav className="flex-1 px-2 space-y-1">
              {navigation.map((item) => {
                const badgeCount = getBadgeCount(item.badge)
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white [&.active]:bg-blue-50 [&.active]:text-blue-700 dark:[&.active]:bg-blue-900/50 dark:[&.active]:text-blue-300 transition-colors"
                    activeProps={{
                      className: 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300',
                    }}
                    title={item.description}
                  >
                    <item.icon className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500 [.active_&]:text-blue-600 dark:text-gray-500 dark:group-hover:text-gray-400 dark:[.active_&]:text-blue-400 transition-colors" />
                    <span className="flex-1">{item.name}</span>
                    {badgeCount && typeof badgeCount === 'number' && badgeCount > 0 && (
                      <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300">
                        {badgeCount > 99 ? '99+' : badgeCount}
                      </span>
                    )}
                  </Link>
                )
              })}
            </nav>
          </div>

          {/* Quick Actions */}
          <div className="flex-shrink-0 px-4 py-2 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                Quick Actions
              </span>
            </div>
            <div className="flex space-x-2">
              {quickActions.map((action) => (
                <button
                  key={action.name}
                  onClick={() => handleQuickAction(action.action)}
                  className={cn(
                    "p-2 rounded-md text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400 transition-colors",
                    action.action === 'toggleShortcuts' && settings?.shortcuts?.enabled && 
                    "text-blue-600 dark:text-blue-400"
                  )}
                  title={action.name}
                >
                  <action.icon className="h-4 w-4" />
                </button>
              ))}
            </div>
          </div>

          {/* Secondary Navigation */}
          <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 p-4">
            <nav className="space-y-1">
              {secondaryNavigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className="group flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white [&.active]:bg-blue-50 [&.active]:text-blue-700 dark:[&.active]:bg-blue-900/50 dark:[&.active]:text-blue-300 transition-colors"
                  activeProps={{
                    className: 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300',
                  }}
                  title={item.description}
                >
                  <item.icon className="mr-3 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500 [.active_&]:text-blue-600 dark:text-gray-500 dark:group-hover:text-gray-400 dark:[.active_&]:text-blue-400 transition-colors" />
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        {/* Mobile Header */}
        <div className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 dark:bg-gray-900 dark:border-gray-700 z-20">
          <div className="flex items-center justify-between px-4 py-3">
            <Link to="/" className="flex items-center">
              <EnvelopeIcon className="h-6 w-6 text-blue-600" />
              <span className="ml-2 text-lg font-bold text-gray-900 dark:text-white">
                E-Connect 2
              </span>
            </Link>
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-500 dark:text-gray-500 dark:hover:text-gray-400"
            >
              {isMobileMenuOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <div className="fixed inset-0 z-30 md:hidden">
            <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setIsMobileMenuOpen(false)} />
            <div className="fixed top-0 left-0 bottom-0 w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
              <div className="pt-16 p-4">
                {/* Account Switcher */}
                <div className="mb-6">
                  <AccountSwitcher />
                </div>

                {/* Navigation */}
                <nav className="space-y-1 mb-6">
                  {navigation.map((item) => {
                    const badgeCount = getBadgeCount(item.badge)
                    return (
                      <Link
                        key={item.name}
                        to={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="group flex items-center px-3 py-3 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white [&.active]:bg-blue-50 [&.active]:text-blue-700 dark:[&.active]:bg-blue-900/50 dark:[&.active]:text-blue-300 transition-colors"
                        activeProps={{
                          className: 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300',
                        }}
                      >
                        <item.icon className="mr-4 h-6 w-6 flex-shrink-0 text-gray-400 group-hover:text-gray-500 [.active_&]:text-blue-600 dark:text-gray-500 dark:group-hover:text-gray-400 dark:[.active_&]:text-blue-400" />
                        <span className="flex-1">{item.name}</span>
                        {badgeCount && typeof badgeCount === 'number' && badgeCount > 0 && (
                          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300">
                            {badgeCount > 99 ? '99+' : badgeCount}
                          </span>
                        )}
                      </Link>
                    )
                  })}
                </nav>

                {/* Secondary Navigation */}
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <nav className="space-y-1">
                    {secondaryNavigation.map((item) => (
                      <Link
                        key={item.name}
                        to={item.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className="group flex items-center px-3 py-3 text-base font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 dark:text-gray-300 dark:hover:bg-gray-800 dark:hover:text-white [&.active]:bg-blue-50 [&.active]:text-blue-700 dark:[&.active]:bg-blue-900/50 dark:[&.active]:text-blue-300 transition-colors"
                        activeProps={{
                          className: 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300',
                        }}
                      >
                        <item.icon className="mr-4 h-6 w-6 flex-shrink-0 text-gray-400 group-hover:text-gray-500 [.active_&]:text-blue-600 dark:text-gray-500 dark:group-hover:text-gray-400 dark:[.active_&]:text-blue-400" />
                        {item.name}
                      </Link>
                    ))}
                  </nav>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Mobile Bottom Navigation */}
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 dark:bg-gray-900 dark:border-gray-700 z-10">
          <nav className="flex justify-around py-2">
            {navigation.slice(0, 4).map((item) => {
              const badgeCount = getBadgeCount(item.badge)
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className="relative flex flex-col items-center px-2 py-1 text-xs font-medium text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white [&.active]:text-blue-700 dark:[&.active]:text-blue-300 transition-colors"
                  activeProps={{
                    className: 'text-blue-700 dark:text-blue-300',
                  }}
                >
                  <item.icon className="h-6 w-6 mb-1" />
                  <span className="truncate max-w-[50px]">{item.name}</span>
                  {badgeCount && badgeCount > 0 && (
                    <span className="absolute -top-1 -right-1 inline-flex items-center justify-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-red-500 text-white min-w-[18px] h-[18px]">
                      {badgeCount > 9 ? '9+' : badgeCount}
                    </span>
                  )}
                </Link>
              )
            })}
            {/* Settings quick access */}
            <Link
              to="/settings"
              className="relative flex flex-col items-center px-2 py-1 text-xs font-medium text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white [&.active]:text-blue-700 dark:[&.active]:text-blue-300 transition-colors"
              activeProps={{
                className: 'text-blue-700 dark:text-blue-300',
              }}
            >
              <Cog6ToothIcon className="h-6 w-6 mb-1" />
              <span className="truncate max-w-[50px]">Settings</span>
            </Link>
          </nav>
        </div>
      </div>
    </>
  )
}

export default Navigation