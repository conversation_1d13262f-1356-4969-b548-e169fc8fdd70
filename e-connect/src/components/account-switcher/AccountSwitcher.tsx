import React, { useState, useRef, useEffect } from 'react'
import { ChevronDownIcon, PlusIcon, CheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { useMultiAccountStore } from '../../stores/multiAccountStore'
import { useEmailStore } from '../../stores/emailStore'
import type { EmailAccount, AccountStatus } from '../../types/multi-account'
import { Dialog } from '../ui/Dialog'
import { Button } from '../ui/Button'
import { Badge } from '../ui/Badge'

interface AccountSwitcherProps {
  showAccountName?: boolean
  showUnreadCount?: boolean
  className?: string
}

const statusConfig: Record<AccountStatus, { 
  color: string
  bgColor: string
  icon?: React.ReactNode
  label: string
}> = {
  active: {
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    label: 'Active'
  },
  disconnected: {
    color: 'text-gray-500',
    bgColor: 'bg-gray-100',
    icon: <ExclamationTriangleIcon className="w-3 h-3" />,
    label: 'Disconnected'
  },
  error: {
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    icon: <ExclamationTriangleIcon className="w-3 h-3" />,
    label: 'Error'
  },
  rate_limited: {
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    icon: <ExclamationTriangleIcon className="w-3 h-3" />,
    label: 'Rate Limited'
  },
  maintenance: {
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    icon: <ExclamationTriangleIcon className="w-3 h-3" />,
    label: 'Maintenance'
  },
  suspended: {
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    label: 'Suspended'
  },
  expired: {
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    icon: <ExclamationTriangleIcon className="w-3 h-3" />,
    label: 'Expired'
  }
}

export function AccountSwitcher({ 
  showAccountName = true, 
  showUnreadCount = true,
  className = ''
}: AccountSwitcherProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [showAddAccount, setShowAddAccount] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  
  const {
    accounts,
    activeAccountId,
    switchToAccount,
    recentAccounts,
    isLoading,
    error
  } = useMultiAccountStore()
  
  const {
    isUnifiedView,
    switchToUnifiedView,
    stats
  } = useEmailStore()

  const activeAccount = accounts.find(acc => acc.id === activeAccountId)
  const sortedAccounts = [...accounts].sort((a, b) => {
    // Sort by: default first, then by display order, then by name
    if (a.isDefault && !b.isDefault) return -1
    if (!a.isDefault && b.isDefault) return 1
    if (a.displayOrder !== b.displayOrder) return a.displayOrder - b.displayOrder
    return a.name.localeCompare(b.name)
  })

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleAccountSwitch = async (accountId: string | null) => {
    try {
      if (accountId === null) {
        switchToUnifiedView()
      } else {
        await switchToAccount(accountId)
      }
      setIsOpen(false)
    } catch (error) {
      console.error('Failed to switch account:', error)
    }
  }

  const getAccountInitials = (account: EmailAccount) => {
    return account.name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getAccountAvatar = (account: EmailAccount) => {
    if (account.avatar) {
      return (
        <img
          src={account.avatar}
          alt={account.name}
          className="w-8 h-8 rounded-full object-cover"
        />
      )
    }

    return (
      <div
        className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
        style={{ backgroundColor: account.color }}
      >
        {getAccountInitials(account)}
      </div>
    )
  }

  const getCurrentDisplayInfo = () => {
    if (isUnifiedView) {
      return {
        name: 'All Accounts',
        email: `${accounts.length} accounts`,
        avatar: (
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-medium">
            ∞
          </div>
        ),
        unreadCount: stats.totalUnread
      }
    }

    if (activeAccount) {
      return {
        name: activeAccount.name,
        email: activeAccount.email,
        avatar: getAccountAvatar(activeAccount),
        unreadCount: activeAccount.stats.unreadEmails
      }
    }

    return {
      name: 'No Account',
      email: 'Select an account',
      avatar: (
        <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
          <PlusIcon className="w-4 h-4 text-gray-600" />
        </div>
      ),
      unreadCount: 0
    }
  }

  const currentInfo = getCurrentDisplayInfo()

  return (
    <>
      <div className={`relative ${className}`} ref={dropdownRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors group"
          disabled={isLoading}
        >
          {/* Avatar */}
          <div className="relative">
            {currentInfo.avatar}
            {/* Status indicator */}
            {activeAccount && (
              <div
                className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                  statusConfig[activeAccount.status].bgColor
                }`}
              >
                {statusConfig[activeAccount.status].icon && (
                  <div className={`w-full h-full flex items-center justify-center ${statusConfig[activeAccount.status].color}`}>
                    {statusConfig[activeAccount.status].icon}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Account info */}
          {showAccountName && (
            <div className="flex-1 min-w-0 text-left">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {currentInfo.name}
                </p>
                {showUnreadCount && currentInfo.unreadCount > 0 && (
                  <Badge variant="primary" size="sm">
                    {currentInfo.unreadCount}
                  </Badge>
                )}
              </div>
              <p className="text-xs text-gray-500 truncate">
                {currentInfo.email}
              </p>
            </div>
          )}

          {/* Dropdown indicator */}
          <ChevronDownIcon 
            className={`w-4 h-4 text-gray-400 transition-transform ${
              isOpen ? 'rotate-180' : ''
            }`}
          />
        </button>

        {/* Dropdown menu */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-y-auto">
            {/* Unified view option */}
            <button
              onClick={() => handleAccountSwitch(null)}
              className={`w-full flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors ${
                isUnifiedView ? 'bg-blue-50' : ''
              }`}
            >
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-medium">
                ∞
              </div>
              <div className="flex-1 min-w-0 text-left">
                <div className="flex items-center space-x-2">
                  <p className="text-sm font-medium text-gray-900">
                    All Accounts
                  </p>
                  {isUnifiedView && (
                    <CheckIcon className="w-4 h-4 text-blue-600" />
                  )}
                </div>
                <p className="text-xs text-gray-500">
                  View emails from all accounts
                </p>
              </div>
              {showUnreadCount && stats.totalUnread > 0 && (
                <Badge variant="primary" size="sm">
                  {stats.totalUnread}
                </Badge>
              )}
            </button>

            {/* Divider */}
            {accounts.length > 0 && (
              <div className="border-t border-gray-200" />
            )}

            {/* Account list */}
            {sortedAccounts.map((account) => (
              <button
                key={account.id}
                onClick={() => handleAccountSwitch(account.id)}
                className={`w-full flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors ${
                  activeAccountId === account.id && !isUnifiedView ? 'bg-blue-50' : ''
                }`}
              >
                <div className="relative">
                  {getAccountAvatar(account)}
                  {/* Status indicator */}
                  <div
                    className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                      statusConfig[account.status].bgColor
                    }`}
                    title={statusConfig[account.status].label}
                  >
                    {statusConfig[account.status].icon && (
                      <div className={`w-full h-full flex items-center justify-center ${statusConfig[account.status].color}`}>
                        {statusConfig[account.status].icon}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex-1 min-w-0 text-left">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {account.name}
                    </p>
                    {account.isDefault && (
                      <Badge variant="secondary" size="sm">
                        Default
                      </Badge>
                    )}
                    {activeAccountId === account.id && !isUnifiedView && (
                      <CheckIcon className="w-4 h-4 text-blue-600" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <p className="text-xs text-gray-500 truncate">
                      {account.email}
                    </p>
                    {account.status !== 'active' && (
                      <span className={`text-xs ${statusConfig[account.status].color}`}>
                        {statusConfig[account.status].label}
                      </span>
                    )}
                  </div>
                </div>

                {showUnreadCount && account.stats.unreadEmails > 0 && (
                  <Badge variant="primary" size="sm">
                    {account.stats.unreadEmails}
                  </Badge>
                )}
              </button>
            ))}

            {/* Add account button */}
            <div className="border-t border-gray-200">
              <button
                onClick={() => {
                  setShowAddAccount(true)
                  setIsOpen(false)
                }}
                className="w-full flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors text-gray-600"
              >
                <div className="w-8 h-8 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center">
                  <PlusIcon className="w-4 h-4" />
                </div>
                <div className="flex-1 text-left">
                  <p className="text-sm font-medium">Add Account</p>
                  <p className="text-xs text-gray-500">Connect a new email account</p>
                </div>
              </button>
            </div>
          </div>
        )}

        {/* Error display */}
        {error && (
          <div className="absolute top-full left-0 right-0 mt-1 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700">
            {error}
          </div>
        )}
      </div>

      {/* Add Account Dialog */}
      <Dialog
        open={showAddAccount}
        onClose={() => setShowAddAccount(false)}
        title="Add Email Account"
        size="lg"
      >
        <div className="space-y-6">
          <p className="text-gray-600">
            Connect a new email account to manage multiple inboxes in one place.
          </p>

          {/* Provider selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[
              { id: 'gmail', name: 'Gmail', icon: '📧', description: 'Google Gmail accounts' },
              { id: 'outlook', name: 'Outlook', icon: '📮', description: 'Microsoft Outlook/Hotmail' },
              { id: 'imap', name: 'IMAP', icon: '📬', description: 'Custom IMAP server' },
              { id: 'exchange', name: 'Exchange', icon: '🏢', description: 'Microsoft Exchange' }
            ].map((provider) => (
              <button
                key={provider.id}
                className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors text-left"
                onClick={() => {
                  // Handle provider selection
                  console.log('Selected provider:', provider.id)
                }}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{provider.icon}</span>
                  <div>
                    <h3 className="font-medium text-gray-900">{provider.name}</h3>
                    <p className="text-sm text-gray-500">{provider.description}</p>
                  </div>
                </div>
              </button>
            ))}
          </div>

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowAddAccount(false)}
            >
              Cancel
            </Button>
          </div>
        </div>
      </Dialog>
    </>
  )
}