import React, { useState, useRef, useEffect } from 'react'
import { ChevronDownIcon, CheckIcon } from '@heroicons/react/24/outline'
import { useMultiAccountStore } from '../../stores/multiAccountStore'
import { useEmailStore } from '../../stores/emailStore'
import type { EmailAccount } from '../../types/multi-account'
import { Badge } from '../ui/Badge'

interface AccountSwitcherCompactProps {
  size?: 'sm' | 'md' | 'lg'
  showUnreadCount?: boolean
  className?: string
}

export function AccountSwitcherCompact({ 
  size = 'md',
  showUnreadCount = true,
  className = ''
}: AccountSwitcherCompactProps) {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)
  
  const {
    accounts,
    activeAccountId,
    switchToAccount,
    isLoading
  } = useMultiAccountStore()
  
  const {
    isUnifiedView,
    switchToUnifiedView,
    stats
  } = useEmailStore()

  const activeAccount = accounts.find(acc => acc.id === activeAccountId)
  
  const sizeClasses = {
    sm: 'w-6 h-6 text-xs',
    md: 'w-8 h-8 text-sm',
    lg: 'w-10 h-10 text-base'
  }

  const avatarSize = sizeClasses[size]

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleAccountSwitch = async (accountId: string | null) => {
    try {
      if (accountId === null) {
        switchToUnifiedView()
      } else {
        await switchToAccount(accountId)
      }
      setIsOpen(false)
    } catch (error) {
      console.error('Failed to switch account:', error)
    }
  }

  const getAccountInitials = (account: EmailAccount) => {
    return account.name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const getAccountAvatar = (account: EmailAccount) => {
    if (account.avatar) {
      return (
        <img
          src={account.avatar}
          alt={account.name}
          className={`${avatarSize} rounded-full object-cover`}
        />
      )
    }

    return (
      <div
        className={`${avatarSize} rounded-full flex items-center justify-center text-white font-medium`}
        style={{ backgroundColor: account.color }}
      >
        {getAccountInitials(account)}
      </div>
    )
  }

  const getCurrentAvatar = () => {
    if (isUnifiedView) {
      return (
        <div className={`${avatarSize} rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium`}>
          ∞
        </div>
      )
    }

    if (activeAccount) {
      return getAccountAvatar(activeAccount)
    }

    return (
      <div className={`${avatarSize} rounded-full bg-gray-300 flex items-center justify-center text-gray-600`}>
        ?
      </div>
    )
  }

  const getUnreadCount = () => {
    if (isUnifiedView) {
      return stats.totalUnread
    }
    return activeAccount?.stats.unreadEmails || 0
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full"
        disabled={isLoading}
        title={isUnifiedView ? 'All Accounts' : activeAccount?.name || 'No Account'}
      >
        {/* Avatar */}
        <div className="relative">
          {getCurrentAvatar()}
          
          {/* Unread badge */}
          {showUnreadCount && getUnreadCount() > 0 && (
            <div className="absolute -top-1 -right-1">
              <Badge variant="primary" size="sm">
                {getUnreadCount() > 99 ? '99+' : getUnreadCount()}
              </Badge>
            </div>
          )}
          
          {/* Dropdown indicator */}
          <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-white rounded-full border border-gray-200 flex items-center justify-center shadow-sm">
            <ChevronDownIcon 
              className={`w-2.5 h-2.5 text-gray-400 transition-transform ${
                isOpen ? 'rotate-180' : ''
              }`}
            />
          </div>
          
          {/* Status indicator for active account */}
          {activeAccount && !isUnifiedView && (
            <div
              className={`absolute top-0 left-0 w-3 h-3 rounded-full border-2 border-white ${
                activeAccount.status === 'active' 
                  ? 'bg-green-400' 
                  : activeAccount.status === 'error'
                    ? 'bg-red-400'
                    : 'bg-yellow-400'
              }`}
            />
          )}
        </div>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-80 overflow-y-auto">
          {/* Unified view option */}
          <button
            onClick={() => handleAccountSwitch(null)}
            className={`w-full flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors ${
              isUnifiedView ? 'bg-blue-50' : ''
            }`}
          >
            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-medium">
              ∞
            </div>
            <div className="flex-1 min-w-0 text-left">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium text-gray-900">
                  All Accounts
                </p>
                {isUnifiedView && (
                  <CheckIcon className="w-4 h-4 text-blue-600" />
                )}
              </div>
              <p className="text-xs text-gray-500">
                {accounts.length} accounts
              </p>
            </div>
            {showUnreadCount && stats.totalUnread > 0 && (
              <Badge variant="primary" size="sm">
                {stats.totalUnread}
              </Badge>
            )}
          </button>

          {/* Divider */}
          {accounts.length > 0 && (
            <div className="border-t border-gray-200" />
          )}

          {/* Account list */}
          {accounts
            .sort((a, b) => {
              if (a.isDefault && !b.isDefault) return -1
              if (!a.isDefault && b.isDefault) return 1
              if (a.displayOrder !== b.displayOrder) return a.displayOrder - b.displayOrder
              return a.name.localeCompare(b.name)
            })
            .map((account) => (
              <button
                key={account.id}
                onClick={() => handleAccountSwitch(account.id)}
                className={`w-full flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors ${
                  activeAccountId === account.id && !isUnifiedView ? 'bg-blue-50' : ''
                }`}
              >
                <div className="relative">
                  {getAccountAvatar(account)}
                  {/* Status indicator */}
                  <div
                    className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                      account.status === 'active' 
                        ? 'bg-green-400' 
                        : account.status === 'error'
                          ? 'bg-red-400'
                          : 'bg-yellow-400'
                    }`}
                    title={account.status}
                  />
                </div>

                <div className="flex-1 min-w-0 text-left">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {account.name}
                    </p>
                    {account.isDefault && (
                      <Badge variant="secondary" size="sm">
                        Default
                      </Badge>
                    )}
                    {activeAccountId === account.id && !isUnifiedView && (
                      <CheckIcon className="w-4 h-4 text-blue-600" />
                    )}
                  </div>
                  <p className="text-xs text-gray-500 truncate">
                    {account.email}
                  </p>
                </div>

                {showUnreadCount && account.stats.unreadEmails > 0 && (
                  <Badge variant="primary" size="sm">
                    {account.stats.unreadEmails}
                  </Badge>
                )}
              </button>
            ))}

          {accounts.length === 0 && (
            <div className="p-4 text-center text-gray-500">
              <p className="text-sm">No accounts connected</p>
              <p className="text-xs">Add an account to get started</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}