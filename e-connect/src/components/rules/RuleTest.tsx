import React, { useState, useEffect, useMemo } from 'react';
import { useRulesStore } from '@/stores/rulesStore';
import { Rule, RuleExecution } from '@/types/rules';
import { Email } from '@/types/email';
import { RuleEngine } from '@/utils/rules';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Select } from '@/components/ui/Select';
import { Checkbox } from '@/components/ui/Checkbox';
import { Badge } from '@/components/ui/Badge';
import { Tabs } from '@/components/ui/Tabs';
import { Progress } from '@/components/ui/Progress';
import { 
  Play, 
  PlayCircle,
  Pause,
  RotateCcw,
  Download,
  Upload,
  Target,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  Pie<PERSON>hart,
  Activity,
  Zap,
  Eye,
  EyeOff,
  Plus,
  Minus,
  FileText,
  Database,
  Settings
} from 'lucide-react';

interface RuleTestProps {
  rule?: Rule;
  onClose?: () => void;
}

interface TestCase {
  id: string;
  name: string;
  email: Partial<Email>;
  expectedMatch: boolean;
  expectedActions: string[];
}

interface TestResult {
  testCaseId: string;
  matched: boolean;
  execution: RuleExecution;
  passed: boolean;
  executionTime: number;
  error?: string;
}

interface BatchTestResult {
  ruleId: string;
  ruleName: string;
  results: TestResult[];
  summary: {
    totalTests: number;
    passed: number;
    failed: number;
    avgExecutionTime: number;
    matchRate: number;
  };
}

export const RuleTest: React.FC<RuleTestProps> = ({ rule, onClose }) => {
  const { 
    rules, 
    testRule, 
    runBatchTest, 
    clearTestResults,
    testResults 
  } = useRulesStore();

  // State
  const [selectedRule, setSelectedRule] = useState<Rule | null>(rule || null);
  const [testMode, setTestMode] = useState<'single' | 'batch'>('single');
  const [testCases, setTestCases] = useState<TestCase[]>([]);
  const [batchResults, setBatchResults] = useState<BatchTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentProgress, setCurrentProgress] = useState(0);
  const [showDebugMode, setShowDebugMode] = useState(false);
  const [selectedTestCase, setSelectedTestCase] = useState<string | null>(null);
  const [emailSample, setEmailSample] = useState<Partial<Email>[]>([]);
  const [sampleSize, setSampleSize] = useState(50);

  // Initialize with default test cases
  useEffect(() => {
    if (selectedRule && testCases.length === 0) {
      generateDefaultTestCases();
    }
  }, [selectedRule]);

  // Generate default test cases based on rule conditions
  const generateDefaultTestCases = () => {
    if (!selectedRule) return;

    const defaultCases: TestCase[] = [
      {
        id: 'test-1',
        name: 'Positive Match Case',
        email: {
          from: '<EMAIL>',
          to: ['<EMAIL>'],
          subject: 'Test Email Subject',
          body: 'This is a test email body with some content.',
          hasAttachment: false,
          read: false,
          important: false,
          starred: false,
          labels: ['inbox']
        },
        expectedMatch: true,
        expectedActions: selectedRule.actions.map(a => a.type)
      },
      {
        id: 'test-2',
        name: 'Negative Match Case',
        email: {
          from: '<EMAIL>',
          to: ['<EMAIL>'],
          subject: 'Different Subject',
          body: 'Different content that should not match.',
          hasAttachment: true,
          read: true,
          important: false,
          starred: false,
          labels: ['archive']
        },
        expectedMatch: false,
        expectedActions: []
      }
    ];

    // Add condition-specific test cases
    selectedRule.conditions.forEach((condition, index) => {
      switch (condition.type) {
        case 'from':
          defaultCases.push({
            id: `test-from-${index}`,
            name: `From Address Test (${condition.operator})`,
            email: {
              from: Array.isArray(condition.value) 
                ? condition.value[0] 
                : condition.value as string,
              subject: 'Test Subject',
              body: 'Test Body'
            },
            expectedMatch: !condition.negate,
            expectedActions: selectedRule.actions.map(a => a.type)
          });
          break;

        case 'subject':
          defaultCases.push({
            id: `test-subject-${index}`,
            name: `Subject Test (${condition.operator})`,
            email: {
              from: '<EMAIL>',
              subject: Array.isArray(condition.value) 
                ? `Email with ${condition.value[0]}` 
                : `Email with ${condition.value}`,
              body: 'Test Body'
            },
            expectedMatch: !condition.negate,
            expectedActions: selectedRule.actions.map(a => a.type)
          });
          break;

        case 'hasAttachment':
          defaultCases.push({
            id: `test-attachment-${index}`,
            name: `Attachment Test`,
            email: {
              from: '<EMAIL>',
              subject: 'Test Subject',
              body: 'Test Body',
              hasAttachment: condition.value as boolean
            },
            expectedMatch: !condition.negate,
            expectedActions: selectedRule.actions.map(a => a.type)
          });
          break;
      }
    });

    setTestCases(defaultCases);
  };

  // Generate sample emails for batch testing
  const generateEmailSample = () => {
    const samples: Partial<Email>[] = [];
    const domains = ['gmail.com', 'yahoo.com', 'company.com', 'test.org', 'example.net'];
    const subjects = [
      'Newsletter: Weekly Updates',
      'URGENT: Action Required',
      'Invoice #12345',
      'Meeting Reminder',
      'Re: Project Discussion',
      'Welcome to our service',
      'Your order has shipped',
      'Security Alert',
      'Free Trial Offer',
      'Account Statement'
    ];
    const keywords = ['urgent', 'newsletter', 'invoice', 'meeting', 'promotion', 'security', 'welcome'];

    for (let i = 0; i < sampleSize; i++) {
      const domain = domains[Math.floor(Math.random() * domains.length)];
      const subject = subjects[Math.floor(Math.random() * subjects.length)];
      
      samples.push({
        id: `sample-${i}`,
        from: `user${i}@${domain}`,
        to: ['<EMAIL>'],
        subject: subject,
        body: `This is a sample email body containing ${keywords[Math.floor(Math.random() * keywords.length)]}.`,
        hasAttachment: Math.random() > 0.7,
        read: Math.random() > 0.5,
        important: Math.random() > 0.8,
        starred: Math.random() > 0.9,
        labels: Math.random() > 0.5 ? ['inbox'] : ['archive'],
        size: Math.floor(Math.random() * 50000) + 1000,
        date: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000)
      });
    }

    setEmailSample(samples);
  };

  // Run single rule test
  const runSingleTest = async () => {
    if (!selectedRule || testCases.length === 0) return;

    setIsRunning(true);
    setCurrentProgress(0);

    try {
      const results = await testRule(selectedRule.id, testCases);
      setCurrentProgress(100);
    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  // Run batch test
  const runBatchTestSuite = async () => {
    if (emailSample.length === 0) {
      generateEmailSample();
      return;
    }

    setIsRunning(true);
    setCurrentProgress(0);

    try {
      const rulesToTest = testMode === 'single' && selectedRule 
        ? [selectedRule.id]
        : rules.filter(r => r.enabled).map(r => r.id);

      const results = await runBatchTest(rulesToTest, emailSample);
      setBatchResults(results.results || []);
      setCurrentProgress(100);
    } catch (error) {
      console.error('Batch test failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  // Add new test case
  const addTestCase = () => {
    const newTestCase: TestCase = {
      id: `test-${Date.now()}`,
      name: 'New Test Case',
      email: {
        from: '<EMAIL>',
        to: ['<EMAIL>'],
        subject: 'Test Subject',
        body: 'Test Body'
      },
      expectedMatch: false,
      expectedActions: []
    };

    setTestCases([...testCases, newTestCase]);
  };

  // Update test case
  const updateTestCase = (index: number, updates: Partial<TestCase>) => {
    const updatedCases = [...testCases];
    updatedCases[index] = { ...updatedCases[index], ...updates };
    setTestCases(updatedCases);
  };

  // Remove test case
  const removeTestCase = (index: number) => {
    const updatedCases = testCases.filter((_, i) => i !== index);
    setTestCases(updatedCases);
  };

  // Calculate test statistics
  const testStats = useMemo(() => {
    if (!testResults?.results) return null;

    const results = testResults.results;
    const passed = results.filter((r: any) => r.passed).length;
    const total = results.length;
    const avgTime = results.reduce((sum: number, r: any) => sum + (r.executionTime || 0), 0) / total;
    const matchRate = results.filter((r: any) => r.matched).length / total;

    return {
      passed,
      failed: total - passed,
      total,
      passRate: (passed / total) * 100,
      avgExecutionTime: avgTime,
      matchRate: matchRate * 100
    };
  }, [testResults]);

  // Render test case editor
  const renderTestCaseEditor = (testCase: TestCase, index: number) => (
    <Card key={testCase.id} className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <Input
            value={testCase.name}
            onChange={(e) => updateTestCase(index, { name: e.target.value })}
            className="font-medium"
          />
          <Button
            variant="outline"
            size="sm"
            onClick={() => removeTestCase(index)}
          >
            <Minus className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">From</label>
            <Input
              value={testCase.email.from || ''}
              onChange={(e) => updateTestCase(index, {
                email: { ...testCase.email, from: e.target.value }
              })}
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">To</label>
            <Input
              value={testCase.email.to?.join(', ') || ''}
              onChange={(e) => updateTestCase(index, {
                email: { ...testCase.email, to: e.target.value.split(',').map(s => s.trim()) }
              })}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Subject</label>
          <Input
            value={testCase.email.subject || ''}
            onChange={(e) => updateTestCase(index, {
              email: { ...testCase.email, subject: e.target.value }
            })}
            placeholder="Email subject"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Body</label>
          <textarea
            className="w-full p-2 border rounded-md resize-none"
            rows={3}
            value={testCase.email.body || ''}
            onChange={(e) => updateTestCase(index, {
              email: { ...testCase.email, body: e.target.value }
            })}
            placeholder="Email body content"
          />
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <label className="flex items-center gap-2">
            <Checkbox
              checked={testCase.email.hasAttachment || false}
              onCheckedChange={(checked) => updateTestCase(index, {
                email: { ...testCase.email, hasAttachment: checked }
              })}
            />
            <span className="text-sm">Has Attachment</span>
          </label>
          <label className="flex items-center gap-2">
            <Checkbox
              checked={testCase.email.read || false}
              onCheckedChange={(checked) => updateTestCase(index, {
                email: { ...testCase.email, read: checked }
              })}
            />
            <span className="text-sm">Read</span>
          </label>
          <label className="flex items-center gap-2">
            <Checkbox
              checked={testCase.email.important || false}
              onCheckedChange={(checked) => updateTestCase(index, {
                email: { ...testCase.email, important: checked }
              })}
            />
            <span className="text-sm">Important</span>
          </label>
          <label className="flex items-center gap-2">
            <Checkbox
              checked={testCase.email.starred || false}
              onCheckedChange={(checked) => updateTestCase(index, {
                email: { ...testCase.email, starred: checked }
              })}
            />
            <span className="text-sm">Starred</span>
          </label>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="flex items-center gap-2">
              <Checkbox
                checked={testCase.expectedMatch}
                onCheckedChange={(checked) => updateTestCase(index, { expectedMatch: checked })}
              />
              <span className="text-sm font-medium">Should Match Rule</span>
            </label>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Expected Actions</label>
            <Input
              value={testCase.expectedActions.join(', ')}
              onChange={(e) => updateTestCase(index, {
                expectedActions: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
              })}
              placeholder="archive, label, etc."
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  // Render test results
  const renderTestResults = () => {
    if (!testResults) return null;

    return (
      <div className="space-y-6">
        {/* Summary Stats */}
        {testStats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Pass Rate</p>
                    <p className="text-2xl font-bold text-green-600">
                      {testStats.passRate.toFixed(1)}%
                    </p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Match Rate</p>
                    <p className="text-2xl font-bold">
                      {testStats.matchRate.toFixed(1)}%
                    </p>
                  </div>
                  <Target className="w-8 h-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Avg Time</p>
                    <p className="text-2xl font-bold">
                      {testStats.avgExecutionTime.toFixed(0)}ms
                    </p>
                  </div>
                  <Clock className="w-8 h-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-600">Total Tests</p>
                    <p className="text-2xl font-bold">{testStats.total}</p>
                  </div>
                  <BarChart3 className="w-8 h-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Individual Results */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testResults.results?.map((result: any, index: number) => (
                <div key={result.testCaseId} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {result.passed ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <XCircle className="w-5 h-5 text-red-500" />
                      )}
                      <span className="font-medium">
                        {testCases[index]?.name || `Test ${index + 1}`}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span>Match: {result.matched ? 'Yes' : 'No'}</span>
                      <span>Time: {result.executionTime}ms</span>
                    </div>
                  </div>

                  {result.error && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                      Error: {result.error}
                    </div>
                  )}

                  {showDebugMode && result.execution && (
                    <div className="mt-3 p-3 bg-gray-50 rounded text-sm">
                      <div className="font-medium mb-2">Execution Details:</div>
                      <div className="space-y-1">
                        <div>Conditions: {result.execution.conditions.length} evaluated</div>
                        <div>Actions: {result.execution.actions.length} executed</div>
                        <div>Success: {result.execution.success ? 'Yes' : 'No'}</div>
                        {result.execution.error && (
                          <div className="text-red-600">Error: {result.execution.error}</div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  // Render batch results
  const renderBatchResults = () => {
    if (batchResults.length === 0) return null;

    return (
      <div className="space-y-6">
        {batchResults.map((result) => (
          <Card key={result.ruleId}>
            <CardHeader>
              <CardTitle>{result.ruleName}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                  <p className="text-sm text-gray-600">Total Tests</p>
                  <p className="text-xl font-bold">{result.summary.totalTests}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Passed</p>
                  <p className="text-xl font-bold text-green-600">{result.summary.passed}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Match Rate</p>
                  <p className="text-xl font-bold">{(result.summary.matchRate * 100).toFixed(1)}%</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Avg Time</p>
                  <p className="text-xl font-bold">{result.summary.avgExecutionTime.toFixed(0)}ms</p>
                </div>
              </div>
              
              <Progress 
                value={(result.summary.passed / result.summary.totalTests) * 100} 
                className="mb-2"
              />
              <p className="text-sm text-gray-600">
                {result.summary.passed} of {result.summary.totalTests} tests passed
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Rule Testing</h1>
          <p className="text-gray-600">Test your rules against sample data to ensure they work as expected</p>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        )}
      </div>

      {/* Controls */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Test Mode</label>
              <Select
                value={testMode}
                onValueChange={(value) => setTestMode(value as 'single' | 'batch')}
              >
                <option value="single">Single Rule</option>
                <option value="batch">Batch Test</option>
              </Select>
            </div>

            {testMode === 'single' && (
              <div>
                <label className="block text-sm font-medium mb-1">Rule</label>
                <Select
                  value={selectedRule?.id || ''}
                  onValueChange={(value) => {
                    const rule = rules.find(r => r.id === value);
                    setSelectedRule(rule || null);
                  }}
                >
                  <option value="">Select a rule...</option>
                  {rules.map(rule => (
                    <option key={rule.id} value={rule.id}>
                      {rule.name}
                    </option>
                  ))}
                </Select>
              </div>
            )}

            {testMode === 'batch' && (
              <div>
                <label className="block text-sm font-medium mb-1">Sample Size</label>
                <Input
                  type="number"
                  min="10"
                  max="1000"
                  value={sampleSize}
                  onChange={(e) => setSampleSize(parseInt(e.target.value) || 50)}
                />
              </div>
            )}

            <div className="flex items-end gap-2">
              <Button
                onClick={testMode === 'single' ? runSingleTest : runBatchTestSuite}
                disabled={isRunning || (testMode === 'single' && (!selectedRule || testCases.length === 0))}
                loading={isRunning}
              >
                <Play className="w-4 h-4 mr-2" />
                {testMode === 'single' ? 'Run Tests' : 'Run Batch Test'}
              </Button>

              {testMode === 'batch' && emailSample.length === 0 && (
                <Button
                  variant="outline"
                  onClick={generateEmailSample}
                >
                  <Database className="w-4 h-4 mr-2" />
                  Generate Sample
                </Button>
              )}
            </div>

            <div className="flex items-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowDebugMode(!showDebugMode)}
              >
                {showDebugMode ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clearTestResults}
              >
                <RotateCcw className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {isRunning && (
            <div className="mt-4">
              <div className="flex items-center gap-2 mb-2">
                <Activity className="w-4 h-4 animate-pulse" />
                <span className="text-sm">Running tests...</span>
              </div>
              <Progress value={currentProgress} />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Configuration */}
        <div>
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>
                  {testMode === 'single' ? 'Test Cases' : 'Email Sample'}
                </CardTitle>
                {testMode === 'single' && (
                  <Button size="sm" onClick={addTestCase}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Test Case
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {testMode === 'single' ? (
                <div className="space-y-4">
                  {testCases.map((testCase, index) => 
                    renderTestCaseEditor(testCase, index)
                  )}
                  
                  {testCases.length === 0 && (
                    <div className="text-center py-8">
                      <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No test cases</h3>
                      <p className="text-gray-600 mb-4">Add test cases to validate your rule behavior</p>
                      <Button onClick={addTestCase}>
                        <Plus className="w-4 h-4 mr-2" />
                        Add First Test Case
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div>
                  {emailSample.length > 0 ? (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <span className="text-sm text-gray-600">
                          {emailSample.length} sample emails generated
                        </span>
                        <Button size="sm" variant="outline" onClick={generateEmailSample}>
                          <RotateCcw className="w-4 h-4 mr-2" />
                          Regenerate
                        </Button>
                      </div>
                      
                      <div className="max-h-64 overflow-y-auto space-y-2">
                        {emailSample.slice(0, 10).map((email, index) => (
                          <div key={index} className="p-2 border rounded text-sm">
                            <div className="font-medium">{email.subject}</div>
                            <div className="text-gray-600">{email.from}</div>
                          </div>
                        ))}
                        {emailSample.length > 10 && (
                          <div className="text-center text-sm text-gray-500">
                            ... and {emailSample.length - 10} more emails
                          </div>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No email sample</h3>
                      <p className="text-gray-600 mb-4">Generate a sample of emails for batch testing</p>
                      <Button onClick={generateEmailSample}>
                        <Database className="w-4 h-4 mr-2" />
                        Generate Sample
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Results */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              {testMode === 'single' ? renderTestResults() : renderBatchResults()}
              
              {!testResults && batchResults.length === 0 && (
                <div className="text-center py-8">
                  <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No results yet</h3>
                  <p className="text-gray-600">Run tests to see results and analytics here</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RuleTest;