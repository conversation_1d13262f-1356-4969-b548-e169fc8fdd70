import React, { useEffect, useState } from 'react'
import { MagnifyingGlassIcon, FunnelIcon, ArrowPathIcon, Squares2X2Icon, ListBulletIcon } from '@heroicons/react/24/outline'
import { useEmailStore } from '../../stores/emailStore'
import { useMultiAccountStore } from '../../stores/multiAccountStore'
import type { UnifiedThread, UnifiedInboxFilter } from '../../types/multi-account'
import { EmailListItem } from '../email-list/EmailListItem'
import { AccountSwitcher } from '../account-switcher'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Select } from '../ui/Select'
import { Badge } from '../ui/Badge'
import { Loading } from '../ui/Loading'

interface UnifiedInboxProps {
  className?: string
}

export function UnifiedInbox({ className = '' }: UnifiedInboxProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [filterOpen, setFilterOpen] = useState(false)
  const [viewMode, setViewMode] = useState<'list' | 'grouped'>('list')
  
  const {
    threads,
    isLoading,
    error,
    stats,
    isUnifiedView,
    switchToUnifiedView,
    fetchUnifiedThreads,
    filter,
    setFilter,
    clearFilter,
    sort,
    setSort,
    currentPage,
    hasMore,
    loadMore
  } = useEmailStore()

  const {
    accounts,
    settings: multiAccountSettings
  } = useMultiAccountStore()

  const [localFilter, setLocalFilter] = useState<UnifiedInboxFilter>({
    accountIds: [],
    providers: [],
    accountGroup: 'all'
  })

  // Switch to unified view when component mounts
  useEffect(() => {
    if (!isUnifiedView) {
      switchToUnifiedView()
    }
  }, [isUnifiedView, switchToUnifiedView])

  // Fetch threads when filter changes
  useEffect(() => {
    if (isUnifiedView) {
      fetchUnifiedThreads()
    }
  }, [isUnifiedView, fetchUnifiedThreads])

  const handleSearch = async (query: string) => {
    setSearchQuery(query)
    if (query.trim()) {
      setFilter({ ...filter, query })
    } else {
      const { query: _, ...filterWithoutQuery } = filter
      setFilter(filterWithoutQuery)
    }
  }

  const handleFilterChange = (newFilter: Partial<UnifiedInboxFilter>) => {
    const updatedFilter = { ...localFilter, ...newFilter }
    setLocalFilter(updatedFilter)
    setFilter({ ...filter, ...updatedFilter })
  }

  const getAccountsForFilter = () => {
    switch (localFilter.accountGroup) {
      case 'active':
        return accounts.filter(acc => acc.status === 'active')
      case 'default':
        return accounts.filter(acc => acc.isDefault)
      case 'custom':
        return accounts.filter(acc => localFilter.accountIds?.includes(acc.id))
      default:
        return accounts
    }
  }

  const groupThreadsByAccount = (threads: UnifiedThread[]) => {
    const grouped = threads.reduce((acc, thread) => {
      const accountId = thread.accountId
      if (!acc[accountId]) {
        acc[accountId] = []
      }
      acc[accountId].push(thread)
      return acc
    }, {} as Record<string, UnifiedThread[]>)

    return Object.entries(grouped).map(([accountId, accountThreads]) => {
      const account = accounts.find(acc => acc.id === accountId)
      return {
        accountId,
        account,
        threads: accountThreads
      }
    })
  }

  const activeAccounts = accounts.filter(acc => acc.status === 'active')
  const unifiedThreads = threads as UnifiedThread[]
  const groupedThreads = viewMode === 'grouped' ? groupThreadsByAccount(unifiedThreads) : null

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="flex-shrink-0 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-900">
              Unified Inbox
            </h1>
            {stats.accountBreakdown && (
              <Badge variant="secondary">
                {stats.accountBreakdown.length} accounts
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {/* View mode toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${
                  viewMode === 'list' 
                    ? 'bg-white shadow-sm text-gray-900' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="List view"
              >
                <ListBulletIcon className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('grouped')}
                className={`p-2 rounded ${
                  viewMode === 'grouped' 
                    ? 'bg-white shadow-sm text-gray-900' 
                    : 'text-gray-500 hover:text-gray-700'
                }`}
                title="Grouped by account"
              >
                <Squares2X2Icon className="w-4 h-4" />
              </button>
            </div>

            {/* Refresh button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchUnifiedThreads()}
              disabled={isLoading}
            >
              <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Search and filters */}
        <div className="px-4 pb-4 space-y-4">
          {/* Search bar */}
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <Input
              type="text"
              placeholder="Search across all accounts..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter controls */}
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setFilterOpen(!filterOpen)}
              className="flex items-center space-x-2"
            >
              <FunnelIcon className="w-4 h-4" />
              <span>Filters</span>
              {Object.keys(filter).length > 0 && (
                <Badge variant="primary" size="sm">
                  {Object.keys(filter).length}
                </Badge>
              )}
            </Button>

            {/* Sort options */}
            <Select
              value={sort.field}
              onChange={(value) => setSort({ ...sort, field: value as any })}
              options={[
                { value: 'date', label: 'Date' },
                { value: 'importance', label: 'Importance' },
                { value: 'subject', label: 'Subject' },
                { value: 'from', label: 'Sender' }
              ]}
              size="sm"
            />

            <Select
              value={sort.order}
              onChange={(value) => setSort({ ...sort, order: value as 'asc' | 'desc' })}
              options={[
                { value: 'desc', label: 'Newest first' },
                { value: 'asc', label: 'Oldest first' }
              ]}
              size="sm"
            />

            {/* Clear filters */}
            {Object.keys(filter).length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilter}
              >
                Clear filters
              </Button>
            )}
          </div>

          {/* Filter panel */}
          {filterOpen && (
            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Account group filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Account Group
                  </label>
                  <Select
                    value={localFilter.accountGroup || 'all'}
                    onChange={(value) => handleFilterChange({ accountGroup: value as any })}
                    options={[
                      { value: 'all', label: 'All Accounts' },
                      { value: 'active', label: 'Active Only' },
                      { value: 'default', label: 'Default Only' },
                      { value: 'custom', label: 'Custom Selection' }
                    ]}
                  />
                </div>

                {/* Provider filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Provider
                  </label>
                  <Select
                    value={localFilter.providers?.[0] || ''}
                    onChange={(value) => handleFilterChange({ 
                      providers: value ? [value as any] : [] 
                    })}
                    options={[
                      { value: '', label: 'All Providers' },
                      { value: 'gmail', label: 'Gmail' },
                      { value: 'outlook', label: 'Outlook' },
                      { value: 'imap', label: 'IMAP' },
                      { value: 'exchange', label: 'Exchange' }
                    ]}
                  />
                </div>

                {/* Status filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <Select
                    value={filter.isUnread !== undefined ? (filter.isUnread ? 'unread' : 'read') : ''}
                    onChange={(value) => setFilter({ 
                      ...filter, 
                      isUnread: value === 'unread' ? true : value === 'read' ? false : undefined 
                    })}
                    options={[
                      { value: '', label: 'All' },
                      { value: 'unread', label: 'Unread' },
                      { value: 'read', label: 'Read' }
                    ]}
                  />
                </div>
              </div>

              {/* Custom account selection */}
              {localFilter.accountGroup === 'custom' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Accounts
                  </label>
                  <div className="space-y-2">
                    {accounts.map((account) => (
                      <label key={account.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          checked={localFilter.accountIds?.includes(account.id) || false}
                          onChange={(e) => {
                            const accountIds = localFilter.accountIds || []
                            if (e.target.checked) {
                              handleFilterChange({ 
                                accountIds: [...accountIds, account.id] 
                              })
                            } else {
                              handleFilterChange({ 
                                accountIds: accountIds.filter(id => id !== account.id) 
                              })
                            }
                          }}
                          className="rounded border-gray-300"
                        />
                        <div className="flex items-center space-x-2">
                          <div
                            className="w-3 h-3 rounded-full"
                            style={{ backgroundColor: account.color }}
                          />
                          <span className="text-sm">{account.name}</span>
                          <span className="text-xs text-gray-500">({account.email})</span>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Stats bar */}
      {stats.accountBreakdown && (
        <div className="flex-shrink-0 bg-gray-50 border-b border-gray-200 px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>{stats.totalUnread} unread</span>
              <span>{stats.totalImportant} important</span>
              <span>{unifiedThreads.length} total</span>
            </div>
            
            {/* Account breakdown */}
            <div className="flex items-center space-x-2">
              {stats.accountBreakdown.map((account) => (
                <div
                  key={account.accountId}
                  className="flex items-center space-x-1 text-xs"
                  title={`${account.accountName}: ${account.unread} unread`}
                >
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: accounts.find(acc => acc.id === account.accountId)?.color }}
                  />
                  <span>{account.unread}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {isLoading && unifiedThreads.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <Loading message="Loading unified inbox..." />
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-red-600">
              <p className="font-medium">Error loading unified inbox</p>
              <p className="text-sm">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchUnifiedThreads()}
                className="mt-2"
              >
                Try again
              </Button>
            </div>
          </div>
        ) : unifiedThreads.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center text-gray-500">
              <p className="font-medium">No emails found</p>
              <p className="text-sm">
                {activeAccounts.length === 0 
                  ? 'Connect an email account to get started'
                  : 'Try adjusting your filters or sync your accounts'
                }
              </p>
            </div>
          </div>
        ) : (
          <div className="h-full overflow-y-auto">
            {viewMode === 'list' ? (
              /* List view */
              <div className="divide-y divide-gray-200">
                {unifiedThreads.map((thread) => (
                  <div key={`${thread.accountId}-${thread.id}`} className="relative">
                    <EmailListItem thread={thread} />
                    {/* Account indicator */}
                    <div className="absolute top-3 right-3 flex items-center space-x-1">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: thread.accountColor }}
                        title={thread.accountName}
                      />
                      <span className="text-xs text-gray-500">
                        {thread.accountName}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              /* Grouped view */
              <div className="space-y-6 p-4">
                {groupedThreads?.map(({ accountId, account, threads }) => (
                  <div key={accountId} className="space-y-2">
                    {/* Account header */}
                    <div className="flex items-center space-x-3 pb-2 border-b border-gray-200">
                      <div
                        className="w-4 h-4 rounded-full"
                        style={{ backgroundColor: account?.color }}
                      />
                      <h3 className="font-medium text-gray-900">
                        {account?.name || 'Unknown Account'}
                      </h3>
                      <Badge variant="secondary" size="sm">
                        {threads.length}
                      </Badge>
                    </div>
                    
                    {/* Account threads */}
                    <div className="divide-y divide-gray-100">
                      {threads.map((thread) => (
                        <EmailListItem 
                          key={thread.id} 
                          thread={thread}
                          className="bg-gray-50 rounded-lg mb-2"
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Load more button */}
            {hasMore && (
              <div className="p-4 border-t border-gray-200">
                <Button
                  variant="outline"
                  onClick={loadMore}
                  disabled={isLoading}
                  className="w-full"
                >
                  {isLoading ? 'Loading...' : 'Load more'}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}