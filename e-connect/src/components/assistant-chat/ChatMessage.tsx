import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { 
  ClipboardIcon, 
  CheckIcon,
  ArrowPathIcon,
  UserIcon,
  CpuChipIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { ChatMessage as ChatMessageType } from '@/types/assistant';
import { Button } from '@/components/ui/Button';
import { useAssistantStore } from '@/stores/assistantStore';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/utils';

interface ChatMessageProps {
  message: ChatMessageType;
  isLast?: boolean;
  showAvatar?: boolean;
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ 
  message, 
  isLast = false,
  showAvatar = true 
}) => {
  const [isCopied, setIsCopied] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const { regenerateMessage, updateMessage } = useAssistantStore();

  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';
  const isSystem = message.role === 'system';
  const hasError = message.isError;
  const isLoading = message.isLoading;

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleRegenerate = async () => {
    if (isAssistant && !isLoading) {
      await regenerateMessage(message.id);
    }
  };

  const handleFeedback = (rating: 'helpful' | 'not-helpful') => {
    updateMessage(message.id, {
      metadata: {
        ...message.metadata,
        feedback: { rating: rating === 'helpful' ? 1 : 0, helpful: rating === 'helpful' }
      }
    });
  };

  const getAvatarContent = () => {
    if (isUser) {
      return <UserIcon className="h-5 w-5" />;
    } else if (isAssistant) {
      return <CpuChipIcon className="h-5 w-5" />;
    } else {
      return <SparklesIcon className="h-5 w-5" />;
    }
  };

  const getModelBadge = () => {
    if (!isAssistant || !message.metadata?.model) return null;
    
    const model = message.metadata.model;
    const confidence = message.metadata.confidence;
    
    return (
      <div className="flex items-center gap-2 text-xs text-gray-500">
        <span className="px-2 py-1 bg-gray-100 rounded-full font-medium">
          {model}
        </span>
        {confidence && (
          <span className="text-gray-400">
            {Math.round(confidence * 100)}% confidence
          </span>
        )}
      </div>
    );
  };

  const formatTimestamp = () => {
    return formatDistanceToNow(message.timestamp, { addSuffix: true });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "group relative flex gap-3 p-4",
        isUser && "flex-row-reverse",
        isLast && "mb-4"
      )}
    >
      {/* Avatar */}
      {showAvatar && (
        <div className={cn(
          "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white",
          isUser ? "bg-blue-600" : isAssistant ? "bg-gray-800" : "bg-purple-600"
        )}>
          {getAvatarContent()}
        </div>
      )}

      {/* Message Container */}
      <div className={cn(
        "flex-1 min-w-0",
        isUser && "flex flex-col items-end"
      )}>
        {/* Message Header */}
        <div className={cn(
          "flex items-center gap-2 mb-1",
          isUser && "flex-row-reverse"
        )}>
          <span className="text-sm font-medium text-gray-900">
            {isUser ? "You" : isAssistant ? "Assistant" : "System"}
          </span>
          <span className="text-xs text-gray-500">
            {formatTimestamp()}
          </span>
          {hasError && (
            <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />
          )}
          {isLoading && (
            <ClockIcon className="h-4 w-4 text-blue-500 animate-pulse" />
          )}
        </div>

        {/* Model Badge */}
        {!isUser && getModelBadge() && (
          <div className="mb-2">
            {getModelBadge()}
          </div>
        )}

        {/* Message Content */}
        <div className={cn(
          "relative max-w-3xl rounded-lg px-4 py-3 text-sm",
          isUser 
            ? "bg-blue-600 text-white" 
            : hasError 
              ? "bg-red-50 border border-red-200 text-red-900"
              : "bg-gray-50 border border-gray-200 text-gray-900",
          isLoading && "opacity-50"
        )}>
          {isLoading ? (
            <div className="flex items-center gap-2">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-2 h-2 bg-current rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-2 h-2 bg-current rounded-full animate-bounce"></div>
              </div>
              <span>Thinking...</span>
            </div>
          ) : (
            <div className="prose prose-sm max-w-none">
              {isUser ? (
                <p className="whitespace-pre-wrap">{message.content}</p>
              ) : (
                <ReactMarkdown
                  components={{
                    code({ node, className, children, ...props }) {
                      const match = /language-(\w+)/.exec(className || '');
                      const isInline = !className?.includes('language-');
                      return !isInline && match ? (
                        <SyntaxHighlighter
                          style={oneDark as any}
                          language={match[1]}
                          PreTag="div"
                          className="rounded-md !mt-2 !mb-2"
                        >
                          {String(children).replace(/\n$/, '')}
                        </SyntaxHighlighter>
                      ) : (
                        <code 
                          className="bg-gray-200 text-gray-800 px-1 py-0.5 rounded text-xs font-mono" 
                          {...props}
                        >
                          {children}
                        </code>
                      );
                    },
                    p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
                    ul: ({ children }) => <ul className="list-disc pl-4 mb-2">{children}</ul>,
                    ol: ({ children }) => <ol className="list-decimal pl-4 mb-2">{children}</ol>,
                    li: ({ children }) => <li className="mb-1">{children}</li>,
                    blockquote: ({ children }) => (
                      <blockquote className="border-l-4 border-gray-300 pl-4 italic text-gray-600 my-2">
                        {children}
                      </blockquote>
                    ),
                    h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
                    h2: ({ children }) => <h2 className="text-base font-bold mb-2">{children}</h2>,
                    h3: ({ children }) => <h3 className="text-sm font-bold mb-2">{children}</h3>,
                  }}
                >
                  {message.content}
                </ReactMarkdown>
              )}
            </div>
          )}

          {/* Error Message */}
          {hasError && message.error && (
            <div className="mt-2 text-xs text-red-600 bg-red-100 rounded px-2 py-1">
              Error: {message.error}
            </div>
          )}
        </div>

        {/* Attachments */}
        {message.attachments && message.attachments.length > 0 && (
          <div className="mt-2 flex flex-wrap gap-2">
            {message.attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="bg-white border border-gray-200 rounded-lg p-2 text-xs flex items-center gap-2"
              >
                <div className="w-4 h-4 bg-gray-200 rounded flex-shrink-0"></div>
                <span className="truncate">{attachment.name}</span>
              </div>
            ))}
          </div>
        )}

        {/* Message Actions */}
        <AnimatePresence>
          {!isLoading && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className={cn(
                "flex items-center gap-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity",
                isUser && "flex-row-reverse"
              )}
            >
              {/* Copy Button */}
              <Button
                variant="ghost"
                size="iconSm"
                onClick={handleCopy}
                className="text-gray-500 hover:text-gray-700"
              >
                {isCopied ? (
                  <CheckIcon className="h-4 w-4" />
                ) : (
                  <ClipboardIcon className="h-4 w-4" />
                )}
              </Button>

              {/* Regenerate Button (AI messages only) */}
              {isAssistant && !hasError && (
                <Button
                  variant="ghost"
                  size="iconSm"
                  onClick={handleRegenerate}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <ArrowPathIcon className="h-4 w-4" />
                </Button>
              )}

              {/* Feedback Buttons (AI messages only) */}
              {isAssistant && !hasError && !message.metadata?.feedback && (
                <div className="flex gap-1 ml-2">
                  <Button
                    variant="ghost"
                    size="xs"
                    onClick={() => handleFeedback('helpful')}
                    className="text-gray-500 hover:text-green-600"
                  >
                    =M
                  </Button>
                  <Button
                    variant="ghost"
                    size="xs"
                    onClick={() => handleFeedback('not-helpful')}
                    className="text-gray-500 hover:text-red-600"
                  >
                    =N
                  </Button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Message Metadata (Expandable) */}
        {message.metadata && Object.keys(message.metadata).length > 0 && (
          <div className="mt-2">
            <Button
              variant="ghost"
              size="xs"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-gray-600"
            >
              {isExpanded ? 'Hide' : 'Show'} details
            </Button>
            
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-2 text-xs text-gray-500 bg-gray-50 rounded p-2 overflow-hidden"
                >
                  <pre className="whitespace-pre-wrap">
                    {JSON.stringify(message.metadata, null, 2)}
                  </pre>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ChatMessage;