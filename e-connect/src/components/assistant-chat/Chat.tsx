import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useHotkeys } from 'react-hotkeys-hook';
import {
  ChatBubbleLeftRightIcon,
  ArrowDownIcon,
  ExclamationTriangleIcon,
  DocumentArrowDownIcon,
  DocumentArrowUpIcon,
  TrashIcon,
  ArchiveBoxIcon,
  EllipsisVerticalIcon,
  SparklesIcon,
  ClockIcon,
  XMarkIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import { ChatMessage } from './ChatMessage';
import { ChatInput } from './ChatInput';
import { SuggestedActions } from './SuggestedActions';
import { RuleBuilder } from './RuleBuilder';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { 
  useAssistantStore, 
  useCurrentMessages, 
  useActiveSuggestions 
} from '@/stores/assistantStore';
import { Conversation } from '@/types/assistant';
import { cn } from '@/utils';

interface ChatProps {
  className?: string;
  showSuggestedActions?: boolean;
  showRuleBuilder?: boolean;
  autoFocus?: boolean;
  maxHeight?: string;
  onConversationChange?: (conversation: Conversation | null) => void;
}

interface TypingIndicatorProps {
  isVisible: boolean;
}

const TypingIndicator: React.FC<TypingIndicatorProps> = ({ isVisible }) => {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="flex items-center gap-3 p-4"
    >
      <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center">
        <SparklesIcon className="h-4 w-4 text-white" />
      </div>
      
      <div className="flex-1">
        <div className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 max-w-xs">
          <div className="flex items-center gap-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            </div>
            <span className="text-sm text-gray-600">AI is thinking...</span>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

const ScrollToBottomButton: React.FC<{
  visible: boolean;
  onClick: () => void;
}> = ({ visible, onClick }) => {
  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="absolute bottom-4 right-4 z-10"
        >
          <Button
            variant="outline"
            size="icon"
            onClick={onClick}
            className="bg-white shadow-lg border-gray-300 hover:bg-gray-50"
          >
            <ArrowDownIcon className="h-4 w-4" />
          </Button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const EmptyState: React.FC<{
  onStartConversation: () => void;
}> = ({ onStartConversation }) => {
  const quickStarters = [
    {
      text: "Help me organize my inbox",
      description: "Get AI-powered suggestions for email organization"
    },
    {
      text: "Create a rule to archive newsletters",
      description: "Set up automatic email processing rules"
    },
    {
      text: "Analyze my email patterns",
      description: "Understand your email habits and productivity"
    },
    {
      text: "Bulk unsubscribe from emails",
      description: "Clean up unwanted email subscriptions"
    }
  ];

  return (
    <div className="flex-1 flex items-center justify-center p-8">
      <div className="text-center max-w-md">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <ChatBubbleLeftRightIcon className="h-8 w-8 text-blue-600" />
        </div>
        
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Welcome to Email Assistant
        </h2>
        
        <p className="text-gray-600 mb-6">
          I'm here to help you manage your emails more efficiently. Ask me anything about organizing, automating, or analyzing your inbox.
        </p>

        <div className="space-y-2">
          <p className="text-sm font-medium text-gray-700 mb-3">Quick starters:</p>
          {quickStarters.map((starter, index) => (
            <button
              key={index}
              onClick={() => onStartConversation()}
              className="w-full text-left p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors group"
            >
              <div className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                "{starter.text}"
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {starter.description}
              </div>
            </button>
          ))}
        </div>

        <div className="mt-6 text-xs text-gray-500">
          <p>=� Tip: Use keyboard shortcuts like +Enter to send messages quickly</p>
        </div>
      </div>
    </div>
  );
};

export const Chat: React.FC<ChatProps> = ({
  className,
  showSuggestedActions = true,
  showRuleBuilder = false,
  autoFocus = true,
  maxHeight = "600px",
  onConversationChange
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [showRuleBuilderModal, setShowRuleBuilderModal] = useState(false);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);

  const {
    currentConversation,
    createConversation,
    isTyping,
    error,
    clearError,
    exportConversation,
    importConversation,
    archiveConversation,
    deleteConversation
  } = useAssistantStore();

  const messages = useCurrentMessages();
  const suggestions = useActiveSuggestions();

  // Keyboard shortcuts
  useHotkeys('mod+shift+n', () => {
    createConversation();
  }, { preventDefault: true });

  useHotkeys('mod+shift+e', () => {
    if (currentConversation) {
      const data = exportConversation(currentConversation.id);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `conversation-${currentConversation.id}.json`;
      a.click();
      URL.revokeObjectURL(url);
    }
  }, { preventDefault: true });

  useHotkeys('escape', () => {
    setShowRuleBuilderModal(false);
    clearError();
  });

  // Auto-scroll to bottom
  const scrollToBottom = useCallback((force = false) => {
    if (messagesEndRef.current && (isAutoScrollEnabled || force)) {
      messagesEndRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, [isAutoScrollEnabled]);

  // Handle scroll events
  const handleScroll = useCallback(() => {
    if (!messagesContainerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current;
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
    
    setShowScrollButton(!isNearBottom && messages.length > 3);
    setIsAutoScrollEnabled(isNearBottom);
  }, [messages.length]);

  // Scroll to bottom when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping, scrollToBottom]);

  // Notify parent of conversation changes
  useEffect(() => {
    onConversationChange?.(currentConversation);
  }, [currentConversation, onConversationChange]);

  const handleStartConversation = () => {
    if (!currentConversation) {
      createConversation();
    }
  };

  const handleExportConversation = () => {
    if (!currentConversation) return;
    
    try {
      const data = exportConversation(currentConversation.id);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `conversation-${currentConversation.id}.json`;
      a.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleImportConversation = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = e.target?.result as string;
          importConversation(data);
        } catch (error) {
          console.error('Import failed:', error);
        }
      };
      reader.readAsText(file);
    };
    input.click();
  };

  const hasMessages = messages.length > 0;

  return (
    <div className={cn(
      "flex flex-col bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden",
      className
    )} style={{ height: maxHeight }}>
      
      {/* Header */}
      <div className="flex-shrink-0 border-b border-gray-200 bg-gray-50 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <SparklesIcon className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900">Email Assistant</h3>
              <p className="text-xs text-gray-500">
                {currentConversation 
                  ? `${messages.length} messages` 
                  : 'Ready to help with your emails'
                }
              </p>
            </div>
          </div>

          {currentConversation && (
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="iconSm"
                onClick={handleExportConversation}
                title="Export conversation"
              >
                <DocumentArrowDownIcon className="h-4 w-4" />
              </Button>
              
              <Button
                variant="ghost"
                size="iconSm"
                onClick={handleImportConversation}
                title="Import conversation"
              >
                <DocumentArrowUpIcon className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="iconSm"
                onClick={() => archiveConversation(currentConversation.id)}
                title="Archive conversation"
              >
                <ArchiveBoxIcon className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="iconSm"
                onClick={() => deleteConversation(currentConversation.id)}
                title="Delete conversation"
                className="text-red-500 hover:text-red-700"
              >
                <TrashIcon className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Error Banner */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg flex items-start gap-2"
            >
              <ExclamationTriangleIcon className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm text-red-800">{error}</p>
              </div>
              <Button
                variant="ghost"
                size="iconSm"
                onClick={clearError}
                className="text-red-500 hover:text-red-700"
              >
                <XMarkIcon className="h-4 w-4" />
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Messages Area */}
      <div className="flex-1 min-h-0 relative">
        <div
          ref={messagesContainerRef}
          onScroll={handleScroll}
          className="h-full overflow-y-auto scroll-smooth"
        >
          {!hasMessages ? (
            <EmptyState onStartConversation={handleStartConversation} />
          ) : (
            <div className="py-4">
              <AnimatePresence mode="popLayout">
                {messages.map((message, index) => (
                  <ChatMessage
                    key={message.id}
                    message={message}
                    isLast={index === messages.length - 1}
                    showAvatar={true}
                  />
                ))}
              </AnimatePresence>

              {/* Typing Indicator */}
              <TypingIndicator isVisible={isTyping} />
              
              {/* Anchor for auto-scroll */}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Scroll to Bottom Button */}
        <ScrollToBottomButton
          visible={showScrollButton}
          onClick={() => scrollToBottom(true)}
        />
      </div>

      {/* Suggested Actions */}
      {showSuggestedActions && suggestions.length > 0 && (
        <div className="flex-shrink-0 border-t border-gray-200 bg-gray-50 p-4">
          <SuggestedActions 
            maxSuggestions={3} 
            compact={true}
          />
        </div>
      )}

      {/* Input Area */}
      <div className="flex-shrink-0 border-t border-gray-200 p-4">
        <ChatInput
          placeholder={
            hasMessages 
              ? "Continue the conversation..." 
              : "Ask me anything about your emails..."
          }
          autoFocus={autoFocus}
          onFocus={() => setIsAutoScrollEnabled(true)}
        />

        {showRuleBuilder && (
          <div className="mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowRuleBuilderModal(true)}
              className="flex items-center gap-2"
            >
              <CogIcon className="h-4 w-4" />
              Create Email Rule
            </Button>
          </div>
        )}
      </div>

      {/* Rule Builder Modal */}
      <AnimatePresence>
        {showRuleBuilderModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowRuleBuilderModal(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="w-full max-w-4xl max-h-[90vh] overflow-auto"
            >
              <RuleBuilder
                onCancel={() => setShowRuleBuilderModal(false)}
                onSave={(rule) => {
                  console.log('Rule saved:', rule);
                  setShowRuleBuilderModal(false);
                }}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Chat;