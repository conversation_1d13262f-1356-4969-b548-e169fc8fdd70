import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  LightBulbIcon,
  XMarkIcon,
  CheckIcon,
  ClockIcon,
  ArrowRightIcon,
  SparklesIcon,
  EnvelopeIcon,
  FunnelIcon,
  TrashIcon,
  ArchiveBoxIcon,
  UserMinusIcon,
  DocumentDuplicateIcon,
  CalendarIcon,
  UserGroupIcon,
  ChartBarIcon,
  CogIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { Suggestion, SuggestionType } from '@/types/assistant';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { useAssistantStore, useActiveSuggestions } from '@/stores/assistantStore';
import { cn } from '@/utils';
import { formatDistanceToNow } from 'date-fns';

interface SuggestedActionsProps {
  maxSuggestions?: number;
  showDismissed?: boolean;
  compact?: boolean;
  className?: string;
}

const suggestionIcons: Record<SuggestionType, React.ElementType> = {
  unsubscribe: UserMinusIcon,
  archive: ArchiveBoxIcon,
  createRule: CogIcon,
  bulkAction: DocumentDuplicateIcon,
  organize: FunnelIcon,
  respond: EnvelopeIcon,
  schedule: CalendarIcon,
  delegate: UserGroupIcon,
  summarize: ChartBarIcon,
  extract: DocumentDuplicateIcon,
  custom: SparklesIcon
};

const priorityColors = {
  high: 'text-red-600 bg-red-50 border-red-200',
  medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
  low: 'text-blue-600 bg-blue-50 border-blue-200'
};

const getSuggestionTypeLabel = (type: SuggestionType): string => {
  const labels: Record<SuggestionType, string> = {
    unsubscribe: 'Unsubscribe',
    archive: 'Archive',
    createRule: 'Create Rule',
    bulkAction: 'Bulk Action',
    organize: 'Organize',
    respond: 'Reply',
    schedule: 'Schedule',
    delegate: 'Delegate',
    summarize: 'Summarize',
    extract: 'Extract',
    custom: 'Custom'
  };
  return labels[type];
};

const SuggestionCard: React.FC<{
  suggestion: Suggestion;
  compact?: boolean;
  onApply: (id: string) => void;
  onDismiss: (id: string) => void;
}> = ({ suggestion, compact = false, onApply, onDismiss }) => {
  const [isApplying, setIsApplying] = useState(false);
  const Icon = suggestionIcons[suggestion.type];

  const handleApply = async () => {
    setIsApplying(true);
    try {
      await onApply(suggestion.id);
    } finally {
      setIsApplying(false);
    }
  };

  const renderImpact = () => {
    if (!suggestion.impact) return null;

    const { timesSaved, emailsAffected, clutterReduced } = suggestion.impact;
    
    return (
      <div className="flex items-center gap-4 text-xs text-gray-600 mt-2">
        {timesSaved && (
          <div className="flex items-center gap-1">
            <ClockIcon className="h-3 w-3" />
            <span>{timesSaved}m saved</span>
          </div>
        )}
        {emailsAffected && (
          <div className="flex items-center gap-1">
            <EnvelopeIcon className="h-3 w-3" />
            <span>{emailsAffected} emails</span>
          </div>
        )}
        {clutterReduced && (
          <div className="flex items-center gap-1">
            <SparklesIcon className="h-3 w-3" />
            <span>{clutterReduced}% less clutter</span>
          </div>
        )}
      </div>
    );
  };

  const confidenceLevel = suggestion.confidence >= 0.8 ? 'high' : 
                         suggestion.confidence >= 0.6 ? 'medium' : 'low';

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        exit={{ opacity: 0, x: -20 }}
        className="flex items-center gap-3 p-3 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
      >
        <div className={cn(
          "w-8 h-8 rounded-full flex items-center justify-center",
          priorityColors[suggestion.priority]
        )}>
          <Icon className="h-4 w-4" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-900 truncate">
              {suggestion.title}
            </span>
            <span className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded-full">
              {Math.round(suggestion.confidence * 100)}%
            </span>
          </div>
          {suggestion.description && (
            <p className="text-xs text-gray-600 mt-1 truncate">
              {suggestion.description}
            </p>
          )}
        </div>

        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="iconSm"
            onClick={handleApply}
            loading={isApplying}
            className="text-green-600 hover:text-green-700"
          >
            <CheckIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="iconSm"
            onClick={() => onDismiss(suggestion.id)}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-4 w-4" />
          </Button>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      layout
    >
      <Card className="hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-3">
              <div className={cn(
                "w-10 h-10 rounded-lg flex items-center justify-center",
                priorityColors[suggestion.priority]
              )}>
                <Icon className="h-5 w-5" />
              </div>
              
              <div>
                <CardTitle className="text-base leading-tight">
                  {suggestion.title}
                </CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <span className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded-full">
                    {getSuggestionTypeLabel(suggestion.type)}
                  </span>
                  <span className={cn(
                    "text-xs px-2 py-1 rounded-full",
                    priorityColors[suggestion.priority]
                  )}>
                    {suggestion.priority} priority
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-1">
              <div className="text-right">
                <div className="text-xs font-medium text-gray-600">
                  {Math.round(suggestion.confidence * 100)}% confidence
                </div>
                <div className={cn(
                  "text-xs mt-1",
                  confidenceLevel === 'high' ? 'text-green-600' :
                  confidenceLevel === 'medium' ? 'text-yellow-600' : 'text-red-600'
                )}>
                  {confidenceLevel} confidence
                </div>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {suggestion.description && (
            <p className="text-sm text-gray-600 mb-3">
              {suggestion.description}
            </p>
          )}

          {suggestion.reasoning && (
            <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-2">
                <InformationCircleIcon className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-xs font-medium text-blue-900 mb-1">Why this suggestion?</p>
                  <p className="text-xs text-blue-800">{suggestion.reasoning}</p>
                </div>
              </div>
            </div>
          )}

          {renderImpact()}

          {suggestion.action?.preview && (
            <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <p className="text-xs font-medium text-gray-700 mb-1">Preview:</p>
              <p className="text-xs text-gray-600 font-mono">
                {suggestion.action.preview}
              </p>
            </div>
          )}

          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-2">
              {suggestion.isApplied && (
                <span className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded-full flex items-center gap-1">
                  <CheckIcon className="h-3 w-3" />
                  Applied {suggestion.appliedAt && formatDistanceToNow(suggestion.appliedAt, { addSuffix: true })}
                </span>
              )}
              {suggestion.isDismissed && (
                <span className="text-xs text-gray-500 bg-gray-50 px-2 py-1 rounded-full">
                  Dismissed
                </span>
              )}
            </div>

            {!suggestion.isApplied && !suggestion.isDismissed && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onDismiss(suggestion.id)}
                  className="text-gray-600"
                >
                  Dismiss
                </Button>
                <Button
                  variant="primaryBlue"
                  size="sm"
                  onClick={handleApply}
                  loading={isApplying}
                  className="flex items-center gap-2"
                >
                  Apply
                  <ArrowRightIcon className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export const SuggestedActions: React.FC<SuggestedActionsProps> = ({
  maxSuggestions = 5,
  showDismissed = false,
  compact = false,
  className
}) => {
  const suggestions = useActiveSuggestions();
  const { applySuggestion, dismissSuggestion } = useAssistantStore();
  
  // Filter and sort suggestions
  const filteredSuggestions = suggestions
    .filter(suggestion => showDismissed || (!suggestion.isApplied && !suggestion.isDismissed))
    .sort((a, b) => {
      // Sort by priority first, then confidence
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return b.confidence - a.confidence;
    })
    .slice(0, maxSuggestions);

  if (filteredSuggestions.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex items-center gap-2">
        <LightBulbIcon className="h-5 w-5 text-yellow-600" />
        <h3 className="text-sm font-medium text-gray-900">
          AI Suggestions
        </h3>
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {filteredSuggestions.length}
        </span>
      </div>

      <AnimatePresence mode="popLayout">
        <div className={cn(
          compact ? "space-y-2" : "space-y-3"
        )}>
          {filteredSuggestions.map((suggestion) => (
            <SuggestionCard
              key={suggestion.id}
              suggestion={suggestion}
              compact={compact}
              onApply={applySuggestion}
              onDismiss={dismissSuggestion}
            />
          ))}
        </div>
      </AnimatePresence>

      {suggestions.length > maxSuggestions && (
        <div className="text-center">
          <Button
            variant="ghost"
            size="sm"
            className="text-gray-600"
          >
            View {suggestions.length - maxSuggestions} more suggestions
          </Button>
        </div>
      )}
    </div>
  );
};

export default SuggestedActions;