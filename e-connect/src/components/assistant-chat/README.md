# AI Chat Interface for Email Management

A sophisticated AI chat interface specifically designed for email management tasks, built with React, TypeScript, and modern UI patterns.

## Features

### 🎯 **Chat Interface (Chat.tsx)**
- **Conversational UI**: ChatGPT/Claude-like experience
- **Message History**: Persistent conversation threading
- **Real-time Indicators**: Typing indicators and message status
- **Auto-scroll**: Smart scroll management with manual override
- **Export/Import**: Conversation backup and restore
- **Keyboard Shortcuts**: Efficient navigation and actions

### 💬 **Message Component (ChatMessage.tsx)**
- **Dual Role Support**: User and AI message rendering
- **Markdown Rendering**: Rich text formatting with ReactMarkdown
- **Code Highlighting**: Syntax highlighting with Prism
- **Interactive Actions**: Copy, regenerate, feedback buttons
- **Timestamps**: Relative time display
- **Model Indicators**: Shows AI model and confidence
- **Attachment Support**: File and media attachments

### ⌨️ **Input Component (ChatInput.tsx)**
- **Auto-resizing Textarea**: Expands as you type
- **Smart Shortcuts**: Enter to send, Shift+Enter for new line
- **File Attachments**: Drag-and-drop file support
- **Voice Input**: Speech-to-text integration
- **Quick Actions**: Predefined action templates
- **Character Counter**: Real-time character and token counting

### 💡 **Suggested Actions (SuggestedActions.tsx)**
- **AI-Powered Suggestions**: Context-aware recommendations
- **Email Automation**: Rule creation suggestions
- **Bulk Operations**: Unsubscribe and organization suggestions
- **Impact Metrics**: Shows time saved and emails affected
- **Priority Scoring**: High/medium/low priority classification
- **One-click Apply**: Instant action execution

### 🔧 **Rule Builder (RuleBuilder.tsx)**
- **Visual Rule Construction**: Drag-and-drop interface
- **Condition Builder**: Complex logic with AND/OR operators
- **Action Configuration**: Multiple action types and parameters
- **Live Preview**: Real-time rule visualization
- **Test Interface**: Rule validation with sample emails
- **Advanced Options**: Regex, custom fields, case sensitivity

## Usage

### Basic Chat Interface

```tsx
import { Chat } from '@/components/assistant-chat';

function EmailAssistantPage() {
  return (
    <div className="container mx-auto p-4">
      <Chat
        showSuggestedActions={true}
        showRuleBuilder={true}
        autoFocus={true}
        maxHeight="600px"
        onConversationChange={(conversation) => {
          console.log('Conversation changed:', conversation);
        }}
      />
    </div>
  );
}
```

### Standalone Components

```tsx
import {
  ChatMessage,
  ChatInput,
  SuggestedActions,
  RuleBuilder
} from '@/components/assistant-chat';

// Use individual components
function CustomChatInterface() {
  return (
    <div className="flex flex-col h-screen">
      {/* Messages */}
      <div className="flex-1 overflow-auto">
        {messages.map(message => (
          <ChatMessage key={message.id} message={message} />
        ))}
      </div>

      {/* Suggestions */}
      <SuggestedActions maxSuggestions={3} compact={true} />

      {/* Input */}
      <ChatInput placeholder="Ask about your emails..." />
    </div>
  );
}
```

### Rule Builder Integration

```tsx
import { RuleBuilder } from '@/components/assistant-chat';

function RuleManagement() {
  const handleSaveRule = (rule) => {
    // Save rule logic
    console.log('Saving rule:', rule);
  };

  const handleTestRule = async (rule) => {
    // Test rule against sample emails
    return await testRuleAPI(rule);
  };

  return (
    <RuleBuilder
      onSave={handleSaveRule}
      onTest={handleTestRule}
      initialRule={existingRule}
    />
  );
}
```

## State Management

The chat interface uses Zustand for state management with the `useAssistantStore` hook:

```tsx
import { useAssistantStore } from '@/stores/assistantStore';

function ChatComponent() {
  const {
    // State
    currentConversation,
    messages,
    isTyping,
    suggestions,
    
    // Actions
    sendMessage,
    createConversation,
    applySuggestion,
    dismissSuggestion
  } = useAssistantStore();

  // Use the state and actions
}
```

## Keyboard Shortcuts

- **⌘+Enter**: Send message
- **⌘+/**: Toggle quick actions
- **⌘+Shift+N**: New conversation
- **⌘+Shift+E**: Export conversation
- **Escape**: Close modals/clear errors

## Customization

### Themes and Styling

The components use Tailwind CSS classes and can be customized via the `className` prop:

```tsx
<Chat
  className="border-2 border-blue-500 rounded-xl"
  // Custom styling
/>
```

### Message Formatting

The ChatMessage component supports custom markdown renderers:

```tsx
// Custom code block rendering
const customComponents = {
  code({ node, inline, className, children, ...props }) {
    // Custom code rendering logic
  }
};
```

### Suggestions Configuration

Configure suggestion types and priorities:

```tsx
<SuggestedActions
  maxSuggestions={5}
  showDismissed={false}
  compact={false}
  className="custom-suggestions"
/>
```

## API Integration

The components work with your existing API by updating the mock functions in `assistantStore.ts`:

```tsx
// Replace mock API calls with real ones
const sendMessageToAPI = async (message: string) => {
  const response = await fetch('/api/chat', {
    method: 'POST',
    body: JSON.stringify({ message }),
    headers: { 'Content-Type': 'application/json' }
  });
  return response.json();
};
```

## Accessibility

- **Keyboard Navigation**: Full keyboard support
- **Screen Reader**: ARIA labels and semantic HTML
- **Focus Management**: Logical tab order
- **High Contrast**: Supports system preferences

## Dependencies

Core dependencies used by this component:

- `react` - Core React library
- `framer-motion` - Animations and transitions
- `react-markdown` - Markdown rendering
- `react-syntax-highlighter` - Code syntax highlighting
- `react-textarea-autosize` - Auto-resizing textarea
- `react-hook-form` - Form management (RuleBuilder)
- `react-hotkeys-hook` - Keyboard shortcuts
- `zustand` - State management
- `date-fns` - Date formatting
- `@heroicons/react` - Icons

## Browser Support

- **Modern Browsers**: Chrome, Firefox, Safari, Edge
- **Speech Recognition**: Chrome/Edge (webkitSpeechRecognition)
- **File Upload**: All modern browsers with drag-and-drop
- **Clipboard API**: HTTPS required for copy functionality

## Performance

- **Virtualization**: Large message lists are handled efficiently
- **Lazy Loading**: Components render on demand
- **Memoization**: Expensive operations are cached
- **Debounced Input**: Search and suggestions are debounced

## Contributing

When contributing to these components:

1. **TypeScript**: Maintain strict type safety
2. **Accessibility**: Test with screen readers
3. **Mobile**: Ensure responsive design
4. **Performance**: Profile with React DevTools
5. **Testing**: Add unit tests for new features

## Troubleshooting

### Common Issues

1. **Speech Recognition Not Working**
   - Check HTTPS requirement
   - Verify browser support
   - Check microphone permissions

2. **File Upload Issues**
   - Verify file size limits (10MB default)
   - Check supported file types
   - Ensure drag-and-drop permissions

3. **Markdown Not Rendering**
   - Check ReactMarkdown configuration
   - Verify custom component props
   - Check for conflicting CSS

4. **State Not Persisting**
   - Verify Zustand store configuration
   - Check localStorage permissions
   - Ensure proper store subscription

For more help, check the component implementations or reach out to the development team.