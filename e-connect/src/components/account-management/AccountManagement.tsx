import React, { useState } from 'react'
import { 
  PlusIcon, 
  PencilIcon, 
  TrashIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowPathIcon,
  CogIcon,
  EyeIcon,
  EyeSlashIcon
} from '@heroicons/react/24/outline'
import { useMultiAccountStore } from '../../stores/multiAccountStore'
import type { EmailAccount, AccountProvider } from '../../types/multi-account'
import { Dialog } from '../ui/Dialog'
import { Button } from '../ui/Button'
import { Input } from '../ui/Input'
import { Select } from '../ui/Select'
import { Toggle } from '../ui/Toggle'
import { Badge } from '../ui/Badge'
import { Card } from '../ui/Card'
import { SettingsSection } from '../settings/SettingsSection'
import { SettingsItem } from '../settings/SettingsItem'

interface AccountManagementProps {
  className?: string
}

export function AccountManagement({ className = '' }: AccountManagementProps) {
  const [showAddAccount, setShowAddAccount] = useState(false)
  const [showEditAccount, setShowEditAccount] = useState<string | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null)
  const [selectedProvider, setSelectedProvider] = useState<AccountProvider>('gmail')
  const [showCredentials, setShowCredentials] = useState<Record<string, boolean>>({})

  const {
    accounts,
    addAccount,
    removeAccount,
    updateAccount,
    refreshAccount,
    setDefaultAccount,
    reorderAccounts,
    authenticateAccount,
    settings,
    updateSettings,
    isLoading,
    error
  } = useMultiAccountStore()

  const [accountForm, setAccountForm] = useState({
    name: '',
    email: '',
    provider: 'gmail' as AccountProvider,
    color: '#3b82f6',
    config: {
      provider: 'gmail' as AccountProvider,
      imap: {
        host: '',
        port: 993,
        secure: true,
        auth: { user: '', pass: '' }
      },
      smtp: {
        host: '',
        port: 587,
        secure: true,
        auth: { user: '', pass: '' }
      }
    }
  })

  const handleAddAccount = async () => {
    try {
      await addAccount({
        name: accountForm.name,
        email: accountForm.email,
        provider: accountForm.provider,
        status: 'disconnected',
        color: accountForm.color,
        isDefault: accounts.length === 0,
        isPrimary: accounts.length === 0,
        displayOrder: accounts.length,
        config: accountForm.config,
        auth: {
          type: accountForm.provider === 'imap' ? 'password' : 'oauth',
          isValid: false,
          lastValidated: new Date()
        },
        sync: {
          enabled: true,
          syncInterval: 15,
          fullSyncInterval: 24,
          emails: true,
          contacts: false,
          calendar: false,
          syncFolders: ['INBOX'],
          excludeFolders: [],
          syncLabels: true,
          syncAttachments: true,
          maxAttachmentSize: 25,
          daysToSync: 30,
          maxEmailsPerSync: 100,
          conflictResolution: 'server_wins',
          backgroundSync: true,
          wifiOnly: false,
          lowBatteryMode: false
        },
        stats: {
          totalEmails: 0,
          unreadEmails: 0,
          syncedEmails: 0,
          lastSyncDuration: 0,
          averageSyncDuration: 0,
          syncErrors: 0,
          consecutiveFailures: 0,
          apiCalls: 0,
          rateLimitHits: 0,
          bandwidthUsed: 0,
          storageUsed: 0,
          attachmentStorage: 0,
          emailsSent: 0,
          emailsReceived: 0,
          threadsProcessed: 0
        }
      })
      
      setShowAddAccount(false)
      resetForm()
    } catch (error) {
      console.error('Failed to add account:', error)
    }
  }

  const handleEditAccount = async (accountId: string) => {
    try {
      const account = accounts.find(acc => acc.id === accountId)
      if (!account) return

      await updateAccount(accountId, {
        name: accountForm.name,
        color: accountForm.color,
        config: accountForm.config
      })
      
      setShowEditAccount(null)
      resetForm()
    } catch (error) {
      console.error('Failed to update account:', error)
    }
  }

  const handleDeleteAccount = async (accountId: string) => {
    try {
      await removeAccount(accountId)
      setShowDeleteConfirm(null)
    } catch (error) {
      console.error('Failed to delete account:', error)
    }
  }

  const handleSetDefault = async (accountId: string) => {
    try {
      await setDefaultAccount(accountId)
    } catch (error) {
      console.error('Failed to set default account:', error)
    }
  }

  const handleRefreshAccount = async (accountId: string) => {
    try {
      await refreshAccount(accountId)
    } catch (error) {
      console.error('Failed to refresh account:', error)
    }
  }

  const resetForm = () => {
    setAccountForm({
      name: '',
      email: '',
      provider: 'gmail',
      color: '#3b82f6',
      config: {
        provider: 'gmail',
        imap: {
          host: '',
          port: 993,
          secure: true,
          auth: { user: '', pass: '' }
        },
        smtp: {
          host: '',
          port: 587,
          secure: true,
          auth: { user: '', pass: '' }
        }
      }
    })
  }

  const getStatusIcon = (account: EmailAccount) => {
    switch (account.status) {
      case 'active':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />
      case 'error':
        return <XCircleIcon className="w-5 h-5 text-red-500" />
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
    }
  }

  const getProviderInfo = (provider: AccountProvider) => {
    const providers = {
      gmail: { name: 'Gmail', icon: '📧', description: 'Google Gmail accounts' },
      outlook: { name: 'Outlook', icon: '📮', description: 'Microsoft Outlook/Hotmail' },
      imap: { name: 'IMAP', icon: '📬', description: 'Custom IMAP server' },
      exchange: { name: 'Exchange', icon: '🏢', description: 'Microsoft Exchange' },
      yahoo: { name: 'Yahoo', icon: '📪', description: 'Yahoo Mail' },
      custom: { name: 'Custom', icon: '⚙️', description: 'Custom configuration' }
    }
    return providers[provider] || providers.custom
  }

  const editingAccount = showEditAccount ? accounts.find(acc => acc.id === showEditAccount) : null

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-medium text-gray-900">Account Management</h2>
          <p className="text-sm text-gray-500">
            Manage your email accounts and their settings
          </p>
        </div>
        <Button onClick={() => setShowAddAccount(true)}>
          <PlusIcon className="w-4 h-4 mr-2" />
          Add Account
        </Button>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <XCircleIcon className="w-5 h-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Accounts list */}
      <div className="space-y-4">
        {accounts.length === 0 ? (
          <Card className="p-8 text-center">
            <div className="space-y-3">
              <div className="w-12 h-12 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                <PlusIcon className="w-6 h-6 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900">No accounts yet</h3>
              <p className="text-gray-500">
                Add your first email account to get started with multi-account management.
              </p>
              <Button onClick={() => setShowAddAccount(true)}>
                Add Your First Account
              </Button>
            </div>
          </Card>
        ) : (
          accounts
            .sort((a, b) => a.displayOrder - b.displayOrder)
            .map((account) => (
              <Card key={account.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4">
                    {/* Avatar */}
                    <div className="relative">
                      {account.avatar ? (
                        <img
                          src={account.avatar}
                          alt={account.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div
                          className="w-12 h-12 rounded-full flex items-center justify-center text-white font-medium"
                          style={{ backgroundColor: account.color }}
                        >
                          {account.name.split(' ').map(word => word[0]).join('').toUpperCase().slice(0, 2)}
                        </div>
                      )}
                      <div className="absolute -bottom-1 -right-1">
                        {getStatusIcon(account)}
                      </div>
                    </div>

                    {/* Account info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-medium text-gray-900">
                          {account.name}
                        </h3>
                        {account.isDefault && (
                          <Badge variant="primary" size="sm">Default</Badge>
                        )}
                        {account.isPrimary && (
                          <Badge variant="secondary" size="sm">Primary</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">{account.email}</p>
                      <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                        <span className="flex items-center">
                          {getProviderInfo(account.provider).icon}
                          <span className="ml-1">{getProviderInfo(account.provider).name}</span>
                        </span>
                        <span>Status: {account.status}</span>
                        <span>{account.stats.unreadEmails} unread</span>
                        <span>{account.stats.totalEmails} total</span>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRefreshAccount(account.id)}
                      disabled={isLoading}
                      title="Refresh account"
                    >
                      <ArrowPathIcon className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setAccountForm({
                          name: account.name,
                          email: account.email,
                          provider: account.provider,
                          color: account.color,
                          config: {
                            ...account.config,
                            imap: {
                              host: account.config.imap?.host || '',
                              port: account.config.imap?.port || 993,
                              secure: account.config.imap?.secure || true,
                              auth: { 
                                user: account.config.imap?.auth?.user || '', 
                                pass: account.config.imap?.auth?.pass || '' 
                              }
                            },
                            smtp: {
                              host: account.config.smtp?.host || '',
                              port: account.config.smtp?.port || 587,
                              secure: account.config.smtp?.secure || true,
                              auth: { 
                                user: account.config.smtp?.auth?.user || '', 
                                pass: account.config.smtp?.auth?.pass || '' 
                              }
                            }
                          }
                        })
                        setShowEditAccount(account.id)
                      }}
                      title="Edit account"
                    >
                      <PencilIcon className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowDeleteConfirm(account.id)}
                      title="Delete account"
                      disabled={account.isPrimary && accounts.length === 1}
                    >
                      <TrashIcon className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Account details */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Last Sync
                    </p>
                    <p className="text-sm text-gray-900">
                      {account.lastSyncAt 
                        ? new Date(account.lastSyncAt).toLocaleString()
                        : 'Never'
                      }
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Sync Enabled
                    </p>
                    <Toggle
                      checked={account.sync.enabled}
                      onChange={(enabled) => updateAccount(account.id, {
                        sync: { ...account.sync, enabled }
                      })}
                    />
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Default Account
                    </p>
                    <Toggle
                      checked={account.isDefault}
                      onChange={() => handleSetDefault(account.id)}
                      disabled={account.isDefault}
                    />
                  </div>
                  <div className="space-y-1">
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Storage Used
                    </p>
                    <p className="text-sm text-gray-900">
                      {(account.stats.storageUsed / 1024 / 1024).toFixed(1)} MB
                    </p>
                  </div>
                </div>

                {/* Credentials (for IMAP accounts) */}
                {account.provider === 'imap' && account.auth.type === 'password' && (
                  <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-sm font-medium text-gray-900">IMAP Configuration</h4>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowCredentials(prev => ({
                          ...prev,
                          [account.id]: !prev[account.id]
                        }))}
                      >
                        {showCredentials[account.id] ? (
                          <EyeSlashIcon className="w-4 h-4" />
                        ) : (
                          <EyeIcon className="w-4 h-4" />
                        )}
                      </Button>
                    </div>
                    
                    {showCredentials[account.id] && (
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">IMAP Server:</p>
                          <p className="font-mono">{account.config.imap?.host}:{account.config.imap?.port}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">SMTP Server:</p>
                          <p className="font-mono">{account.config.smtp?.host}:{account.config.smtp?.port}</p>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </Card>
            ))
        )}
      </div>

      {/* Multi-Account Settings */}
      <SettingsSection
        title="Multi-Account Settings"
        description="Configure how multiple accounts work together"
      >
        <SettingsItem
          label="Unified Inbox"
          description="Show emails from all accounts in a single inbox"
        >
          <Toggle
            checked={settings.unifiedInboxEnabled}
            onChange={(unifiedInboxEnabled) => updateSettings({ unifiedInboxEnabled })}
          />
        </SettingsItem>

        <SettingsItem
          label="Auto-switch on Email Click"
          description="Automatically switch to the account when clicking an email"
        >
          <Toggle
            checked={settings.autoSwitchOnEmail}
            onChange={(autoSwitchOnEmail) => updateSettings({ autoSwitchOnEmail })}
          />
        </SettingsItem>

        <SettingsItem
          label="Show Account in Email List"
          description="Display account indicators in the email list"
        >
          <Toggle
            checked={settings.showAccountInEmailList}
            onChange={(showAccountInEmailList) => updateSettings({ showAccountInEmailList })}
          />
        </SettingsItem>

        <SettingsItem
          label="Consolidate Notifications"
          description="Group notifications from all accounts together"
        >
          <Toggle
            checked={settings.consolidateNotifications}
            onChange={(consolidateNotifications) => updateSettings({ consolidateNotifications })}
          />
        </SettingsItem>

        <SettingsItem
          label="Data Isolation"
          description="Keep account data completely separate"
        >
          <Toggle
            checked={settings.dataIsolation}
            onChange={(dataIsolation) => updateSettings({ dataIsolation })}
          />
        </SettingsItem>
      </SettingsSection>

      {/* Add Account Dialog */}
      <Dialog
        open={showAddAccount}
        onClose={() => {
          setShowAddAccount(false)
          resetForm()
        }}
        title="Add Email Account"
        size="lg"
      >
        <div className="space-y-6">
          {/* Provider selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Email Provider
            </label>
            <div className="grid grid-cols-2 gap-3">
              {(['gmail', 'outlook', 'imap', 'exchange'] as AccountProvider[]).map((provider) => {
                const info = getProviderInfo(provider)
                return (
                  <button
                    key={provider}
                    onClick={() => {
                      setSelectedProvider(provider)
                      setAccountForm(prev => ({ ...prev, provider }))
                    }}
                    className={`p-4 border-2 rounded-lg text-left transition-colors ${
                      selectedProvider === provider
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{info.icon}</span>
                      <div>
                        <h3 className="font-medium">{info.name}</h3>
                        <p className="text-xs text-gray-500">{info.description}</p>
                      </div>
                    </div>
                  </button>
                )
              })}
            </div>
          </div>

          {/* Basic information */}
          <div className="grid grid-cols-2 gap-4">
            <Input
              label="Account Name"
              value={accountForm.name}
              onChange={(e) => setAccountForm(prev => ({ ...prev, name: e.target.value }))}
              placeholder="e.g., Work Email"
              required
            />
            <Input
              label="Email Address"
              type="email"
              value={accountForm.email}
              onChange={(e) => setAccountForm(prev => ({ ...prev, email: e.target.value }))}
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Account Color
            </label>
            <input
              type="color"
              value={accountForm.color}
              onChange={(e) => setAccountForm(prev => ({ ...prev, color: e.target.value }))}
              className="w-16 h-10 rounded border border-gray-300"
            />
          </div>

          {/* IMAP configuration */}
          {selectedProvider === 'imap' && (
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">IMAP Configuration</h4>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="IMAP Host"
                  value={accountForm.config.imap?.host || ''}
                  onChange={(e) => setAccountForm(prev => ({
                    ...prev,
                    config: {
                      ...prev.config,
                      imap: { ...prev.config.imap!, host: e.target.value }
                    }
                  }))}
                  placeholder="imap.example.com"
                />
                <Input
                  label="IMAP Port"
                  type="number"
                  value={accountForm.config.imap?.port || 993}
                  onChange={(e) => setAccountForm(prev => ({
                    ...prev,
                    config: {
                      ...prev.config,
                      imap: { ...prev.config.imap!, port: parseInt(e.target.value) }
                    }
                  }))}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <Input
                  label="SMTP Host"
                  value={accountForm.config.smtp?.host || ''}
                  onChange={(e) => setAccountForm(prev => ({
                    ...prev,
                    config: {
                      ...prev.config,
                      smtp: { ...prev.config.smtp!, host: e.target.value }
                    }
                  }))}
                  placeholder="smtp.example.com"
                />
                <Input
                  label="SMTP Port"
                  type="number"
                  value={accountForm.config.smtp?.port || 587}
                  onChange={(e) => setAccountForm(prev => ({
                    ...prev,
                    config: {
                      ...prev.config,
                      smtp: { ...prev.config.smtp!, port: parseInt(e.target.value) }
                    }
                  }))}
                />
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => {
                setShowAddAccount(false)
                resetForm()
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleAddAccount}
              disabled={!accountForm.name || !accountForm.email || isLoading}
            >
              {isLoading ? 'Adding...' : 'Add Account'}
            </Button>
          </div>
        </div>
      </Dialog>

      {/* Edit Account Dialog */}
      <Dialog
        open={!!showEditAccount}
        onClose={() => {
          setShowEditAccount(null)
          resetForm()
        }}
        title="Edit Account"
        size="lg"
      >
        {editingAccount && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <Input
                label="Account Name"
                value={accountForm.name}
                onChange={(e) => setAccountForm(prev => ({ ...prev, name: e.target.value }))}
                required
              />
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Account Color
                </label>
                <input
                  type="color"
                  value={accountForm.color}
                  onChange={(e) => setAccountForm(prev => ({ ...prev, color: e.target.value }))}
                  className="w-16 h-10 rounded border border-gray-300"
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => {
                  setShowEditAccount(null)
                  resetForm()
                }}
              >
                Cancel
              </Button>
              <Button
                onClick={() => showEditAccount && handleEditAccount(showEditAccount)}
                disabled={isLoading || !showEditAccount}
              >
                {isLoading ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        )}
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(null)}
        title="Delete Account"
        size="md"
      >
        {showDeleteConfirm && (
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-gray-900">Delete Account</h3>
                <p className="text-sm text-gray-500">
                  This action cannot be undone. All local data for this account will be removed.
                </p>
              </div>
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-sm text-red-700">
                <strong>Warning:</strong> Deleting this account will remove all synced emails, 
                settings, and configurations. The account will need to be re-added to access again.
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowDeleteConfirm(null)}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={() => handleDeleteAccount(showDeleteConfirm)}
                disabled={isLoading}
              >
                {isLoading ? 'Deleting...' : 'Delete Account'}
              </Button>
            </div>
          </div>
        )}
      </Dialog>
    </div>
  )
}