# ProgressTracker Component Documentation

## Overview

The `ProgressTracker` component is a comprehensive, reusable progress tracking solution designed for all bulk operations in the e-connect-2 application. It provides real-time progress monitoring, error handling, analytics, and interactive controls for operations like bulk unsubscribe, clean inbox, and cold email blocking.

## Features

### 1. Universal Progress Display
- **Real-time Progress Bar**: Animated progress bar with percentage completion
- **Segmented Visualization**: Shows success, failure, and skipped items in different colors
- **Current Operation Status**: Displays the current item being processed
- **Time Estimation**: Dynamic calculation of remaining time based on processing speed
- **Speed Metrics**: Shows items per second/minute processing rate

### 2. Detailed Operation Tracking
- **Step-by-Step Breakdown**: Granular view of operation progress
- **Queue Management**: Visual representation of pending, processing, and completed items
- **Pause/Resume**: Full control over operation execution
- **Priority Adjustment**: Change processing priority for specific items
- **State Persistence**: Saves UI preferences across page reloads

### 3. Error Handling & Logging
- **Error Categorization**: Filter errors by type (retryable, permanent, network, validation)
- **Retry Mechanisms**: Individual or batch retry for failed items
- **Detailed Logs**: Timestamped logs with error context and stack traces
- **Warning System**: Visual indicators for potential issues
- **Recovery Suggestions**: Automated suggestions for common problems

### 4. Results Summary
- **Completion Statistics**: Comprehensive metrics on operation results
- **Impact Visualization**: Shows storage saved, emails processed, cost savings
- **Performance Analytics**: Processing speed, efficiency, and throughput metrics
- **Export Functionality**: Export reports in JSON, CSV, or PDF formats
- **Before/After Comparisons**: Visual representation of changes

### 5. Interactive Controls
- **Operation Control**: Pause, resume, and cancel operations
- **Batch Operations**: Select multiple items for bulk actions
- **Live Updates**: Real-time configuration changes during operation
- **Emergency Stop**: Immediate operation termination
- **View Modes**: Switch between overview, detailed, errors, logs, and analytics

### 6. Notification System
- **Milestone Alerts**: Notifications at 25%, 50%, 75%, and 100% completion
- **Sound Alerts**: Audio feedback for important events
- **Desktop Notifications**: Browser notification API integration
- **Email Notifications**: Simulated email alerts for completion
- **Custom Preferences**: User-configurable notification settings

## Usage

### Basic Implementation

```tsx
import { ProgressTracker } from './components/bulk-actions/ProgressTracker'

function MyBulkOperation() {
  const [operation, setOperation] = useState<BulkOperation>(/* ... */)

  return (
    <ProgressTracker
      operation={operation}
      onPause={() => pauseOperation()}
      onResume={() => resumeOperation()}
      onCancel={() => cancelOperation()}
      onRetry={(itemId) => retryItem(itemId)}
      onExport={(format) => exportResults(format)}
    />
  )
}
```

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `operation` | `BulkOperation` | Required | The operation object to track |
| `onPause` | `() => void` | - | Callback when pause is clicked |
| `onResume` | `() => void` | - | Callback when resume is clicked |
| `onCancel` | `() => void` | - | Callback when cancel is clicked |
| `onRetry` | `(itemId: string) => void` | - | Callback to retry a specific item |
| `onExport` | `(format: 'json' \| 'csv' \| 'pdf') => void` | - | Export callback |
| `onPriorityChange` | `(itemId: string, priority: Priority) => void` | - | Priority change callback |
| `className` | `string` | - | Additional CSS classes |
| `compact` | `boolean` | `false` | Show in compact mode |
| `showNotifications` | `boolean` | `true` | Enable notifications |
| `persistState` | `boolean` | `true` | Persist UI state to localStorage |
| `websocketUrl` | `string` | - | WebSocket URL for real-time updates |

### View Modes

1. **Overview**: Default view with progress bar and basic metrics
2. **Detailed**: Comprehensive metrics and performance data
3. **Errors**: Filtered error list with retry options
4. **Logs**: Operation logs with timestamps and categories
5. **Analytics**: Full analytics dashboard with charts and summaries

### Compact Mode

The component supports a compact mode for space-constrained layouts:

```tsx
<ProgressTracker
  operation={operation}
  compact={true}
/>
```

In compact mode, the tracker shows minimal information with an expand button for full details.

## Integration Examples

### With BulkUnsubscribe

```tsx
const [currentOperation, setCurrentOperation] = useState<BulkUnsubscribeOperation>()

// In your unsubscribe component
<ProgressTracker
  operation={currentOperation}
  onPause={async () => {
    await fetch(`/api/bulk/unsubscribe/${currentOperation.id}/pause`, { method: 'POST' })
  }}
  onResume={async () => {
    await fetch(`/api/bulk/unsubscribe/${currentOperation.id}/resume`, { method: 'POST' })
  }}
  onCancel={async () => {
    await fetch(`/api/bulk/unsubscribe/${currentOperation.id}/cancel`, { method: 'POST' })
  }}
/>
```

### With CleanInbox

```tsx
<ProgressTracker
  operation={cleanInboxOperation}
  showNotifications={true}
  websocketUrl={`wss://api.example.com/operations/${cleanInboxOperation.id}`}
/>
```

### With ColdEmailBlocker

```tsx
<ProgressTracker
  operation={blockOperation}
  onExport={async (format) => {
    const response = await fetch(`/api/operations/${blockOperation.id}/export?format=${format}`)
    const blob = await response.blob()
    downloadBlob(blob, `blocked-emails.${format}`)
  }}
/>
```

## Customization

### Styling

The component uses Tailwind CSS classes and accepts a `className` prop for additional styling:

```tsx
<ProgressTracker
  operation={operation}
  className="shadow-lg rounded-xl"
/>
```

### Custom Operation Types

Add custom operation icons and colors by extending the helper functions:

```tsx
// In ProgressTracker.tsx
const getOperationIcon = (type: string) => {
  const icons: Record<string, React.ReactNode> = {
    'custom-operation': <CustomIcon className="w-4 h-4" />,
    // ... other operations
  }
  return icons[type] || icons.default
}
```

## Performance Considerations

1. **Metric Calculation**: Metrics are calculated every second to balance accuracy and performance
2. **Log Limits**: Logs are limited to the last 100 entries to prevent memory issues
3. **Error Display**: Errors are paginated when exceeding 20 items
4. **WebSocket Reconnection**: Automatic reconnection with exponential backoff
5. **State Persistence**: Uses localStorage with size limits consideration

## Accessibility

- Full keyboard navigation support
- Screen reader announcements for progress updates
- ARIA labels for all interactive elements
- High contrast mode support
- Focus management for dialogs and modals

## Browser Compatibility

- Chrome/Edge: Full support
- Firefox: Full support
- Safari: Full support (notifications require user permission)
- Mobile browsers: Responsive design with touch support

## Testing

The component includes a demo page for testing all features:

```tsx
import { ProgressTrackerDemo } from './components/bulk-actions/ProgressTrackerDemo'

// Add to your routes
<Route path="/progress-demo" element={<ProgressTrackerDemo />} />
```

## Future Enhancements

1. **Graph Visualizations**: Add charts for operation metrics
2. **Multi-Operation View**: Track multiple operations simultaneously
3. **Operation Templates**: Save and reuse operation configurations
4. **Advanced Filtering**: More sophisticated error and result filtering
5. **Real-time Collaboration**: Share operation progress with team members