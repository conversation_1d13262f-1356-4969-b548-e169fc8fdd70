import React, { useState, useEffect } from 'react'
import { ProgressTracker } from './ProgressTracker'
import { Button } from '../ui/Button'
import { Card } from '../ui/Card'
import type { BulkOperation } from '../../types/bulk'

// Demo component to showcase ProgressTracker functionality
export function ProgressTrackerDemo() {
  const [operation, setOperation] = useState<BulkOperation | null>(null)
  const [isPaused, setIsPaused] = useState(false)

  // Simulate an operation
  const startOperation = (type: 'unsubscribe' | 'archive' | 'delete') => {
    const totalItems = 1000
    const newOperation: BulkOperation = {
      id: `op-${Date.now()}`,
      type,
      status: 'processing',
      createdAt: new Date(),
      startedAt: new Date(),
      selection: {
        type: 'filter',
        totalCount: totalItems,
        filter: {
          category: ['newsletter', 'marketing'],
          dateRange: {
            start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            end: new Date()
          }
        }
      },
      progress: {
        processed: 0,
        succeeded: 0,
        failed: 0,
        skipped: 0,
        percentage: 0,
        currentItem: '<EMAIL>'
      },
      errors: [],
      retryCount: 0,
      maxRetries: 3
    }
    setOperation(newOperation)
    setIsPaused(false)
  }

  // Simulate progress updates
  useEffect(() => {
    if (!operation || operation.status !== 'processing' || isPaused) return

    const interval = setInterval(() => {
      setOperation(prev => {
        if (!prev || prev.status !== 'processing') return prev

        const progress = { ...prev.progress }
        const totalProcessed = progress.processed + progress.succeeded + progress.failed + progress.skipped
        
        if (totalProcessed >= prev.selection.totalCount) {
          return {
            ...prev,
            status: 'completed',
            completedAt: new Date(),
            summary: {
              totalItems: prev.selection.totalCount,
              processedItems: prev.selection.totalCount,
              successfulItems: progress.succeeded,
              failedItems: progress.failed,
              skippedItems: progress.skipped,
              totalTime: Date.now() - prev.startedAt!.getTime(),
              avgTimePerItem: (Date.now() - prev.startedAt!.getTime()) / prev.selection.totalCount,
              changes: {
                unsubscribed: progress.succeeded,
                archived: Math.floor(progress.succeeded * 0.8),
                deleted: Math.floor(progress.succeeded * 0.2)
              },
              sizeReclaimed: progress.succeeded * 1024 * 50, // 50KB per item
              costSaved: progress.succeeded * 0.001 // $0.001 per item
            }
          }
        }

        // Simulate processing
        const random = Math.random()
        if (random < 0.85) {
          progress.succeeded++
        } else if (random < 0.95) {
          progress.failed++
          // Add random error
          const errors = [...prev.errors]
          if (errors.length < 20) {
            errors.push({
              itemId: `item-${totalProcessed}`,
              error: ['Network timeout', 'Invalid response', 'Rate limit exceeded'][Math.floor(Math.random() * 3)],
              code: ['NETWORK_ERROR', 'INVALID_RESPONSE', 'RATE_LIMIT'][Math.floor(Math.random() * 3)],
              timestamp: new Date(),
              retryable: Math.random() > 0.3
            })
          }
          return { ...prev, progress, errors }
        } else {
          progress.skipped++
        }

        progress.processed = totalProcessed + 1
        progress.percentage = ((totalProcessed + 1) / prev.selection.totalCount) * 100
        progress.estimatedTimeRemaining = ((prev.selection.totalCount - totalProcessed) / (totalProcessed / ((Date.now() - prev.startedAt!.getTime()) / 1000)))
        progress.currentItem = `sender${totalProcessed + 1}@example.com`

        return { ...prev, progress }
      })
    }, 100) // Update every 100ms for demo

    return () => clearInterval(interval)
  }, [operation, isPaused])

  const handlePause = () => {
    setIsPaused(true)
    setOperation(prev => prev ? { ...prev, status: 'paused' } : null)
  }

  const handleResume = () => {
    setIsPaused(false)
    setOperation(prev => prev ? { ...prev, status: 'processing' } : null)
  }

  const handleCancel = () => {
    setOperation(prev => prev ? { ...prev, status: 'cancelled', completedAt: new Date() } : null)
  }

  const handleRetry = (itemId: string) => {
    console.log('Retrying item:', itemId)
    // In a real implementation, this would retry the specific item
  }

  const handleExport = (format: 'json' | 'csv' | 'pdf') => {
    console.log('Exporting in format:', format)
    // In a real implementation, this would trigger the export
  }

  const handlePriorityChange = (itemId: string, priority: 'high' | 'normal' | 'low') => {
    console.log('Changing priority for item:', itemId, 'to:', priority)
    // In a real implementation, this would update the item's priority
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Progress Tracker Demo</h1>
        <p className="text-gray-600 mt-1">
          Test the comprehensive progress tracking component with different operations
        </p>
      </div>

      {/* Operation Selector */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold mb-4">Start a Demo Operation</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button
            onClick={() => startOperation('unsubscribe')}
            disabled={operation?.status === 'processing'}
            className="w-full"
          >
            Start Bulk Unsubscribe
          </Button>
          <Button
            onClick={() => startOperation('archive')}
            disabled={operation?.status === 'processing'}
            className="w-full"
          >
            Start Clean Inbox
          </Button>
          <Button
            onClick={() => startOperation('delete')}
            disabled={operation?.status === 'processing'}
            className="w-full"
          >
            Start Cold Email Blocker
          </Button>
        </div>
      </Card>

      {/* Progress Tracker */}
      {operation && (
        <ProgressTracker
          operation={operation}
          onPause={handlePause}
          onResume={handleResume}
          onCancel={handleCancel}
          onRetry={handleRetry}
          onExport={handleExport}
          onPriorityChange={handlePriorityChange}
          showNotifications={true}
          persistState={true}
        />
      )}

      {/* Compact View Demo */}
      {operation && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Compact View</h3>
          <ProgressTracker
            operation={operation}
            onPause={handlePause}
            onResume={handleResume}
            onCancel={handleCancel}
            compact={true}
          />
        </Card>
      )}

      {/* Feature List */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Features Demonstrated</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Progress Display</h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Real-time progress bar with percentage</li>
              <li>• Segmented view for success/failure/skipped</li>
              <li>• Current item being processed</li>
              <li>• Estimated time remaining</li>
              <li>• Processing speed metrics</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Operation Tracking</h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Step-by-step progress breakdown</li>
              <li>• Queue status visualization</li>
              <li>• Pause/resume functionality</li>
              <li>• Cancel operation support</li>
              <li>• Priority adjustment for items</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Error Handling</h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Error categorization and filtering</li>
              <li>• Retry mechanisms for failed items</li>
              <li>• Detailed error logs with context</li>
              <li>• Warning indicators</li>
              <li>• Recovery suggestions</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Results & Analytics</h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Comprehensive completion statistics</li>
              <li>• Before/after comparisons</li>
              <li>• Impact metrics visualization</li>
              <li>• Export in JSON/CSV/PDF formats</li>
              <li>• Performance analytics</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Interactive Controls</h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Live configuration updates</li>
              <li>• Manual retry for failed items</li>
              <li>• Batch selection and operations</li>
              <li>• Emergency stop functionality</li>
              <li>• View mode switching</li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Notifications</h4>
            <ul className="space-y-1 text-sm text-gray-600">
              <li>• Milestone notifications (25%, 50%, etc)</li>
              <li>• Sound alerts for events</li>
              <li>• Desktop notifications support</li>
              <li>• Email notification simulation</li>
              <li>• Customizable preferences</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  )
}