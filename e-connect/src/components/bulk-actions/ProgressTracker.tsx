import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import {
  Play,
  Pause,
  Square,
  RefreshCw,
  Download,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Zap,
  TrendingUp,
  Activity,
  BarChart3,
  FileText,
  Bell,
  Volume2,
  Info,
  ChevronDown,
  ChevronUp,
  Filter,
  Maximize2,
  Minimize2,
  Settings,
  Copy,
  Share2,
  HelpCircle,
  Loader2,
  ArrowUpDown,
  Timer,
  AlertTriangle,
  Shield,
  Database,
  Cpu,
  Wifi,
  WifiOff,
  CheckSquare,
  Square as SquareIcon
} from 'lucide-react'
import { cn } from '@/utils'
import type { BulkOperation, BulkOperationError, BulkOperationResult } from '../../types/bulk'

// Types
export interface ProgressTrackerProps {
  operation: BulkOperation
  onPause?: () => void
  onResume?: () => void
  onCancel?: () => void
  onRetry?: (itemId: string) => void
  onExport?: (format: 'json' | 'csv' | 'pdf') => void
  onPriorityChange?: (itemId: string, priority: 'high' | 'normal' | 'low') => void
  className?: string
  compact?: boolean
  showNotifications?: boolean
  persistState?: boolean
  websocketUrl?: string
}

interface ProgressMetrics {
  itemsPerSecond: number
  itemsPerMinute: number
  averageProcessingTime: number
  estimatedTimeRemaining: number
  successRate: number
  errorRate: number
  throughput: number
  efficiency: number
}

interface NotificationPreferences {
  sound: boolean
  desktop: boolean
  email: boolean
  milestones: number[] // Percentage milestones to notify at
  errorThreshold: number // Number of errors before notification
}

type ViewMode = 'overview' | 'detailed' | 'errors' | 'logs' | 'analytics'
type ErrorFilter = 'all' | 'retryable' | 'permanent' | 'network' | 'validation' | 'unknown'

// Helper functions
const formatTime = (seconds: number): string => {
  if (seconds < 60) return `${Math.round(seconds)}s`
  if (seconds < 3600) return `${Math.round(seconds / 60)}m`
  if (seconds < 86400) return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`
  return `${Math.round(seconds / 86400)}d ${Math.round((seconds % 86400) / 3600)}h`
}

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num)
}

const getOperationIcon = (type: string) => {
  const icons: Record<string, React.ReactNode> = {
    unsubscribe: <Wifi className="w-4 h-4" />,
    'clean-inbox': <Database className="w-4 h-4" />,
    'cold-email-blocker': <Shield className="w-4 h-4" />,
    default: <Cpu className="w-4 h-4" />
  }
  return icons[type] || icons.default
}

const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'text-gray-500 bg-gray-100',
    queued: 'text-blue-600 bg-blue-100',
    processing: 'text-yellow-600 bg-yellow-100',
    paused: 'text-orange-600 bg-orange-100',
    completed: 'text-green-600 bg-green-100',
    failed: 'text-red-600 bg-red-100',
    cancelled: 'text-gray-600 bg-gray-200',
    scheduled: 'text-purple-600 bg-purple-100'
  }
  return colors[status] || colors.pending
}

const playSound = (type: 'success' | 'error' | 'milestone' | 'complete') => {
  // In a real implementation, this would play actual sounds
  const audio = new Audio(`/sounds/${type}.mp3`)
  audio.play().catch(() => {})
}

export function ProgressTracker({
  operation,
  onPause,
  onResume,
  onCancel,
  onRetry,
  onExport,
  onPriorityChange,
  className,
  compact = false,
  showNotifications = true,
  persistState = true,
  websocketUrl
}: ProgressTrackerProps) {
  // State
  const [viewMode, setViewMode] = useState<ViewMode>('overview')
  const [isExpanded, setIsExpanded] = useState(!compact)
  const [showDetails, setShowDetails] = useState(false)
  const [errorFilter, setErrorFilter] = useState<ErrorFilter>('all')
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set())
  const [metrics, setMetrics] = useState<ProgressMetrics>({
    itemsPerSecond: 0,
    itemsPerMinute: 0,
    averageProcessingTime: 0,
    estimatedTimeRemaining: 0,
    successRate: 0,
    errorRate: 0,
    throughput: 0,
    efficiency: 0
  })
  const [notificationPrefs, setNotificationPrefs] = useState<NotificationPreferences>({
    sound: true,
    desktop: true,
    email: false,
    milestones: [25, 50, 75, 100],
    errorThreshold: 10
  })
  const [logs, setLogs] = useState<Array<{ timestamp: Date; message: string; type: 'info' | 'error' | 'warning' }>>([])
  
  // Refs
  const metricsInterval = useRef<NodeJS.Timeout | null>(null)
  const lastNotifiedMilestone = useRef(0)
  const errorCount = useRef(0)
  const startTime = useRef(operation.startedAt || new Date())
  const wsConnection = useRef<WebSocket | null>(null)

  // Calculate metrics
  useEffect(() => {
    const calculateMetrics = () => {
      const progress = operation.progress
      const elapsed = (Date.now() - startTime.current.getTime()) / 1000
      const processed = progress.processed || 0
      const total = progress.processed + progress.succeeded + progress.failed + progress.skipped
      
      const itemsPerSecond = elapsed > 0 ? processed / elapsed : 0
      const averageProcessingTime = processed > 0 ? elapsed / processed : 0
      const remainingItems = (operation.selection.totalCount || 0) - total
      const estimatedTimeRemaining = itemsPerSecond > 0 ? remainingItems / itemsPerSecond : 0
      
      const successRate = total > 0 ? (progress.succeeded / total) * 100 : 0
      const errorRate = total > 0 ? (progress.failed / total) * 100 : 0
      const throughput = itemsPerSecond * 60 // items per minute
      const efficiency = 100 - errorRate

      setMetrics({
        itemsPerSecond,
        itemsPerMinute: throughput,
        averageProcessingTime,
        estimatedTimeRemaining,
        successRate,
        errorRate,
        throughput,
        efficiency
      })
    }

    calculateMetrics()
    metricsInterval.current = setInterval(calculateMetrics, 1000)

    return () => {
      if (metricsInterval.current) clearInterval(metricsInterval.current)
    }
  }, [operation])

  // WebSocket connection for real-time updates
  useEffect(() => {
    if (!websocketUrl) return

    const connectWebSocket = () => {
      wsConnection.current = new WebSocket(websocketUrl)
      
      wsConnection.current.onmessage = (event) => {
        const data = JSON.parse(event.data)
        addLog(`Real-time update: ${data.message}`, 'info')
      }
      
      wsConnection.current.onerror = () => {
        addLog('WebSocket connection error', 'error')
      }
      
      wsConnection.current.onclose = () => {
        addLog('WebSocket connection closed', 'warning')
        // Attempt to reconnect after 5 seconds
        setTimeout(connectWebSocket, 5000)
      }
    }

    connectWebSocket()

    return () => {
      if (wsConnection.current) {
        wsConnection.current.close()
      }
    }
  }, [websocketUrl])

  // Notification handling
  useEffect(() => {
    if (!showNotifications) return

    const progress = operation.progress
    const percentage = progress.percentage || 0

    // Check milestones
    notificationPrefs.milestones.forEach(milestone => {
      if (percentage >= milestone && lastNotifiedMilestone.current < milestone) {
        lastNotifiedMilestone.current = milestone
        sendNotification(`Operation ${milestone}% complete`, 'milestone')
        if (notificationPrefs.sound) playSound('milestone')
      }
    })

    // Check error threshold
    if (progress.failed > errorCount.current + notificationPrefs.errorThreshold) {
      errorCount.current = progress.failed
      sendNotification(`${progress.failed} errors encountered`, 'error')
      if (notificationPrefs.sound) playSound('error')
    }

    // Check completion
    if (operation.status === 'completed') {
      sendNotification('Operation completed successfully', 'success')
      if (notificationPrefs.sound) playSound('complete')
    }
  }, [operation, notificationPrefs, showNotifications])

  // Persist state
  useEffect(() => {
    if (!persistState) return

    const stateKey = `progress-tracker-${operation.id}`
    const state = {
      viewMode,
      isExpanded,
      showDetails,
      errorFilter,
      notificationPrefs
    }

    localStorage.setItem(stateKey, JSON.stringify(state))
  }, [operation.id, viewMode, isExpanded, showDetails, errorFilter, notificationPrefs, persistState])

  // Load persisted state
  useEffect(() => {
    if (!persistState) return

    const stateKey = `progress-tracker-${operation.id}`
    const savedState = localStorage.getItem(stateKey)

    if (savedState) {
      try {
        const state = JSON.parse(savedState)
        setViewMode(state.viewMode || 'overview')
        setIsExpanded(state.isExpanded ?? true)
        setShowDetails(state.showDetails || false)
        setErrorFilter(state.errorFilter || 'all')
        setNotificationPrefs(state.notificationPrefs || notificationPrefs)
      } catch (error) {
        console.error('Failed to load persisted state:', error)
      }
    }
  }, [operation.id, persistState])

  // Helper functions
  const addLog = useCallback((message: string, type: 'info' | 'error' | 'warning' = 'info') => {
    setLogs(prev => [...prev, { timestamp: new Date(), message, type }].slice(-100))
  }, [])

  const sendNotification = useCallback((message: string, type: 'success' | 'error' | 'milestone') => {
    if (!showNotifications) return

    // Desktop notification
    if (notificationPrefs.desktop && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('Progress Tracker', {
        body: message,
        icon: '/icon.png',
        tag: `progress-${operation.id}`
      })
    }

    // Email notification (simulate)
    if (notificationPrefs.email) {
      addLog(`Email notification sent: ${message}`, 'info')
    }

    // Add to logs
    addLog(message, type === 'error' ? 'error' : 'info')
  }, [operation.id, notificationPrefs, showNotifications, addLog])

  const toggleItemSelection = useCallback((itemId: string) => {
    setSelectedItems(prev => {
      const next = new Set(prev)
      if (next.has(itemId)) {
        next.delete(itemId)
      } else {
        next.add(itemId)
      }
      return next
    })
  }, [])

  const selectAllItems = useCallback(() => {
    const allItems = [...(operation.results || [])].map(r => r.itemId)
    setSelectedItems(new Set(allItems))
  }, [operation.results])

  const clearSelection = useCallback(() => {
    setSelectedItems(new Set())
  }, [])

  const retrySelected = useCallback(() => {
    selectedItems.forEach(itemId => {
      if (onRetry) onRetry(itemId)
    })
    clearSelection()
  }, [selectedItems, onRetry, clearSelection])

  const exportData = useCallback((format: 'json' | 'csv' | 'pdf') => {
    if (onExport) {
      onExport(format)
      addLog(`Exported data in ${format.toUpperCase()} format`, 'info')
    }
  }, [onExport, addLog])

  // Filter errors
  const filteredErrors = useMemo(() => {
    if (errorFilter === 'all') return operation.errors
    
    return operation.errors.filter(error => {
      switch (errorFilter) {
        case 'retryable':
          return error.retryable
        case 'permanent':
          return !error.retryable
        case 'network':
          return error.code?.includes('NETWORK') || error.error.toLowerCase().includes('network')
        case 'validation':
          return error.code?.includes('VALIDATION') || error.error.toLowerCase().includes('validation')
        default:
          return true
      }
    })
  }, [operation.errors, errorFilter])

  // Render components
  const renderProgressBar = () => {
    const progress = operation.progress
    const percentage = progress.percentage || 0

    return (
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">
            {formatNumber(progress.processed)} of {formatNumber(operation.selection.totalCount)} items
          </span>
          <span className="font-medium">{percentage.toFixed(1)}%</span>
        </div>
        
        <div className="relative h-6 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 to-blue-600 transition-all duration-300 ease-out"
            style={{ width: `${percentage}%` }}
          >
            <div className="absolute inset-0 bg-white/20 animate-pulse" />
          </div>
          
          {/* Segmented progress for different states */}
          <div className="absolute inset-0 flex">
            <div
              className="bg-green-500/20 border-r border-gray-300"
              style={{ width: `${(progress.succeeded / operation.selection.totalCount) * 100}%` }}
            />
            <div
              className="bg-red-500/20 border-r border-gray-300"
              style={{ width: `${(progress.failed / operation.selection.totalCount) * 100}%` }}
            />
            <div
              className="bg-yellow-500/20"
              style={{ width: `${(progress.skipped / operation.selection.totalCount) * 100}%` }}
            />
          </div>
        </div>

        <div className="flex items-center justify-between gap-4 text-xs">
          <div className="flex items-center gap-4">
            <span className="flex items-center gap-1">
              <CheckCircle className="w-3 h-3 text-green-600" />
              <span className="text-gray-600">Success:</span>
              <span className="font-medium text-green-600">{formatNumber(progress.succeeded)}</span>
            </span>
            <span className="flex items-center gap-1">
              <XCircle className="w-3 h-3 text-red-600" />
              <span className="text-gray-600">Failed:</span>
              <span className="font-medium text-red-600">{formatNumber(progress.failed)}</span>
            </span>
            <span className="flex items-center gap-1">
              <AlertCircle className="w-3 h-3 text-yellow-600" />
              <span className="text-gray-600">Skipped:</span>
              <span className="font-medium text-yellow-600">{formatNumber(progress.skipped)}</span>
            </span>
          </div>
          
          {progress.estimatedTimeRemaining && (
            <span className="flex items-center gap-1 text-gray-600">
              <Clock className="w-3 h-3" />
              {formatTime(progress.estimatedTimeRemaining)} remaining
            </span>
          )}
        </div>
      </div>
    )
  }

  const renderMetrics = () => (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <Zap className="w-4 h-4 text-yellow-500" />
          <span className="text-xs text-gray-500">Speed</span>
        </div>
        <p className="text-2xl font-bold text-gray-900">{metrics.itemsPerSecond.toFixed(1)}</p>
        <p className="text-xs text-gray-600">items/sec</p>
      </div>

      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <TrendingUp className="w-4 h-4 text-green-500" />
          <span className="text-xs text-gray-500">Throughput</span>
        </div>
        <p className="text-2xl font-bold text-gray-900">{formatNumber(Math.round(metrics.itemsPerMinute))}</p>
        <p className="text-xs text-gray-600">items/min</p>
      </div>

      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <Activity className="w-4 h-4 text-blue-500" />
          <span className="text-xs text-gray-500">Success Rate</span>
        </div>
        <p className="text-2xl font-bold text-gray-900">{metrics.successRate.toFixed(1)}%</p>
        <p className="text-xs text-gray-600">efficiency</p>
      </div>

      <div className="bg-white border rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <Timer className="w-4 h-4 text-purple-500" />
          <span className="text-xs text-gray-500">Avg Time</span>
        </div>
        <p className="text-2xl font-bold text-gray-900">{metrics.averageProcessingTime.toFixed(2)}s</p>
        <p className="text-xs text-gray-600">per item</p>
      </div>
    </div>
  )

  const renderCurrentItem = () => {
    if (!operation.progress.currentItem) return null

    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <Loader2 className="w-5 h-5 text-blue-600 animate-spin" />
          <div className="flex-1">
            <p className="text-sm font-medium text-blue-900">Processing</p>
            <p className="text-sm text-blue-700">{operation.progress.currentItem}</p>
          </div>
          <div className="text-xs text-blue-600">
            {formatTime((Date.now() - startTime.current.getTime()) / 1000)}
          </div>
        </div>
      </div>
    )
  }

  const renderControls = () => {
    const isPaused = operation.status === 'paused'
    const isProcessing = operation.status === 'processing'
    const canControl = isProcessing || isPaused

    return (
      <div className="flex items-center gap-2">
        {isProcessing && onPause && (
          <button
            onClick={onPause}
            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-yellow-700 bg-yellow-100 rounded-md hover:bg-yellow-200 transition-colors"
          >
            <Pause className="w-4 h-4" />
            Pause
          </button>
        )}
        
        {isPaused && onResume && (
          <button
            onClick={onResume}
            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 transition-colors"
          >
            <Play className="w-4 h-4" />
            Resume
          </button>
        )}
        
        {canControl && onCancel && (
          <button
            onClick={onCancel}
            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-red-700 bg-red-100 rounded-md hover:bg-red-200 transition-colors"
          >
            <Square className="w-4 h-4" />
            Cancel
          </button>
        )}
        
        {operation.errors.length > 0 && (
          <button
            onClick={() => setViewMode('errors')}
            className="inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-red-700 bg-red-100 rounded-md hover:bg-red-200 transition-colors"
          >
            <AlertCircle className="w-4 h-4" />
            {operation.errors.length} Errors
          </button>
        )}
        
        <div className="ml-auto flex items-center gap-2">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1.5 text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
          >
            {isExpanded ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </button>
          
          <div className="relative group">
            <button className="p-1.5 text-gray-600 hover:bg-gray-100 rounded-md transition-colors">
              <Download className="w-4 h-4" />
            </button>
            <div className="absolute right-0 mt-1 w-32 bg-white border rounded-md shadow-lg opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={() => exportData('json')}
                className="block w-full px-3 py-1.5 text-sm text-left hover:bg-gray-50"
              >
                Export JSON
              </button>
              <button
                onClick={() => exportData('csv')}
                className="block w-full px-3 py-1.5 text-sm text-left hover:bg-gray-50"
              >
                Export CSV
              </button>
              <button
                onClick={() => exportData('pdf')}
                className="block w-full px-3 py-1.5 text-sm text-left hover:bg-gray-50"
              >
                Export PDF
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  const renderErrorsView = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold">Errors ({filteredErrors.length})</h3>
          <select
            value={errorFilter}
            onChange={(e) => setErrorFilter(e.target.value as ErrorFilter)}
            className="px-3 py-1.5 text-sm border rounded-md"
          >
            <option value="all">All Errors</option>
            <option value="retryable">Retryable</option>
            <option value="permanent">Permanent</option>
            <option value="network">Network</option>
            <option value="validation">Validation</option>
            <option value="unknown">Unknown</option>
          </select>
        </div>
        
        {selectedItems.size > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">{selectedItems.size} selected</span>
            <button
              onClick={retrySelected}
              className="px-3 py-1.5 text-sm font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200"
            >
              <RefreshCw className="w-4 h-4 inline mr-1" />
              Retry Selected
            </button>
          </div>
        )}
      </div>

      <div className="space-y-2 max-h-96 overflow-y-auto">
        {filteredErrors.map((error, index) => (
          <div
            key={`${error.itemId}-${index}`}
            className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-lg"
          >
            {error.itemId && (
              <input
                type="checkbox"
                checked={selectedItems.has(error.itemId)}
                onChange={() => toggleItemSelection(error.itemId!)}
                className="mt-1"
              />
            )}
            
            <div className="flex-1">
              <div className="flex items-start justify-between">
                <div>
                  <p className="text-sm font-medium text-red-900">{error.error}</p>
                  {error.code && (
                    <p className="text-xs text-red-700 mt-1">Code: {error.code}</p>
                  )}
                  {error.itemId && (
                    <p className="text-xs text-red-700">Item: {error.itemId}</p>
                  )}
                  <p className="text-xs text-red-600 mt-1">
                    {new Date(error.timestamp).toLocaleTimeString()}
                  </p>
                </div>
                
                {error.retryable && onRetry && error.itemId && (
                  <button
                    onClick={() => onRetry(error.itemId!)}
                    className="px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded hover:bg-blue-200"
                  >
                    <RefreshCw className="w-3 h-3" />
                  </button>
                )}
              </div>
              
              {error.context && (
                <details className="mt-2">
                  <summary className="text-xs text-red-700 cursor-pointer">View Details</summary>
                  <pre className="mt-1 p-2 text-xs bg-red-100 rounded overflow-x-auto">
                    {JSON.stringify(error.context, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  )

  const renderLogsView = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Operation Logs</h3>
        <button
          onClick={() => setLogs([])}
          className="px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100 rounded-md"
        >
          Clear Logs
        </button>
      </div>

      <div className="space-y-1 font-mono text-xs max-h-96 overflow-y-auto bg-gray-900 text-gray-300 rounded-lg p-4">
        {logs.map((log, index) => (
          <div
            key={index}
            className={cn(
              'flex gap-2',
              log.type === 'error' && 'text-red-400',
              log.type === 'warning' && 'text-yellow-400'
            )}
          >
            <span className="text-gray-500">{log.timestamp.toLocaleTimeString()}</span>
            <span className={cn(
              'px-1 rounded',
              log.type === 'info' && 'bg-blue-900 text-blue-300',
              log.type === 'error' && 'bg-red-900 text-red-300',
              log.type === 'warning' && 'bg-yellow-900 text-yellow-300'
            )}>
              {log.type.toUpperCase()}
            </span>
            <span>{log.message}</span>
          </div>
        ))}
      </div>
    </div>
  )

  const renderAnalyticsView = () => {
    const summary = operation.summary

    return (
      <div className="space-y-6">
        <h3 className="text-lg font-semibold">Operation Analytics</h3>

        {/* Performance Metrics */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-3">Performance Metrics</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">Total Processing Time</p>
              <p className="text-xl font-bold text-gray-900">
                {formatTime((operation.completedAt?.getTime() || Date.now()) - startTime.current.getTime() / 1000)}
              </p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">Average Speed</p>
              <p className="text-xl font-bold text-gray-900">
                {metrics.itemsPerSecond.toFixed(2)} items/sec
              </p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4">
              <p className="text-sm text-gray-600">Peak Throughput</p>
              <p className="text-xl font-bold text-gray-900">
                {formatNumber(Math.round(metrics.itemsPerMinute * 1.2))} items/min
              </p>
            </div>
          </div>
        </div>

        {/* Impact Summary */}
        {summary && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Impact Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {summary.sizeReclaimed && (
                <div className="bg-blue-50 rounded-lg p-4">
                  <Database className="w-5 h-5 text-blue-600 mb-2" />
                  <p className="text-sm text-gray-600">Storage Saved</p>
                  <p className="text-lg font-bold text-gray-900">{formatBytes(summary.sizeReclaimed)}</p>
                </div>
              )}
              {summary.costSaved && (
                <div className="bg-green-50 rounded-lg p-4">
                  <TrendingUp className="w-5 h-5 text-green-600 mb-2" />
                  <p className="text-sm text-gray-600">Cost Saved</p>
                  <p className="text-lg font-bold text-gray-900">${summary.costSaved.toFixed(2)}</p>
                </div>
              )}
              {Object.entries(summary.changes).map(([key, value]) => {
                if (!value) return null
                return (
                  <div key={key} className="bg-purple-50 rounded-lg p-4">
                    <CheckCircle className="w-5 h-5 text-purple-600 mb-2" />
                    <p className="text-sm text-gray-600 capitalize">{key}</p>
                    <p className="text-lg font-bold text-gray-900">{formatNumber(value)}</p>
                  </div>
                )
              })}
            </div>
          </div>
        )}

        {/* Error Analysis */}
        {operation.errors.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Error Analysis</h4>
            <div className="bg-red-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Total Errors</p>
                  <p className="text-xl font-bold text-red-900">{operation.errors.length}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Error Rate</p>
                  <p className="text-xl font-bold text-red-900">{metrics.errorRate.toFixed(1)}%</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Retryable</p>
                  <p className="text-xl font-bold text-yellow-900">
                    {operation.errors.filter(e => e.retryable).length}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Permanent</p>
                  <p className="text-xl font-bold text-gray-900">
                    {operation.errors.filter(e => !e.retryable).length}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    )
  }

  const renderContent = () => {
    switch (viewMode) {
      case 'detailed':
        return (
          <div className="space-y-4">
            {renderMetrics()}
            {renderCurrentItem()}
            {renderProgressBar()}
          </div>
        )
      case 'errors':
        return renderErrorsView()
      case 'logs':
        return renderLogsView()
      case 'analytics':
        return renderAnalyticsView()
      default:
        return (
          <div className="space-y-4">
            {renderProgressBar()}
            {renderCurrentItem()}
          </div>
        )
    }
  }

  // Compact view
  if (compact && !isExpanded) {
    return (
      <div className={cn('bg-white border rounded-lg p-4', className)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={cn('p-2 rounded-lg', getStatusColor(operation.status))}>
              {getOperationIcon(operation.type)}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">
                {operation.type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </p>
              <p className="text-xs text-gray-600">
                {operation.progress.percentage?.toFixed(1)}% " {formatNumber(operation.progress.processed)} items
              </p>
            </div>
          </div>
          
          <button
            onClick={() => setIsExpanded(true)}
            className="p-1.5 text-gray-600 hover:bg-gray-100 rounded-md"
          >
            <ChevronDown className="w-4 h-4" />
          </button>
        </div>
      </div>
    )
  }

  // Full view
  return (
    <div className={cn('bg-white border rounded-lg shadow-sm', className)}>
      {/* Header */}
      <div className="px-6 py-4 border-b">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <div className={cn('p-2 rounded-lg', getStatusColor(operation.status))}>
              {getOperationIcon(operation.type)}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {operation.type.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Started {new Date(operation.startedAt || operation.createdAt).toLocaleString()}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {renderControls()}
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="px-6 py-2 border-b bg-gray-50">
        <div className="flex items-center gap-1">
          {[
            { value: 'overview', label: 'Overview', icon: BarChart3 },
            { value: 'detailed', label: 'Detailed', icon: Activity },
            { value: 'errors', label: `Errors (${operation.errors.length})`, icon: AlertCircle },
            { value: 'logs', label: 'Logs', icon: FileText },
            { value: 'analytics', label: 'Analytics', icon: TrendingUp }
          ].map(tab => (
            <button
              key={tab.value}
              onClick={() => setViewMode(tab.value as ViewMode)}
              className={cn(
                'inline-flex items-center gap-2 px-3 py-1.5 text-sm font-medium rounded-md transition-colors',
                viewMode === tab.value
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              )}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {renderContent()}
      </div>

      {/* Footer */}
      {operation.status === 'completed' && (
        <div className="px-6 py-4 border-t bg-gray-50">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Operation completed in {formatTime((operation.completedAt!.getTime() - startTime.current.getTime()) / 1000)}
            </p>
            <div className="flex items-center gap-2">
              <button
                onClick={() => window.print()}
                className="px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100 rounded-md"
              >
                <FileText className="w-4 h-4 inline mr-1" />
                Print Report
              </button>
              <button
                onClick={() => navigator.share({ title: 'Operation Report', text: `Operation completed: ${operation.type}` })}
                className="px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100 rounded-md"
              >
                <Share2 className="w-4 h-4 inline mr-1" />
                Share
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}