import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useNavigate } from '@tanstack/react-router';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Tabs, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '../ui/Tabs';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { Toggle } from '../ui/Toggle';
import { Progress } from '../ui/Progress';
import { Dialog } from '../ui/Dialog';
import { Tooltip } from '../ui/Tooltip';
import { Toast } from '../ui/Toast';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Brain,
  TrendingUp,
  Users,
  Mail,
  Filter,
  Settings,
  Download,
  Upload,
  BarChart3,
  Eye,
  Ban,
  Plus,
  Search,
  RefreshCw,
  Info,
  Clock,
  Globe,
  Building,
  Zap,
  Database,
  Link,
  Image,
  FileText,
  AlertCircle,
  ChevronRight,
  ChevronDown,
  Star
} from 'lucide-react';
import type { 
  ColdEmailDetection, 
  ColdEmailSettings,
  BlockedSender,
  WhitelistedSender,
  ColdEmailAnalytics,
  SenderAnalysis,
  ContentAnalysis,
  CustomFilter,
  TimeBasedFilter,
  GeographicFilter,
  IndustryFilter,
  ColdEmailExport,
  TrainingData
} from '../../types/cold-email';

// AI Detection Engine Component
const AIDetectionEngine: React.FC<{ 
  detection: ColdEmailDetection;
  onReview: (decision: 'block' | 'allow' | 'whitelist', feedback?: any) => void;
}> = ({ detection, onReview }) => {
  const [expanded, setExpanded] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-red-600';
    if (confidence >= 60) return 'text-orange-600';
    if (confidence >= 40) return 'text-yellow-600';
    return 'text-gray-600';
  };

  const getReputationBadge = (level: string) => {
    const badges = {
      trusted: { color: 'green', icon: CheckCircle },
      neutral: { color: 'gray', icon: Info },
      suspicious: { color: 'yellow', icon: AlertTriangle },
      malicious: { color: 'red', icon: XCircle }
    };
    const badge = badges[level as keyof typeof badges] || badges.neutral;
    const Icon = badge.icon;
    
    return (
      <Badge variant={badge.color as any}>
        <Icon className="w-3 h-3 mr-1" />
        {level}
      </Badge>
    );
  };

  return (
    <Card className="border-l-4 border-orange-500">
      <div className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <Shield className="w-5 h-5 text-orange-600" />
              <h4 className="font-medium">{detection.email.from}</h4>
              <span className={`text-sm font-medium ${getConfidenceColor(detection.confidence)}`}>
                {detection.confidence}% confidence
              </span>
              {getReputationBadge(detection.senderAnalysis.reputation.level)}
            </div>
            
            <p className="text-sm text-gray-600 mb-2">{detection.email.subject}</p>
            <p className="text-xs text-gray-500 line-clamp-2">{detection.email.snippet}</p>
            
            <div className="flex items-center gap-4 mt-3">
              <Button
                size="sm"
                variant="ghost"
                onClick={() => setExpanded(!expanded)}
              >
                {expanded ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                Detection Details
              </Button>
              
              <div className="flex gap-2 ml-auto">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onReview('allow')}
                >
                  <CheckCircle className="w-4 h-4 mr-1" />
                  Allow
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => onReview('block')}
                >
                  <Ban className="w-4 h-4 mr-1" />
                  Block
                </Button>
                <Button
                  size="sm"
                  variant="default"
                  onClick={() => onReview('whitelist')}
                >
                  <Star className="w-4 h-4 mr-1" />
                  Whitelist
                </Button>
              </div>
            </div>
          </div>
        </div>
        
        {expanded && (
          <div className="mt-4 pt-4 border-t">
            <div className="grid grid-cols-2 gap-4">
              {/* Detection Methods */}
              <div>
                <h5 className="text-sm font-medium mb-2">Detection Methods</h5>
                <div className="space-y-1">
                  {detection.detection.method.map(method => (
                    <div key={method} className="text-xs text-gray-600">
                      " {method.replace(/_/g, ' ')}
                    </div>
                  ))}
                </div>
              </div>
              
              {/* ML Analysis */}
              <div>
                <h5 className="text-sm font-medium mb-2">ML Analysis</h5>
                <div className="space-y-1">
                  <div className="text-xs">
                    <span className="text-gray-600">Cold Email: </span>
                    <span className="font-medium">
                      {detection.contentAnalysis.mlAnalysis.coldEmailProbability}%
                    </span>
                  </div>
                  <div className="text-xs">
                    <span className="text-gray-600">Spam: </span>
                    <span className="font-medium">
                      {detection.contentAnalysis.mlAnalysis.spamProbability}%
                    </span>
                  </div>
                  <div className="text-xs">
                    <span className="text-gray-600">Phishing: </span>
                    <span className="font-medium">
                      {detection.contentAnalysis.mlAnalysis.phishingProbability}%
                    </span>
                  </div>
                </div>
              </div>
              
              {/* Sender Info */}
              <div>
                <h5 className="text-sm font-medium mb-2">Sender Analysis</h5>
                <div className="space-y-1 text-xs text-gray-600">
                  <div>Domain Age: {detection.senderAnalysis.domainInfo.age} days</div>
                  <div>Total Emails: {detection.senderAnalysis.statistics.totalEmails}</div>
                  <div>Engagement Rate: {detection.senderAnalysis.statistics.engagementRate}%</div>
                  <div>Mass Mailer: {detection.senderAnalysis.patterns.massMailer ? 'Yes' : 'No'}</div>
                </div>
              </div>
              
              {/* Content Patterns */}
              <div>
                <h5 className="text-sm font-medium mb-2">Content Patterns</h5>
                <div className="space-y-1 text-xs">
                  {Object.entries(detection.contentAnalysis.patterns).map(([key, value]) => (
                    <div key={key} className="flex items-center gap-2">
                      <span className="text-gray-600">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                      <Badge variant={value ? 'destructive' : 'success'}>
                        {value ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-gray-50 rounded-lg">
              <p className="text-xs text-gray-600">{detection.detection.explanation}</p>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

// Review Queue Component
const ReviewQueue: React.FC<{
  detections: ColdEmailDetection[];
  onReview: (id: string, decision: 'block' | 'allow' | 'whitelist', feedback?: any) => void;
  onBulkReview: (ids: string[], decision: 'block' | 'allow') => void;
}> = ({ detections, onReview, onBulkReview }) => {
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [filter, setFilter] = useState<'all' | 'high' | 'medium' | 'low'>('all');
  
  const filteredDetections = useMemo(() => {
    if (filter === 'all') return detections;
    
    const thresholds = {
      high: 80,
      medium: 60,
      low: 40
    };
    
    const min = thresholds[filter as keyof typeof thresholds];
    const max = filter === 'high' ? 100 : thresholds[filter as keyof typeof thresholds] + 20;
    
    return detections.filter(d => d.confidence >= min && d.confidence < max);
  }, [detections, filter]);
  
  const handleSelectAll = () => {
    if (selectedIds.length === filteredDetections.length) {
      setSelectedIds([]);
    } else {
      setSelectedIds(filteredDetections.map(d => d.id));
    }
  };
  
  const handleSelect = (id: string) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(i => i !== id)
        : [...prev, id]
    );
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="text-lg font-semibold">Review Queue</h3>
          <Badge variant="warning">
            {detections.length} pending
          </Badge>
        </div>
        
        <div className="flex items-center gap-3">
          <Select value={filter} onValueChange={(value: string) => setFilter(value as any)} className="w-32">
            <Select.Item value="all">All</Select.Item>
            <Select.Item value="high">High (80%+)</Select.Item>
            <Select.Item value="medium">Medium (60-80%)</Select.Item>
            <Select.Item value="low">Low (40-60%)</Select.Item>
          </Select>
          
          {selectedIds.length > 0 && (
            <>
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  onBulkReview(selectedIds, 'allow');
                  setSelectedIds([]);
                }}
              >
                Allow {selectedIds.length}
              </Button>
              <Button
                size="sm"
                variant="destructive"
                onClick={() => {
                  onBulkReview(selectedIds, 'block');
                  setSelectedIds([]);
                }}
              >
                Block {selectedIds.length}
              </Button>
            </>
          )}
        </div>
      </div>
      
      {filteredDetections.length > 0 && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <input
            type="checkbox"
            checked={selectedIds.length === filteredDetections.length}
            onChange={handleSelectAll}
            className="rounded"
          />
          <span>Select all {filteredDetections.length} items</span>
        </div>
      )}
      
      <div className="space-y-3">
        {filteredDetections.map(detection => (
          <div key={detection.id} className="relative">
            <input
              type="checkbox"
              checked={selectedIds.includes(detection.id)}
              onChange={() => handleSelect(detection.id)}
              className="absolute left-4 top-8 rounded"
            />
            <div className="pl-10">
              <AIDetectionEngine
                detection={detection}
                onReview={(decision, feedback) => onReview(detection.id, decision, feedback)}
              />
            </div>
          </div>
        ))}
      </div>
      
      {filteredDetections.length === 0 && (
        <Card className="p-8 text-center">
          <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">All caught up!</h3>
          <p className="text-gray-600">No cold emails pending review.</p>
        </Card>
      )}
    </div>
  );
};

// Sender Management Component
const SenderManagement: React.FC<{
  blockedSenders: BlockedSender[];
  whitelistedSenders: WhitelistedSender[];
  onBlock: (sender: Partial<BlockedSender>) => void;
  onWhitelist: (sender: Partial<WhitelistedSender>) => void;
  onRemove: (id: string, type: 'blocked' | 'whitelisted') => void;
  onImport: (file: File, type: 'blocked' | 'whitelisted') => void;
  onExport: (type: 'blocked' | 'whitelisted') => void;
}> = ({ 
  blockedSenders, 
  whitelistedSenders, 
  onBlock, 
  onWhitelist, 
  onRemove,
  onImport,
  onExport 
}) => {
  const [activeTab, setActiveTab] = useState<'blocked' | 'whitelisted'>('blocked');
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [addType, setAddType] = useState<'email' | 'domain' | 'pattern'>('email');
  const [addValue, setAddValue] = useState('');
  const [addReason, setAddReason] = useState('');
  
  const filteredSenders = useMemo(() => {
    const senders = activeTab === 'blocked' ? blockedSenders : whitelistedSenders;
    if (!searchQuery) return senders;
    
    return senders.filter(sender => 
      sender.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sender.domain?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      sender.pattern?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [activeTab, blockedSenders, whitelistedSenders, searchQuery]);
  
  const handleAdd = () => {
    const sender = {
      type: addType,
      [addType]: addValue,
      reason: addReason,
    };
    
    if (activeTab === 'blocked') {
      onBlock(sender);
    } else {
      onWhitelist(sender);
    }
    
    setShowAddDialog(false);
    setAddValue('');
    setAddReason('');
  };
  
  const handleFileImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      onImport(file, activeTab);
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Tabs value={activeTab} onValueChange={(value: string) => setActiveTab(value as any)}>
          <TabsList>
            <TabsTrigger value="blocked">
              Blocked Senders
              <Badge variant="outline" className="ml-2">{blockedSenders.length}</Badge>
            </TabsTrigger>
            <TabsTrigger value="whitelisted">
              Whitelisted Senders
              <Badge variant="outline" className="ml-2">{whitelistedSenders.length}</Badge>
            </TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="flex items-center gap-3">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="search"
              placeholder="Search senders..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64 pl-10"
            />
          </div>
          
          <input
            type="file"
            accept=".csv,.json"
            onChange={handleFileImport}
            className="hidden"
            id="import-file"
          />
          <Button
            size="sm"
            variant="outline"
            onClick={() => document.getElementById('import-file')?.click()}
          >
            <Upload className="w-4 h-4 mr-1" />
            Import
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={() => onExport(activeTab)}
          >
            <Download className="w-4 h-4 mr-1" />
            Export
          </Button>
          
          <Button
            size="sm"
            onClick={() => setShowAddDialog(true)}
          >
            <Plus className="w-4 h-4 mr-1" />
            Add {activeTab === 'blocked' ? 'Blocked' : 'Whitelisted'}
          </Button>
        </div>
      </div>
      
      <div className="grid gap-3">
        {filteredSenders.map((sender: any) => (
          <Card key={sender.id} className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-1">
                  {sender.type === 'email' && <Mail className="w-4 h-4 text-gray-500" />}
                  {sender.type === 'domain' && <Globe className="w-4 h-4 text-gray-500" />}
                  {sender.type === 'pattern' && <Filter className="w-4 h-4 text-gray-500" />}
                  
                  <span className="font-medium">
                    {sender.email || sender.domain || sender.pattern}
                  </span>
                  
                  <Badge variant="secondary">
                    {sender.type}
                  </Badge>
                  
                  {sender.vipSettings && (
                    <Badge variant="warning">
                      <Star className="w-3 h-3 mr-1" />
                      VIP
                    </Badge>
                  )}
                </div>
                
                <p className="text-sm text-gray-600 mb-2">{sender.reason}</p>
                
                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <span>
                    {activeTab === 'blocked' ? 'Blocked' : 'Whitelisted'} on{' '}
                    {new Date(sender[activeTab === 'blocked' ? 'blockedAt' : 'whitelistedAt']).toLocaleDateString()}
                  </span>
                  {sender.statistics && (
                    <span>
                      {sender.statistics[activeTab === 'blocked' ? 'emailsBlocked' : 'emailsAllowed']} emails
                    </span>
                  )}
                  {sender.expiry?.type === 'temporary' && (
                    <Badge variant="warning">
                      <Clock className="w-3 h-3 mr-1" />
                      Expires {new Date(sender.expiry.expiresAt!).toLocaleDateString()}
                    </Badge>
                  )}
                </div>
              </div>
              
              <Button
                size="sm"
                variant="ghost"
                onClick={() => onRemove(sender.id, activeTab)}
                className="text-red-600 hover:text-red-700"
              >
                <XCircle className="w-4 h-4" />
              </Button>
            </div>
          </Card>
        ))}
      </div>
      
      {filteredSenders.length === 0 && (
        <Card className="p-8 text-center">
          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium mb-2">
            No {activeTab === 'blocked' ? 'blocked' : 'whitelisted'} senders
          </h3>
          <p className="text-gray-600">
            {searchQuery 
              ? 'No senders match your search criteria.'
              : `Add ${activeTab === 'blocked' ? 'blocked' : 'whitelisted'} senders to manage them here.`
            }
          </p>
        </Card>
      )}
      
      <Dialog
        open={showAddDialog}
        onClose={() => setShowAddDialog(false)}
        title={`Add ${activeTab === 'blocked' ? 'Blocked' : 'Whitelisted'} Sender`}
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
            <Select value={addType} onValueChange={(value: string) => setAddType(value as any)}>
              <Select.Item value="email">Email Address</Select.Item>
              <Select.Item value="domain">Domain</Select.Item>
              <Select.Item value="pattern">Pattern</Select.Item>
            </Select>
          </div>
          
          <Input
            label={
              addType === 'email' ? 'Email Address' :
              addType === 'domain' ? 'Domain' :
              'Pattern (regex)'
            }
            value={addValue}
            onChange={(e) => setAddValue(e.target.value)}
            placeholder={
              addType === 'email' ? '<EMAIL>' :
              addType === 'domain' ? 'company.com' :
              '.*@company\\.com'
            }
          />
          
          <Input
            label="Reason"
            value={addReason}
            onChange={(e) => setAddReason(e.target.value)}
            placeholder={`Why are you ${activeTab === 'blocked' ? 'blocking' : 'whitelisting'} this sender?`}
          />
          
          <div className="flex justify-end gap-3">
            <Button variant="outline" onClick={() => setShowAddDialog(false)}>
              Cancel
            </Button>
            <Button onClick={handleAdd} disabled={!addValue || !addReason}>
              Add {activeTab === 'blocked' ? 'Blocked' : 'Whitelisted'} Sender
            </Button>
          </div>
        </div>
      </Dialog>
    </div>
  );
};

// Analytics Dashboard Component
const AnalyticsDashboard: React.FC<{ analytics: ColdEmailAnalytics }> = ({ analytics }) => {
  const [timeRange, setTimeRange] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  
  const effectivenessMetrics = [
    {
      label: 'Detection Rate',
      value: `${analytics.effectiveness.detectionRate}%`,
      icon: Shield,
      color: 'text-blue-600',
      trend: '+5%'
    },
    {
      label: 'Accuracy',
      value: `${analytics.detection.accuracy}%`,
      icon: Brain,
      color: 'text-purple-600',
      trend: '+2%'
    },
    {
      label: 'Time Saved',
      value: `${analytics.effectiveness.timesSaved}h`,
      icon: Clock,
      color: 'text-green-600',
      trend: '+12%'
    },
    {
      label: 'Productivity Gain',
      value: `${analytics.effectiveness.productivityGain}%`,
      icon: TrendingUp,
      color: 'text-orange-600',
      trend: '+8%'
    }
  ];
  
  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-4 gap-4">
        {effectivenessMetrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index} className="p-4">
              <div className="flex items-start justify-between">
                <div>
                  <p className="text-sm text-gray-600 mb-1">{metric.label}</p>
                  <p className="text-2xl font-bold">{metric.value}</p>
                  <p className="text-xs text-green-600 mt-1">{metric.trend}</p>
                </div>
                <Icon className={`w-8 h-8 ${metric.color} opacity-80`} />
              </div>
            </Card>
          );
        })}
      </div>
      
      {/* Detection Overview */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Detection Overview</h3>
          <Select value={timeRange} onValueChange={(value: string) => setTimeRange(value as any)} className="w-32">
            <Select.Item value="daily">Daily</Select.Item>
            <Select.Item value="weekly">Weekly</Select.Item>
            <Select.Item value="monthly">Monthly</Select.Item>
          </Select>
        </div>
        
        <div className="grid grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium mb-3">Detection Stats</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Total Detected</span>
                <span className="font-medium">{analytics.detection.total}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Blocked</span>
                <span className="font-medium text-red-600">{analytics.detection.blocked}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Allowed</span>
                <span className="font-medium text-green-600">{analytics.detection.allowed}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Reviewed</span>
                <span className="font-medium text-blue-600">{analytics.detection.reviewed}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">False Positives</span>
                <span className="font-medium text-orange-600">{analytics.detection.falsePositives}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="text-sm font-medium mb-3">Categories</h4>
            <div className="space-y-3">
              {Object.entries(analytics.categories).map(([category, count]) => (
                <div key={category} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">{category}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(count / analytics.detection.total) * 100}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium w-12 text-right">{count}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>
      
      {/* ML Performance */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">ML Model Performance</h3>
        
        <div className="grid grid-cols-4 gap-4 mb-6">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">
              {analytics.mlPerformance.accuracy}%
            </p>
            <p className="text-sm text-gray-600">Accuracy</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">
              {analytics.mlPerformance.precision}%
            </p>
            <p className="text-sm text-gray-600">Precision</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600">
              {analytics.mlPerformance.recall}%
            </p>
            <p className="text-sm text-gray-600">Recall</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">
              {analytics.mlPerformance.f1Score}%
            </p>
            <p className="text-sm text-gray-600">F1 Score</p>
          </div>
        </div>
        
        <div>
          <h4 className="text-sm font-medium mb-3">Recent Improvements</h4>
          <div className="space-y-2">
            {analytics.mlPerformance.improvements.map((improvement, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Badge variant="secondary">{improvement.version}</Badge>
                  <span className="text-sm">
                    {new Date(improvement.date).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">
                    {improvement.samplesUsed} samples
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      {improvement.accuracyBefore}%
                    </span>
                    <ChevronRight className="w-3 h-3 text-gray-400" />
                    <span className="text-sm font-medium text-green-600">
                      {improvement.accuracyAfter}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
      
      {/* Top Blocked Senders */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold mb-4">Top Blocked Senders</h3>
        <div className="space-y-3">
          {analytics.senders.topBlocked.slice(0, 5).map((sender, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium">{index + 1}.</span>
                <div>
                  <p className="text-sm font-medium">{sender.sender}</p>
                  <p className="text-xs text-gray-500">{sender.domain}</p>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <Badge variant="destructive">{sender.count} blocked</Badge>
                <Badge variant={sender.reputation < 30 ? 'destructive' : 'warning'}>
                  {sender.reputation} rep
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

// Settings Component
const SettingsPanel: React.FC<{
  settings: ColdEmailSettings;
  onUpdate: (settings: Partial<ColdEmailSettings>) => void;
}> = ({ settings, onUpdate }) => {
  const [activeSection, setActiveSection] = useState<'general' | 'detection' | 'filters' | 'integration'>('general');
  
  return (
    <div className="grid grid-cols-4 gap-6">
      <div className="col-span-1">
        <nav className="space-y-1">
          <button
            onClick={() => setActiveSection('general')}
            className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeSection === 'general' 
                ? 'bg-blue-50 text-blue-700' 
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            General Settings
          </button>
          <button
            onClick={() => setActiveSection('detection')}
            className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeSection === 'detection' 
                ? 'bg-blue-50 text-blue-700' 
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            Detection Engine
          </button>
          <button
            onClick={() => setActiveSection('filters')}
            className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeSection === 'filters' 
                ? 'bg-blue-50 text-blue-700' 
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            Filters & Rules
          </button>
          <button
            onClick={() => setActiveSection('integration')}
            className={`w-full text-left px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              activeSection === 'integration' 
                ? 'bg-blue-50 text-blue-700' 
                : 'text-gray-700 hover:bg-gray-50'
            }`}
          >
            Integrations
          </button>
        </nav>
      </div>
      
      <div className="col-span-3">
        {activeSection === 'general' && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-6">General Settings</h3>
            
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Cold Email Blocker</h4>
                  <p className="text-sm text-gray-600">Enable AI-powered cold email detection</p>
                </div>
                <Toggle
                  checked={settings.enabled}
                  onChange={(checked) => onUpdate({ enabled: checked })}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Sensitivity Level</label>
                <Select
                  value={settings.sensitivity}
                  onValueChange={(value: string) => onUpdate({ sensitivity: value as any })}
                  className="w-full"
                >
                  <Select.Item value="conservative">Conservative (fewer false positives)</Select.Item>
                  <Select.Item value="balanced">Balanced (recommended)</Select.Item>
                  <Select.Item value="aggressive">Aggressive (catch more cold emails)</Select.Item>
                </Select>
                <p className="text-xs text-gray-500 mt-1">
                  {settings.sensitivity === 'conservative' && 'Only blocks high-confidence cold emails'}
                  {settings.sensitivity === 'balanced' && 'Balanced detection with manual review'}
                  {settings.sensitivity === 'aggressive' && 'Blocks most suspected cold emails'}
                </p>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-medium">Actions</h4>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Auto-block high confidence</p>
                    <p className="text-xs text-gray-600">Automatically block emails above threshold</p>
                  </div>
                  <Toggle
                    checked={settings.actions.autoBlock}
                    onChange={(checked) => onUpdate({ 
                      actions: { ...settings.actions, autoBlock: checked }
                    })}
                  />
                </div>
                
                {settings.actions.autoBlock && (
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Auto-block threshold: {settings.actions.autoBlockThreshold}%
                    </label>
                    <input
                      type="range"
                      min="60"
                      max="100"
                      value={settings.actions.autoBlockThreshold}
                      onChange={(e) => onUpdate({
                        actions: { 
                          ...settings.actions, 
                          autoBlockThreshold: parseInt(e.target.value)
                        }
                      })}
                      className="w-full"
                    />
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Move to spam</p>
                    <p className="text-xs text-gray-600">Move blocked emails to spam folder</p>
                  </div>
                  <Toggle
                    checked={settings.actions.moveToSpam}
                    onChange={(checked) => onUpdate({ 
                      actions: { ...settings.actions, moveToSpam: checked }
                    })}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">Require manual review</p>
                    <p className="text-xs text-gray-600">Review all detections before blocking</p>
                  </div>
                  <Toggle
                    checked={settings.actions.requireReview}
                    onChange={(checked) => onUpdate({ 
                      actions: { ...settings.actions, requireReview: checked }
                    })}
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Delete blocked emails after
                  </label>
                  <Select
                    value={settings.actions.deleteAfterDays?.toString() || '0'}
                    onValueChange={(value: string) => onUpdate({
                      actions: { 
                        ...settings.actions, 
                        deleteAfterDays: parseInt(value) || undefined
                      }
                    })}
                  >
                    <Select.Item value="0">Never</Select.Item>
                    <Select.Item value="7">7 days</Select.Item>
                    <Select.Item value="14">14 days</Select.Item>
                    <Select.Item value="30">30 days</Select.Item>
                    <Select.Item value="90">90 days</Select.Item>
                  </Select>
                </div>
              </div>
            </div>
          </Card>
        )}
        
        {activeSection === 'detection' && (
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-6">Detection Engine Settings</h3>
            
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Machine Learning</h4>
                  <p className="text-sm text-gray-600">Use ML models for detection</p>
                </div>
                <Toggle
                  checked={settings.detection.mlEnabled}
                  onChange={(checked) => onUpdate({ 
                    detection: { ...settings.detection, mlEnabled: checked }
                  })}
                />
              </div>
              
              {settings.detection.mlEnabled && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    ML confidence threshold: {settings.detection.mlThreshold}%
                  </label>
                  <input
                    type="range"
                    min="30"
                    max="90"
                    value={settings.detection.mlThreshold}
                    onChange={(e) => onUpdate({
                      detection: { 
                        ...settings.detection, 
                        mlThreshold: parseInt(e.target.value)
                      }
                    })}
                    className="w-full"
                  />
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Sender Reputation</h4>
                  <p className="text-sm text-gray-600">Analyze sender reputation and history</p>
                </div>
                <Toggle
                  checked={settings.detection.reputationEnabled}
                  onChange={(checked) => onUpdate({ 
                    detection: { ...settings.detection, reputationEnabled: checked }
                  })}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Content Analysis</h4>
                  <p className="text-sm text-gray-600">Analyze email content and patterns</p>
                </div>
                <Toggle
                  checked={settings.detection.contentAnalysisEnabled}
                  onChange={(checked) => onUpdate({ 
                    detection: { ...settings.detection, contentAnalysisEnabled: checked }
                  })}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Behavioral Analysis</h4>
                  <p className="text-sm text-gray-600">Detect mass mailing patterns</p>
                </div>
                <Toggle
                  checked={settings.detection.behavioralAnalysisEnabled}
                  onChange={(checked) => onUpdate({ 
                    detection: { ...settings.detection, behavioralAnalysisEnabled: checked }
                  })}
                />
              </div>
              
              <div className="border-t pt-6">
                <h4 className="font-medium mb-4">Learning Settings</h4>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Enable learning</p>
                      <p className="text-xs text-gray-600">Improve detection from user feedback</p>
                    </div>
                    <Toggle
                      checked={settings.learning.enableLearning}
                      onChange={(checked) => onUpdate({ 
                        learning: { ...settings.learning, enableLearning: checked }
                      })}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Auto-update model</p>
                      <p className="text-xs text-gray-600">Automatically retrain with new data</p>
                    </div>
                    <Toggle
                      checked={settings.learning.autoUpdateModel}
                      onChange={(checked) => onUpdate({ 
                        learning: { ...settings.learning, autoUpdateModel: checked }
                      })}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium">Share anonymous data</p>
                      <p className="text-xs text-gray-600">Help improve detection for all users</p>
                    </div>
                    <Toggle
                      checked={settings.learning.shareAnonymousData}
                      onChange={(checked) => onUpdate({ 
                        learning: { ...settings.learning, shareAnonymousData: checked }
                      })}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

// Main Cold Email Blocker Component
export const ColdEmailBlocker: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'review' | 'senders' | 'analytics' | 'settings'>('review');
  const [loading, setLoading] = useState(false);
  const [detections, setDetections] = useState<ColdEmailDetection[]>([]);
  const [blockedSenders, setBlockedSenders] = useState<BlockedSender[]>([]);
  const [whitelistedSenders, setWhitelistedSenders] = useState<WhitelistedSender[]>([]);
  const [settings, setSettings] = useState<ColdEmailSettings>({
    enabled: true,
    sensitivity: 'balanced',
    detection: {
      mlEnabled: true,
      mlThreshold: 60,
      reputationEnabled: true,
      reputationThreshold: 30,
      contentAnalysisEnabled: true,
      behavioralAnalysisEnabled: true,
      customRulesEnabled: true
    },
    actions: {
      autoBlock: false,
      autoBlockThreshold: 80,
      moveToSpam: true,
      deleteAfterDays: 30,
      notifyOnDetection: true,
      requireReview: true
    },
    filters: {
      timeBasedFiltering: [],
      geographicFiltering: [],
      industryFilters: [],
      customFilters: []
    },
    learning: {
      enableLearning: true,
      requireMinSamples: 100,
      autoUpdateModel: true,
      shareAnonymousData: false
    },
    integration: {
      webhooksEnabled: false,
      apiAccess: true
    }
  });
  const [analytics, setAnalytics] = useState<ColdEmailAnalytics | null>(null);
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  
  // Load initial data
  useEffect(() => {
    loadData();
  }, []);
  
  const loadData = async () => {
    setLoading(true);
    try {
      // Simulate API calls
      const [detectionsRes, blocklistRes, whitelistRes, analyticsRes] = await Promise.all([
        fetch('/api/cold-email/detections'),
        fetch('/api/cold-email/blocklist'),
        fetch('/api/cold-email/whitelist'),
        fetch('/api/cold-email/analytics')
      ]);
      
      const [detectionsData, blocklistData, whitelistData, analyticsData] = await Promise.all([
        detectionsRes.json(),
        blocklistRes.json(),
        whitelistRes.json(),
        analyticsRes.json()
      ]);
      
      setDetections(detectionsData.detections);
      setBlockedSenders(blocklistData.senders);
      setWhitelistedSenders(whitelistData.senders);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Failed to load data:', error);
      setToast({ message: 'Failed to load cold email data', type: 'error' });
    } finally {
      setLoading(false);
    }
  };
  
  const handleReview = async (id: string, decision: 'block' | 'allow' | 'whitelist', feedback?: any) => {
    try {
      const response = await fetch(`/api/cold-email/detections/${id}/review`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ decision, feedback })
      });
      
      if (response.ok) {
        setDetections(prev => prev.filter(d => d.id !== id));
        setToast({ 
          message: `Email ${decision === 'block' ? 'blocked' : decision === 'allow' ? 'allowed' : 'whitelisted'} successfully`, 
          type: 'success' 
        });
        
        // Update analytics
        if (analytics) {
          setAnalytics({
            ...analytics,
            detection: {
              ...analytics.detection,
              reviewed: analytics.detection.reviewed + 1,
              [decision === 'block' ? 'blocked' : 'allowed']: 
                analytics.detection[decision === 'block' ? 'blocked' : 'allowed'] + 1
            }
          });
        }
      }
    } catch (error) {
      console.error('Failed to review detection:', error);
      setToast({ message: 'Failed to process review', type: 'error' });
    }
  };
  
  const handleBulkReview = async (ids: string[], decision: 'block' | 'allow') => {
    try {
      const response = await fetch('/api/cold-email/detections/bulk-review', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids, decision })
      });
      
      if (response.ok) {
        setDetections(prev => prev.filter(d => !ids.includes(d.id)));
        setToast({ 
          message: `${ids.length} emails ${decision === 'block' ? 'blocked' : 'allowed'} successfully`, 
          type: 'success' 
        });
      }
    } catch (error) {
      console.error('Failed to bulk review:', error);
      setToast({ message: 'Failed to process bulk review', type: 'error' });
    }
  };
  
  const handleBlockSender = async (sender: Partial<BlockedSender>) => {
    try {
      const response = await fetch('/api/cold-email/blocklist', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sender)
      });
      
      if (response.ok) {
        const newSender = await response.json();
        setBlockedSenders(prev => [...prev, newSender]);
        setToast({ message: 'Sender blocked successfully', type: 'success' });
      }
    } catch (error) {
      console.error('Failed to block sender:', error);
      setToast({ message: 'Failed to block sender', type: 'error' });
    }
  };
  
  const handleWhitelistSender = async (sender: Partial<WhitelistedSender>) => {
    try {
      const response = await fetch('/api/cold-email/whitelist', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sender)
      });
      
      if (response.ok) {
        const newSender = await response.json();
        setWhitelistedSenders(prev => [...prev, newSender]);
        setToast({ message: 'Sender whitelisted successfully', type: 'success' });
      }
    } catch (error) {
      console.error('Failed to whitelist sender:', error);
      setToast({ message: 'Failed to whitelist sender', type: 'error' });
    }
  };
  
  const handleRemoveSender = async (id: string, type: 'blocked' | 'whitelisted') => {
    try {
      const endpoint = type === 'blocked' ? 'blocklist' : 'whitelist';
      const response = await fetch(`/api/cold-email/${endpoint}/${id}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        if (type === 'blocked') {
          setBlockedSenders(prev => prev.filter(s => s.id !== id));
        } else {
          setWhitelistedSenders(prev => prev.filter(s => s.id !== id));
        }
        setToast({ message: `Sender removed from ${type} list`, type: 'success' });
      }
    } catch (error) {
      console.error('Failed to remove sender:', error);
      setToast({ message: 'Failed to remove sender', type: 'error' });
    }
  };
  
  const handleImport = async (file: File, type: 'blocked' | 'whitelisted') => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    try {
      const response = await fetch('/api/cold-email/import', {
        method: 'POST',
        body: formData
      });
      
      if (response.ok) {
        await loadData();
        setToast({ message: `${type} list imported successfully`, type: 'success' });
      }
    } catch (error) {
      console.error('Failed to import:', error);
      setToast({ message: 'Failed to import file', type: 'error' });
    }
  };
  
  const handleExport = async (type: 'blocked' | 'whitelisted') => {
    try {
      const response = await fetch(`/api/cold-email/export?type=${type}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${type}-senders-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to export:', error);
      setToast({ message: 'Failed to export data', type: 'error' });
    }
  };
  
  const handleUpdateSettings = async (updates: Partial<ColdEmailSettings>) => {
    const newSettings = { ...settings, ...updates };
    setSettings(newSettings);
    
    try {
      const response = await fetch('/api/cold-email/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSettings)
      });
      
      if (response.ok) {
        setToast({ message: 'Settings updated successfully', type: 'success' });
      }
    } catch (error) {
      console.error('Failed to update settings:', error);
      setToast({ message: 'Failed to update settings', type: 'error' });
    }
  };
  
  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-3">
            <Shield className="w-8 h-8 text-blue-600" />
            Cold Email Blocker
          </h1>
          <p className="text-gray-600 mt-1">
            AI-powered protection against unwanted cold emails
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Badge variant={settings.enabled ? 'success' : 'secondary'}>
            {settings.enabled ? 'Active' : 'Inactive'}
          </Badge>
          <Button
            variant="outline"
            onClick={() => navigate({ to: '/mail' })}
          >
            Back to Inbox
          </Button>
        </div>
      </div>
      
      {/* Stats Overview */}
      {analytics && (
        <div className="grid grid-cols-4 gap-4 mb-8">
          <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-700">Emails Protected</p>
                <p className="text-2xl font-bold text-blue-900">{analytics.detection.total}</p>
              </div>
              <Shield className="w-8 h-8 text-blue-600" />
            </div>
          </Card>
          
          <Card className="p-4 bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-700">Blocked Today</p>
                <p className="text-2xl font-bold text-red-900">
                  {analytics.trends.daily[analytics.trends.daily.length - 1]?.blocked || 0}
                </p>
              </div>
              <Ban className="w-8 h-8 text-red-600" />
            </div>
          </Card>
          
          <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-700">Time Saved</p>
                <p className="text-2xl font-bold text-green-900">{analytics.effectiveness.timesSaved}h</p>
              </div>
              <Clock className="w-8 h-8 text-green-600" />
            </div>
          </Card>
          
          <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-700">ML Accuracy</p>
                <p className="text-2xl font-bold text-purple-900">{analytics.mlPerformance.accuracy}%</p>
              </div>
              <Brain className="w-8 h-8 text-purple-600" />
            </div>
          </Card>
        </div>
      )}
      
      {/* Main Content */}
      <Card>
        <Tabs
          value={activeTab}
          onValueChange={(value: string) => setActiveTab(value as any)}
        >
          <TabsList>
            <TabsTrigger value="review">
              <Eye className="w-4 h-4 mr-2" />
              Review Queue
              {detections.length > 0 && (
                <Badge variant="outline" className="ml-2">{detections.length}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="senders">
              <Users className="w-4 h-4 mr-2" />
              Sender Management
            </TabsTrigger>
            <TabsTrigger value="analytics">
              <BarChart3 className="w-4 h-4 mr-2" />
              Analytics
            </TabsTrigger>
            <TabsTrigger value="settings">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>
        </Tabs>
        
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
            </div>
          ) : (
            <>
              {activeTab === 'review' && (
                <ReviewQueue
                  detections={detections}
                  onReview={handleReview}
                  onBulkReview={handleBulkReview}
                />
              )}
              
              {activeTab === 'senders' && (
                <SenderManagement
                  blockedSenders={blockedSenders}
                  whitelistedSenders={whitelistedSenders}
                  onBlock={handleBlockSender}
                  onWhitelist={handleWhitelistSender}
                  onRemove={handleRemoveSender}
                  onImport={handleImport}
                  onExport={handleExport}
                />
              )}
              
              {activeTab === 'analytics' && analytics && (
                <AnalyticsDashboard analytics={analytics} />
              )}
              
              {activeTab === 'settings' && (
                <SettingsPanel
                  settings={settings}
                  onUpdate={handleUpdateSettings}
                />
              )}
            </>
          )}
        </div>
      </Card>
      
      {/* Toast Notification */}
      {toast && (
        <Toast
          description={toast.message}
          variant={toast.type === 'error' ? 'destructive' : 'success'}
          onClose={() => setToast(null)}
        />
      )}
    </div>
  );
};