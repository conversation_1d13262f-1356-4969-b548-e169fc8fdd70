# Clean Inbox Feature

## Overview

The Clean Inbox feature provides an intelligent, AI-powered solution for managing email clutter at scale. It analyzes your entire inbox, categorizes emails based on patterns and content, and suggests appropriate cleaning actions while maintaining full safety through backups and preview capabilities.

## Key Features

### 1. Smart Email Analysis
- **AI-Powered Categorization**: Automatically categorizes emails into types (marketing, notifications, newsletters, receipts, etc.)
- **Pattern Recognition**: Identifies email patterns based on sender, subject, age, and content
- **Confidence Scoring**: Each category includes a confidence score (0-100%) indicating AI certainty
- **Storage Analysis**: Calculates potential storage savings and organization benefits

### 2. Cleaning Workflows

#### Pre-defined Workflows:
- **Quick Clean**: Fast cleanup of obvious junk and old emails
  - Removes marketing emails older than 30 days
  - Archives old notifications (7+ days)
  - Typical completion: 2-5 minutes

- **Deep Clean**: Thorough analysis and cleanup
  - Full AI categorization of all emails
  - Review and clean each category individually
  - Typical completion: 10-20 minutes

- **Smart Clean**: AI-powered intelligent cleaning
  - Learns from your email patterns
  - Applies personalized cleaning rules
  - Adapts to your preferences over time

#### Custom Workflows:
- Create personalized cleaning workflows
- Save and reuse custom criteria
- Combine multiple rules and conditions

### 3. Interactive Review Interface

- **Preview Mode**: See exactly what will be cleaned before taking action
- **Category View**: Expandable groups showing emails by category
- **Individual Selection**: Option to review and select individual emails
- **Grid/List Views**: Switch between different visualization modes
- **Real-time Impact**: See storage and email count impacts instantly

### 4. Progressive Cleaning Process

- **Step-by-Step Guidance**: Clear progression through analysis, review, and cleaning
- **First-Time Onboarding**: Helpful explanations for new users
- **Customizable Criteria**: Adjust thresholds and rules to your needs
- **Save Preferences**: Your settings are remembered for future sessions

### 5. Safety & Recovery

- **Automatic Backups**: Full backup created before any cleaning operation
- **Preview Everything**: See exactly what will happen before confirming
- **Granular Undo**: Restore specific categories or actions
- **7-Day Recovery**: Backups retained for one week
- **Confirmation Checkpoints**: Additional confirmations for destructive actions

### 6. Analytics & Insights

- **Before/After Statistics**: Clear visualization of cleaning impact
- **Storage Metrics**: See exactly how much space you've freed
- **Growth Patterns**: Understand your email accumulation trends
- **Recommendations**: AI-generated suggestions for ongoing maintenance
- **Historical Reports**: Track cleaning effectiveness over time

## Usage

### Starting a Cleaning Session

1. **Choose a Workflow**: Select Quick, Deep, Smart, or Custom cleaning
2. **Review Analytics**: See your current inbox statistics
3. **Start Analysis**: Let AI categorize your emails
4. **Review Suggestions**: See what will be cleaned
5. **Execute Cleaning**: Confirm and start the cleaning process
6. **View Results**: See detailed results and recommendations

### Category Types

The AI identifies these email categories:
- **Marketing Emails**: Promotional content and advertisements
- **Old Notifications**: System alerts and social media notifications
- **Unread Newsletters**: Subscriptions you haven't opened
- **Old Receipts**: Purchase confirmations and invoices
- **Large Attachments**: Emails with files over 10MB
- **Duplicate Emails**: Multiple copies of the same message

### Settings

- **Auto Backup**: Automatically create backups (enabled by default)
- **Confirm Destructive**: Require confirmation for deletions
- **Show Preview**: Always preview before cleaning
- **Preserve Starred**: Never clean starred emails
- **Preserve Important**: Never clean emails marked as important
- **Batch Size**: Number of emails to process at once (10-500)

### Scheduling

Set up automatic cleaning:
- **Frequency**: Daily, Weekly, Bi-weekly, or Monthly
- **Time**: Choose when cleaning should run
- **Workflow**: Select which workflow to use
- **Notifications**: Get notified when cleaning completes

## API Endpoints

- `GET /api/cleaning/analytics` - Get inbox analytics
- `POST /api/cleaning/start` - Start a cleaning session
- `POST /api/cleaning/:sessionId/analyze` - Analyze emails
- `POST /api/cleaning/:sessionId/execute` - Execute cleaning
- `GET /api/cleaning/:sessionId/status` - Get cleaning progress
- `POST /api/cleaning/backup/:backupId/restore` - Restore from backup
- `GET/POST /api/cleaning/workflows` - Manage custom workflows

## Technical Implementation

### State Management
- Uses React hooks for local state management
- Session tracking for multi-step process
- Real-time progress updates via polling

### Performance
- Batch processing for large inboxes
- Configurable delays between batches
- Progressive loading for preview
- Optimized for 100k+ email inboxes

### Security
- All operations require authentication
- Backups encrypted at rest
- No email content exposed in logs
- Rate limiting on API endpoints

### Accessibility
- Full keyboard navigation support
- Screen reader announcements
- High contrast mode compatible
- Focus management throughout workflow

## Best Practices

1. **Start with Quick Clean** for first-time users
2. **Review high-confidence categories** first
3. **Use preview mode** to understand impact
4. **Schedule regular cleaning** to maintain inbox health
5. **Keep backups enabled** for safety
6. **Customize thresholds** based on your needs

## Troubleshooting

### Common Issues:
- **Analysis taking too long**: Large inboxes may take 5-10 minutes
- **Categories not accurate**: Adjust confidence thresholds
- **Missing emails**: Check if they match preservation rules
- **Can't restore backup**: Ensure within 7-day window

### Support
Contact support with your session ID for assistance with any cleaning operations.