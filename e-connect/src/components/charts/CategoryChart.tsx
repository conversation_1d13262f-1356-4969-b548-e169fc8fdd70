import { useMemo } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Toolt<PERSON>,
  Legend,
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LabelList,
} from 'recharts'
import { CategoryStats } from '@/types/analytics'
import { 
  ArrowUpIcon, 
  ArrowDownIcon, 
  MinusIcon 
} from '@heroicons/react/24/outline'

interface CategoryChartProps {
  data?: CategoryStats[]
  isDark?: boolean
  height?: number
  chartType?: 'pie' | 'donut' | 'bar'
  showTrends?: boolean
}

export function CategoryChart({ 
  data = [], 
  isDark = false, 
  height = 300,
  chartType = 'donut',
  showTrends = false
}: CategoryChartProps) {
  const chartData = useMemo(() => {
    return data.map(category => ({
      name: category.category,
      value: category.count,
      percentage: category.percentage,
      unread: category.unreadCount,
      trend: category.trend,
      trendPercentage: category.trendPercentage,
    }))
  }, [data])

  const colors = [
    isDark ? '#3b82f6' : '#2563eb', // blue
    isDark ? '#10b981' : '#059669', // emerald
    isDark ? '#f59e0b' : '#d97706', // amber
    isDark ? '#8b5cf6' : '#7c3aed', // violet
    isDark ? '#ef4444' : '#dc2626', // red
    isDark ? '#06b6d4' : '#0891b2', // cyan
    isDark ? '#84cc16' : '#65a30d', // lime
    isDark ? '#f97316' : '#ea580c', // orange
    isDark ? '#ec4899' : '#db2777', // pink
    isDark ? '#6b7280' : '#4b5563', // gray
  ]

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className={`p-3 rounded-lg shadow-lg ${
          isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } border`}>
          <p className="font-semibold">{data.name}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">Count: <span className="font-medium">{data.value.toLocaleString()}</span></p>
            <p className="text-sm">Percentage: <span className="font-medium">{data.percentage.toFixed(1)}%</span></p>
            <p className="text-sm">Unread: <span className="font-medium">{data.unread.toLocaleString()}</span></p>
            {showTrends && (
              <div className="flex items-center gap-1">
                <span className="text-sm">Trend:</span>
                <TrendIcon trend={data.trend} />
                <span className={`text-sm font-medium ${
                  data.trend === 'increasing' ? 'text-green-600' :
                  data.trend === 'decreasing' ? 'text-red-600' :
                  'text-gray-600'
                }`}>
                  {Math.abs(data.trendPercentage).toFixed(1)}%
                </span>
              </div>
            )}
          </div>
        </div>
      )
    }
    return null
  }

  const renderLabel = (entry: any) => {
    return `${entry.name} (${entry.percentage.toFixed(1)}%)`
  }

  if (chartType === 'bar') {
    return (
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={chartData}
          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        >
          <CartesianGrid 
            strokeDasharray="3 3" 
            stroke={isDark ? '#374151' : '#e5e7eb'} 
          />
          <XAxis 
            dataKey="name"
            stroke={isDark ? '#9ca3af' : '#6b7280'}
            tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis 
            stroke={isDark ? '#9ca3af' : '#6b7280'}
            tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" radius={[4, 4, 0, 0]}>
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
            <LabelList 
              dataKey="percentage" 
              position="top"
              formatter={(value: any) => `${value.toFixed(1)}%`}
              fill={isDark ? '#9ca3af' : '#6b7280'}
              style={{ fontSize: '12px' }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    )
  }

  const innerRadius = chartType === 'donut' ? 60 : 0

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={height}>
        <PieChart>
          <Pie
            data={chartData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={renderLabel}
            outerRadius={Math.min(height * 0.3, 100)}
            innerRadius={innerRadius}
            fill="#8884d8"
            dataKey="value"
          >
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
          </Pie>
          <Tooltip content={<CustomTooltip />} />
          <Legend 
            verticalAlign="bottom"
            height={36}
            iconType="circle"
            wrapperStyle={{ paddingTop: '20px' }}
          />
        </PieChart>
      </ResponsiveContainer>
      
      {showTrends && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium">Category Trends</h4>
          <div className="grid grid-cols-2 gap-2">
            {chartData.map((category, index) => (
              <div key={category.name} className="flex items-center justify-between text-sm">
                <span className="truncate">{category.name}</span>
                <div className="flex items-center gap-1">
                  <TrendIcon trend={category.trend} />
                  <span className={`font-medium ${
                    category.trend === 'increasing' ? 'text-green-600' :
                    category.trend === 'decreasing' ? 'text-red-600' :
                    'text-gray-600'
                  }`}>
                    {Math.abs(category.trendPercentage).toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

function TrendIcon({ trend }: { trend: 'increasing' | 'decreasing' | 'stable' }) {
  const iconClass = "h-3 w-3"
  
  switch (trend) {
    case 'increasing':
      return <ArrowUpIcon className={`${iconClass} text-green-600`} />
    case 'decreasing':
      return <ArrowDownIcon className={`${iconClass} text-red-600`} />
    case 'stable':
    default:
      return <MinusIcon className={`${iconClass} text-gray-600`} />
  }
}

// Export category comparison chart
export function CategoryComparisonChart({ 
  data = [], 
  isDark = false,
  height = 300 
}: Pick<CategoryChartProps, 'data' | 'isDark' | 'height'>) {
  const comparisonData = useMemo(() => {
    return data.map(category => ({
      category: category.category,
      total: category.count,
      unread: category.unreadCount,
      read: category.count - category.unreadCount,
    }))
  }, [data])

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={comparisonData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid 
          strokeDasharray="3 3" 
          stroke={isDark ? '#374151' : '#e5e7eb'} 
        />
        <XAxis 
          dataKey="category"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis 
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Legend />
        <Bar 
          dataKey="read" 
          stackId="a" 
          fill={isDark ? '#10b981' : '#059669'}
          name="Read"
          radius={[0, 0, 0, 0]}
        />
        <Bar 
          dataKey="unread" 
          stackId="a" 
          fill={isDark ? '#ef4444' : '#dc2626'}
          name="Unread"
          radius={[4, 4, 0, 0]}
        />
      </BarChart>
    </ResponsiveContainer>
  )
}

// Interactive category drill-down chart
export function InteractiveCategoryChart({ 
  data = [], 
  isDark = false,
  height = 300,
  onCategoryClick
}: Pick<CategoryChartProps, 'data' | 'isDark' | 'height'> & {
  onCategoryClick?: (category: string) => void
}) {
  const handleClick = (data: any) => {
    if (onCategoryClick) {
      onCategoryClick(data.name)
    }
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={data.map(cat => ({
          name: cat.category,
          value: cat.count,
          percentage: cat.percentage,
        }))}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
        onClick={handleClick}
      >
        <CartesianGrid 
          strokeDasharray="3 3" 
          stroke={isDark ? '#374151' : '#e5e7eb'} 
        />
        <XAxis 
          dataKey="name"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          tick={{ fill: isDark ? '#9ca3af' : '#6b7280', cursor: 'pointer' }}
          angle={-45}
          textAnchor="end"
          height={80}
        />
        <YAxis 
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
        />
        <Bar 
          dataKey="value" 
          radius={[4, 4, 0, 0]}
          cursor="pointer"
          onClick={handleClick}
        >
          {data.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={`hsl(${index * 60}, 70%, ${isDark ? '60%' : '50%'})`} 
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}