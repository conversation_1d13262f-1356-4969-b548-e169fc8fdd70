import { useMemo } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Cell,
  LabelList,
} from 'recharts'
import { SenderStats } from '@/types/analytics'

interface SenderChartProps {
  data?: SenderStats[]
  isDark?: boolean
  height?: number
  showGrid?: boolean
  maxSenders?: number
  orientation?: 'horizontal' | 'vertical'
}

export function SenderChart({ 
  data = [], 
  isDark = false, 
  height = 400,
  showGrid = true,
  maxSenders = 10,
  orientation = 'horizontal'
}: SenderChartProps) {
  const chartData = useMemo(() => {
    return data
      .slice(0, maxSenders)
      .map(sender => ({
        name: sender.name || sender.email.split('@')[0],
        email: sender.email,
        messages: sender.messageCount,
        threads: sender.threadCount,
        unread: sender.unreadCount,
        domain: sender.domain,
        importance: sender.importance,
      }))
      .reverse() // Reverse for horizontal bar chart
  }, [data, max<PERSON>enders])

  const colors = {
    high: isDark ? '#ef4444' : '#dc2626',
    medium: isDark ? '#f59e0b' : '#d97706',
    low: isDark ? '#6b7280' : '#4b5563',
  }

  const gridColor = isDark ? '#374151' : '#e5e7eb'
  const textColor = isDark ? '#9ca3af' : '#6b7280'

  const getBarColor = (importance: string) => {
    return colors[importance as keyof typeof colors] || colors.low
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className={`p-3 rounded-lg shadow-lg ${
          isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } border`}>
          <p className="font-semibold">{data.name}</p>
          <p className="text-sm text-muted-foreground">{data.email}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">Messages: <span className="font-medium">{data.messages}</span></p>
            <p className="text-sm">Threads: <span className="font-medium">{data.threads}</span></p>
            <p className="text-sm">Unread: <span className="font-medium">{data.unread}</span></p>
            <p className="text-sm">Domain: <span className="font-medium">{data.domain}</span></p>
          </div>
        </div>
      )
    }
    return null
  }

  if (orientation === 'horizontal') {
    return (
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={chartData}
          layout="horizontal"
          margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
        >
          {showGrid && (
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
          )}
          <XAxis 
            type="number"
            stroke={textColor}
            tick={{ fill: textColor }}
          />
          <YAxis 
            type="category"
            dataKey="name"
            stroke={textColor}
            tick={{ fill: textColor }}
            width={90}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="messages" radius={[0, 4, 4, 0]}>
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={getBarColor(entry.importance)} />
            ))}
            <LabelList 
              dataKey="messages" 
              position="right"
              fill={textColor}
              style={{ fontSize: '12px' }}
            />
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    )
  }

  // Vertical orientation
  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={chartData.reverse()} // Re-reverse for vertical
        margin={{ top: 5, right: 30, left: 20, bottom: 60 }}
      >
        {showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
        )}
        <XAxis 
          dataKey="name"
          stroke={textColor}
          tick={{ fill: textColor }}
          angle={-45}
          textAnchor="end"
          height={100}
        />
        <YAxis 
          stroke={textColor}
          tick={{ fill: textColor }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Bar dataKey="messages" radius={[4, 4, 0, 0]}>
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={getBarColor(entry.importance)} />
          ))}
          <LabelList 
            dataKey="messages" 
            position="top"
            fill={textColor}
            style={{ fontSize: '12px' }}
          />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}

// Export sender distribution pie chart
export function SenderDistributionChart({ 
  data = [], 
  isDark = false,
  height = 300 
}: Pick<SenderChartProps, 'data' | 'isDark' | 'height'>) {
  const pieData = useMemo(() => {
    const totalMessages = data.reduce((sum, sender) => sum + sender.messageCount, 0)
    const topSenders = data.slice(0, 5)
    const othersCount = data.slice(5).reduce((sum, sender) => sum + sender.messageCount, 0)
    
    const result = topSenders.map(sender => ({
      name: sender.name || sender.email.split('@')[0],
      value: sender.messageCount,
      percentage: ((sender.messageCount / totalMessages) * 100).toFixed(1),
    }))
    
    if (othersCount > 0) {
      result.push({
        name: 'Others',
        value: othersCount,
        percentage: ((othersCount / totalMessages) * 100).toFixed(1),
      })
    }
    
    return result
  }, [data])

  const colors = [
    isDark ? '#3b82f6' : '#2563eb',
    isDark ? '#10b981' : '#059669',
    isDark ? '#f59e0b' : '#d97706',
    isDark ? '#8b5cf6' : '#7c3aed',
    isDark ? '#ef4444' : '#dc2626',
    isDark ? '#6b7280' : '#4b5563',
  ]

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={pieData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke={isDark ? '#374151' : '#e5e7eb'} />
        <XAxis 
          dataKey="name"
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
        />
        <YAxis 
          stroke={isDark ? '#9ca3af' : '#6b7280'}
          tick={{ fill: isDark ? '#9ca3af' : '#6b7280' }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any) => [`${value} emails`, 'Count']}
        />
        <Bar dataKey="value" radius={[4, 4, 0, 0]}>
          {pieData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
          ))}
          <LabelList 
            dataKey="percentage" 
            position="top"
            formatter={(value: any) => `${value}%`}
            fill={isDark ? '#9ca3af' : '#6b7280'}
            style={{ fontSize: '12px' }}
          />
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}