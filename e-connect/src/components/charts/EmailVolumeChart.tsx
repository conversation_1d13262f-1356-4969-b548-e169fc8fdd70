import { useMemo } from 'react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
} from 'recharts'
import { format } from 'date-fns'
import { VolumeData } from '@/types/analytics'

interface EmailVolumeChartProps {
  data?: VolumeData[]
  isDark?: boolean
  height?: number
  showArea?: boolean
  showGrid?: boolean
}

export function EmailVolumeChart({ 
  data = [], 
  isDark = false, 
  height = 300,
  showArea = true,
  showGrid = true 
}: EmailVolumeChartProps) {
  const chartData = useMemo(() => {
    return data.map(item => ({
      date: format(new Date(item.date), 'MMM dd'),
      received: item.received,
      sent: item.sent,
      archived: item.archived,
      deleted: item.deleted,
      total: item.received + item.sent,
    }))
  }, [data])

  const averageTotal = useMemo(() => {
    if (chartData.length === 0) return 0
    const sum = chartData.reduce((acc, item) => acc + item.total, 0)
    return Math.round(sum / chartData.length)
  }, [chartData])

  const colors = {
    received: isDark ? '#60a5fa' : '#3b82f6', // blue
    sent: isDark ? '#34d399' : '#10b981', // green
    archived: isDark ? '#fbbf24' : '#f59e0b', // amber
    deleted: isDark ? '#f87171' : '#ef4444', // red
    total: isDark ? '#a78bfa' : '#8b5cf6', // purple
  }

  const gridColor = isDark ? '#374151' : '#e5e7eb'
  const textColor = isDark ? '#9ca3af' : '#6b7280'

  const ChartComponent = showArea ? AreaChart : LineChart
  const DataComponent = showArea ? Area : Line

  return (
    <ResponsiveContainer width="100%" height={height}>
      <ChartComponent
        data={chartData}
        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
      >
        {showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
        )}
        <XAxis 
          dataKey="date" 
          stroke={textColor}
          tick={{ fill: textColor }}
        />
        <YAxis 
          stroke={textColor}
          tick={{ fill: textColor }}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          labelStyle={{ color: isDark ? '#f3f4f6' : '#111827' }}
          itemStyle={{ color: isDark ? '#d1d5db' : '#4b5563' }}
        />
        <Legend 
          wrapperStyle={{ paddingTop: '20px' }}
          iconType="line"
          iconSize={18}
        />
        
        {/* Reference line for average */}
        <ReferenceLine 
          y={averageTotal} 
          stroke={isDark ? '#6b7280' : '#9ca3af'}
          strokeDasharray="5 5"
          label={{ value: `Avg: ${averageTotal}`, fill: textColor }}
        />
        
        <DataComponent
          type="monotone"
          dataKey="received"
          stroke={colors.received}
          fill={colors.received}
          fillOpacity={showArea ? 0.6 : 0}
          strokeWidth={2}
          name="Received"
          dot={false}
          activeDot={{ r: 6 }}
        />
        <DataComponent
          type="monotone"
          dataKey="sent"
          stroke={colors.sent}
          fill={colors.sent}
          fillOpacity={showArea ? 0.6 : 0}
          strokeWidth={2}
          name="Sent"
          dot={false}
          activeDot={{ r: 6 }}
        />
        <DataComponent
          type="monotone"
          dataKey="archived"
          stroke={colors.archived}
          fill={colors.archived}
          fillOpacity={showArea ? 0.4 : 0}
          strokeWidth={2}
          name="Archived"
          dot={false}
          activeDot={{ r: 6 }}
        />
        <DataComponent
          type="monotone"
          dataKey="deleted"
          stroke={colors.deleted}
          fill={colors.deleted}
          fillOpacity={showArea ? 0.4 : 0}
          strokeWidth={2}
          name="Deleted"
          dot={false}
          activeDot={{ r: 6 }}
        />
      </ChartComponent>
    </ResponsiveContainer>
  )
}

// Export additional chart variations
export function EmailVolumeLineChart(props: Omit<EmailVolumeChartProps, 'showArea'>) {
  return <EmailVolumeChart {...props} showArea={false} />
}

export function EmailVolumeMiniChart(props: Omit<EmailVolumeChartProps, 'height' | 'showGrid'>) {
  return <EmailVolumeChart {...props} height={100} showGrid={false} />
}

// Accessibility-enhanced version
export function AccessibleEmailVolumeChart(props: EmailVolumeChartProps) {
  return (
    <div role="img" aria-label="Email volume chart showing received, sent, archived, and deleted emails over time">
      <EmailVolumeChart {...props} />
    </div>
  )
}

// Real-time updating version
export function RealTimeEmailVolumeChart(props: EmailVolumeChartProps) {
  // This would integrate with WebSocket updates in a real implementation
  return <EmailVolumeChart {...props} />
}