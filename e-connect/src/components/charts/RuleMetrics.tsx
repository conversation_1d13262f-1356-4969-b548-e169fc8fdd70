import { useMemo } from 'react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  Area,
  AreaChart,
  ComposedChart,
  Cell,
} from 'recharts'
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ClockIcon,
  SparklesIcon 
} from '@heroicons/react/24/outline'

interface RulePerformanceData {
  rules: Array<{
    id: string
    name: string
    executions: number
    successes: number
    failures: number
    successRate: number
    timeSaved: number // in minutes
    isActive: boolean
    category: string
    createdAt: Date
    lastExecuted?: Date
  }>
  totalTimeSaved: number
  totalExecutions: number
  averageSuccessRate: number
  periodStart: Date
  periodEnd: Date
}

interface RuleMetricsProps {
  data?: RulePerformanceData
  isDark?: boolean
  height?: number
  showGrid?: boolean
  chartType?: 'performance' | 'savings' | 'success-rate'
}

export function RuleMetrics({ 
  data, 
  isDark = false, 
  height = 400,
  showGrid = true,
  chartType = 'performance'
}: RuleMetricsProps) {
  const chartData = useMemo(() => {
    if (!data?.rules) return []
    
    return data.rules
      .sort((a, b) => b.executions - a.executions)
      .slice(0, 10)
      .map(rule => ({
        name: rule.name.length > 20 ? rule.name.substring(0, 20) + '...' : rule.name,
        fullName: rule.name,
        executions: rule.executions,
        successes: rule.successes,
        failures: rule.failures,
        successRate: rule.successRate,
        timeSaved: rule.timeSaved,
        timeSavedHours: (rule.timeSaved / 60).toFixed(1),
        isActive: rule.isActive,
        category: rule.category,
      }))
  }, [data])

  const colors = {
    success: isDark ? '#10b981' : '#059669',
    failure: isDark ? '#ef4444' : '#dc2626',
    executions: isDark ? '#3b82f6' : '#2563eb',
    timeSaved: isDark ? '#8b5cf6' : '#7c3aed',
    active: isDark ? '#10b981' : '#059669',
    inactive: isDark ? '#6b7280' : '#9ca3af',
  }

  const gridColor = isDark ? '#374151' : '#e5e7eb'
  const textColor = isDark ? '#9ca3af' : '#6b7280'

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div className={`p-3 rounded-lg shadow-lg ${
          isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } border`}>
          <p className="font-semibold">{data.fullName}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">Executions: <span className="font-medium">{data.executions}</span></p>
            <p className="text-sm">Success Rate: <span className="font-medium">{data.successRate.toFixed(1)}%</span></p>
            <p className="text-sm">Time Saved: <span className="font-medium">{data.timeSavedHours}h</span></p>
            <p className="text-sm">Category: <span className="font-medium">{data.category}</span></p>
            <div className="flex items-center gap-1">
              <span className="text-sm">Status:</span>
              {data.isActive ? (
                <CheckCircleIcon className="h-4 w-4 text-green-600" />
              ) : (
                <XCircleIcon className="h-4 w-4 text-red-600" />
              )}
              <span className={`text-sm font-medium ${data.isActive ? 'text-green-600' : 'text-red-600'}`}>
                {data.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>
      )
    }
    return null
  }

  if (chartType === 'success-rate') {
    return (
      <ResponsiveContainer width="100%" height={height}>
        <BarChart
          data={chartData}
          margin={{ top: 5, right: 30, left: 20, bottom: 60 }}
        >
          {showGrid && (
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
          )}
          <XAxis 
            dataKey="name"
            stroke={textColor}
            tick={{ fill: textColor }}
            angle={-45}
            textAnchor="end"
            height={100}
          />
          <YAxis 
            domain={[0, 100]}
            stroke={textColor}
            tick={{ fill: textColor }}
            label={{ value: 'Success Rate (%)', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="successRate" radius={[4, 4, 0, 0]}>
            {chartData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.isActive ? colors.success : colors.inactive} 
              />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    )
  }

  if (chartType === 'savings') {
    return (
      <ResponsiveContainer width="100%" height={height}>
        <AreaChart
          data={chartData}
          margin={{ top: 5, right: 30, left: 20, bottom: 60 }}
        >
          {showGrid && (
            <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
          )}
          <XAxis 
            dataKey="name"
            stroke={textColor}
            tick={{ fill: textColor }}
            angle={-45}
            textAnchor="end"
            height={100}
          />
          <YAxis 
            stroke={textColor}
            tick={{ fill: textColor }}
            label={{ value: 'Time Saved (minutes)', angle: -90, position: 'insideLeft' }}
          />
          <Tooltip 
            content={<CustomTooltip />}
            formatter={(value: any) => [`${value} minutes`, 'Time Saved']}
          />
          <Area 
            type="monotone" 
            dataKey="timeSaved" 
            stroke={colors.timeSaved}
            fill={colors.timeSaved}
            fillOpacity={0.6}
          />
        </AreaChart>
      </ResponsiveContainer>
    )
  }

  // Default: performance chart (executions vs success rate)
  return (
    <ResponsiveContainer width="100%" height={height}>
      <ComposedChart
        data={chartData}
        margin={{ top: 5, right: 30, left: 20, bottom: 60 }}
      >
        {showGrid && (
          <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
        )}
        <XAxis 
          dataKey="name"
          stroke={textColor}
          tick={{ fill: textColor }}
          angle={-45}
          textAnchor="end"
          height={100}
        />
        <YAxis 
          yAxisId="executions"
          stroke={textColor}
          tick={{ fill: textColor }}
          label={{ value: 'Executions', angle: -90, position: 'insideLeft' }}
        />
        <YAxis 
          yAxisId="rate"
          orientation="right"
          domain={[0, 100]}
          stroke={textColor}
          tick={{ fill: textColor }}
          label={{ value: 'Success Rate (%)', angle: 90, position: 'insideRight' }}
        />
        <Tooltip content={<CustomTooltip />} />
        <Legend />
        <Bar 
          yAxisId="executions"
          dataKey="executions" 
          fill={colors.executions}
          name="Executions"
          radius={[4, 4, 0, 0]}
        />
        <Line 
          yAxisId="rate"
          type="monotone" 
          dataKey="successRate" 
          stroke={colors.success}
          strokeWidth={3}
          name="Success Rate (%)"
          dot={{ fill: colors.success, strokeWidth: 2, r: 4 }}
          activeDot={{ r: 6 }}
        />
      </ComposedChart>
    </ResponsiveContainer>
  )
}

// Summary metrics component
export function RuleSummaryMetrics({ 
  data, 
  isDark = false 
}: Pick<RuleMetricsProps, 'data' | 'isDark'>) {
  if (!data) return null

  const totalHoursSaved = (data.totalTimeSaved / 60).toFixed(1)
  const activeRules = data.rules.filter(rule => rule.isActive).length
  const totalRules = data.rules.length

  return (
    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      <div className={`p-4 rounded-lg ${
        isDark ? 'bg-blue-900/20' : 'bg-blue-50'
      }`}>
        <div className="flex items-center gap-2">
          <SparklesIcon className="h-5 w-5 text-blue-600" />
          <span className="text-sm font-medium">Total Executions</span>
        </div>
        <p className="text-2xl font-bold mt-1">{data.totalExecutions.toLocaleString()}</p>
      </div>
      
      <div className={`p-4 rounded-lg ${
        isDark ? 'bg-green-900/20' : 'bg-green-50'
      }`}>
        <div className="flex items-center gap-2">
          <ClockIcon className="h-5 w-5 text-green-600" />
          <span className="text-sm font-medium">Time Saved</span>
        </div>
        <p className="text-2xl font-bold mt-1">{totalHoursSaved}h</p>
      </div>
      
      <div className={`p-4 rounded-lg ${
        isDark ? 'bg-purple-900/20' : 'bg-purple-50'
      }`}>
        <div className="flex items-center gap-2">
          <CheckCircleIcon className="h-5 w-5 text-purple-600" />
          <span className="text-sm font-medium">Success Rate</span>
        </div>
        <p className="text-2xl font-bold mt-1">{data.averageSuccessRate.toFixed(1)}%</p>
      </div>
      
      <div className={`p-4 rounded-lg ${
        isDark ? 'bg-amber-900/20' : 'bg-amber-50'
      }`}>
        <div className="flex items-center gap-2">
          <SparklesIcon className="h-5 w-5 text-amber-600" />
          <span className="text-sm font-medium">Active Rules</span>
        </div>
        <p className="text-2xl font-bold mt-1">{activeRules}/{totalRules}</p>
      </div>
    </div>
  )
}

// Rule optimization suggestions
export function RuleOptimizationSuggestions({ 
  data, 
  isDark = false 
}: Pick<RuleMetricsProps, 'data' | 'isDark'>) {
  const suggestions = useMemo(() => {
    if (!data?.rules) return []

    const suggestions = []
    
    // Find rules with low success rates
    const lowSuccessRules = data.rules.filter(rule => 
      rule.successRate < 70 && rule.executions > 10
    )
    if (lowSuccessRules.length > 0) {
      suggestions.push({
        type: 'warning',
        title: 'Low Success Rate Rules',
        description: `${lowSuccessRules.length} rules have success rates below 70%`,
        rules: lowSuccessRules.map(r => r.name),
      })
    }

    // Find inactive rules that used to be effective
    const inactiveEffectiveRules = data.rules.filter(rule => 
      !rule.isActive && rule.successRate > 85 && rule.timeSaved > 60
    )
    if (inactiveEffectiveRules.length > 0) {
      suggestions.push({
        type: 'info',
        title: 'Consider Reactivating',
        description: `${inactiveEffectiveRules.length} inactive rules were previously effective`,
        rules: inactiveEffectiveRules.map(r => r.name),
      })
    }

    // Find rules with high time savings potential
    const highImpactRules = data.rules.filter(rule => 
      rule.isActive && rule.successRate > 90 && rule.executions > 50
    ).slice(0, 3)
    if (highImpactRules.length > 0) {
      suggestions.push({
        type: 'success',
        title: 'Top Performing Rules',
        description: `${highImpactRules.length} rules are performing exceptionally well`,
        rules: highImpactRules.map(r => r.name),
      })
    }

    return suggestions
  }, [data])

  if (suggestions.length === 0) return null

  return (
    <div className="mt-6 space-y-3">
      <h4 className="text-lg font-semibold">Optimization Suggestions</h4>
      {suggestions.map((suggestion, index) => (
        <div key={index} className={`p-4 rounded-lg border ${
          suggestion.type === 'warning' ? 
            isDark ? 'bg-yellow-900/20 border-yellow-700' : 'bg-yellow-50 border-yellow-200' :
          suggestion.type === 'success' ? 
            isDark ? 'bg-green-900/20 border-green-700' : 'bg-green-50 border-green-200' :
            isDark ? 'bg-blue-900/20 border-blue-700' : 'bg-blue-50 border-blue-200'
        }`}>
          <h5 className="font-medium">{suggestion.title}</h5>
          <p className="text-sm text-muted-foreground mt-1">{suggestion.description}</p>
          {suggestion.rules.length > 0 && (
            <div className="mt-2">
              <p className="text-xs text-muted-foreground">Affected rules:</p>
              <div className="flex flex-wrap gap-1 mt-1">
                {suggestion.rules.slice(0, 3).map((ruleName, ruleIndex) => (
                  <span key={ruleIndex} className={`text-xs px-2 py-1 rounded ${
                    isDark ? 'bg-gray-700' : 'bg-gray-100'
                  }`}>
                    {ruleName}
                  </span>
                ))}
                {suggestion.rules.length > 3 && (
                  <span className="text-xs text-muted-foreground">
                    +{suggestion.rules.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

// Real-time rule metrics with live updates
export function RealTimeRuleMetrics({ 
  data, 
  isDark = false,
  height = 400,
  updateInterval = 30000 // 30 seconds
}: RuleMetricsProps & { updateInterval?: number }) {
  // In a real implementation, this would connect to WebSocket or polling
  // For now, we'll just render the standard component
  return <RuleMetrics data={data} isDark={isDark} height={height} />
}

// Compact rule performance dashboard
export function CompactRuleMetrics({ 
  data, 
  isDark = false 
}: Pick<RuleMetricsProps, 'data' | 'isDark'>) {
  if (!data?.rules) return null

  const topRules = data.rules
    .sort((a, b) => b.timeSaved - a.timeSaved)
    .slice(0, 5)

  return (
    <div className="space-y-3">
      {topRules.map((rule, index) => (
        <div key={rule.id} className={`p-3 rounded-lg ${
          isDark ? 'bg-gray-800' : 'bg-gray-50'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${
                rule.isActive ? 'bg-green-500' : 'bg-gray-400'
              }`} />
              <span className="font-medium text-sm">{rule.name}</span>
            </div>
            <div className="text-right text-sm">
              <div className="font-medium">{(rule.timeSaved / 60).toFixed(1)}h saved</div>
              <div className="text-muted-foreground">{rule.successRate.toFixed(1)}% success</div>
            </div>
          </div>
          <div className="mt-2">
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <div 
                className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(rule.successRate, 100)}%` }}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Rule performance comparison chart
export function RuleComparisonChart({ 
  data, 
  isDark = false,
  height = 300,
  compareBy = 'efficiency' // 'efficiency' | 'reliability' | 'impact'
}: RuleMetricsProps & { compareBy?: 'efficiency' | 'reliability' | 'impact' }) {
  const chartData = useMemo(() => {
    if (!data?.rules) return []
    
    return data.rules.map(rule => {
      let score = 0
      switch (compareBy) {
        case 'efficiency':
          score = (rule.timeSaved / Math.max(rule.executions, 1)) * 100
          break
        case 'reliability':
          score = rule.successRate
          break
        case 'impact':
          score = rule.timeSaved / 60 // hours
          break
      }
      
      return {
        name: rule.name.length > 15 ? rule.name.substring(0, 15) + '...' : rule.name,
        score: Number(score.toFixed(1)),
        executions: rule.executions,
        successRate: rule.successRate,
        timeSaved: rule.timeSaved,
        isActive: rule.isActive,
      }
    }).sort((a, b) => b.score - a.score).slice(0, 8)
  }, [data, compareBy])

  const colors = {
    active: isDark ? '#10b981' : '#059669',
    inactive: isDark ? '#6b7280' : '#9ca3af',
  }

  const gridColor = isDark ? '#374151' : '#e5e7eb'
  const textColor = isDark ? '#9ca3af' : '#6b7280'

  const getScoreLabel = () => {
    switch (compareBy) {
      case 'efficiency': return 'Efficiency Score'
      case 'reliability': return 'Reliability (%)'
      case 'impact': return 'Impact (hours saved)'
      default: return 'Score'
    }
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      <BarChart
        data={chartData}
        layout="horizontal"
        margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
      >
        <CartesianGrid strokeDasharray="3 3" stroke={gridColor} />
        <XAxis 
          type="number"
          stroke={textColor}
          tick={{ fill: textColor }}
          label={{ value: getScoreLabel(), position: 'bottom' }}
        />
        <YAxis 
          type="category"
          dataKey="name"
          stroke={textColor}
          tick={{ fill: textColor }}
          width={90}
        />
        <Tooltip
          contentStyle={{
            backgroundColor: isDark ? '#1f2937' : '#ffffff',
            border: `1px solid ${isDark ? '#374151' : '#e5e7eb'}`,
            borderRadius: '0.375rem',
          }}
          formatter={(value: any) => [value, getScoreLabel()]}
        />
        <Bar dataKey="score" radius={[0, 4, 4, 0]}>
          {chartData.map((entry, index) => (
            <Cell 
              key={`cell-${index}`} 
              fill={entry.isActive ? colors.active : colors.inactive} 
            />
          ))}
        </Bar>
      </BarChart>
    </ResponsiveContainer>
  )
}