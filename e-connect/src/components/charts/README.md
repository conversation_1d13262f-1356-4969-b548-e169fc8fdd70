# Analytics Charts

This directory contains comprehensive chart components for the email analytics dashboard. All charts are built with Recharts and support both light and dark themes, accessibility features, and responsive design.

## Chart Components

### EmailVolumeChart
Displays email volume trends over time with support for both line and area chart formats.

```tsx
import { EmailVolumeChart } from '@/components/charts/EmailVolumeChart'

<EmailVolumeChart 
  data={volumeData}
  isDark={false}
  height={300}
  showArea={true}
  showGrid={true}
/>
```

**Props:**
- `data`: Array of VolumeData objects
- `isDark`: Boolean for dark mode styling
- `height`: Chart height in pixels
- `showArea`: Whether to show area fill
- `showGrid`: Whether to show grid lines

**Variants:**
- `EmailVolumeLineChart`: Line chart version
- `EmailVolumeMiniChart`: Compact version for dashboards
- `AccessibleEmailVolumeChart`: Enhanced accessibility version

### SenderChart
Horizontal or vertical bar chart showing top email senders by message count.

```tsx
import { Sender<PERSON>hart } from '@/components/charts/SenderChart'

<SenderChart 
  data={senderData}
  isDark={false}
  height={400}
  maxSenders={10}
  orientation="horizontal"
/>
```

**Props:**
- `data`: Array of SenderStats objects
- `maxSenders`: Maximum number of senders to display
- `orientation`: 'horizontal' or 'vertical' layout

**Additional Components:**
- `SenderDistributionChart`: Alternative visualization as bar chart

### CategoryChart
Pie, donut, or bar chart for email category distribution with trend indicators.

```tsx
import { CategoryChart } from '@/components/charts/CategoryChart'

<CategoryChart 
  data={categoryData}
  isDark={false}
  height={300}
  chartType="donut"
  showTrends={true}
/>
```

**Props:**
- `chartType`: 'pie', 'donut', or 'bar'
- `showTrends`: Whether to display trend indicators

**Additional Components:**
- `CategoryComparisonChart`: Read vs unread comparison
- `InteractiveCategoryChart`: Clickable categories for drill-down

### RuleMetrics
Comprehensive rule performance visualization with multiple chart types.

```tsx
import { RuleMetrics, RuleSummaryMetrics } from '@/components/charts/RuleMetrics'

<RuleMetrics 
  data={rulePerformanceData}
  isDark={false}
  height={400}
  chartType="performance"
/>

<RuleSummaryMetrics data={rulePerformanceData} isDark={false} />
```

**Chart Types:**
- `performance`: Executions vs success rate
- `savings`: Time saved visualization
- `success-rate`: Success rate comparison

**Additional Components:**
- `RuleSummaryMetrics`: Key metrics overview
- `RuleOptimizationSuggestions`: AI-powered suggestions
- `CompactRuleMetrics`: Condensed rule list
- `RuleComparisonChart`: Compare rules by different criteria

## Chart Wrapper and Utilities

### ChartWrapper
Provides consistent error handling, loading states, and accessibility features.

```tsx
import { ChartWrapper } from '@/components/charts/ChartWrapper'

<ChartWrapper
  title="Email Volume Trends"
  description="Daily email volume over the last 30 days"
  isLoading={isLoading}
  error={error}
  height={300}
>
  <EmailVolumeChart data={data} />
</ChartWrapper>
```

### Chart Export Utilities
Built-in utilities for exporting chart data and images.

```tsx
import { chartExportUtils } from '@/components/charts/ChartWrapper'

// Export as CSV
const csvData = chartExportUtils.dataToCSV(data, ['date', 'received', 'sent'])
chartExportUtils.downloadAsFile(csvData, 'email-volume.csv')

// Export as image (requires html2canvas)
await chartExportUtils.chartToImage(chartElement, 'chart.png')
```

## Theme Support

All charts support automatic dark mode detection and custom color schemes:

```tsx
import { getChartColors } from '@/components/charts/ChartWrapper'

const colors = getChartColors(isDark, 'accessibility') // or 'default', 'monochrome'
```

## Accessibility Features

- **ARIA labels**: All charts include descriptive labels
- **Keyboard navigation**: Charts are focusable and accessible via keyboard
- **High contrast**: Support for accessibility color schemes
- **Screen reader support**: Proper semantic markup and descriptions

## Responsive Design

Charts automatically adapt to different screen sizes:

- **Mobile** (< 480px): Simplified layouts, reduced data points
- **Tablet** (480px - 768px): Compact layouts with essential information
- **Desktop** (> 768px): Full feature set with detailed visualizations

## Error Handling

All charts include comprehensive error boundaries:

- **Data validation**: Charts handle missing or invalid data gracefully
- **Render errors**: Automatic fallback to error states with retry options
- **Loading states**: Skeleton loaders while data is fetching

## Performance Optimization

- **Memoization**: Chart data is memoized to prevent unnecessary re-renders
- **Lazy loading**: Charts can be loaded on-demand
- **Virtual scrolling**: For charts with large datasets

## Real-time Updates

Support for live data updates:

```tsx
// Enable real-time updates (placeholder for WebSocket integration)
<RealTimeEmailVolumeChart data={data} updateInterval={30000} />
<RealTimeRuleMetrics data={ruleData} updateInterval={60000} />
```

## Development Guidelines

### Adding New Charts

1. Create a new component in this directory
2. Follow the existing prop interface patterns
3. Include TypeScript types for all props
4. Add dark mode support using the `isDark` prop
5. Include accessibility features (ARIA labels, keyboard support)
6. Add error boundaries and loading states
7. Document the component in this README

### Chart Structure

```tsx
interface ChartProps {
  data?: DataType[]
  isDark?: boolean
  height?: number
  // ... specific props
}

export function MyChart({ 
  data = [], 
  isDark = false, 
  height = 300 
}: ChartProps) {
  // Memoized data processing
  const chartData = useMemo(() => {
    return processData(data)
  }, [data])

  // Theme-aware colors
  const colors = getChartColors(isDark)
  
  // Error handling
  if (!data || data.length === 0) {
    return <EmptyState />
  }

  return (
    <ResponsiveContainer width="100%" height={height}>
      {/* Chart implementation */}
    </ResponsiveContainer>
  )
}
```

### Testing

Test charts with:
- Empty data sets
- Large data sets (performance)
- Invalid data
- Dark/light mode switching
- Different screen sizes
- Accessibility tools

## Dependencies

- **Recharts**: Core charting library
- **date-fns**: Date formatting and manipulation
- **React**: Component framework
- **TypeScript**: Type safety

## Future Enhancements

- [ ] Add more chart types (scatter plots, radar charts)
- [ ] Implement chart animations and transitions
- [ ] Add advanced filtering and zooming capabilities
- [ ] Integrate with data streaming for real-time updates
- [ ] Add chart comparison and overlay features
- [ ] Implement chart annotation and marking tools