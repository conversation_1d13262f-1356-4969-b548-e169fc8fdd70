import React from 'react'
import { clsx } from 'clsx'

interface ToggleProps {
  checked: boolean
  onChange: (checked: boolean) => void
  label?: string
  description?: string
  disabled?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function Toggle({
  checked,
  onChange,
  label,
  description,
  disabled = false,
  size = 'md',
  className
}: ToggleProps) {
  const sizes = {
    sm: {
      switch: 'h-5 w-9',
      dot: 'h-3 w-3',
      translate: 'translate-x-4'
    },
    md: {
      switch: 'h-6 w-11',
      dot: 'h-4 w-4',
      translate: 'translate-x-5'
    },
    lg: {
      switch: 'h-7 w-14',
      dot: 'h-5 w-5',
      translate: 'translate-x-7'
    }
  }

  const sizeConfig = sizes[size]

  return (
    <div className={clsx('flex items-start', className)}>
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        onClick={() => onChange(!checked)}
        disabled={disabled}
        className={clsx(
          'relative inline-flex flex-shrink-0 cursor-pointer rounded-full',
          'border-2 border-transparent transition-colors duration-200 ease-in-out',
          'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
          sizeConfig.switch,
          checked ? 'bg-blue-600' : 'bg-gray-200',
          disabled && 'opacity-50 cursor-not-allowed'
        )}
      >
        <span
          aria-hidden="true"
          className={clsx(
            'pointer-events-none inline-block transform rounded-full',
            'bg-white shadow ring-0 transition duration-200 ease-in-out',
            sizeConfig.dot,
            checked ? sizeConfig.translate : 'translate-x-0'
          )}
        />
      </button>
      
      {(label || description) && (
        <div className="ml-3">
          {label && (
            <label
              className={clsx(
                'text-sm font-medium text-gray-900',
                disabled && 'opacity-50'
              )}
            >
              {label}
            </label>
          )}
          {description && (
            <p className={clsx(
              'text-sm text-gray-500',
              disabled && 'opacity-50'
            )}>
              {description}
            </p>
          )}
        </div>
      )}
    </div>
  )
}