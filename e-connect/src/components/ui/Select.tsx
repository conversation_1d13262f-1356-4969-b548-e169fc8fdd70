import * as React from "react";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import { cn } from "@/utils";

interface SelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  children: React.ReactNode;
  className?: string;
}

interface SelectItemProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

const SelectContext = React.createContext<{
  value?: string;
  onValueChange?: (value: string) => void;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
}>({
  isOpen: false,
  setIsOpen: () => {}
});

const Select: React.FC<SelectProps> & { Item: React.FC<SelectItemProps> } = ({
  value,
  onValueChange,
  placeholder = "Select an option",
  disabled = false,
  children,
  className
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const selectRef = React.useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Find selected item label
  const selectedLabel = React.useMemo(() => {
    let label = placeholder;
    
    React.Children.forEach(children, (child) => {
      if (React.isValidElement(child) && child.props.value === value) {
        // Extract text content from children
        const extractText = (node: React.ReactNode): string => {
          if (typeof node === 'string') return node;
          if (typeof node === 'number') return String(node);
          if (React.isValidElement(node)) {
            if (typeof node.props.children === 'string') return node.props.children;
            if (React.isValidElement(node.props.children)) {
              return extractText(node.props.children);
            }
            if (Array.isArray(node.props.children)) {
              return node.props.children.map(extractText).join('');
            }
          }
          return '';
        };
        
        label = extractText(child.props.children);
      }
    });
    
    return label;
  }, [value, children, placeholder]);

  return (
    <SelectContext.Provider value={{ value, onValueChange, isOpen, setIsOpen }}>
      <div ref={selectRef} className={cn("relative", className)}>
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className={cn(
            "flex h-10 w-full items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm ring-offset-white placeholder:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
            isOpen && "ring-2 ring-blue-500 ring-offset-2"
          )}
        >
          <span className={cn(
            "block truncate",
            value ? "text-gray-900" : "text-gray-500"
          )}>
            {selectedLabel}
          </span>
          <ChevronDownIcon 
            className={cn(
              "h-4 w-4 text-gray-400 transition-transform",
              isOpen && "rotate-180"
            )}
          />
        </button>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
            {children}
          </div>
        )}
      </div>
    </SelectContext.Provider>
  );
};

const SelectItem: React.FC<SelectItemProps> = ({ value, children, className }) => {
  const { value: selectedValue, onValueChange, setIsOpen } = React.useContext(SelectContext);
  
  const handleClick = () => {
    onValueChange?.(value);
    setIsOpen(false);
  };

  const isSelected = selectedValue === value;

  return (
    <button
      type="button"
      onClick={handleClick}
      className={cn(
        "flex w-full items-center px-3 py-2 text-sm text-left hover:bg-gray-50 focus:bg-gray-50 focus:outline-none",
        isSelected && "bg-blue-50 text-blue-900",
        className
      )}
    >
      {children}
    </button>
  );
};

Select.Item = SelectItem;

export { Select };