import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "../../utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        primary: "border-transparent bg-blue-600 text-white hover:bg-blue-700",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground border-border",
        success: "border-transparent bg-green-100 text-green-800 hover:bg-green-200",
        warning: "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
        info: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200",
        // Email categories
        newsletter: "border-transparent bg-blue-100 text-blue-800 border-blue-200",
        receipt: "border-transparent bg-green-100 text-green-800 border-green-200",
        marketing: "border-transparent bg-purple-100 text-purple-800 border-purple-200",
        social: "border-transparent bg-pink-100 text-pink-800 border-pink-200",
        updates: "border-transparent bg-cyan-100 text-cyan-800 border-cyan-200",
        personal: "border-transparent bg-indigo-100 text-indigo-800 border-indigo-200",
        work: "border-transparent bg-orange-100 text-orange-800 border-orange-200",
        finance: "border-transparent bg-emerald-100 text-emerald-800 border-emerald-200",
        travel: "border-transparent bg-teal-100 text-teal-800 border-teal-200",
        security: "border-transparent bg-red-100 text-red-800 border-red-200",
        notification: "border-transparent bg-yellow-100 text-yellow-800 border-yellow-200",
        important: "border-transparent bg-red-100 text-red-800 border-red-200",
        spam: "border-transparent bg-gray-100 text-gray-800 border-gray-200",
        other: "border-transparent bg-gray-100 text-gray-800 border-gray-200",
      },
      size: {
        default: "px-2.5 py-0.5 text-xs",
        sm: "px-2 py-0.5 text-xs",
        lg: "px-3 py-1 text-sm",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  children: React.ReactNode;
}

function Badge({ className, variant, size, children, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props}>
      {children}
    </div>
  );
}

export { Badge, badgeVariants };