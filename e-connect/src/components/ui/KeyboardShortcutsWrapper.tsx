import { useState, useEffect } from 'react'
import { KeyboardShortcuts } from './KeyboardShortcuts'
import { useKeyboardShortcuts } from '../../hooks/useKeyboardShortcuts'
import { useSettingsStore } from '../../stores/settingsStore'

export function KeyboardShortcutsWrapper() {
  const [showDialog, setShowDialog] = useState(false)
  const { keyboardShortcutsEnabled } = useSettingsStore()
  
  // Define keyboard shortcuts
  const shortcuts = [
    {
      key: 'c',
      description: 'Compose new email',
      handler: () => window.location.href = '/mail/compose'
    },
    {
      key: 'r',
      description: 'Refresh inbox',
      handler: () => window.location.reload()
    },
    {
      key: 'g',
      shift: true,
      description: 'Go to inbox',
      handler: () => window.location.href = '/'
    },
    {
      key: 'a',
      description: 'Go to assistant',
      handler: () => window.location.href = '/assistant'
    },
    {
      key: 's',
      description: 'Go to stats',
      handler: () => window.location.href = '/stats'
    },
    {
      key: 'u',
      description: 'Go to automation',
      handler: () => window.location.href = '/automation'
    },
    {
      key: 'b',
      description: 'Go to bulk actions',
      handler: () => window.location.href = '/bulk-unsubscribe'
    },
    {
      key: '/',
      description: 'Show keyboard shortcuts',
      handler: () => setShowDialog(true)
    },
    {
      key: '?',
      description: 'Show help',
      handler: () => setShowDialog(true)
    }
  ]

  // Register keyboard shortcuts
  useKeyboardShortcuts(shortcuts)

  if (!keyboardShortcutsEnabled) {
    return null
  }

  return (
    <KeyboardShortcuts
      open={showDialog}
      onClose={() => setShowDialog(false)}
      shortcuts={shortcuts}
      title="Keyboard Shortcuts"
    />
  )
}