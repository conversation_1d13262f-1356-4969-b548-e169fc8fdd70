import React from 'react'
import { clsx } from 'clsx'

interface SliderProps {
  value: number
  onChange: (value: number) => void
  min?: number
  max?: number
  step?: number
  label?: string
  showValue?: boolean
  formatValue?: (value: number) => string
  marks?: Array<{ value: number; label?: string }>
  disabled?: boolean
  className?: string
}

export function Slider({
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  label,
  showValue = true,
  formatValue = (v) => v.toString(),
  marks,
  disabled = false,
  className
}: SliderProps) {
  const percentage = ((value - min) / (max - min)) * 100

  return (
    <div className={clsx('w-full', className)}>
      {(label || showValue) && (
        <div className="flex items-center justify-between mb-2">
          {label && (
            <label className="text-sm font-medium text-gray-700">
              {label}
            </label>
          )}
          {showValue && (
            <span className="text-sm font-medium text-gray-900">
              {formatValue(value)}
            </span>
          )}
        </div>
      )}
      
      <div className="relative">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          disabled={disabled}
          className={clsx(
            'w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
            disabled && 'opacity-50 cursor-not-allowed',
            '[&::-webkit-slider-thumb]:appearance-none',
            '[&::-webkit-slider-thumb]:w-4',
            '[&::-webkit-slider-thumb]:h-4',
            '[&::-webkit-slider-thumb]:bg-blue-600',
            '[&::-webkit-slider-thumb]:rounded-full',
            '[&::-webkit-slider-thumb]:cursor-pointer',
            '[&::-webkit-slider-thumb]:hover:bg-blue-700',
            '[&::-webkit-slider-thumb]:focus:ring-2',
            '[&::-webkit-slider-thumb]:focus:ring-blue-500',
            '[&::-moz-range-thumb]:w-4',
            '[&::-moz-range-thumb]:h-4',
            '[&::-moz-range-thumb]:bg-blue-600',
            '[&::-moz-range-thumb]:rounded-full',
            '[&::-moz-range-thumb]:cursor-pointer',
            '[&::-moz-range-thumb]:hover:bg-blue-700',
            '[&::-moz-range-thumb]:focus:ring-2',
            '[&::-moz-range-thumb]:focus:ring-blue-500',
            '[&::-moz-range-thumb]:border-0'
          )}
          style={{
            background: `linear-gradient(to right, rgb(59 130 246) 0%, rgb(59 130 246) ${percentage}%, rgb(229 231 235) ${percentage}%, rgb(229 231 235) 100%)`
          }}
        />
        
        {marks && (
          <div className="absolute inset-x-0 -bottom-6 flex justify-between px-2">
            {marks.map((mark) => {
              const markPercentage = ((mark.value - min) / (max - min)) * 100
              return (
                <div
                  key={mark.value}
                  className="absolute text-xs text-gray-500"
                  style={{ left: `${markPercentage}%`, transform: 'translateX(-50%)' }}
                >
                  {mark.label || mark.value}
                </div>
              )
            })}
          </div>
        )}
      </div>
    </div>
  )
}