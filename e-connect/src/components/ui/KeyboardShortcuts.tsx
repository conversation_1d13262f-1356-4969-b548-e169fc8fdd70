import React from 'react'
import { Keyboard } from 'lucide-react'
import { Dialog } from './Dialog'
import type { KeyboardShortcut } from '../../hooks/useKeyboardShortcuts'

interface KeyboardShortcutsProps {
  open: boolean
  onClose: () => void
  shortcuts: KeyboardShortcut[]
  title?: string
}

export function KeyboardShortcuts({ 
  open, 
  onClose, 
  shortcuts,
  title = "Keyboard Shortcuts"
}: KeyboardShortcutsProps) {
  const formatKey = (shortcut: KeyboardShortcut) => {
    const parts: string[] = []
    
    if (shortcut.ctrl) parts.push('Ctrl')
    if (shortcut.shift) parts.push('Shift')
    if (shortcut.alt) parts.push('Alt')
    if (shortcut.meta) parts.push('Cmd')
    
    parts.push(shortcut.key.toUpperCase())
    
    return parts.join(' + ')
  }

  return (
    <Dialog
      open={open}
      onClose={onClose}
      title={title}
      className="max-w-md"
    >
      <div className="space-y-2">
        {shortcuts.map((shortcut, index) => (
          <div 
            key={index} 
            className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50"
          >
            <span className="text-sm text-gray-700">
              {shortcut.description || 'No description'}
            </span>
            <kbd className="px-2 py-1 text-xs font-mono bg-gray-100 border border-gray-300 rounded">
              {formatKey(shortcut)}
            </kbd>
          </div>
        ))}
      </div>
      
      <div className="mt-4 pt-4 border-t">
        <p className="text-xs text-gray-500 flex items-center gap-2">
          <Keyboard className="w-4 h-4" />
          Press ? to show this help anytime
        </p>
      </div>
    </Dialog>
  )
}