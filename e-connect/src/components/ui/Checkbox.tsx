import * as React from "react";
import { Check } from "lucide-react";
import { cn } from "../../utils";

export interface CheckboxProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {
  label?: string;
  description?: string;
  error?: string;
  indeterminate?: boolean;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  ({ className, label, description, error, indeterminate = false, ...props }, ref) => {
    const checkboxRef = React.useRef<HTMLInputElement>(null);

    React.useImperativeHandle(ref, () => checkboxRef.current!, []);

    React.useEffect(() => {
      if (checkboxRef.current) {
        checkboxRef.current.indeterminate = indeterminate;
      }
    }, [indeterminate]);

    const id = props.id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;

    return (
      <div className="flex items-start space-x-3">
        <div className="relative flex items-center">
          <input
            ref={checkboxRef}
            type="checkbox"
            id={id}
            className={cn(
              "h-4 w-4 rounded border-2 border-gray-300 text-blue-600",
              "focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
              "transition-colors duration-200",
              "disabled:opacity-50 disabled:cursor-not-allowed",
              error && "border-red-500 focus:ring-red-500",
              className
            )}
            {...props}
          />
          {/* Custom checkmark for better visual feedback */}
          {props.checked && !indeterminate && (
            <Check className="absolute left-0.5 top-0.5 h-3 w-3 text-white pointer-events-none" />
          )}
          {indeterminate && (
            <div className="absolute left-1 top-2 h-0.5 w-2 bg-white pointer-events-none" />
          )}
        </div>
        
        {(label || description) && (
          <div className="flex-1">
            {label && (
              <label
                htmlFor={id}
                className={cn(
                  "text-sm font-medium leading-none cursor-pointer",
                  "peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
                  error ? "text-red-900" : "text-gray-900"
                )}
              >
                {label}
              </label>
            )}
            {description && (
              <p className={cn(
                "text-sm",
                error ? "text-red-600" : "text-gray-600",
                label && "mt-1"
              )}>
                {description}
              </p>
            )}
            {error && (
              <p className="text-sm text-red-600 mt-1">
                {error}
              </p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Checkbox.displayName = "Checkbox";

export { Checkbox };