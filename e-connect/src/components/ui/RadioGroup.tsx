import React from 'react'
import { clsx } from 'clsx'

interface RadioOption {
  value: string
  label: string
  description?: string
  disabled?: boolean
}

interface RadioGroupProps {
  name: string
  value: string
  onChange: (value: string) => void
  options: RadioOption[]
  label?: string
  error?: string
  className?: string
  orientation?: 'horizontal' | 'vertical'
}

export function RadioGroup({
  name,
  value,
  onChange,
  options,
  label,
  error,
  className,
  orientation = 'vertical'
}: RadioGroupProps) {
  return (
    <div className={className}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      <div className={clsx(
        'space-y-2',
        orientation === 'horizontal' && 'flex space-y-0 space-x-4'
      )}>
        {options.map((option) => (
          <label
            key={option.value}
            className={clsx(
              'flex items-start cursor-pointer',
              option.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={(e) => onChange(e.target.value)}
              disabled={option.disabled}
              className={clsx(
                'mt-1 h-4 w-4 text-blue-600 border-gray-300',
                'focus:ring-blue-500 focus:ring-2',
                option.disabled && 'cursor-not-allowed'
              )}
            />
            <div className="ml-3">
              <span className="block text-sm font-medium text-gray-900">
                {option.label}
              </span>
              {option.description && (
                <span className="block text-sm text-gray-500">
                  {option.description}
                </span>
              )}
            </div>
          </label>
        ))}
      </div>
      
      {error && (
        <p className="mt-2 text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}