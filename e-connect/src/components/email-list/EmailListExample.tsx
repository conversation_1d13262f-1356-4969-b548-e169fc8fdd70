import React, { useState } from 'react';
import { EmailList } from './EmailList';
import { EmailListItem } from './EmailListItem';
import type { Thread } from '../../types/email';

// Mock data for EmailListItem demonstration
const mockThreads: Thread[] = [
  {
    id: '1',
    subject: 'Welcome to your new account',
    snippet: 'Thank you for joining us! Here are some quick tips to get you started with your new account...',
    category: 'Personal',
    labels: ['Important', 'Welcome'],
    participants: [
      {
        email: '<EMAIL>',
        name: 'Company Team',
        role: 'sender',
        messageCount: 1,
        lastMessageDate: new Date(),
      }
    ],
    lastMessageDate: new Date(),
    firstMessageDate: new Date(),
    messageCount: 1,
    unreadCount: 1,
    status: {
      isUnread: true,
      isImportant: true,
      isStarred: false,
      isDraft: false,
      isSpam: false,
      isTrash: false,
      isSnoozed: false,
    },
    priority: 'high',
    messages: [
      {
        id: '1-1',
        threadId: '1',
        messageId: 'msg-1',
        subject: 'Welcome to your new account',
        snippet: 'Thank you for joining us! Here are some quick tips...',
        body: { text: 'Welcome email content' },
        headers: {} as any,
        from: { email: '<EMAIL>', name: 'Company Team' },
        to: [{ email: '<EMAIL>', name: 'User' }],
        date: new Date(),
        receivedDate: new Date(),
        attachments: [],
        labels: ['Important'],
        flags: {
          isUnread: true,
          isImportant: true,
          isStarred: false,
          isDraft: false,
          isSpam: false,
          isTrash: false,
        },
        size: 1024,
        rawSize: 1024,
        historyId: 'h1',
        internalDate: new Date().toISOString(),
      }
    ]
  },
  {
    id: '2',
    subject: 'Your monthly newsletter is here',
    snippet: 'Check out the latest features, tips, and updates from our team. This month we have exciting news about...',
    category: 'Newsletter',
    labels: ['Marketing'],
    participants: [
      {
        email: '<EMAIL>',
        name: 'Newsletter Team',
        role: 'sender',
        messageCount: 1,
        lastMessageDate: new Date(Date.now() - 2 * 60 * 60 * 1000),
      }
    ],
    lastMessageDate: new Date(Date.now() - 2 * 60 * 60 * 1000),
    firstMessageDate: new Date(Date.now() - 2 * 60 * 60 * 1000),
    messageCount: 1,
    unreadCount: 0,
    status: {
      isUnread: false,
      isImportant: false,
      isStarred: true,
      isDraft: false,
      isSpam: false,
      isTrash: false,
      isSnoozed: false,
    },
    messages: [
      {
        id: '2-1',
        threadId: '2',
        messageId: 'msg-2',
        subject: 'Your monthly newsletter is here',
        snippet: 'Check out the latest features...',
        body: { text: 'Newsletter content' },
        headers: {} as any,
        from: { email: '<EMAIL>', name: 'Newsletter Team' },
        to: [{ email: '<EMAIL>', name: 'User' }],
        date: new Date(Date.now() - 2 * 60 * 60 * 1000),
        receivedDate: new Date(Date.now() - 2 * 60 * 60 * 1000),
        attachments: [
          {
            id: 'att-1',
            filename: 'newsletter.pdf',
            mimeType: 'application/pdf',
            size: 2048,
            inline: false,
          }
        ],
        labels: ['Marketing'],
        flags: {
          isUnread: false,
          isImportant: false,
          isStarred: true,
          isDraft: false,
          isSpam: false,
          isTrash: false,
        },
        size: 2048,
        rawSize: 2048,
        historyId: 'h2',
        internalDate: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      }
    ]
  },
  {
    id: '3',
    subject: 'Project Update: Q4 Planning',
    snippet: 'Hi team, I wanted to share the latest updates on our Q4 planning session. We have made significant progress...',
    category: 'Work',
    labels: ['Project', 'Q4'],
    participants: [
      {
        email: '<EMAIL>',
        name: 'Sarah Johnson',
        role: 'sender',
        messageCount: 3,
        lastMessageDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      }
    ],
    lastMessageDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    firstMessageDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    messageCount: 3,
    unreadCount: 0,
    status: {
      isUnread: false,
      isImportant: false,
      isStarred: false,
      isDraft: false,
      isSpam: false,
      isTrash: false,
      isSnoozed: false,
    },
    priority: 'medium',
    messages: [
      {
        id: '3-1',
        threadId: '3',
        messageId: 'msg-3',
        subject: 'Project Update: Q4 Planning',
        snippet: 'Hi team, I wanted to share the latest updates...',
        body: { text: 'Project update content' },
        headers: {} as any,
        from: { email: '<EMAIL>', name: 'Sarah Johnson' },
        to: [{ email: '<EMAIL>', name: 'User' }],
        date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        receivedDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        attachments: [],
        labels: ['Project'],
        flags: {
          isUnread: false,
          isImportant: false,
          isStarred: false,
          isDraft: false,
          isSpam: false,
          isTrash: false,
        },
        size: 1500,
        rawSize: 1500,
        historyId: 'h3',
        internalDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
      }
    ]
  }
];

// EmailListItem demonstration component
export const EmailListItemDemo: React.FC = () => {
  const [selectedThread, setSelectedThread] = useState<string | null>(null);
  const [selectedThreads, setSelectedThreads] = useState<Set<string>>(new Set());
  const [selectMode, setSelectMode] = useState(false);

  const handleSelect = (threadId: string) => {
    setSelectedThread(threadId);
  };

  const handleToggleSelect = (threadId: string) => {
    const newSelection = new Set(selectedThreads);
    if (newSelection.has(threadId)) {
      newSelection.delete(threadId);
    } else {
      newSelection.add(threadId);
    }
    setSelectedThreads(newSelection);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            EmailListItem Component Examples
          </h2>
          
          <div className="flex items-center gap-4 mb-4">
            <button
              onClick={() => setSelectMode(!selectMode)}
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            >
              {selectMode ? 'Exit Select Mode' : 'Enter Select Mode'}
            </button>
            
            {selectedThreads.size > 0 && (
              <span className="text-sm text-gray-600">
                {selectedThreads.size} selected
              </span>
            )}
          </div>
        </div>
        
        <div className="divide-y divide-gray-100">
          <div className="p-4 bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Regular View</h3>
            {mockThreads.map((thread) => (
              <EmailListItem
                key={thread.id}
                thread={thread}
                isSelected={selectedThread === thread.id}
                isSelectMode={selectMode}
                onSelect={handleSelect}
                onToggleSelect={handleToggleSelect}
                className="mb-2 last:mb-0"
              />
            ))}
          </div>
          
          <div className="p-4 bg-gray-50">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Compact View</h3>
            {mockThreads.map((thread) => (
              <EmailListItem
                key={`compact-${thread.id}`}
                thread={thread}
                isSelected={selectedThread === thread.id}
                isSelectMode={selectMode}
                onSelect={handleSelect}
                onToggleSelect={handleToggleSelect}
                compact={true}
                className="mb-1 last:mb-0"
              />
            ))}
          </div>
        </div>
      </div>
      
      <div className="bg-blue-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">Features Demonstrated</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Gmail-style visual design with hover states</li>
          <li>• Unread indicators with bold text</li>
          <li>• Colored avatars with initials</li>
          <li>• Star toggle with smooth animations</li>
          <li>• Attachment and importance icons</li>
          <li>• Category badges and labels</li>
          <li>• Relative timestamps (time, Yesterday, date)</li>
          <li>• Thread message counts</li>
          <li>• Selection checkboxes</li>
          <li>• Right-click context menus</li>
          <li>• Keyboard navigation support</li>
          <li>• Responsive compact mode</li>
          <li>• Accessibility features</li>
        </ul>
      </div>
    </div>
  );
};

// Example usage of the EmailList component
export const EmailListExample: React.FC = () => {
  const handleThreadSelect = (thread: Thread) => {
    console.log('Selected thread:', thread);
    // Navigate to thread view or show thread panel
    // You can use react-router or any navigation solution
  };

  return (
    <div className="h-screen flex">
      {/* Email list takes full height */}
      <div className="w-full max-w-2xl border-r border-gray-200">
        <EmailList 
          onThreadSelect={handleThreadSelect}
          className="h-full"
        />
      </div>
      
      {/* Thread viewer panel would go here */}
      <div className="flex-1 bg-gray-50">
        {/* Thread content */}
      </div>
    </div>
  );
};

// Integration with split panel
export const EmailListSplitView: React.FC = () => {
  const [selectedThread, setSelectedThread] = React.useState<Thread | null>(null);
  
  return (
    <div className="h-screen flex">
      <div className="w-1/3 min-w-[320px] max-w-[500px] border-r border-gray-200">
        <EmailList 
          onThreadSelect={setSelectedThread}
          className="h-full"
        />
      </div>
      
      <div className="flex-1">
        {selectedThread ? (
          <div className="p-4">
            <h2 className="text-lg font-semibold mb-2">{selectedThread.subject}</h2>
            <p className="text-gray-600">{selectedThread.snippet}</p>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center text-gray-500">
            Select an email to view
          </div>
        )}
      </div>
    </div>
  );
};