import React, { useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useHotkeys } from 'react-hotkeys-hook';
import { format, isToday, isYesterday, isThisWeek, isThisYear } from 'date-fns';
import { 
  Archive,
  Trash2,
  Mail,
  MailOpen,
  Star,
  Search,
  Filter,
  ChevronDown,
  Paperclip,
  Tag,
  Calendar,
  Check,
  X,
  MoreHorizontal,
  FolderOpen,
  Inbox
} from 'lucide-react';
import { useEmailStore } from '../../stores/emailStore';
import type { Thread, EmailFilter, EmailSort, EmailCategory } from '../../types/email';
import { formatShortDate } from '../../utils/date';
import { cn } from '../../utils';

// Types
interface EmailListProps {
  className?: string;
  onThreadSelect?: (thread: Thread) => void;
}

interface FilterState extends EmailFilter {
  showFilters: boolean;
}

// Avatar component
const Avatar: React.FC<{ 
  name?: string; 
  email: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({ name, email, size = 'md' }) => {
  const initials = name 
    ? name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
    : email[0].toUpperCase();
  
  const sizeClasses = {
    sm: 'w-8 h-8 text-xs',
    md: 'w-10 h-10 text-sm',
    lg: 'w-12 h-12 text-base'
  };
  
  // Generate consistent color based on email
  const colorIndex = email.charCodeAt(0) % 6;
  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-yellow-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500'
  ];
  
  return (
    <div className={cn(
      'rounded-full flex items-center justify-center text-white font-medium',
      sizeClasses[size],
      colors[colorIndex]
    )}>
      {initials}
    </div>
  );
};

// Thread item component
const ThreadItem: React.FC<{
  thread: Thread;
  isSelected: boolean;
  isChecked: boolean;
  onSelect: () => void;
  onCheck: () => void;
  showCheckbox: boolean;
  style?: React.CSSProperties;
}> = ({ 
  thread, 
  isSelected, 
  isChecked, 
  onSelect, 
  onCheck,
  showCheckbox,
  style 
}) => {
  const primaryParticipant = thread.participants.find(p => p.role === 'sender') || thread.participants[0];
  const hasAttachments = thread.messages.some(m => m.attachments.length > 0);
  
  const formatDate = (date: Date) => {
    if (isToday(date)) {
      return format(date, 'h:mm a');
    } else if (isYesterday(date)) {
      return 'Yesterday';
    } else if (isThisWeek(date)) {
      return format(date, 'EEE');
    } else if (isThisYear(date)) {
      return format(date, 'MMM d');
    } else {
      return format(date, 'MMM d, yyyy');
    }
  };
  
  return (
    <div
      style={style}
      className={cn(
        'group flex items-center px-4 py-3 border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors',
        isSelected && 'bg-blue-50 hover:bg-blue-100',
        thread.status.isUnread && 'bg-white'
      )}
      onClick={onSelect}
    >
      {/* Checkbox */}
      <div className="flex-shrink-0 mr-3">
        <input
          type="checkbox"
          checked={isChecked}
          onChange={(e) => {
            e.stopPropagation();
            onCheck();
          }}
          className={cn(
            'w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500',
            !showCheckbox && !isChecked && 'opacity-0 group-hover:opacity-100'
          )}
        />
      </div>
      
      {/* Star */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          // Handle star toggle
        }}
        className="flex-shrink-0 mr-3 text-gray-400 hover:text-yellow-500 transition-colors"
      >
        <Star className={cn('w-5 h-5', thread.status.isStarred && 'fill-yellow-500 text-yellow-500')} />
      </button>
      
      {/* Avatar */}
      <div className="flex-shrink-0 mr-3">
        <Avatar 
          name={primaryParticipant?.name} 
          email={primaryParticipant?.email || ''} 
        />
      </div>
      
      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between mb-1">
          <div className="flex items-center min-w-0">
            <span className={cn(
              'truncate mr-2',
              thread.status.isUnread ? 'font-semibold text-gray-900' : 'text-gray-700'
            )}>
              {primaryParticipant?.name || primaryParticipant?.email}
            </span>
            {thread.messageCount > 1 && (
              <span className="text-sm text-gray-500">({thread.messageCount})</span>
            )}
          </div>
          <span className="flex-shrink-0 text-sm text-gray-500">
            {formatDate(new Date(thread.lastMessageDate))}
          </span>
        </div>
        
        <div className="flex items-center">
          <span className={cn(
            'truncate mr-2',
            thread.status.isUnread ? 'font-medium text-gray-900' : 'text-gray-700'
          )}>
            {thread.subject}
          </span>
        </div>
        
        <div className="flex items-center mt-1">
          <span className="text-sm text-gray-500 truncate flex-1">
            {thread.snippet}
          </span>
          
          {/* Icons */}
          <div className="flex items-center gap-2 ml-2 flex-shrink-0">
            {hasAttachments && <Paperclip className="w-4 h-4 text-gray-400" />}
            {thread.status.isImportant && (
              <div className="w-2 h-2 bg-yellow-500 rounded-full" />
            )}
            {thread.category && (
              <span className="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full">
                {thread.category}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Filter bar component
const FilterBar: React.FC<{
  filters: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
  onClearFilters: () => void;
}> = ({ filters, onFilterChange, onClearFilters }) => {
  const categories: EmailCategory[] = [
    'Newsletter', 'Receipt', 'Marketing', 'Social', 'Updates', 
    'Personal', 'Work', 'Finance', 'Travel', 'Security'
  ];
  
  return (
    <div className="border-b border-gray-200 bg-white p-4 space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <button
            onClick={() => onFilterChange({ isUnread: !filters.isUnread })}
            className={cn(
              'flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors',
              filters.isUnread 
                ? 'bg-blue-100 text-blue-700' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            )}
          >
            <MailOpen className="w-4 h-4" />
            Unread
          </button>
          
          <button
            onClick={() => onFilterChange({ isStarred: !filters.isStarred })}
            className={cn(
              'flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors',
              filters.isStarred 
                ? 'bg-yellow-100 text-yellow-700' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            )}
          >
            <Star className="w-4 h-4" />
            Starred
          </button>
          
          <button
            onClick={() => onFilterChange({ hasAttachment: !filters.hasAttachment })}
            className={cn(
              'flex items-center gap-2 px-3 py-1.5 rounded-md text-sm font-medium transition-colors',
              filters.hasAttachment 
                ? 'bg-green-100 text-green-700' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            )}
          >
            <Paperclip className="w-4 h-4" />
            Has attachment
          </button>
        </div>
        
        <button
          onClick={onClearFilters}
          className="text-sm text-gray-500 hover:text-gray-700"
        >
          Clear filters
        </button>
      </div>
      
      {filters.showFilters && (
        <div className="space-y-3">
          {/* Categories */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">
              Categories
            </label>
            <div className="flex flex-wrap gap-2">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => {
                    const currentCategories = filters.category || [];
                    const newCategories = currentCategories.includes(category)
                      ? currentCategories.filter(c => c !== category)
                      : [...currentCategories, category];
                    onFilterChange({ category: newCategories });
                  }}
                  className={cn(
                    'px-3 py-1 rounded-full text-sm transition-colors',
                    filters.category?.includes(category)
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  )}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
          
          {/* Date range */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                From date
              </label>
              <input
                type="date"
                value={filters.dateRange?.start ? format(filters.dateRange.start, 'yyyy-MM-dd') : ''}
                onChange={(e) => {
                  const date = e.target.value ? new Date(e.target.value) : undefined;
                  onFilterChange({
                    dateRange: {
                      ...filters.dateRange,
                      start: date
                    }
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">
                To date
              </label>
              <input
                type="date"
                value={filters.dateRange?.end ? format(filters.dateRange.end, 'yyyy-MM-dd') : ''}
                onChange={(e) => {
                  const date = e.target.value ? new Date(e.target.value) : undefined;
                  onFilterChange({
                    dateRange: {
                      ...filters.dateRange,
                      end: date
                    }
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Bulk action toolbar
const BulkActionToolbar: React.FC<{
  selectedCount: number;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onArchive: () => void;
  onDelete: () => void;
  onMarkRead: () => void;
  onMarkUnread: () => void;
  onStar: () => void;
  onUnstar: () => void;
}> = ({ 
  selectedCount, 
  onSelectAll, 
  onClearSelection,
  onArchive,
  onDelete,
  onMarkRead,
  onMarkUnread,
  onStar,
  onUnstar
}) => {
  if (selectedCount === 0) return null;
  
  return (
    <div className="bg-blue-50 border-b border-blue-200 px-4 py-2 flex items-center justify-between">
      <div className="flex items-center gap-4">
        <span className="text-sm font-medium text-blue-700">
          {selectedCount} selected
        </span>
        <button
          onClick={onSelectAll}
          className="text-sm text-blue-600 hover:text-blue-700"
        >
          Select all
        </button>
        <button
          onClick={onClearSelection}
          className="text-sm text-blue-600 hover:text-blue-700"
        >
          Clear selection
        </button>
      </div>
      
      <div className="flex items-center gap-2">
        <button
          onClick={onArchive}
          className="p-2 hover:bg-blue-100 rounded-md transition-colors"
          title="Archive"
        >
          <Archive className="w-4 h-4 text-gray-700" />
        </button>
        <button
          onClick={onDelete}
          className="p-2 hover:bg-blue-100 rounded-md transition-colors"
          title="Delete"
        >
          <Trash2 className="w-4 h-4 text-gray-700" />
        </button>
        <button
          onClick={onMarkRead}
          className="p-2 hover:bg-blue-100 rounded-md transition-colors"
          title="Mark as read"
        >
          <MailOpen className="w-4 h-4 text-gray-700" />
        </button>
        <button
          onClick={onMarkUnread}
          className="p-2 hover:bg-blue-100 rounded-md transition-colors"
          title="Mark as unread"
        >
          <Mail className="w-4 h-4 text-gray-700" />
        </button>
        <button
          onClick={onStar}
          className="p-2 hover:bg-blue-100 rounded-md transition-colors"
          title="Star"
        >
          <Star className="w-4 h-4 text-gray-700" />
        </button>
      </div>
    </div>
  );
};

// Main EmailList component
export const EmailList: React.FC<EmailListProps> = ({ 
  className,
  onThreadSelect
}) => {
  const queryClient = useQueryClient();
  const parentRef = useRef<HTMLDivElement>(null);
  
  // Email store
  const {
    threads,
    selectedThreadIds,
    isSelectMode,
    filter,
    sort,
    isLoading,
    error,
    currentPage,
    hasMore,
    fetchThreads,
    toggleThreadSelection,
    selectAllThreads,
    clearSelection,
    setSelectMode,
    markAsRead,
    star,
    archive,
    deleteThreads,
    setFilter,
    clearFilter,
    setSort,
    search,
    refresh
  } = useEmailStore();
  
  // Local state
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<FilterState>({
    ...filter,
    showFilters: false
  });
  const [focusedIndex, setFocusedIndex] = useState(0);
  
  // Fetch threads with React Query
  const { data, isLoading: isQueryLoading, refetch } = useQuery({
    queryKey: ['threads', filter, sort, currentPage],
    queryFn: fetchThreads,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // 1 minute
  });
  
  // Virtual scrolling
  const virtualizer = useVirtualizer({
    count: threads.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 88, // Estimated height of each thread item
    overscan: 5,
  });
  
  // Keyboard shortcuts
  useHotkeys('j', () => {
    setFocusedIndex(prev => Math.min(prev + 1, threads.length - 1));
  }, [threads.length]);
  
  useHotkeys('k', () => {
    setFocusedIndex(prev => Math.max(prev - 1, 0));
  });
  
  useHotkeys('x', () => {
    if (threads[focusedIndex]) {
      toggleThreadSelection(threads[focusedIndex].id);
    }
  }, [focusedIndex, threads]);
  
  useHotkeys('e', () => {
    const selected = Array.from(selectedThreadIds);
    if (selected.length > 0) {
      handleArchive();
    }
  }, [selectedThreadIds]);
  
  useHotkeys('#', () => {
    const selected = Array.from(selectedThreadIds);
    if (selected.length > 0) {
      handleDelete();
    }
  }, [selectedThreadIds]);
  
  useHotkeys('cmd+a, ctrl+a', (e) => {
    e.preventDefault();
    selectAllThreads();
  });
  
  useHotkeys('escape', () => {
    clearSelection();
  });
  
  // Handlers
  const handleSearch = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    search(searchQuery);
  }, [searchQuery, search]);
  
  const handleFilterChange = useCallback((newFilters: Partial<FilterState>) => {
    const updated = { ...filters, ...newFilters };
    setFilters(updated);
    const { showFilters, ...apiFilters } = updated;
    setFilter(apiFilters);
  }, [filters, setFilter]);
  
  const handleClearFilters = useCallback(() => {
    setFilters({ showFilters: filters.showFilters });
    clearFilter();
  }, [filters.showFilters, clearFilter]);
  
  const handleArchive = useCallback(async () => {
    const selected = Array.from(selectedThreadIds);
    if (selected.length > 0) {
      await archive(selected);
      clearSelection();
      refetch();
    }
  }, [selectedThreadIds, archive, clearSelection, refetch]);
  
  const handleDelete = useCallback(async () => {
    const selected = Array.from(selectedThreadIds);
    if (selected.length > 0) {
      await deleteThreads(selected);
      clearSelection();
      refetch();
    }
  }, [selectedThreadIds, deleteThreads, clearSelection, refetch]);
  
  const handleMarkRead = useCallback(async () => {
    const selected = Array.from(selectedThreadIds);
    if (selected.length > 0) {
      await markAsRead(selected, true);
      refetch();
    }
  }, [selectedThreadIds, markAsRead, refetch]);
  
  const handleMarkUnread = useCallback(async () => {
    const selected = Array.from(selectedThreadIds);
    if (selected.length > 0) {
      await markAsRead(selected, false);
      refetch();
    }
  }, [selectedThreadIds, markAsRead, refetch]);
  
  const handleStar = useCallback(async () => {
    const selected = Array.from(selectedThreadIds);
    if (selected.length > 0) {
      await star(selected, true);
      refetch();
    }
  }, [selectedThreadIds, star, refetch]);
  
  const handleUnstar = useCallback(async () => {
    const selected = Array.from(selectedThreadIds);
    if (selected.length > 0) {
      await star(selected, false);
      refetch();
    }
  }, [selectedThreadIds, star, refetch]);
  
  const handleThreadSelect = useCallback((thread: Thread) => {
    if (onThreadSelect) {
      onThreadSelect(thread);
    }
  }, [onThreadSelect]);
  
  // Empty state
  if (!isLoading && !isQueryLoading && threads.length === 0) {
    return (
      <div className={cn('flex flex-col h-full bg-white', className)}>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <Inbox className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No emails found
            </h3>
            <p className="text-gray-500">
              {Object.keys(filter).length > 0 
                ? 'Try adjusting your filters'
                : 'Your inbox is empty'}
            </p>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className={cn('flex flex-col h-full bg-white', className)}>
      {/* Search bar */}
      <div className="border-b border-gray-200 px-4 py-3">
        <form onSubmit={handleSearch} className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search emails..."
            className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <button
            type="button"
            onClick={() => setFilters(prev => ({ ...prev, showFilters: !prev.showFilters }))}
            className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <Filter className="w-5 h-5" />
          </button>
        </form>
      </div>
      
      {/* Filters */}
      {filters.showFilters && (
        <FilterBar
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={handleClearFilters}
        />
      )}
      
      {/* Bulk actions */}
      <BulkActionToolbar
        selectedCount={selectedThreadIds.size}
        onSelectAll={selectAllThreads}
        onClearSelection={clearSelection}
        onArchive={handleArchive}
        onDelete={handleDelete}
        onMarkRead={handleMarkRead}
        onMarkUnread={handleMarkUnread}
        onStar={handleStar}
        onUnstar={handleUnstar}
      />
      
      {/* Sorting */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200">
        <span className="text-sm text-gray-500">
          {threads.length} conversations
        </span>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-500">Sort by:</span>
          <select
            value={`${sort.field}-${sort.order}`}
            onChange={(e) => {
              const [field, order] = e.target.value.split('-') as [EmailSort['field'], EmailSort['order']];
              setSort({ field, order });
            }}
            className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="date-desc">Newest first</option>
            <option value="date-asc">Oldest first</option>
            <option value="subject-asc">Subject A-Z</option>
            <option value="subject-desc">Subject Z-A</option>
            <option value="from-asc">Sender A-Z</option>
            <option value="from-desc">Sender Z-A</option>
            <option value="importance-desc">Most important</option>
          </select>
        </div>
      </div>
      
      {/* Thread list with virtual scrolling */}
      <div ref={parentRef} className="flex-1 overflow-auto">
        {isLoading || isQueryLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Loading emails...</p>
            </div>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <X className="w-16 h-16 text-red-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Error loading emails
              </h3>
              <p className="text-gray-500 mb-4">{error}</p>
              <button
                onClick={() => refetch()}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                Try again
              </button>
            </div>
          </div>
        ) : (
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: '100%',
              position: 'relative',
            }}
          >
            {virtualizer.getVirtualItems().map((virtualItem) => {
              const thread = threads[virtualItem.index];
              const isChecked = selectedThreadIds.has(thread.id);
              const isFocused = focusedIndex === virtualItem.index;
              
              return (
                <ThreadItem
                  key={thread.id}
                  thread={thread}
                  isSelected={isFocused}
                  isChecked={isChecked}
                  onSelect={() => handleThreadSelect(thread)}
                  onCheck={() => toggleThreadSelection(thread.id)}
                  showCheckbox={isSelectMode || selectedThreadIds.size > 0}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: `${virtualItem.size}px`,
                    transform: `translateY(${virtualItem.start}px)`,
                  }}
                />
              );
            })}
          </div>
        )}
      </div>
      
      {/* Load more */}
      {hasMore && !isLoading && (
        <div className="border-t border-gray-200 px-4 py-3 text-center">
          <button
            onClick={() => fetchThreads({ page: currentPage + 1 })}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            Load more
          </button>
        </div>
      )}
    </div>
  );
};

export default EmailList;