# Email List Components

A sophisticated, production-ready email list system with Gmail-like functionality, built with React, TypeScript, and modern libraries. This package includes both the `EmailList` container component and the individual `EmailListItem` component for maximum flexibility.

## Features

### Core Features
- **Thread-based grouping**: Emails are displayed as conversation threads
- **Virtual scrolling**: Efficient rendering of large email lists using @tanstack/react-virtual
- **Real-time updates**: Automatic refresh every minute with manual refresh option
- **Responsive design**: Works seamlessly on mobile and desktop devices

### Filtering & Search
- **Advanced filtering**:
  - Read/unread status
  - Starred emails
  - Emails with attachments
  - Category filtering (Newsletter, Receipt, Marketing, etc.)
  - Date range filtering
- **Full-text search**: Search across subject, body, and sender
- **Quick filters**: One-click filters for common email states

### Selection & Bulk Actions
- **Multi-selection**: Checkbox for each email with visual selection state
- **Select all**: Quick select/deselect all visible emails
- **Bulk actions toolbar**:
  - Archive selected
  - Delete selected
  - Mark as read/unread
  - Star/unstar
  - Apply labels

### Keyboard Shortcuts
- `j` - Navigate to next email
- `k` - Navigate to previous email
- `x` - Toggle selection of focused email
- `e` - Archive selected emails
- `#` - Delete selected emails
- `Cmd/Ctrl + A` - Select all emails
- `Escape` - Clear selection

### UI/UX Features
- **Avatar display**: Color-coded avatars with initials
- **Smart date formatting**: "3:44 PM" for today, "Yesterday", "Mon", "Jul 5"
- **Thread indicators**: Shows message count for conversations
- **Attachment indicators**: Visual indicator for emails with attachments
- **Unread indicators**: Bold text and background highlighting
- **Category badges**: Visual categorization of emails
- **Loading states**: Skeleton loading and error states
- **Empty states**: Helpful messages when no emails are found

## Usage

```tsx
import { EmailList } from '@/components/email-list/EmailList';

function MyEmailApp() {
  const handleThreadSelect = (thread: Thread) => {
    // Handle thread selection
    console.log('Selected thread:', thread);
  };

  return (
    <EmailList 
      onThreadSelect={handleThreadSelect}
      className="h-full"
    />
  );
}
```

## Props

| Prop | Type | Description |
|------|------|-------------|
| `className` | `string?` | Additional CSS classes to apply to the container |
| `onThreadSelect` | `(thread: Thread) => void` | Callback when a thread is selected |

## Integration with Zustand Store

The component automatically integrates with the `emailStore` for state management:

```tsx
// The component uses these store methods internally:
const {
  threads,              // Email threads data
  fetchThreads,        // Fetch threads from API
  toggleThreadSelection, // Toggle thread selection
  markAsRead,          // Mark threads as read/unread
  archive,             // Archive threads
  deleteThreads,       // Delete threads
  setFilter,           // Apply filters
  search,              // Search emails
  // ... and more
} = useEmailStore();
```

## Customization

### Styling
The component uses Tailwind CSS classes and can be customized through:
- The `className` prop for container styling
- CSS variables for theme customization
- Tailwind configuration for design system integration

### Virtual Scrolling Configuration
The virtual scroller can be configured by modifying:
```tsx
const virtualizer = useVirtualizer({
  count: threads.length,
  getScrollElement: () => parentRef.current,
  estimateSize: () => 88, // Adjust based on your row height
  overscan: 5, // Number of items to render outside visible area
});
```

## Performance Considerations

1. **Virtual Scrolling**: Only visible items are rendered, making it efficient for large lists
2. **Memoization**: Thread items are memoized to prevent unnecessary re-renders
3. **Debounced Search**: Search input is debounced to reduce API calls
4. **Optimistic Updates**: UI updates immediately while API calls happen in background
5. **Query Caching**: Uses TanStack Query for intelligent caching and background refetching

## API Integration

The component expects the following API endpoints:

- `GET /api/threads` - Fetch email threads with pagination and filtering
- `PATCH /api/threads/:id/read` - Mark thread as read/unread
- `PATCH /api/threads/:id/star` - Star/unstar thread
- `POST /api/threads/:id/archive` - Archive thread
- `DELETE /api/threads/:id` - Delete thread
- `PATCH /api/threads/:id/categorize` - Categorize thread

## TypeScript Types

```tsx
interface Thread {
  id: string;
  messages: ParsedMessage[];
  snippet: string;
  subject: string;
  category?: EmailCategory;
  labels: string[];
  participants: EmailParticipant[];
  lastMessageDate: Date;
  messageCount: number;
  unreadCount: number;
  status: {
    isUnread: boolean;
    isImportant: boolean;
    isStarred: boolean;
    // ... more status flags
  };
}
```

## Dependencies

- React 19+
- TypeScript 5+
- @tanstack/react-virtual
- @tanstack/react-query
- react-hotkeys-hook
- date-fns
- lucide-react
- zustand
- tailwindcss

# EmailListItem Component

A comprehensive individual email list item component that displays email threads with Gmail-style design and interaction patterns.

## Features

### Visual Design
- **Gmail-style layout**: Clean, modern appearance matching Gmail's design language
- **Unread indicators**: Bold text and blue accent for unread emails
- **Colored avatars**: Auto-generated avatar circles with sender initials
- **Hover states**: Smooth transitions and interactive feedback
- **Selection highlighting**: Visual feedback for selected items

### Email Metadata Display  
- **Sender information**: Name and email with fallback handling
- **Subject line**: Truncated with ellipsis for long subjects
- **Email snippet**: First 100 characters preview
- **Relative timestamps**: "3:44 PM", "Yesterday", "Jul 15" format
- **Thread counts**: Message count indicator for conversations
- **Attachment icons**: Visual indicator for emails with attachments
- **Priority indicators**: High/medium priority flags
- **Category badges**: Color-coded labels for email categories

### Interactive Elements
- **Selection checkbox**: Appears on hover or when in select mode
- **Star button**: Toggle with smooth animation and visual feedback
- **Context menu**: Right-click menu with common actions
- **Click to select**: Single click to select email thread
- **Keyboard navigation**: Full keyboard accessibility support

### Labels and Categories
- **Category badges**: Newsletter, Receipt, Marketing, Work, etc.
- **Custom labels**: User-defined labels with color coding
- **Priority indicators**: Visual flags for important emails
- **Multiple label support**: Shows first 2 labels plus count indicator

### Responsive Design
- **Compact mode**: Reduced padding and font sizes for mobile
- **Touch-friendly**: Appropriate touch targets for mobile devices
- **Responsive layout**: Adapts to different screen sizes

### Accessibility
- **ARIA labels**: Comprehensive screen reader support
- **Keyboard focus**: Clear focus indicators and navigation
- **High contrast**: Supports system high contrast modes
- **Semantic HTML**: Proper role and state attributes

## Usage

```tsx
import { EmailListItem } from '@/components/email-list/EmailListItem';

function CustomEmailList() {
  const [selectedThread, setSelectedThread] = useState<string | null>(null);
  const [selectedThreads, setSelectedThreads] = useState<Set<string>>(new Set());

  return (
    <div className="divide-y divide-gray-100">
      {threads.map((thread) => (
        <EmailListItem
          key={thread.id}
          thread={thread}
          isSelected={selectedThread === thread.id}
          isSelectMode={false}
          onSelect={setSelectedThread}
          onToggleSelect={(id) => {
            const newSet = new Set(selectedThreads);
            if (newSet.has(id)) {
              newSet.delete(id);
            } else {
              newSet.add(id);
            }
            setSelectedThreads(newSet);
          }}
          compact={false}
        />
      ))}
    </div>
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `thread` | `Thread` | Required | The email thread data to display |
| `isSelected` | `boolean` | `false` | Whether this item is currently selected |
| `isSelectMode` | `boolean` | `false` | Whether the list is in selection mode |
| `onSelect` | `(threadId: string) => void` | - | Callback when item is clicked |
| `onToggleSelect` | `(threadId: string) => void` | - | Callback when selection checkbox is toggled |
| `compact` | `boolean` | `false` | Whether to use compact layout |
| `className` | `string` | - | Additional CSS classes |

## Keyboard Navigation

When an item is selected, these keyboard shortcuts are available:

- `Enter` / `Space` - Open the thread
- `x` - Toggle selection checkbox
- `s` - Toggle star status

## Context Menu Actions

Right-clicking an item shows a context menu with:

- Mark as read/unread
- Add/remove star
- Archive
- Delete

## Integration with EmailStore

The component automatically integrates with the email store for actions:

```tsx
const { markAsRead, star, archive, deleteThreads } = useEmailStore();
```

## Styling and Customization

### Category Colors
Categories are automatically color-coded:
- Newsletter: Blue
- Receipt: Green  
- Marketing: Purple
- Work: Orange
- Personal: Indigo
- Finance: Emerald
- Security: Red
- And more...

### Avatar Colors
Sender avatars use deterministic colors based on the sender's name, ensuring consistent coloring across sessions.

### Animation
- Smooth hover transitions
- Star animation on toggle
- Context menu fade in/out
- Selection state changes

## Best Practices

1. **Error Handling**: Always handle errors gracefully with user-friendly messages
2. **Loading States**: Show appropriate loading indicators during data fetching
3. **Accessibility**: Ensure keyboard navigation and screen reader support
4. **Performance**: Use virtual scrolling for large lists
5. **Real-time Updates**: Implement WebSocket or polling for real-time email updates