# EmailListItem Component Usage Guide

## Quick Start

```tsx
import { EmailListItem } from './components/email-list/EmailListItem';
import { EmailListItemDemo } from './components/email-list/EmailListExample';

// For a quick demo, use the demo component
<EmailListItemDemo />
```

## Basic Implementation

```tsx
import React, { useState } from 'react';
import { EmailListItem } from './components/email-list/EmailListItem';
import { useEmailStore } from './stores/emailStore';

function EmailInbox() {
  const { threads } = useEmailStore();
  const [selectedThread, setSelectedThread] = useState<string | null>(null);

  return (
    <div className="divide-y divide-gray-100">
      {threads.map((thread) => (
        <EmailListItem
          key={thread.id}
          thread={thread}
          isSelected={selectedThread === thread.id}
          onSelect={(threadId) => setSelectedThread(threadId)}
        />
      ))}
    </div>
  );
}
```

## With Selection Mode

```tsx
function EmailListWithSelection() {
  const [selectedThread, setSelectedThread] = useState<string | null>(null);
  const [selectedThreads, setSelectedThreads] = useState<Set<string>>(new Set());
  const [selectMode, setSelectMode] = useState(false);

  const handleToggleSelect = (threadId: string) => {
    const newSet = new Set(selectedThreads);
    if (newSet.has(threadId)) {
      newSet.delete(threadId);
    } else {
      newSet.add(threadId);
    }
    setSelectedThreads(newSet);
  };

  return (
    <div>
      <div className="p-4 border-b">
        <button 
          onClick={() => setSelectMode(!selectMode)}
          className="px-3 py-2 border rounded-md"
        >
          {selectMode ? 'Exit Select Mode' : 'Select Mode'}
        </button>
        {selectedThreads.size > 0 && (
          <span className="ml-4 text-sm text-gray-600">
            {selectedThreads.size} selected
          </span>
        )}
      </div>
      
      <div className="divide-y divide-gray-100">
        {threads.map((thread) => (
          <EmailListItem
            key={thread.id}
            thread={thread}
            isSelected={selectedThread === thread.id}
            isSelectMode={selectMode}
            onSelect={setSelectedThread}
            onToggleSelect={handleToggleSelect}
          />
        ))}
      </div>
    </div>
  );
}
```

## Compact Mobile View

```tsx
function MobileEmailList() {
  const isMobile = window.innerWidth < 768;
  
  return (
    <div className="divide-y divide-gray-100">
      {threads.map((thread) => (
        <EmailListItem
          key={thread.id}
          thread={thread}
          compact={isMobile}
          onSelect={handleSelect}
        />
      ))}
    </div>
  );
}
```

## Integration with Router

```tsx
import { useNavigate } from '@tanstack/react-router';

function EmailListWithRouting() {
  const navigate = useNavigate();

  const handleThreadSelect = (threadId: string) => {
    navigate({ to: `/mail/thread/${threadId}` });
  };

  return (
    <div className="divide-y divide-gray-100">
      {threads.map((thread) => (
        <EmailListItem
          key={thread.id}
          thread={thread}
          onSelect={handleThreadSelect}
        />
      ))}
    </div>
  );
}
```

## Virtual Scrolling for Performance

```tsx
import { useVirtualizer } from '@tanstack/react-virtual';

function VirtualizedEmailList() {
  const parentRef = useRef<HTMLDivElement>(null);
  const { threads } = useEmailStore();

  const virtualizer = useVirtualizer({
    count: threads.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 88, // Estimated height of each item
    overscan: 5,
  });

  return (
    <div ref={parentRef} className="h-full overflow-auto">
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`,
            }}
          >
            <EmailListItem
              thread={threads[virtualItem.index]}
              onSelect={handleSelect}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Custom Styling

```tsx
// Custom CSS classes for specific styling
<EmailListItem
  thread={thread}
  onSelect={handleSelect}
  className="border-l-4 border-blue-500 bg-blue-50"
/>

// Or use conditional styling
<EmailListItem
  thread={thread}
  onSelect={handleSelect}
  className={cn(
    'transition-all duration-200',
    thread.status.isImportant && 'border-l-4 border-red-500',
    thread.category === 'Work' && 'bg-orange-50'
  )}
/>
```

## Event Handling

```tsx
function EmailListWithCustomHandlers() {
  const handleSelect = (threadId: string) => {
    console.log('Thread selected:', threadId);
    // Custom logic here
  };

  const handleToggleSelect = (threadId: string) => {
    console.log('Thread selection toggled:', threadId);
    // Custom logic here
  };

  // The component also handles these events internally:
  // - Right-click context menu
  // - Star toggling
  // - Keyboard navigation
  // - Hover states

  return (
    <EmailListItem
      thread={thread}
      onSelect={handleSelect}
      onToggleSelect={handleToggleSelect}
    />
  );
}
```

## Accessibility Features

The component includes:

- **ARIA labels** for screen readers
- **Keyboard navigation** (Enter, Space, x, s keys)
- **Focus management** with visible focus indicators  
- **Semantic HTML** with proper roles and states
- **High contrast** support

## Performance Tips

1. **Use React.memo()** for the parent component if needed
2. **Virtual scrolling** for large lists (>100 items)
3. **Debounce** selection changes if needed
4. **Memoize** callback functions to prevent unnecessary re-renders

```tsx
const handleSelect = useCallback((threadId: string) => {
  setSelectedThread(threadId);
}, []);

const MemoizedEmailListItem = React.memo(EmailListItem);
```

## Error Handling

```tsx
function EmailListWithErrorHandling() {
  const [error, setError] = useState<string | null>(null);

  const handleSelect = async (threadId: string) => {
    try {
      setError(null);
      // Your selection logic
    } catch (err) {
      setError('Failed to select email');
      console.error(err);
    }
  };

  if (error) {
    return <div className="p-4 text-red-600">{error}</div>;
  }

  return (
    <EmailListItem
      thread={thread}
      onSelect={handleSelect}
    />
  );
}
```