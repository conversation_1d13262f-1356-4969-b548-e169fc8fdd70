import React, { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Star, 
  Paperclip, 
  AlertCircle, 
  Flag,
  MoreVertical,
  Clock,
  Archive,
  Trash2,
  Tag
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import type { Thread, EmailCategory } from '../../types/email';
import { useEmailStore } from '../../stores/emailStore';
import { cn, truncate } from '../../utils';
import { formatShortDate, isToday, isYesterday } from '../../utils/date';

interface EmailListItemProps {
  thread: Thread;
  isSelected?: boolean;
  isSelectMode?: boolean;
  onSelect?: (threadId: string) => void;
  onToggleSelect?: (threadId: string) => void;
  compact?: boolean;
  className?: string;
}

// Color mappings for categories and priority
const categoryColors: Record<EmailCategory, string> = {
  Newsletter: 'bg-blue-100 text-blue-800 border-blue-200',
  Receipt: 'bg-green-100 text-green-800 border-green-200',
  Marketing: 'bg-purple-100 text-purple-800 border-purple-200',
  Social: 'bg-pink-100 text-pink-800 border-pink-200',
  Updates: 'bg-cyan-100 text-cyan-800 border-cyan-200',
  Personal: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  Work: 'bg-orange-100 text-orange-800 border-orange-200',
  Finance: 'bg-emerald-100 text-emerald-800 border-emerald-200',
  Travel: 'bg-teal-100 text-teal-800 border-teal-200',
  Security: 'bg-red-100 text-red-800 border-red-200',
  Notification: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  Important: 'bg-red-100 text-red-800 border-red-200',
  Spam: 'bg-gray-100 text-gray-800 border-gray-200',
  Other: 'bg-gray-100 text-gray-800 border-gray-200'
};

const priorityColors = {
  high: 'text-red-600',
  medium: 'text-yellow-600',
  low: 'text-green-600'
};

// Generate avatar color based on sender name
const getAvatarColor = (name: string): string => {
  const colors = [
    'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
    'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-gray-500'
  ];
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
};

// Get initials from sender name
const getInitials = (name: string): string => {
  if (!name) return '?';
  const parts = name.trim().split(' ');
  if (parts.length === 1) return parts[0].charAt(0).toUpperCase();
  return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
};

// Format relative timestamp
const formatTimestamp = (date: Date): string => {
  if (isToday(date)) {
    return date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
  } else if (isYesterday(date)) {
    return 'Yesterday';
  } else {
    return formatShortDate(date);
  }
};

// Context menu component
const ContextMenu: React.FC<{
  thread: Thread;
  isOpen: boolean;
  onClose: () => void;
  position: { x: number; y: number };
}> = ({ thread, isOpen, onClose, position }) => {
  const { markAsRead, star, archive, deleteThreads, categorize } = useEmailStore();
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleAction = async (action: () => Promise<void>) => {
    try {
      await action();
      onClose();
    } catch (error) {
      console.error('Action failed:', error);
      onClose();
    }
  };

  return (
    <motion.div
      ref={menuRef}
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      transition={{ duration: 0.1 }}
      className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-48"
      style={{ left: position.x, top: position.y }}
    >
      <button
        onClick={() => handleAction(() => markAsRead([thread.id], !thread.status.isUnread))}
        className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
      >
        <Clock className="h-4 w-4" />
        Mark as {thread.status.isUnread ? 'read' : 'unread'}
      </button>
      
      <button
        onClick={() => handleAction(() => star([thread.id], !thread.status.isStarred))}
        className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
      >
        <Star className="h-4 w-4" />
        {thread.status.isStarred ? 'Remove star' : 'Add star'}
      </button>
      
      <div className="border-t border-gray-100 my-1" />
      
      <button
        onClick={() => handleAction(() => archive([thread.id]))}
        className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
      >
        <Archive className="h-4 w-4" />
        Archive
      </button>
      
      <button
        onClick={() => handleAction(() => deleteThreads([thread.id]))}
        className="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2 text-red-600"
      >
        <Trash2 className="h-4 w-4" />
        Delete
      </button>
    </motion.div>
  );
};

export const EmailListItem: React.FC<EmailListItemProps> = ({
  thread,
  isSelected = false,
  isSelectMode = false,
  onSelect,
  onToggleSelect,
  compact = false,
  className
}) => {
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [isHovered, setIsHovered] = useState(false);
  const { selectedThreadIds, toggleThreadSelection, star } = useEmailStore();
  const itemRef = useRef<HTMLDivElement>(null);

  const isThreadSelected = selectedThreadIds.has(thread.id);
  const primarySender = thread.participants.find(p => p.role === 'sender') || thread.participants[0];
  const senderName = primarySender?.name || primarySender?.email || 'Unknown';
  const senderEmail = primarySender?.email || '';
  const hasAttachments = thread.messages.some(m => m.attachments.length > 0);

  // Handle click events
  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    if (isSelectMode) {
      onToggleSelect?.(thread.id);
    } else {
      onSelect?.(thread.id);
    }
  }, [isSelectMode, onSelect, onToggleSelect, thread.id]);

  const handleCheckboxChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    toggleThreadSelection(thread.id);
  }, [toggleThreadSelection, thread.id]);

  const handleStarClick = useCallback(async (e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await star([thread.id], !thread.status.isStarred);
    } catch (error) {
      console.error('Failed to toggle star:', error);
    }
  }, [star, thread.id, thread.status.isStarred]);

  const handleRightClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    setContextMenuPosition({ x: e.clientX, y: e.clientY });
    setShowContextMenu(true);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isSelected) return;

      switch (e.key) {
        case 'Enter':
        case ' ':
          e.preventDefault();
          onSelect?.(thread.id);
          break;
        case 'x':
          e.preventDefault();
          toggleThreadSelection(thread.id);
          break;
        case 's':
          e.preventDefault();
          star([thread.id], !thread.status.isStarred);
          break;
      }
    };

    if (isSelected) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isSelected, onSelect, toggleThreadSelection, star, thread.id, thread.status.isStarred]);

  const avatarColor = getAvatarColor(senderName);
  const initials = getInitials(senderName);
  const timestamp = formatTimestamp(thread.lastMessageDate);

  return (
    <>
      <motion.div
        ref={itemRef}
        layout
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2 }}
        className={cn(
          'group relative bg-white hover:bg-gray-50 transition-colors duration-150',
          'border-b border-gray-100 cursor-pointer',
          'focus-within:bg-blue-50 focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-inset',
          isSelected && 'bg-blue-50 ring-2 ring-blue-500 ring-inset',
          isThreadSelected && 'bg-blue-100',
          thread.status.isUnread && 'bg-blue-50/30',
          compact ? 'py-2' : 'py-3',
          className
        )}
        onClick={handleClick}
        onContextMenu={handleRightClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        role="button"
        tabIndex={0}
        aria-label={`Email from ${senderName}: ${thread.subject}`}
        aria-selected={isSelected}
      >
        <div className={cn('flex items-center gap-3', compact ? 'px-3' : 'px-4')}>
          {/* Selection checkbox */}
          <AnimatePresence mode="wait">
            {(isSelectMode || isHovered || isThreadSelected) && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.1 }}
              >
                <input
                  type="checkbox"
                  checked={isThreadSelected}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  aria-label={`Select email from ${senderName}`}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Sender avatar */}
          <div className={cn(
            'flex-shrink-0 rounded-full flex items-center justify-center text-white text-sm font-medium',
            avatarColor,
            compact ? 'h-8 w-8' : 'h-10 w-10'
          )}>
            {initials}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              {/* Sender and subject line */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className={cn(
                    'text-sm truncate',
                    thread.status.isUnread ? 'font-semibold text-gray-900' : 'font-normal text-gray-700'
                  )}>
                    {senderName}
                  </span>
                  
                  {/* Message count for threads */}
                  {thread.messageCount > 1 && (
                    <span className="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-full">
                      {thread.messageCount}
                    </span>
                  )}
                  
                  {/* Priority indicator */}
                  {thread.priority && thread.priority !== 'low' && (
                    <Flag className={cn('h-3 w-3', priorityColors[thread.priority])} />
                  )}
                  
                  {/* Important indicator */}
                  {thread.status.isImportant && (
                    <AlertCircle className="h-3 w-3 text-orange-500" />
                  )}
                  
                  {/* Attachment indicator */}
                  {hasAttachments && (
                    <Paperclip className="h-3 w-3 text-gray-400" />
                  )}
                </div>
                
                {/* Subject */}
                <div className={cn(
                  'text-sm truncate mt-0.5',
                  thread.status.isUnread ? 'font-medium text-gray-900' : 'text-gray-600'
                )}>
                  {thread.subject || '(no subject)'}
                </div>
                
                {/* Snippet - only show in non-compact mode */}
                {!compact && (
                  <div className="text-xs text-gray-500 mt-1 line-clamp-1">
                    {truncate(thread.snippet || '', 100)}
                  </div>
                )}
              </div>

              {/* Right side - timestamp, star, actions */}
              <div className="flex items-center gap-2 ml-4 flex-shrink-0">
                {/* Category badge */}
                {thread.category && (
                  <span className={cn(
                    'text-xs px-2 py-0.5 rounded-full border',
                    categoryColors[thread.category]
                  )}>
                    {thread.category}
                  </span>
                )}
                
                {/* Labels */}
                {thread.labels.slice(0, 2).map((label) => (
                  <span
                    key={label}
                    className="text-xs px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 border border-gray-200"
                  >
                    {label}
                  </span>
                ))}
                
                {thread.labels.length > 2 && (
                  <span className="text-xs text-gray-400">
                    +{thread.labels.length - 2}
                  </span>
                )}

                {/* Timestamp */}
                <span className={cn(
                  'text-xs whitespace-nowrap',
                  thread.status.isUnread ? 'font-medium text-gray-900' : 'text-gray-500'
                )}>
                  {timestamp}
                </span>

                {/* Star button */}
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleStarClick}
                  className={cn(
                    'p-1 rounded hover:bg-gray-200 transition-colors',
                    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1'
                  )}
                  aria-label={thread.status.isStarred ? 'Remove star' : 'Add star'}
                >
                  <Star
                    className={cn(
                      'h-4 w-4 transition-colors',
                      thread.status.isStarred
                        ? 'fill-yellow-400 text-yellow-400'
                        : 'text-gray-400 hover:text-yellow-400'
                    )}
                  />
                </motion.button>

                {/* More actions button */}
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setContextMenuPosition({ 
                      x: e.currentTarget.getBoundingClientRect().right - 200,
                      y: e.currentTarget.getBoundingClientRect().bottom 
                    });
                    setShowContextMenu(true);
                  }}
                  className={cn(
                    'p-1 rounded hover:bg-gray-200 transition-colors opacity-0 group-hover:opacity-100',
                    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 focus:opacity-100'
                  )}
                  aria-label="More actions"
                >
                  <MoreVertical className="h-4 w-4 text-gray-400" />
                </motion.button>
              </div>
            </div>
          </div>
        </div>

        {/* Unread indicator */}
        {thread.status.isUnread && (
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-blue-500" />
        )}

        {/* Loading overlay for pending actions */}
        <AnimatePresence>
          {thread.status.isUnread && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="absolute inset-0 bg-blue-500/5 pointer-events-none"
            />
          )}
        </AnimatePresence>
      </motion.div>

      {/* Context menu */}
      <AnimatePresence>
        {showContextMenu && (
          <ContextMenu
            thread={thread}
            isOpen={showContextMenu}
            onClose={() => setShowContextMenu(false)}
            position={contextMenuPosition}
          />
        )}
      </AnimatePresence>
    </>
  );
};

export default EmailListItem;