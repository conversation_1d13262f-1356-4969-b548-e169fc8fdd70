import type { 
  ApiRequest, 
  ApiResponse, 
  ApiError, 
  ApiConfig,
  NetworkError,
  RateLimitError,
  AuthError,
  ValidationError,
  FieldError
} from '../types/api'
import { useAuthStore } from '../stores/authStore'

// Default configuration
const defaultConfig: ApiConfig = {
  baseUrl: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000, // 30 seconds
  retries: 3,
  headers: {
    'Content-Type': 'application/json',
  },
  cache: {
    enabled: true,
    duration: 300, // 5 minutes
    maxSize: 100,
    exclude: []
  },
  rateLimit: {
    maxRequests: 100,
    perMilliseconds: 60000, // 1 minute
    retryAfter: 60000
  },
}

// Request queue for rate limiting
const requestQueue: Array<() => Promise<any>> = []
const requestTimestamps: number[] = []

// Cache storage
const cache = new Map<string, { data: any; timestamp: number }>()

// Merge configurations
function mergeConfig(custom?: Partial<ApiConfig>): ApiConfig {
  return {
    ...defaultConfig,
    ...custom,
    headers: {
      ...defaultConfig.headers,
      ...custom?.headers,
    },
    cache: {
      enabled: custom?.cache?.enabled ?? defaultConfig.cache.enabled,
      duration: custom?.cache?.duration ?? defaultConfig.cache.duration,
      maxSize: custom?.cache?.maxSize ?? defaultConfig.cache.maxSize,
      exclude: custom?.cache?.exclude ?? defaultConfig.cache.exclude
    },
    rateLimit: {
      maxRequests: custom?.rateLimit?.maxRequests ?? defaultConfig.rateLimit.maxRequests,
      perMilliseconds: custom?.rateLimit?.perMilliseconds ?? defaultConfig.rateLimit.perMilliseconds,
      retryAfter: custom?.rateLimit?.retryAfter ?? defaultConfig.rateLimit.retryAfter
    },
  }
}

// Create cache key from request
function getCacheKey(request: ApiRequest): string {
  const { method, endpoint, query, body } = request
  return `${method}:${endpoint}:${JSON.stringify(query)}:${JSON.stringify(body)}`
}

// Check if cached data is still valid
function isCacheValid(timestamp: number, duration: number): boolean {
  return Date.now() - timestamp < duration * 1000
}

// Rate limiting
async function checkRateLimit(config: ApiConfig): Promise<void> {
  const now = Date.now()
  const { maxRequests, perMilliseconds } = config.rateLimit!
  
  // Remove old timestamps
  const cutoff = now - perMilliseconds
  while (requestTimestamps.length > 0 && requestTimestamps[0] < cutoff) {
    requestTimestamps.shift()
  }
  
  // Check if we're at the limit
  if (requestTimestamps.length >= maxRequests) {
    const oldestTimestamp = requestTimestamps[0]
    const waitTime = oldestTimestamp + perMilliseconds - now
    
    const error: RateLimitError = {
      code: 'RATE_LIMIT_ERROR',
      message: `Rate limit exceeded. Please wait ${Math.ceil(waitTime / 1000)} seconds.`,
      timestamp: new Date(),
      limit: maxRequests,
      remaining: 0,
      reset: new Date(oldestTimestamp + perMilliseconds),
      retryAfter: Math.ceil(waitTime / 1000),
    }
    
    throw error
  }
  
  requestTimestamps.push(now)
}

// Create API error from response
async function createApiError(response: Response): Promise<ApiError> {
  let error: ApiError = {
    code: 'API_ERROR',
    message: response.statusText || 'An error occurred',
    statusCode: response.status,
    timestamp: new Date(),
  }
  
  try {
    const data = await response.json()
    if (data.error) {
      error = { ...error, ...data.error }
    } else if (data.message) {
      error.message = data.message
    }
  } catch {
    // Ignore JSON parsing errors
  }
  
  // Enhance error based on status code
  if (response.status === 401) {
    return {
      ...error,
      code: 'AUTH_ERROR',
      message: error.message || 'Authentication required',
    } as AuthError
  }
  
  if (response.status === 422 || response.status === 400) {
    return {
      ...error,
      code: 'VALIDATION_ERROR',
      validationErrors: error.details?.errors || [],
    } as ValidationError
  }
  
  if (response.status === 429) {
    const retryAfter = parseInt(response.headers.get('Retry-After') || '60')
    return {
      ...error,
      code: 'RATE_LIMIT_ERROR',
      limit: parseInt(response.headers.get('X-RateLimit-Limit') || '100'),
      remaining: parseInt(response.headers.get('X-RateLimit-Remaining') || '0'),
      reset: new Date(parseInt(response.headers.get('X-RateLimit-Reset') || '0') * 1000),
      retryAfter,
    } as RateLimitError
  }
  
  return error
}

// Main API client
export class ApiClient {
  private config: ApiConfig
  
  constructor(config?: Partial<ApiConfig>) {
    this.config = mergeConfig(config)
  }
  
  // Make API request
  async request<T = any>(request: ApiRequest): Promise<ApiResponse<T>> {
    const startTime = Date.now()
    const { method, endpoint, params, query, body, headers, auth, timeout, retries, cache: cacheConfig } = request
    
    // Check cache for GET requests
    if (method === 'GET' && (cacheConfig?.enabled ?? this.config.cache?.enabled)) {
      const cacheKey = getCacheKey(request)
      const cached = cache.get(cacheKey)
      
      if (cached && isCacheValid(cached.timestamp, cacheConfig?.duration || this.config.cache?.duration || 300)) {
        return {
          data: cached.data,
          status: 200,
          statusText: 'OK',
          headers: {},
          request,
          timestamp: new Date(),
          duration: 0,
          cached: true,
        }
      }
    }
    
    // Check rate limit
    await checkRateLimit(this.config)
    
    // Build URL
    let url = `${this.config.baseUrl}${endpoint}`
    
    // Replace path parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url = url.replace(`:${key}`, encodeURIComponent(String(value)))
      })
    }
    
    // Add query parameters
    if (query) {
      const queryString = new URLSearchParams(
        Object.entries(query)
          .filter(([_, value]) => value !== undefined && value !== null)
          .map(([key, value]) => [key, String(value)])
      ).toString()
      
      if (queryString) {
        url += `?${queryString}`
      }
    }
    
    // Get auth token
    let authHeader: Record<string, string> = {}
    if (auth || this.config.auth) {
      const authConfig = auth || this.config.auth
      
      if (authConfig && authConfig.type === 'bearer') {
        const token = (authConfig as any).token || authConfig.credentials || useAuthStore.getState().token
        if (token) {
          authHeader = { Authorization: `Bearer ${token}` }
        }
      } else if (authConfig && authConfig.type === 'apikey') {
        authHeader = { 'X-API-Key': authConfig.credentials || '' }
      }
    }
    
    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers: {
        ...this.config.headers,
        ...authHeader,
        ...headers,
      },
      signal: AbortSignal.timeout(timeout || this.config.timeout || 30000),
    }
    
    // Add body for non-GET requests
    if (body && method !== 'GET') {
      requestOptions.body = JSON.stringify(body)
    }
    
    // Apply request interceptor
    let finalRequest = request
    if (this.config.interceptors?.request) {
      finalRequest = await this.config.interceptors.request(request)
    }
    
    // Make request with retries
    let lastError: ApiError | null = null
    const maxRetries = retries ?? this.config.retries ?? 3
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(url, requestOptions)
        
        // Handle non-OK responses
        if (!response.ok) {
          lastError = await createApiError(response)
          
          // Don't retry on client errors (4xx)
          if (response.status >= 400 && response.status < 500) {
            break
          }
          
          // Retry on server errors (5xx)
          if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
            continue
          }
        }
        
        // Parse response
        let data: T
        const contentType = response.headers.get('content-type')
        
        if (contentType?.includes('application/json')) {
          data = await response.json()
        } else {
          data = await response.text() as T
        }
        
        // Create response object
        const apiResponse: ApiResponse<T> = {
          data,
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries()),
          request: finalRequest,
          timestamp: new Date(),
          duration: Date.now() - startTime,
          cached: false,
          error: response.ok ? undefined : lastError || undefined,
        }
        
        // Apply response interceptor
        if (this.config.interceptors?.response) {
          return await this.config.interceptors.response(apiResponse)
        }
        
        // Cache successful GET responses
        if (method === 'GET' && response.ok && (cacheConfig?.enabled ?? this.config.cache?.enabled)) {
          const cacheKey = getCacheKey(request)
          cache.set(cacheKey, {
            data,
            timestamp: Date.now(),
          })
          
          // Cleanup old cache entries
          if (cache.size > (this.config.cache.maxSize || 100)) {
            const firstKey = cache.keys().next().value
            if (firstKey) {
              cache.delete(firstKey)
            }
          }
        }
        
        // Handle errors
        if (!response.ok && lastError) {
          if (this.config.interceptors?.error) {
            throw this.config.interceptors.error(lastError)
          }
          throw lastError
        }
        
        return apiResponse
        
      } catch (error: any) {
        // Handle network errors
        if (error.name === 'AbortError') {
          lastError = {
            code: 'NETWORK_ERROR',
            message: 'Request timeout',
            type: 'timeout',
            timestamp: new Date(),
          } as NetworkError
        } else if (error.code === 'RATE_LIMIT_ERROR') {
          throw error // Don't retry rate limit errors
        } else if (!lastError) {
          lastError = {
            code: 'NETWORK_ERROR',
            message: error.message || 'Network error',
            type: 'unknown',
            originalError: error,
            timestamp: new Date(),
          } as NetworkError
        }
        
        // Retry if we have attempts left
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
          continue
        }
      }
    }
    
    // If we get here, all retries failed
    if (this.config.interceptors?.error && lastError) {
      throw this.config.interceptors.error(lastError)
    }
    
    throw lastError || new Error('Request failed')
  }
  
  // Convenience methods
  async get<T = any>(endpoint: string, query?: Record<string, any>): Promise<T> {
    const response = await this.request<T>({
      method: 'GET',
      endpoint,
      query,
    })
    return response.data
  }
  
  async post<T = any>(endpoint: string, body?: any, query?: Record<string, any>): Promise<T> {
    const response = await this.request<T>({
      method: 'POST',
      endpoint,
      body,
      query,
    })
    return response.data
  }
  
  async put<T = any>(endpoint: string, body?: any, query?: Record<string, any>): Promise<T> {
    const response = await this.request<T>({
      method: 'PUT',
      endpoint,
      body,
      query,
    })
    return response.data
  }
  
  async patch<T = any>(endpoint: string, body?: any, query?: Record<string, any>): Promise<T> {
    const response = await this.request<T>({
      method: 'PATCH',
      endpoint,
      body,
      query,
    })
    return response.data
  }
  
  async delete<T = any>(endpoint: string, query?: Record<string, any>): Promise<T> {
    const response = await this.request<T>({
      method: 'DELETE',
      endpoint,
      query,
    })
    return response.data
  }
  
  // Clear cache
  clearCache(pattern?: string): void {
    if (pattern) {
      for (const key of cache.keys()) {
        if (key.includes(pattern)) {
          cache.delete(key)
        }
      }
    } else {
      cache.clear()
    }
  }
  
  // Update configuration
  updateConfig(config: Partial<ApiConfig>): void {
    this.config = mergeConfig({ ...this.config, ...config })
  }
}

// Default instance
export const api = new ApiClient()

// Typed API methods for common endpoints
export const emailApi = {
  getThreads: (params?: any) => api.get('/threads', params),
  getThread: (threadId: string) => api.get(`/threads/${threadId}`),
  markAsRead: (threadId: string, isRead: boolean) => api.patch(`/threads/${threadId}/read`, { isRead }),
  star: (threadId: string, isStarred: boolean) => api.patch(`/threads/${threadId}/star`, { isStarred }),
  archive: (threadId: string) => api.post(`/threads/${threadId}/archive`),
  deleteThread: (threadId: string) => api.delete(`/threads/${threadId}`),
  sendMessage: (data: any) => api.post('/messages/send', data),
  searchMessages: (query: string) => api.get('/messages/search', { q: query }),
}

export const authApi = {
  login: (email: string, password: string) => api.post('/auth/login', { email, password }),
  logout: () => api.post('/auth/logout'),
  register: (data: any) => api.post('/auth/register', data),
  getSession: () => api.get('/auth/session'),
  refreshToken: (refreshToken: string) => api.post('/auth/refresh', { refreshToken }),
  forgotPassword: (email: string) => api.post('/auth/forgot-password', { email }),
  resetPassword: (token: string, password: string) => api.post('/auth/reset-password', { token, password }),
  googleAuth: () => api.get('/auth/google'),
  googleCallback: (code: string) => api.post('/auth/google/callback', { code }),
}

export const rulesApi = {
  getRules: () => api.get('/user/rules'),
  createRule: (data: any) => api.post('/user/rules', data),
  updateRule: (ruleId: string, data: any) => api.patch(`/user/rules/${ruleId}`, data),
  deleteRule: (ruleId: string) => api.delete(`/user/rules/${ruleId}`),
  testRule: (ruleId: string, emailId: string) => api.post(`/user/rules/${ruleId}/test`, { emailId }),
}

export const assistantApi = {
  chat: (message: string, context?: any) => api.post('/ai/chat', { message, context }),
  categorize: (emailId: string) => api.post('/ai/categorize', { emailId }),
  summarize: (emailId: string) => api.post('/ai/summarize', { emailId }),
  suggestActions: (threadId: string) => api.post('/ai/suggest-actions', { threadId }),
}

export const bulkApi = {
  unsubscribe: (senderIds: string[]) => api.post('/bulk/unsubscribe', { senderIds }),
  blockColdEmails: (criteria: any) => api.post('/bulk/block-cold-emails', criteria),
  cleanInbox: (params: any) => api.post('/bulk/clean-inbox', params),
  getProgress: (operationId: string) => api.get(`/bulk/operations/${operationId}`),
}