import { Rule, RuleCondition, RuleAction, RuleConditionType, RuleOperator, RuleActionType } from '@/types/rules';

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface FieldValidationResult {
  field: string;
  valid: boolean;
  error?: string;
  warning?: string;
}

export class RuleValidationEngine {
  /**
   * Validate complete rule configuration
   */
  static validateRule(rule: Partial<Rule>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    const basicResult = this.validateBasicInfo(rule);
    errors.push(...basicResult.errors);
    warnings.push(...basicResult.warnings);

    // Conditions validation
    if (rule.conditions) {
      const conditionsResult = this.validateConditions(rule.conditions, rule.conditionLogic);
      errors.push(...conditionsResult.errors);
      warnings.push(...conditionsResult.warnings);
    }

    // Actions validation
    if (rule.actions) {
      const actionsResult = this.validateActions(rule.actions);
      errors.push(...actionsResult.errors);
      warnings.push(...actionsResult.warnings);
    }

    // Schedule validation
    if (rule.schedule) {
      const scheduleResult = this.validateSchedule(rule.schedule);
      errors.push(...scheduleResult.errors);
      warnings.push(...scheduleResult.warnings);
    }

    // Cross-validation
    const crossResult = this.validateCrossReferences(rule);
    errors.push(...crossResult.errors);
    warnings.push(...crossResult.warnings);

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate basic rule information
   */
  static validateBasicInfo(rule: Partial<Rule>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields
    if (!rule.name?.trim()) {
      errors.push('Rule name is required');
    } else if (rule.name.trim().length < 3) {
      errors.push('Rule name must be at least 3 characters long');
    } else if (rule.name.trim().length > 100) {
      errors.push('Rule name must be less than 100 characters');
    }

    // Name pattern validation
    if (rule.name && !/^[a-zA-Z0-9\s\-_()]+$/.test(rule.name)) {
      warnings.push('Rule name contains special characters that may cause issues');
    }

    // Description validation
    if (rule.description && rule.description.length > 500) {
      warnings.push('Rule description is very long and may be truncated in some views');
    }

    // Priority validation
    if (rule.priority !== undefined) {
      if (rule.priority < 0 || rule.priority > 100) {
        errors.push('Priority must be between 0 and 100');
      }
      if (rule.priority === 0) {
        warnings.push('Priority 0 (highest) should be reserved for critical rules');
      }
    }

    // Tags validation
    if (rule.tags) {
      if (rule.tags.length > 10) {
        warnings.push('Too many tags may make the rule difficult to organize');
      }
      
      const invalidTags = rule.tags.filter(tag => 
        !tag.trim() || tag.length > 50 || !/^[a-zA-Z0-9\-_]+$/.test(tag)
      );
      
      if (invalidTags.length > 0) {
        errors.push('Tags must be non-empty, less than 50 characters, and contain only letters, numbers, hyphens, and underscores');
      }
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate rule conditions
   */
  static validateConditions(conditions: RuleCondition[], logic?: string): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (conditions.length === 0) {
      errors.push('At least one condition is required');
      return { valid: false, errors, warnings };
    }

    if (conditions.length > 20) {
      warnings.push('Too many conditions may impact rule performance');
    }

    conditions.forEach((condition, index) => {
      const conditionResult = this.validateCondition(condition, index + 1);
      errors.push(...conditionResult.errors);
      warnings.push(...conditionResult.warnings);
    });

    // Logic validation
    if (conditions.length > 1 && (!logic || !['all', 'any'].includes(logic))) {
      errors.push('Condition logic must be "all" or "any" when multiple conditions are present');
    }

    // Duplicate condition detection
    const duplicates = this.findDuplicateConditions(conditions);
    if (duplicates.length > 0) {
      warnings.push(`Duplicate conditions detected: ${duplicates.join(', ')}`);
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate single condition
   */
  static validateCondition(condition: RuleCondition, index: number): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const prefix = `Condition ${index}:`;

    // Required fields
    if (!condition.id) {
      errors.push(`${prefix} ID is required`);
    }

    if (!condition.type) {
      errors.push(`${prefix} Type is required`);
    }

    if (!condition.operator) {
      errors.push(`${prefix} Operator is required`);
    }

    if (condition.value === undefined || condition.value === '') {
      errors.push(`${prefix} Value is required`);
    }

    // Type-specific validation
    if (condition.type && condition.operator && condition.value !== undefined) {
      const typeResult = this.validateConditionByType(condition, index);
      errors.push(...typeResult.errors);
      warnings.push(...typeResult.warnings);
    }

    // Operator compatibility
    const operatorResult = this.validateOperatorCompatibility(condition, index);
    errors.push(...operatorResult.errors);
    warnings.push(...operatorResult.warnings);

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate condition by type
   */
  static validateConditionByType(condition: RuleCondition, index: number): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const prefix = `Condition ${index}:`;

    switch (condition.type) {
      case 'from':
      case 'to':
        if (typeof condition.value === 'string') {
          if (!this.isValidEmail(condition.value) && !this.isDomainPattern(condition.value)) {
            warnings.push(`${prefix} Email address or domain pattern recommended`);
          }
        } else if (Array.isArray(condition.value)) {
          const invalidEmails = condition.value.filter(email => 
            typeof email === 'string' && !this.isValidEmail(email) && !this.isDomainPattern(email)
          );
          if (invalidEmails.length > 0) {
            warnings.push(`${prefix} Some email addresses may be invalid`);
          }
        }
        break;

      case 'subject':
      case 'body':
        if (typeof condition.value === 'string' && condition.value.length > 1000) {
          warnings.push(`${prefix} Very long text patterns may impact performance`);
        }
        break;

      case 'regex':
        if (typeof condition.value === 'string') {
          try {
            new RegExp(condition.value);
          } catch (e) {
            errors.push(`${prefix} Invalid regular expression pattern`);
          }
        }
        break;

      case 'hasAttachment':
      case 'isUnread':
      case 'isImportant':
      case 'isStarred':
        if (typeof condition.value !== 'boolean') {
          errors.push(`${prefix} Value must be true or false`);
        }
        break;

      case 'size':
      case 'attachmentSize':
      case 'age':
        if (typeof condition.value !== 'number' || condition.value < 0) {
          errors.push(`${prefix} Value must be a positive number`);
        }
        if (condition.type === 'age' && typeof condition.value === 'number' && condition.value > 365) {
          warnings.push(`${prefix} Age filter over 1 year may match very old emails`);
        }
        if ((condition.type === 'size' || condition.type === 'attachmentSize') && typeof condition.value === 'number' && condition.value > 100000000) {
          warnings.push(`${prefix} Size filter is very large (100MB+)`);
        }
        break;

      case 'attachmentType':
        if (typeof condition.value === 'string') {
          const validTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'jpg', 'jpeg', 'png', 'gif', 'zip', 'rar'];
          if (Array.isArray(condition.value)) {
            const invalidTypes = condition.value.filter(type => !validTypes.includes(type.toLowerCase()));
            if (invalidTypes.length > 0) {
              warnings.push(`${prefix} Unknown file types: ${invalidTypes.join(', ')}`);
            }
          }
        }
        break;

      case 'domain':
        if (typeof condition.value === 'string' && !this.isValidDomain(condition.value)) {
          warnings.push(`${prefix} Domain format may be invalid`);
        }
        break;
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate operator compatibility with condition type
   */
  static validateOperatorCompatibility(condition: RuleCondition, index: number): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const prefix = `Condition ${index}:`;

    const numericTypes = ['size', 'attachmentSize', 'age'];
    const booleanTypes = ['hasAttachment', 'isUnread', 'isImportant', 'isStarred'];
    const textTypes = ['from', 'to', 'subject', 'body', 'keyword', 'custom'];
    const listTypes = ['label', 'category'];

    if (numericTypes.includes(condition.type)) {
      const numericOperators = ['equals', 'notEquals', 'greaterThan', 'lessThan', 'greaterThanOrEqual', 'lessThanOrEqual', 'between'];
      if (!numericOperators.includes(condition.operator)) {
        errors.push(`${prefix} Operator "${condition.operator}" is not compatible with numeric fields`);
      }
    }

    if (booleanTypes.includes(condition.type)) {
      const booleanOperators = ['equals', 'notEquals'];
      if (!booleanOperators.includes(condition.operator)) {
        errors.push(`${prefix} Operator "${condition.operator}" is not compatible with boolean fields`);
      }
    }

    if (condition.operator === 'between' && !Array.isArray(condition.value)) {
      errors.push(`${prefix} "Between" operator requires an array of two values`);
    }

    if (condition.operator === 'between' && Array.isArray(condition.value) && condition.value.length !== 2) {
      errors.push(`${prefix} "Between" operator requires exactly two values`);
    }

    if (['in', 'notIn'].includes(condition.operator) && !Array.isArray(condition.value)) {
      errors.push(`${prefix} "${condition.operator}" operator requires an array of values`);
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate rule actions
   */
  static validateActions(actions: RuleAction[]): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (actions.length === 0) {
      errors.push('At least one action is required');
      return { valid: false, errors, warnings };
    }

    if (actions.length > 10) {
      warnings.push('Too many actions may impact rule performance');
    }

    // Validate individual actions
    actions.forEach((action, index) => {
      const actionResult = this.validateAction(action, index + 1);
      errors.push(...actionResult.errors);
      warnings.push(...actionResult.warnings);
    });

    // Check for conflicting actions
    const conflicts = this.findConflictingActions(actions);
    if (conflicts.length > 0) {
      errors.push(...conflicts);
    }

    // Check action order
    const orderIssues = this.validateActionOrder(actions);
    warnings.push(...orderIssues);

    // Check for destructive actions
    const destructiveActions = actions.filter(action => 
      ['delete', 'archive'].includes(action.type)
    );
    if (destructiveActions.length > 1) {
      warnings.push('Multiple destructive actions detected - only the first will execute');
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate single action
   */
  static validateAction(action: RuleAction, index: number): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const prefix = `Action ${index}:`;

    // Required fields
    if (!action.id) {
      errors.push(`${prefix} ID is required`);
    }

    if (!action.type) {
      errors.push(`${prefix} Type is required`);
    }

    if (action.order === undefined || action.order < 1) {
      errors.push(`${prefix} Order must be a positive number`);
    }

    // Type-specific validation
    if (action.type) {
      const typeResult = this.validateActionByType(action, index);
      errors.push(...typeResult.errors);
      warnings.push(...typeResult.warnings);
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate action by type
   */
  static validateActionByType(action: RuleAction, index: number): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const prefix = `Action ${index}:`;

    switch (action.type) {
      case 'label':
      case 'removeLabel':
        if (!action.value || typeof action.value !== 'string') {
          errors.push(`${prefix} Label name is required`);
        } else if (action.value.length > 50) {
          errors.push(`${prefix} Label name is too long`);
        }
        break;

      case 'categorize':
        const validCategories = ['primary', 'social', 'promotions', 'updates', 'forums'];
        if (!action.value || !validCategories.includes(action.value as string)) {
          errors.push(`${prefix} Valid category is required (${validCategories.join(', ')})`);
        }
        break;

      case 'moveToFolder':
        if (!action.params?.folder) {
          errors.push(`${prefix} Folder path is required`);
        } else if (action.params.folder.length > 200) {
          errors.push(`${prefix} Folder path is too long`);
        }
        break;

      case 'forward':
        if (!action.params?.address) {
          errors.push(`${prefix} Forward address is required`);
        } else if (!this.isValidEmail(action.params.address)) {
          errors.push(`${prefix} Invalid email address`);
        }
        break;

      case 'reply':
        if (!action.template) {
          errors.push(`${prefix} Reply template is required`);
        } else if (action.template.length > 10000) {
          warnings.push(`${prefix} Reply template is very long`);
        }
        break;

      case 'snooze':
        if (!action.params?.duration || action.params.duration < 1) {
          errors.push(`${prefix} Snooze duration must be at least 1 minute`);
        } else if (action.params.duration > 525600) { // 1 year in minutes
          warnings.push(`${prefix} Snooze duration is very long (over 1 year)`);
        }
        break;

      case 'webhook':
        if (!action.params?.url) {
          errors.push(`${prefix} Webhook URL is required`);
        } else if (!this.isValidUrl(action.params.url)) {
          errors.push(`${prefix} Invalid webhook URL`);
        }
        
        const validMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
        if (action.params?.method && !validMethods.includes(action.params.method)) {
          errors.push(`${prefix} Invalid HTTP method`);
        }
        break;

      case 'aiProcess':
        if (action.params?.confidence && (action.params.confidence < 0 || action.params.confidence > 1)) {
          errors.push(`${prefix} AI confidence must be between 0 and 1`);
        }
        break;

      case 'createTask':
        if (action.params?.category && action.params.category.length > 100) {
          warnings.push(`${prefix} Task category name is very long`);
        }
        break;
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Validate rule schedule
   */
  static validateSchedule(schedule: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!schedule.type) {
      errors.push('Schedule type is required');
      return { valid: false, errors, warnings };
    }

    const validTypes = ['immediate', 'delayed', 'scheduled', 'recurring'];
    if (!validTypes.includes(schedule.type)) {
      errors.push('Invalid schedule type');
    }

    switch (schedule.type) {
      case 'delayed':
        if (!schedule.delay || schedule.delay < 1) {
          errors.push('Delay must be at least 1 minute');
        } else if (schedule.delay > 10080) { // 1 week
          warnings.push('Delay is very long (over 1 week)');
        }
        break;

      case 'scheduled':
      case 'recurring':
        if (!schedule.time) {
          errors.push('Schedule time is required');
        } else if (!/^\d{2}:\d{2}$/.test(schedule.time)) {
          errors.push('Schedule time must be in HH:MM format');
        }

        if (!schedule.days || !Array.isArray(schedule.days) || schedule.days.length === 0) {
          errors.push('At least one day must be selected');
        } else {
          const invalidDays = schedule.days.filter((day: number) => day < 0 || day > 6);
          if (invalidDays.length > 0) {
            errors.push('Days must be between 0 (Sunday) and 6 (Saturday)');
          }
        }
        break;
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  /**
   * Cross-validation between different rule parts
   */
  static validateCrossReferences(rule: Partial<Rule>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if actions make sense with conditions
    if (rule.conditions && rule.actions) {
      const hasAttachmentCondition = rule.conditions.some(c => c.type === 'hasAttachment');
      const attachmentActions = rule.actions.filter(a => 
        ['forward', 'createTask'].includes(a.type)
      );
      
      if (!hasAttachmentCondition && attachmentActions.length > 0) {
        warnings.push('Actions that work with attachments but no attachment condition defined');
      }
    }

    // Check for logical inconsistencies
    if (rule.conditions && rule.conditionLogic === 'all') {
      const contradictory = this.findContradictoryConditions(rule.conditions);
      if (contradictory.length > 0) {
        warnings.push('Contradictory conditions detected with "all" logic - rule may never match');
      }
    }

    return { valid: errors.length === 0, errors, warnings };
  }

  // Helper methods
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private static isValidDomain(domain: string): boolean {
    const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
    return domainRegex.test(domain);
  }

  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private static isDomainPattern(pattern: string): boolean {
    return pattern.startsWith('@') && this.isValidDomain(pattern.substring(1));
  }

  private static findDuplicateConditions(conditions: RuleCondition[]): string[] {
    const seen = new Set<string>();
    const duplicates = new Set<string>();

    conditions.forEach(condition => {
      const key = `${condition.type}-${condition.operator}-${JSON.stringify(condition.value)}`;
      if (seen.has(key)) {
        duplicates.add(`${condition.type} ${condition.operator}`);
      }
      seen.add(key);
    });

    return Array.from(duplicates);
  }

  private static findConflictingActions(actions: RuleAction[]): string[] {
    const conflicts: string[] = [];
    
    // Check for conflicting archive/delete actions
    const hasArchive = actions.some(a => a.type === 'archive');
    const hasDelete = actions.some(a => a.type === 'delete');
    if (hasArchive && hasDelete) {
      conflicts.push('Archive and delete actions conflict - email cannot be both archived and deleted');
    }

    // Check for conflicting read/unread actions
    const hasMarkRead = actions.some(a => a.type === 'markRead');
    const hasMarkUnread = actions.some(a => a.type === 'markUnread');
    if (hasMarkRead && hasMarkUnread) {
      conflicts.push('Mark read and mark unread actions conflict');
    }

    // Check for conflicting star/unstar actions
    const hasStar = actions.some(a => a.type === 'star');
    const hasUnstar = actions.some(a => a.type === 'unstar');
    if (hasStar && hasUnstar) {
      conflicts.push('Star and unstar actions conflict');
    }

    return conflicts;
  }

  private static validateActionOrder(actions: RuleAction[]): string[] {
    const warnings: string[] = [];
    
    // Check for gaps in order
    const orders = actions.map(a => a.order).sort((a, b) => a - b);
    for (let i = 1; i < orders.length; i++) {
      if (orders[i] !== orders[i-1] + 1) {
        warnings.push('Action order has gaps - consider reordering for clarity');
        break;
      }
    }

    // Check for duplicate orders
    const orderCounts = orders.reduce((acc, order) => {
      acc[order] = (acc[order] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    const duplicateOrders = Object.entries(orderCounts)
      .filter(([, count]) => count > 1)
      .map(([order]) => order);

    if (duplicateOrders.length > 0) {
      warnings.push(`Duplicate action orders: ${duplicateOrders.join(', ')}`);
    }

    return warnings;
  }

  private static findContradictoryConditions(conditions: RuleCondition[]): string[] {
    const contradictions: string[] = [];
    
    // Group conditions by field
    const conditionsByField = conditions.reduce((acc, condition) => {
      const key = condition.type;
      if (!acc[key]) acc[key] = [];
      acc[key].push(condition);
      return acc;
    }, {} as Record<string, RuleCondition[]>);

    // Check for contradictions within each field
    Object.entries(conditionsByField).forEach(([field, fieldConditions]) => {
      if (fieldConditions.length < 2) return;

      // Check for boolean contradictions
      const booleanTypes = ['hasAttachment', 'isUnread', 'isImportant', 'isStarred'];
      if (booleanTypes.includes(field)) {
        const trueConditions = fieldConditions.filter(c => c.value === true && !c.negate);
        const falseConditions = fieldConditions.filter(c => c.value === false && !c.negate);
        const negatedTrueConditions = fieldConditions.filter(c => c.value === true && c.negate);
        const negatedFalseConditions = fieldConditions.filter(c => c.value === false && c.negate);

        if ((trueConditions.length > 0 && (falseConditions.length > 0 || negatedTrueConditions.length > 0)) ||
            (falseConditions.length > 0 && negatedFalseConditions.length > 0)) {
          contradictions.push(`${field} conditions contradict each other`);
        }
      }
    });

    return contradictions;
  }
}

export default RuleValidationEngine;