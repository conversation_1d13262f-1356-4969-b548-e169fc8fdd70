import { Rule, RuleCondition, RuleAction, RuleConditionType, RuleActionType, RuleOperator } from '../../types/rules';
import { ParsedMessage, Thread, EmailCategory } from '../../types/email';
import { AIModel } from '../../types/assistant';

export interface RuleSuggestion {
  id: string;
  name: string;
  description: string;
  confidence: number;
  priority: 'high' | 'medium' | 'low';
  impact: RuleImpact;
  rule: Partial<Rule>;
  reasoning: string;
  examples: string[];
  category: RuleSuggestionCategory;
  complexity: 'simple' | 'moderate' | 'complex';
  estimatedTimeToImplement: number; // minutes
  userFeedback?: 'accepted' | 'rejected' | 'modified';
}

export interface RuleImpact {
  emailsAffected: number;
  timesSaved: number; // minutes per week
  clutterReduced: number; // percentage
  automationLevel: number; // 0-1
  riskLevel: 'low' | 'medium' | 'high';
  reversibility: boolean;
}

export interface RuleSuggestionCategory {
  type: 'organization' | 'productivity' | 'security' | 'cleanup' | 'workflow';
  subcategory: string;
  tags: string[];
}

export interface EmailPattern {
  id: string;
  type: 'sender' | 'subject' | 'content' | 'timing' | 'volume' | 'interaction';
  pattern: any;
  frequency: number;
  examples: ParsedMessage[];
  strength: number; // 0-1
  discovered: Date;
  lastSeen: Date;
}

export interface RuleAnalysisContext {
  timeframe: {
    start: Date;
    end: Date;
  };
  emailVolume: number;
  userBehavior: {
    archiveRate: number;
    deleteRate: number;
    readRate: number;
    responseRate: number;
    categoryPreferences: Record<EmailCategory, number>;
  };
  existingRules: Rule[];
  currentProblems: string[];
}

export interface RuleSuggestionConfig {
  model: AIModel;
  analysisDepth: 'quick' | 'standard' | 'comprehensive';
  suggestionLimit: number;
  confidenceThreshold: number;
  includeComplexRules: boolean;
  focusAreas: RuleSuggestionCategory['type'][];
  learningEnabled: boolean;
}

export interface PatternAnalysisResult {
  patterns: EmailPattern[];
  insights: string[];
  recommendations: string[];
  ruleOpportunities: Array<{
    pattern: EmailPattern;
    suggestedActions: RuleActionType[];
    impact: RuleImpact;
  }>;
}

export class AIRulesEngine {
  private config: RuleSuggestionConfig;
  private discoveredPatterns: Map<string, EmailPattern>;
  private suggestionHistory: RuleSuggestion[];
  private userPreferences: Record<string, any>;

  constructor(config: RuleSuggestionConfig) {
    this.config = config;
    this.discoveredPatterns = new Map();
    this.suggestionHistory = [];
    this.userPreferences = {};
  }

  /**
   * Analyze email patterns and suggest automation rules
   */
  async suggestRules(
    emails: ParsedMessage[],
    context: RuleAnalysisContext
  ): Promise<RuleSuggestion[]> {
    // Step 1: Discover patterns in email data
    const patternAnalysis = await this.analyzePatterns(emails, context);
    
    // Step 2: Generate rule suggestions based on patterns
    const suggestions = await this.generateRuleSuggestions(patternAnalysis, context);
    
    // Step 3: Score and rank suggestions
    const rankedSuggestions = this.rankSuggestions(suggestions, context);
    
    // Step 4: Apply filters and limits
    const filteredSuggestions = this.filterSuggestions(rankedSuggestions);
    
    // Step 5: Store suggestions for learning
    this.suggestionHistory.push(...filteredSuggestions);
    
    return filteredSuggestions.slice(0, this.config.suggestionLimit);
  }

  /**
   * Analyze a specific rule opportunity
   */
  async analyzeRuleOpportunity(
    pattern: EmailPattern,
    context: RuleAnalysisContext
  ): Promise<{
    viability: number;
    suggestedConditions: RuleCondition[];
    suggestedActions: RuleAction[];
    risks: string[];
    benefits: string[];
    implementation: {
      difficulty: 'easy' | 'medium' | 'hard';
      timeRequired: number;
      testing: string[];
    };
  }> {
    const viability = this.calculateRuleViability(pattern, context);
    const conditions = await this.generateConditions(pattern);
    const actions = await this.generateActions(pattern, context);
    const risks = this.identifyRisks(pattern, conditions, actions);
    const benefits = this.identifyBenefits(pattern, context);
    
    return {
      viability,
      suggestedConditions: conditions,
      suggestedActions: actions,
      risks,
      benefits,
      implementation: {
        difficulty: this.assessImplementationDifficulty(conditions, actions),
        timeRequired: this.estimateImplementationTime(conditions, actions),
        testing: this.generateTestingSteps(conditions, actions)
      }
    };
  }

  /**
   * Get rule suggestions for natural language queries
   */
  async processNaturalLanguageRequest(
    query: string,
    emails: ParsedMessage[],
    context: RuleAnalysisContext
  ): Promise<{
    intent: string;
    suggestions: RuleSuggestion[];
    clarificationQuestions?: string[];
    relatedPatterns: EmailPattern[];
  }> {
    // Parse user intent
    const intent = await this.parseUserIntent(query);
    
    // Find relevant patterns
    const relevantPatterns = this.findRelevantPatterns(intent, emails);
    
    // Generate targeted suggestions
    const suggestions = await this.generateTargetedSuggestions(intent, relevantPatterns, context);
    
    // Generate clarification questions if needed
    const clarificationQuestions = this.generateClarificationQuestions(intent, suggestions);
    
    return {
      intent,
      suggestions,
      clarificationQuestions,
      relatedPatterns: relevantPatterns
    };
  }

  /**
   * Learn from user feedback on suggestions
   */
  async addFeedback(
    suggestionId: string,
    feedback: 'accepted' | 'rejected' | 'modified',
    modifications?: Partial<Rule>,
    comment?: string
  ): Promise<void> {
    const suggestion = this.suggestionHistory.find(s => s.id === suggestionId);
    if (!suggestion) return;

    suggestion.userFeedback = feedback;

    // Update learning data
    if (this.config.learningEnabled) {
      await this.updateLearningData(suggestion, feedback, modifications, comment);
    }

    // Adjust future suggestions based on feedback
    this.updateUserPreferences(suggestion, feedback);
  }

  /**
   * Get performance metrics for rule suggestions
   */
  getPerformanceMetrics(): {
    totalSuggestions: number;
    acceptanceRate: number;
    averageConfidence: number;
    categoryBreakdown: Record<RuleSuggestionCategory['type'], number>;
    impactMetrics: {
      totalTimesSaved: number;
      totalEmailsAffected: number;
      averageClutterReduction: number;
    };
    userSatisfaction: number;
  } {
    const total = this.suggestionHistory.length;
    const accepted = this.suggestionHistory.filter(s => s.userFeedback === 'accepted').length;
    const avgConfidence = this.suggestionHistory.reduce((sum, s) => sum + s.confidence, 0) / total;
    
    const categoryBreakdown: Record<RuleSuggestionCategory['type'], number> = {
      organization: 0,
      productivity: 0,
      security: 0,
      cleanup: 0,
      workflow: 0
    };

    this.suggestionHistory.forEach(s => {
      categoryBreakdown[s.category.type]++;
    });

    const impactMetrics = this.suggestionHistory.reduce(
      (acc, s) => ({
        totalTimesSaved: acc.totalTimesSaved + s.impact.timesSaved,
        totalEmailsAffected: acc.totalEmailsAffected + s.impact.emailsAffected,
        averageClutterReduction: acc.averageClutterReduction + s.impact.clutterReduced
      }),
      { totalTimesSaved: 0, totalEmailsAffected: 0, averageClutterReduction: 0 }
    );

    if (total > 0) {
      impactMetrics.averageClutterReduction /= total;
    }

    return {
      totalSuggestions: total,
      acceptanceRate: total > 0 ? accepted / total : 0,
      averageConfidence: avgConfidence || 0,
      categoryBreakdown,
      impactMetrics,
      userSatisfaction: this.calculateUserSatisfaction()
    };
  }

  /**
   * Analyze patterns in email data
   */
  private async analyzePatterns(
    emails: ParsedMessage[],
    context: RuleAnalysisContext
  ): Promise<PatternAnalysisResult> {
    const patterns: EmailPattern[] = [];
    
    // Analyze sender patterns
    const senderPatterns = this.analyzeSenderPatterns(emails);
    patterns.push(...senderPatterns);
    
    // Analyze subject patterns
    const subjectPatterns = this.analyzeSubjectPatterns(emails);
    patterns.push(...subjectPatterns);
    
    // Analyze content patterns
    const contentPatterns = this.analyzeContentPatterns(emails);
    patterns.push(...contentPatterns);
    
    // Analyze timing patterns
    const timingPatterns = this.analyzeTimingPatterns(emails);
    patterns.push(...timingPatterns);
    
    // Analyze volume patterns
    const volumePatterns = this.analyzeVolumePatterns(emails, context);
    patterns.push(...volumePatterns);
    
    // Store discovered patterns
    patterns.forEach(pattern => {
      this.discoveredPatterns.set(pattern.id, pattern);
    });
    
    // Generate insights and recommendations
    const insights = this.generateInsights(patterns, context);
    const recommendations = this.generateRecommendations(patterns, context);
    const ruleOpportunities = this.identifyRuleOpportunities(patterns);
    
    return {
      patterns,
      insights,
      recommendations,
      ruleOpportunities
    };
  }

  /**
   * Generate rule suggestions based on pattern analysis
   */
  private async generateRuleSuggestions(
    analysis: PatternAnalysisResult,
    context: RuleAnalysisContext
  ): Promise<RuleSuggestion[]> {
    const suggestions: RuleSuggestion[] = [];
    
    for (const opportunity of analysis.ruleOpportunities) {
      const suggestion = await this.createRuleSuggestion(opportunity, context);
      if (suggestion) {
        suggestions.push(suggestion);
      }
    }
    
    // Add context-based suggestions
    const contextSuggestions = await this.generateContextBasedSuggestions(context);
    suggestions.push(...contextSuggestions);
    
    return suggestions;
  }

  /**
   * Create a rule suggestion from a pattern opportunity
   */
  private async createRuleSuggestion(
    opportunity: { pattern: EmailPattern; suggestedActions: RuleActionType[]; impact: RuleImpact },
    context: RuleAnalysisContext
  ): Promise<RuleSuggestion | null> {
    const { pattern, suggestedActions, impact } = opportunity;
    
    // Generate conditions based on pattern
    const conditions = this.patternToConditions(pattern);
    if (conditions.length === 0) return null;
    
    // Generate actions
    const actions = suggestedActions.map((type, index) => this.createRuleAction(type, index, pattern));
    
    // Create rule suggestion
    const suggestion: RuleSuggestion = {
      id: `suggestion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      name: this.generateRuleName(pattern, actions),
      description: this.generateRuleDescription(pattern, actions),
      confidence: this.calculateSuggestionConfidence(pattern, impact, context),
      priority: this.determinePriority(impact),
      impact,
      rule: {
        name: this.generateRuleName(pattern, actions),
        description: this.generateRuleDescription(pattern, actions),
        enabled: true,
        priority: 1,
        conditions,
        conditionLogic: 'all',
        actions,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      reasoning: this.generateReasoning(pattern, impact),
      examples: pattern.examples.map(email => `${email.from.email}: ${email.subject}`),
      category: this.categorizeRule(pattern, actions),
      complexity: this.assessComplexity(conditions, actions),
      estimatedTimeToImplement: this.estimateImplementationTime(conditions, actions)
    };
    
    return suggestion;
  }

  /**
   * Analyze sender patterns
   */
  private analyzeSenderPatterns(emails: ParsedMessage[]): EmailPattern[] {
    const senderCounts = new Map<string, { count: number; emails: ParsedMessage[] }>();
    
    emails.forEach(email => {
      const sender = email.from.email;
      if (!senderCounts.has(sender)) {
        senderCounts.set(sender, { count: 0, emails: [] });
      }
      const data = senderCounts.get(sender)!;
      data.count++;
      data.emails.push(email);
    });
    
    const patterns: EmailPattern[] = [];
    
    senderCounts.forEach((data, sender) => {
      if (data.count >= 3) { // Minimum threshold for pattern
        patterns.push({
          id: `sender_${sender.replace(/[^a-zA-Z0-9]/g, '_')}`,
          type: 'sender',
          pattern: { sender, domain: sender.split('@')[1] },
          frequency: data.count,
          examples: data.emails.slice(0, 3),
          strength: Math.min(data.count / emails.length, 1),
          discovered: new Date(),
          lastSeen: new Date(Math.max(...data.emails.map(e => e.date.getTime())))
        });
      }
    });
    
    return patterns.sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Analyze subject patterns
   */
  private analyzeSubjectPatterns(emails: ParsedMessage[]): EmailPattern[] {
    const patterns: EmailPattern[] = [];
    
    // Look for common prefixes
    const prefixes = new Map<string, { count: number; emails: ParsedMessage[] }>();
    
    emails.forEach(email => {
      const subject = email.subject.toLowerCase();
      const words = subject.split(' ').slice(0, 3); // First 3 words
      const prefix = words.join(' ');
      
      if (prefix.length > 5) { // Minimum prefix length
        if (!prefixes.has(prefix)) {
          prefixes.set(prefix, { count: 0, emails: [] });
        }
        const data = prefixes.get(prefix)!;
        data.count++;
        data.emails.push(email);
      }
    });
    
    prefixes.forEach((data, prefix) => {
      if (data.count >= 3) {
        patterns.push({
          id: `subject_prefix_${prefix.replace(/[^a-zA-Z0-9]/g, '_')}`,
          type: 'subject',
          pattern: { type: 'prefix', value: prefix },
          frequency: data.count,
          examples: data.emails.slice(0, 3),
          strength: Math.min(data.count / emails.length, 1),
          discovered: new Date(),
          lastSeen: new Date(Math.max(...data.emails.map(e => e.date.getTime())))
        });
      }
    });
    
    return patterns.sort((a, b) => b.frequency - a.frequency);
  }

  /**
   * Analyze content patterns
   */
  private analyzeContentPatterns(emails: ParsedMessage[]): EmailPattern[] {
    const patterns: EmailPattern[] = [];
    
    // Look for unsubscribe patterns
    const unsubscribeEmails = emails.filter(email => {
      const content = (email.body.text || email.body.plain || '').toLowerCase();
      return content.includes('unsubscribe') || content.includes('opt out');
    });
    
    if (unsubscribeEmails.length >= 3) {
      patterns.push({
        id: 'content_unsubscribe',
        type: 'content',
        pattern: { type: 'unsubscribe', keywords: ['unsubscribe', 'opt out'] },
        frequency: unsubscribeEmails.length,
        examples: unsubscribeEmails.slice(0, 3),
        strength: unsubscribeEmails.length / emails.length,
        discovered: new Date(),
        lastSeen: new Date()
      });
    }
    
    return patterns;
  }

  /**
   * Analyze timing patterns
   */
  private analyzeTimingPatterns(emails: ParsedMessage[]): EmailPattern[] {
    const patterns: EmailPattern[] = [];
    
    // Group by day of week
    const dayGroups = new Map<number, ParsedMessage[]>();
    emails.forEach(email => {
      const day = email.date.getDay();
      if (!dayGroups.has(day)) {
        dayGroups.set(day, []);
      }
      dayGroups.get(day)!.push(email);
    });
    
    // Find days with unusual volume
    const avgPerDay = emails.length / 7;
    dayGroups.forEach((dayEmails, day) => {
      if (dayEmails.length > avgPerDay * 1.5) {
        patterns.push({
          id: `timing_day_${day}`,
          type: 'timing',
          pattern: { type: 'day_of_week', day },
          frequency: dayEmails.length,
          examples: dayEmails.slice(0, 3),
          strength: dayEmails.length / emails.length,
          discovered: new Date(),
          lastSeen: new Date()
        });
      }
    });
    
    return patterns;
  }

  /**
   * Analyze volume patterns
   */
  private analyzeVolumePatterns(emails: ParsedMessage[], context: RuleAnalysisContext): EmailPattern[] {
    // This would analyze email volume trends over time
    // For now, return empty array
    return [];
  }

  /**
   * Convert pattern to rule conditions
   */
  private patternToConditions(pattern: EmailPattern): RuleCondition[] {
    const conditions: RuleCondition[] = [];
    
    switch (pattern.type) {
      case 'sender':
        conditions.push({
          id: `condition_${Date.now()}`,
          type: 'from',
          operator: 'equals',
          value: pattern.pattern.sender
        });
        break;
        
      case 'subject':
        if (pattern.pattern.type === 'prefix') {
          conditions.push({
            id: `condition_${Date.now()}`,
            type: 'subject',
            operator: 'startsWith',
            value: pattern.pattern.value
          });
        }
        break;
        
      case 'content':
        if (pattern.pattern.type === 'unsubscribe') {
          conditions.push({
            id: `condition_${Date.now()}`,
            type: 'body',
            operator: 'contains',
            value: 'unsubscribe'
          });
        }
        break;
    }
    
    return conditions;
  }

  /**
   * Create rule action
   */
  private createRuleAction(type: RuleActionType, order: number, pattern: EmailPattern): RuleAction {
    return {
      id: `action_${Date.now()}_${order}`,
      type,
      order,
      params: this.getActionParams(type, pattern)
    };
  }

  /**
   * Get action parameters based on type and pattern
   */
  private getActionParams(type: RuleActionType, pattern: EmailPattern): any {
    switch (type) {
      case 'label':
        return { label: this.suggestLabel(pattern) };
      case 'categorize':
        return { category: this.suggestCategory(pattern) };
      case 'snooze':
        return { duration: 24 * 60 }; // 24 hours
      default:
        return {};
    }
  }

  /**
   * Suggest label based on pattern
   */
  private suggestLabel(pattern: EmailPattern): string {
    switch (pattern.type) {
      case 'sender':
        const domain = pattern.pattern.domain || pattern.pattern.sender.split('@')[1];
        return domain.split('.')[0];
      case 'subject':
        return pattern.pattern.value.split(' ')[0];
      default:
        return 'auto-labeled';
    }
  }

  /**
   * Suggest category based on pattern
   */
  private suggestCategory(pattern: EmailPattern): EmailCategory {
    if (pattern.type === 'content' && pattern.pattern.type === 'unsubscribe') {
      return 'Newsletter';
    }
    if (pattern.type === 'sender' && pattern.pattern.domain?.includes('noreply')) {
      return 'Notification';
    }
    return 'Other';
  }

  // Additional helper methods
  private generateInsights(patterns: EmailPattern[], context: RuleAnalysisContext): string[] {
    const insights: string[] = [];
    
    if (patterns.length > 10) {
      insights.push('You have many repetitive email patterns that could benefit from automation');
    }
    
    const senderPatterns = patterns.filter(p => p.type === 'sender');
    if (senderPatterns.length > 5) {
      insights.push(`${senderPatterns.length} frequent senders could be automatically organized`);
    }
    
    return insights;
  }

  private generateRecommendations(patterns: EmailPattern[], context: RuleAnalysisContext): string[] {
    const recommendations: string[] = [];
    
    if (patterns.some(p => p.pattern.type === 'unsubscribe')) {
      recommendations.push('Consider creating a rule to automatically archive newsletter emails');
    }
    
    return recommendations;
  }

  private identifyRuleOpportunities(patterns: EmailPattern[]): Array<{
    pattern: EmailPattern;
    suggestedActions: RuleActionType[];
    impact: RuleImpact;
  }> {
    return patterns.map(pattern => ({
      pattern,
      suggestedActions: this.suggestActionsForPattern(pattern),
      impact: this.calculatePatternImpact(pattern)
    }));
  }

  private suggestActionsForPattern(pattern: EmailPattern): RuleActionType[] {
    const actions: RuleActionType[] = [];
    
    if (pattern.type === 'sender' && pattern.frequency > 5) {
      actions.push('label', 'archive');
    }
    
    if (pattern.type === 'content' && pattern.pattern.type === 'unsubscribe') {
      actions.push('categorize', 'markRead');
    }
    
    return actions;
  }

  private calculatePatternImpact(pattern: EmailPattern): RuleImpact {
    return {
      emailsAffected: pattern.frequency,
      timesSaved: pattern.frequency * 0.5, // 30 seconds per email
      clutterReduced: (pattern.frequency / 100) * 10, // Rough estimate
      automationLevel: 0.8,
      riskLevel: 'low',
      reversibility: true
    };
  }

  private generateRuleName(pattern: EmailPattern, actions: RuleAction[]): string {
    const actionNames = actions.map(a => a.type).join(' and ');
    return `Auto-${actionNames} emails from ${pattern.pattern.sender || 'pattern'}`;
  }

  private generateRuleDescription(pattern: EmailPattern, actions: RuleAction[]): string {
    return `Automatically handle emails matching the ${pattern.type} pattern with ${pattern.frequency} occurrences`;
  }

  private generateReasoning(pattern: EmailPattern, impact: RuleImpact): string {
    return `This pattern occurs ${pattern.frequency} times and could save ${impact.timesSaved} minutes per week through automation`;
  }

  private categorizeRule(pattern: EmailPattern, actions: RuleAction[]): RuleSuggestionCategory {
    return {
      type: 'organization',
      subcategory: pattern.type,
      tags: [pattern.type, ...actions.map(a => a.type)]
    };
  }

  private assessComplexity(conditions: RuleCondition[], actions: RuleAction[]): 'simple' | 'moderate' | 'complex' {
    const totalItems = conditions.length + actions.length;
    if (totalItems <= 3) return 'simple';
    if (totalItems <= 6) return 'moderate';
    return 'complex';
  }

  private estimateImplementationTime(conditions: RuleCondition[], actions: RuleAction[]): number {
    return (conditions.length * 2) + (actions.length * 3); // minutes
  }

  private calculateSuggestionConfidence(pattern: EmailPattern, impact: RuleImpact, context: RuleAnalysisContext): number {
    let confidence = pattern.strength * 0.6;
    confidence += (impact.emailsAffected / context.emailVolume) * 0.3;
    confidence += (impact.riskLevel === 'low' ? 0.1 : impact.riskLevel === 'medium' ? 0.05 : 0);
    return Math.min(confidence, 1);
  }

  private determinePriority(impact: RuleImpact): 'high' | 'medium' | 'low' {
    if (impact.timesSaved > 60 || impact.emailsAffected > 50) return 'high';
    if (impact.timesSaved > 20 || impact.emailsAffected > 20) return 'medium';
    return 'low';
  }

  private rankSuggestions(suggestions: RuleSuggestion[], context: RuleAnalysisContext): RuleSuggestion[] {
    return suggestions.sort((a, b) => {
      // Primary sort by priority
      const priorityWeight = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityWeight[b.priority] - priorityWeight[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      // Secondary sort by confidence
      return b.confidence - a.confidence;
    });
  }

  private filterSuggestions(suggestions: RuleSuggestion[]): RuleSuggestion[] {
    return suggestions.filter(s => {
      if (s.confidence < this.config.confidenceThreshold) return false;
      if (!this.config.includeComplexRules && s.complexity === 'complex') return false;
      if (!this.config.focusAreas.includes(s.category.type)) return false;
      return true;
    });
  }

  private generateContextBasedSuggestions(context: RuleAnalysisContext): Promise<RuleSuggestion[]> {
    // Generate suggestions based on user behavior and current problems
    return Promise.resolve([]);
  }

  private parseUserIntent(query: string): Promise<string> {
    // Simple intent parsing - in real implementation would use NLP
    return Promise.resolve(query.toLowerCase());
  }

  private findRelevantPatterns(intent: string, emails: ParsedMessage[]): EmailPattern[] {
    return Array.from(this.discoveredPatterns.values()).slice(0, 5);
  }

  private generateTargetedSuggestions(intent: string, patterns: EmailPattern[], context: RuleAnalysisContext): Promise<RuleSuggestion[]> {
    // Generate suggestions based on specific intent
    return Promise.resolve([]);
  }

  private generateClarificationQuestions(intent: string, suggestions: RuleSuggestion[]): string[] | undefined {
    if (suggestions.length === 0) {
      return ['Could you provide more specific details about what you want to automate?'];
    }
    return undefined;
  }

  private updateLearningData(suggestion: RuleSuggestion, feedback: string, modifications?: any, comment?: string): Promise<void> {
    // Update ML models based on feedback
    return Promise.resolve();
  }

  private updateUserPreferences(suggestion: RuleSuggestion, feedback: string): void {
    // Update user preferences based on feedback
    if (feedback === 'accepted') {
      this.userPreferences[suggestion.category.type] = (this.userPreferences[suggestion.category.type] || 0) + 1;
    }
  }

  private calculateUserSatisfaction(): number {
    const feedback = this.suggestionHistory.filter(s => s.userFeedback);
    if (feedback.length === 0) return 0.75; // Default
    
    const positive = feedback.filter(s => s.userFeedback === 'accepted' || s.userFeedback === 'modified').length;
    return positive / feedback.length;
  }

  private calculateRuleViability(pattern: EmailPattern, context: RuleAnalysisContext): number {
    return pattern.strength * 0.7 + (pattern.frequency / context.emailVolume) * 0.3;
  }

  private generateConditions(pattern: EmailPattern): Promise<RuleCondition[]> {
    return Promise.resolve(this.patternToConditions(pattern));
  }

  private generateActions(pattern: EmailPattern, context: RuleAnalysisContext): Promise<RuleAction[]> {
    const actions = this.suggestActionsForPattern(pattern);
    return Promise.resolve(actions.map((type, index) => this.createRuleAction(type, index, pattern)));
  }

  private identifyRisks(pattern: EmailPattern, conditions: RuleCondition[], actions: RuleAction[]): string[] {
    const risks: string[] = [];
    
    if (actions.some(a => a.type === 'delete')) {
      risks.push('Risk of permanently deleting important emails');
    }
    
    if (conditions.length === 1) {
      risks.push('Single condition may be too broad and catch unintended emails');
    }
    
    return risks;
  }

  private identifyBenefits(pattern: EmailPattern, context: RuleAnalysisContext): string[] {
    const benefits: string[] = [];
    
    benefits.push(`Automatically handle ${pattern.frequency} emails`);
    benefits.push('Reduce inbox clutter');
    benefits.push('Save time on manual email management');
    
    return benefits;
  }

  private assessImplementationDifficulty(conditions: RuleCondition[], actions: RuleAction[]): 'easy' | 'medium' | 'hard' {
    const complexity = this.assessComplexity(conditions, actions);
    return complexity === 'simple' ? 'easy' : complexity === 'moderate' ? 'medium' : 'hard';
  }

  private generateTestingSteps(conditions: RuleCondition[], actions: RuleAction[]): string[] {
    return [
      'Test rule with a small sample of emails',
      'Verify conditions match intended emails',
      'Check that actions perform as expected',
      'Monitor for any false positives'
    ];
  }
}

/**
 * Default rules engine configuration
 */
export const defaultRulesEngineConfig: RuleSuggestionConfig = {
  model: {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'openai',
    type: 'chat',
    capabilities: ['text-analysis', 'pattern-recognition', 'rule-generation'],
    limits: {
      maxTokens: 4096,
      maxContextLength: 128000
    },
    isDefault: true,
    isAvailable: true
  },
  analysisDepth: 'standard',
  suggestionLimit: 10,
  confidenceThreshold: 0.6,
  includeComplexRules: true,
  focusAreas: ['organization', 'productivity', 'cleanup'],
  learningEnabled: true
};

/**
 * Create and configure AI rules engine
 */
export function createAIRulesEngine(config: Partial<RuleSuggestionConfig> = {}): AIRulesEngine {
  const finalConfig = { ...defaultRulesEngineConfig, ...config };
  return new AIRulesEngine(finalConfig);
}

