import { EmailCategory, ParsedMessage, Thread } from '../../types/email';
import { AIModel, AITrainingData } from '../../types/assistant';

export interface CategorizationResult {
  category: EmailCategory;
  confidence: number;
  reasoning: string;
  alternatives: Array<{
    category: EmailCategory;
    confidence: number;
  }>;
  processingTime: number;
  features: CategorizationFeatures;
}

export interface CategorizationFeatures {
  senderDomain: string;
  senderType: 'known' | 'unknown' | 'service' | 'noreply';
  hasUnsubscribe: boolean;
  hasAttachments: boolean;
  contentLength: number;
  subjectKeywords: string[];
  bodyKeywords: string[];
  headerAnalysis: {
    listHeaders: boolean;
    marketingHeaders: boolean;
    securityHeaders: boolean;
    receiptHeaders: boolean;
  };
  languageFeatures: {
    sentiment: 'positive' | 'negative' | 'neutral';
    urgency: 'high' | 'medium' | 'low';
    formality: 'formal' | 'informal' | 'automated';
    personalization: number; // 0-1 score
  };
}

export interface CategorizationConfig {
  model: AIModel;
  enableLearning: boolean;
  confidenceThreshold: number;
  fallbackCategory: EmailCategory;
  batchSize: number;
  useCache: boolean;
  contextSize: number; // Number of previous emails to consider
}

export interface CategoryPattern {
  category: EmailCategory;
  patterns: {
    senderPatterns: RegExp[];
    subjectPatterns: RegExp[];
    bodyPatterns: RegExp[];
    headerPatterns: Record<string, RegExp>;
  };
  weight: number;
  examples: string[];
}

export interface LearningFeedback {
  messageId: string;
  originalCategory: EmailCategory;
  correctedCategory: EmailCategory;
  confidence: number;
  userFeedback?: string;
  timestamp: Date;
  features: CategorizationFeatures;
}

export class EmailCategorizer {
  private config: CategorizationConfig;
  private patterns: CategoryPattern[];
  private trainingData: AITrainingData[];
  private cache: Map<string, CategorizationResult>;
  private feedbackQueue: LearningFeedback[];

  constructor(config: CategorizationConfig) {
    this.config = config;
    this.patterns = this.initializePatterns();
    this.trainingData = [];
    this.cache = new Map();
    this.feedbackQueue = [];
  }

  /**
   * Categorize a single email message
   */
  async categorizeMessage(message: ParsedMessage, context?: ParsedMessage[]): Promise<CategorizationResult> {
    const startTime = Date.now();
    
    // Check cache first
    const cacheKey = this.getCacheKey(message);
    if (this.config.useCache && this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }

    // Extract features
    const features = this.extractFeatures(message);
    
    // Apply pattern matching
    const patternResults = this.applyPatterns(features, message);
    
    // Apply ML classification (simulated)
    const mlResults = await this.applyMLClassification(features, message, context);
    
    // Combine results
    const result = this.combineResults(patternResults, mlResults, features);
    result.processingTime = Date.now() - startTime;

    // Cache result
    if (this.config.useCache) {
      this.cache.set(cacheKey, result);
    }

    return result;
  }

  /**
   * Categorize multiple emails in batch
   */
  async categorizeThreads(threads: Thread[]): Promise<Map<string, CategorizationResult>> {
    const results = new Map<string, CategorizationResult>();
    const batches = this.createBatches(threads, this.config.batchSize);

    for (const batch of batches) {
      const batchPromises = batch.map(async (thread) => {
        const lastMessage = thread.messages[thread.messages.length - 1];
        const context = thread.messages.slice(0, -1).slice(-this.config.contextSize);
        const result = await this.categorizeMessage(lastMessage, context);
        return { threadId: thread.id, result };
      });

      const batchResults = await Promise.all(batchPromises);
      batchResults.forEach(({ threadId, result }) => {
        results.set(threadId, result);
      });

      // Add small delay between batches to prevent overwhelming
      await this.delay(50);
    }

    return results;
  }

  /**
   * Learn from user feedback
   */
  async addFeedback(feedback: LearningFeedback): Promise<void> {
    this.feedbackQueue.push(feedback);

    // Convert to training data
    const trainingEntry: AITrainingData = {
      id: `feedback_${Date.now()}_${Math.random()}`,
      type: 'classification',
      input: {
        features: feedback.features,
        messageId: feedback.messageId
      },
      expectedOutput: feedback.correctedCategory,
      actualOutput: feedback.originalCategory,
      feedback: feedback.originalCategory === feedback.correctedCategory ? 'correct' : 'incorrect',
      corrections: feedback.correctedCategory,
      timestamp: feedback.timestamp,
      userId: 'user' // In real app, would be actual user ID
    };

    this.trainingData.push(trainingEntry);

    // Process feedback if queue is full
    if (this.feedbackQueue.length >= 10) {
      await this.processFeedbackBatch();
    }
  }

  /**
   * Get categorization statistics
   */
  getStats(): {
    totalProcessed: number;
    accuracyByCategory: Record<EmailCategory, number>;
    averageConfidence: number;
    averageProcessingTime: number;
    cacheHitRate: number;
    learningProgress: {
      feedbackCount: number;
      improvementRate: number;
      lastUpdate: Date;
    };
  } {
    const processed = this.trainingData.length;
    const correct = this.trainingData.filter(d => d.feedback === 'correct').length;
    
    return {
      totalProcessed: processed,
      accuracyByCategory: this.calculateAccuracyByCategory(),
      averageConfidence: this.calculateAverageConfidence(),
      averageProcessingTime: this.calculateAverageProcessingTime(),
      cacheHitRate: this.calculateCacheHitRate(),
      learningProgress: {
        feedbackCount: this.feedbackQueue.length,
        improvementRate: processed > 0 ? correct / processed : 0,
        lastUpdate: new Date()
      }
    };
  }

  /**
   * Extract features from email message
   */
  private extractFeatures(message: ParsedMessage): CategorizationFeatures {
    const senderDomain = this.extractDomain(message.from.email);
    const senderType = this.classifySenderType(message.from, message.headers);
    
    return {
      senderDomain,
      senderType,
      hasUnsubscribe: this.hasUnsubscribeHeader(message.headers),
      hasAttachments: message.attachments.length > 0,
      contentLength: (message.body.text || message.body.plain || '').length,
      subjectKeywords: this.extractKeywords(message.subject),
      bodyKeywords: this.extractKeywords(message.body.text || message.body.plain || ''),
      headerAnalysis: this.analyzeHeaders(message.headers),
      languageFeatures: this.analyzeLanguage(message.subject, message.body.text || message.body.plain || '')
    };
  }

  /**
   * Apply pattern-based classification
   */
  private applyPatterns(features: CategorizationFeatures, message: ParsedMessage): Array<{
    category: EmailCategory;
    confidence: number;
    matchedPatterns: string[];
  }> {
    const results: Array<{
      category: EmailCategory;
      confidence: number;
      matchedPatterns: string[];
    }> = [];

    for (const pattern of this.patterns) {
      const matches: string[] = [];
      let score = 0;

      // Check sender patterns
      pattern.patterns.senderPatterns.forEach(regex => {
        if (regex.test(message.from.email) || (message.from.name && regex.test(message.from.name))) {
          matches.push(`sender:${regex.source}`);
          score += 0.3;
        }
      });

      // Check subject patterns
      pattern.patterns.subjectPatterns.forEach(regex => {
        if (regex.test(message.subject)) {
          matches.push(`subject:${regex.source}`);
          score += 0.25;
        }
      });

      // Check body patterns
      pattern.patterns.bodyPatterns.forEach(regex => {
        const content = message.body.text || message.body.plain || '';
        if (regex.test(content)) {
          matches.push(`body:${regex.source}`);
          score += 0.2;
        }
      });

      // Check header patterns
      Object.entries(pattern.patterns.headerPatterns).forEach(([header, regex]) => {
        const headerValue = message.headers[header];
        if (headerValue && regex.test(headerValue)) {
          matches.push(`header:${header}`);
          score += 0.25;
        }
      });

      if (matches.length > 0) {
        results.push({
          category: pattern.category,
          confidence: Math.min(score * pattern.weight, 1),
          matchedPatterns: matches
        });
      }
    }

    return results.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Apply ML-based classification (simulated)
   */
  private async applyMLClassification(
    features: CategorizationFeatures,
    message: ParsedMessage,
    context?: ParsedMessage[]
  ): Promise<{
    category: EmailCategory;
    confidence: number;
    reasoning: string;
  }> {
    // Simulate ML processing delay
    await this.delay(100 + Math.random() * 200);

    // Simulate ML decision based on features
    const { category, confidence, reasoning } = this.simulateMLClassification(features, message, context);

    return { category, confidence, reasoning };
  }

  /**
   * Combine pattern and ML results
   */
  private combineResults(
    patternResults: Array<{ category: EmailCategory; confidence: number; matchedPatterns: string[] }>,
    mlResult: { category: EmailCategory; confidence: number; reasoning: string },
    features: CategorizationFeatures
  ): CategorizationResult {
    // Weight pattern vs ML results
    const patternWeight = 0.4;
    const mlWeight = 0.6;

    // Get top pattern result
    const topPattern = patternResults[0];
    
    let finalCategory: EmailCategory;
    let finalConfidence: number;
    let reasoning: string;

    if (topPattern && topPattern.confidence > 0.7) {
      // High confidence pattern match
      finalCategory = topPattern.category;
      finalConfidence = topPattern.confidence * patternWeight + mlResult.confidence * mlWeight;
      reasoning = `Pattern match: ${topPattern.matchedPatterns.join(', ')}. ${mlResult.reasoning}`;
    } else if (mlResult.confidence > this.config.confidenceThreshold) {
      // Trust ML result
      finalCategory = mlResult.category;
      finalConfidence = mlResult.confidence;
      reasoning = mlResult.reasoning;
    } else {
      // Low confidence, use fallback
      finalCategory = this.config.fallbackCategory;
      finalConfidence = 0.5;
      reasoning = 'Low confidence classification, using fallback category';
    }

    // Generate alternatives
    const alternatives = patternResults
      .filter(r => r.category !== finalCategory)
      .slice(0, 3)
      .map(r => ({ category: r.category, confidence: r.confidence }));

    return {
      category: finalCategory,
      confidence: Math.min(finalConfidence, 1),
      reasoning,
      alternatives,
      processingTime: 0, // Will be set by caller
      features
    };
  }

  /**
   * Initialize category patterns
   */
  private initializePatterns(): CategoryPattern[] {
    return [
      {
        category: 'Newsletter',
        weight: 1.0,
        patterns: {
          senderPatterns: [
            /newsletter|digest|updates?/i,
            /no-?reply|noreply/i,
            /news@|newsletter@|updates@/i
          ],
          subjectPatterns: [
            /newsletter|digest|weekly|monthly|daily/i,
            /update|news|bulletin/i
          ],
          bodyPatterns: [
            /unsubscribe|opt.?out/i,
            /view.*browser|online.*version/i,
            /newsletter|digest/i
          ],
          headerPatterns: {
            'list-unsubscribe': /.+/,
            'list-id': /.+/
          }
        },
        examples: ['Weekly Newsletter', 'Product Updates']
      },
      {
        category: 'Receipt',
        weight: 1.0,
        patterns: {
          senderPatterns: [
            /@amazon|@paypal|@stripe|@square/i,
            /receipt|billing|payment/i,
            /no-?reply.*payment|no-?reply.*order/i
          ],
          subjectPatterns: [
            /receipt|invoice|order.*confirmation/i,
            /payment.*received|transaction|purchase/i,
            /your.*order|order.*#\d+/i
          ],
          bodyPatterns: [
            /order.*number|transaction.*id|receipt/i,
            /\$[\d,]+\.\d{2}|total.*amount/i,
            /thank.*you.*purchase|payment.*confirmed/i
          ],
          headerPatterns: {}
        },
        examples: ['Order Receipt', 'Payment Confirmation']
      },
      {
        category: 'Marketing',
        weight: 1.0,
        patterns: {
          senderPatterns: [
            /marketing@|promo@|offers@/i,
            /no-?reply.*marketing/i
          ],
          subjectPatterns: [
            /sale|discount|offer|deal|promo/i,
            /limited.*time|urgent|act.*now/i,
            /free|save|\d+%.*off/i
          ],
          bodyPatterns: [
            /limited.*time|expires|hurry/i,
            /discount|coupon|promo.*code/i,
            /shop.*now|buy.*now|order.*today/i
          ],
          headerPatterns: {
            'list-unsubscribe': /.+/
          }
        },
        examples: ['Flash Sale', 'Special Offer']
      },
      {
        category: 'Work',
        weight: 1.0,
        patterns: {
          senderPatterns: [
            /@.*\.(com|org|net)$/i
          ],
          subjectPatterns: [
            /meeting|call|project|deadline/i,
            /urgent|asap|important/i,
            /re:|fwd?:|follow.*up/i
          ],
          bodyPatterns: [
            /meeting|conference|call|schedule/i,
            /project|deadline|deliverable/i,
            /team|colleague|manager/i
          ],
          headerPatterns: {}
        },
        examples: ['Project Update', 'Meeting Request']
      },
      {
        category: 'Personal',
        weight: 1.0,
        patterns: {
          senderPatterns: [
            /@gmail\.com|@yahoo\.com|@hotmail\.com|@outlook\.com/i
          ],
          subjectPatterns: [
            /hi|hello|hey|sup/i,
            /family|friend|personal/i
          ],
          bodyPatterns: [
            /how.*are.*you|hope.*you.*well/i,
            /family|friend|personal/i,
            /catch.*up|get.*together/i
          ],
          headerPatterns: {}
        },
        examples: ['Personal Message', 'Family Update']
      },
      {
        category: 'Social',
        weight: 1.0,
        patterns: {
          senderPatterns: [
            /@facebook|@twitter|@linkedin|@instagram/i,
            /@.*social/i
          ],
          subjectPatterns: [
            /notification|activity|mention/i,
            /friend.*request|connection/i,
            /like|comment|share/i
          ],
          bodyPatterns: [
            /notification|activity/i,
            /like|comment|share|tag/i,
            /profile|social.*network/i
          ],
          headerPatterns: {}
        },
        examples: ['Facebook Notification', 'LinkedIn Update']
      },
      {
        category: 'Notification',
        weight: 1.0,
        patterns: {
          senderPatterns: [
            /notification|alert|system/i,
            /no-?reply.*system/i
          ],
          subjectPatterns: [
            /notification|alert|reminder/i,
            /system|automated|auto/i,
            /security|login|password/i
          ],
          bodyPatterns: [
            /notification|alert|reminder/i,
            /automated.*message|system.*generated/i,
            /account.*activity|security/i
          ],
          headerPatterns: {}
        },
        examples: ['System Alert', 'Security Notification']
      },
      {
        category: 'Spam',
        weight: 1.0,
        patterns: {
          senderPatterns: [
            /\d{5,}@/i, // Random number senders
            /@.*\.tk$|@.*\.ml$/i // Suspicious domains
          ],
          subjectPatterns: [
            /congratulations.*won|lottery.*winner/i,
            /urgent.*transfer|nigerian.*prince/i,
            /click.*here.*now|act.*immediately/i
          ],
          bodyPatterns: [
            /congratulations.*won|lottery/i,
            /transfer.*money|millions.*dollars/i,
            /click.*here.*immediately/i
          ],
          headerPatterns: {}
        },
        examples: ['Phishing Attempt', 'Spam Email']
      }
    ];
  }

  /**
   * Simulate ML classification logic
   */
  private simulateMLClassification(
    features: CategorizationFeatures,
    message: ParsedMessage,
    context?: ParsedMessage[]
  ): { category: EmailCategory; confidence: number; reasoning: string } {
    // Simple rule-based simulation of ML classification
    
    // Newsletter detection
    if (features.hasUnsubscribe && features.senderType === 'noreply') {
      return {
        category: 'Newsletter',
        confidence: 0.85,
        reasoning: 'Email has unsubscribe link and is from a no-reply address, typical of newsletters'
      };
    }

    // Receipt detection
    if (features.subjectKeywords.some(k => ['receipt', 'order', 'payment', 'invoice'].includes(k.toLowerCase()))) {
      return {
        category: 'Receipt',
        confidence: 0.9,
        reasoning: 'Subject contains receipt/order keywords indicating transactional email'
      };
    }

    // Marketing detection
    if (features.subjectKeywords.some(k => ['sale', 'discount', 'offer', 'deal'].includes(k.toLowerCase()))) {
      return {
        category: 'Marketing',
        confidence: 0.8,
        reasoning: 'Subject contains promotional keywords indicating marketing email'
      };
    }

    // Work email detection
    if (features.languageFeatures.formality === 'formal' && 
        features.subjectKeywords.some(k => ['meeting', 'project', 'deadline'].includes(k.toLowerCase()))) {
      return {
        category: 'Work',
        confidence: 0.85,
        reasoning: 'Formal language with work-related keywords indicates business email'
      };
    }

    // Personal email detection
    if (features.senderDomain.includes('gmail.com') || features.senderDomain.includes('yahoo.com')) {
      return {
        category: 'Personal',
        confidence: 0.7,
        reasoning: 'Email from personal email provider domain'
      };
    }

    // Social notification
    if (features.senderDomain.includes('facebook') || features.senderDomain.includes('twitter') || 
        features.senderDomain.includes('linkedin')) {
      return {
        category: 'Social',
        confidence: 0.9,
        reasoning: 'Email from social media platform'
      };
    }

    // Security/Notification
    if (features.subjectKeywords.some(k => ['security', 'alert', 'notification'].includes(k.toLowerCase()))) {
      return {
        category: 'Notification',
        confidence: 0.8,
        reasoning: 'Contains security or notification keywords'
      };
    }

    // Spam detection
    if (features.languageFeatures.urgency === 'high' && 
        features.subjectKeywords.some(k => ['urgent', 'immediate', 'act now'].includes(k.toLowerCase()))) {
      return {
        category: 'Spam',
        confidence: 0.75,
        reasoning: 'High urgency language with suspicious keywords'
      };
    }

    // Default to Other
    return {
      category: 'Other',
      confidence: 0.6,
      reasoning: 'Could not confidently classify into specific category'
    };
  }

  // Helper methods
  private extractDomain(email: string): string {
    return email.split('@')[1] || '';
  }

  private classifySenderType(from: { email: string; name?: string }, headers: any): 'known' | 'unknown' | 'service' | 'noreply' {
    if (from.email.includes('noreply') || from.email.includes('no-reply')) {
      return 'noreply';
    }
    if (from.email.includes('service') || from.email.includes('support') || from.email.includes('info')) {
      return 'service';
    }
    return from.name ? 'known' : 'unknown';
  }

  private hasUnsubscribeHeader(headers: any): boolean {
    return !!(headers['list-unsubscribe'] || headers['List-Unsubscribe']);
  }

  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase().match(/\b\w{3,}\b/g) || [];
    const commonWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'her', 'was', 'one', 'our', 'had', 'will'];
    return words.filter(word => !commonWords.includes(word)).slice(0, 10);
  }

  private analyzeHeaders(headers: any) {
    return {
      listHeaders: !!(headers['list-id'] || headers['List-ID']),
      marketingHeaders: !!(headers['x-campaign'] || headers['X-Campaign']),
      securityHeaders: !!(headers['x-security'] || headers['X-Security']),
      receiptHeaders: !!(headers['x-order'] || headers['X-Order'])
    };
  }

  private analyzeLanguage(subject: string, body: string) {
    const text = (subject + ' ' + body).toLowerCase();
    
    // Simple sentiment analysis
    const positiveWords = ['great', 'excellent', 'good', 'happy', 'love', 'amazing'];
    const negativeWords = ['bad', 'terrible', 'hate', 'problem', 'issue', 'error'];
    const positiveScore = positiveWords.filter(word => text.includes(word)).length;
    const negativeScore = negativeWords.filter(word => text.includes(word)).length;
    
    let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
    if (positiveScore > negativeScore) sentiment = 'positive';
    else if (negativeScore > positiveScore) sentiment = 'negative';

    // Urgency detection
    const urgentWords = ['urgent', 'asap', 'immediate', 'now', 'quickly', 'rush'];
    const urgencyScore = urgentWords.filter(word => text.includes(word)).length;
    let urgency: 'high' | 'medium' | 'low' = 'low';
    if (urgencyScore >= 2) urgency = 'high';
    else if (urgencyScore >= 1) urgency = 'medium';

    // Formality detection
    const formalWords = ['please', 'sincerely', 'regards', 'dear', 'thank you'];
    const informalWords = ['hey', 'hi', 'sup', 'yeah', 'cool'];
    const formalScore = formalWords.filter(word => text.includes(word)).length;
    const informalScore = informalWords.filter(word => text.includes(word)).length;
    
    let formality: 'formal' | 'informal' | 'automated' = 'automated';
    if (formalScore > informalScore) formality = 'formal';
    else if (informalScore > 0) formality = 'informal';

    return {
      sentiment,
      urgency,
      formality,
      personalization: formalScore > 0 ? 0.7 : 0.3
    };
  }

  private getCacheKey(message: ParsedMessage): string {
    return `${message.id}_${message.from.email}_${message.subject.substring(0, 50)}`;
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async processFeedbackBatch(): Promise<void> {
    // Simulate processing feedback for learning
    await this.delay(200);
    
    // In a real implementation, this would update the ML model
    // For now, we'll just clear the queue
    this.feedbackQueue = [];
  }

  private calculateAccuracyByCategory(): Record<EmailCategory, number> {
    const categories: EmailCategory[] = [
      'Newsletter', 'Receipt', 'Marketing', 'Personal', 'Work', 
      'Social', 'Notification', 'Spam', 'Other'
    ];
    
    const accuracy: Record<EmailCategory, number> = {} as any;
    
    categories.forEach(category => {
      const categoryData = this.trainingData.filter(d => d.expectedOutput === category);
      const correct = categoryData.filter(d => d.feedback === 'correct');
      accuracy[category] = categoryData.length > 0 ? correct.length / categoryData.length : 0;
    });

    return accuracy;
  }

  private calculateAverageConfidence(): number {
    // Simulate based on cache entries
    const confidences = Array.from(this.cache.values()).map(r => r.confidence);
    return confidences.length > 0 ? confidences.reduce((a, b) => a + b, 0) / confidences.length : 0.75;
  }

  private calculateAverageProcessingTime(): number {
    const times = Array.from(this.cache.values()).map(r => r.processingTime);
    return times.length > 0 ? times.reduce((a, b) => a + b, 0) / times.length : 150;
  }

  private calculateCacheHitRate(): number {
    // Simple simulation
    return Math.random() * 0.3 + 0.2; // 20-50% hit rate
  }
}

/**
 * Default categorization configuration
 */
export const defaultCategorizationConfig: CategorizationConfig = {
  model: {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'openai',
    type: 'classification',
    capabilities: ['text-classification', 'feature-extraction'],
    limits: {
      maxTokens: 4096,
      maxContextLength: 128000,
      rateLimit: 500
    },
    pricing: {
      inputTokens: 0.01,
      outputTokens: 0.03,
      currency: 'USD'
    },
    isDefault: true,
    isAvailable: true
  },
  enableLearning: true,
  confidenceThreshold: 0.7,
  fallbackCategory: 'Other',
  batchSize: 20,
  useCache: true,
  contextSize: 5
};

/**
 * Create and configure email categorizer
 */
export function createEmailCategorizer(config: Partial<CategorizationConfig> = {}): EmailCategorizer {
  const finalConfig = { ...defaultCategorizationConfig, ...config };
  return new EmailCategorizer(finalConfig);
}

