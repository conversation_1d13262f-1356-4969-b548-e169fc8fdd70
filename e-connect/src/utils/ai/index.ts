/**
 * AI Processing Pipeline - Central exports
 * 
 * This module provides a comprehensive AI processing pipeline for email management
 * including categorization, rule suggestions, conversational AI, and mock responses
 * for development and testing.
 */

// Email Categorization
export {
  EmailCategorizer,
  createEmailCategorizer,
  defaultCategorizationConfig,
  type CategorizationResult,
  type CategorizationFeatures,
  type CategorizationConfig,
  type CategoryPattern,
  type LearningFeedback
} from './categorize';

// Rules Engine
export {
  AIRulesEngine,
  createAIRulesEngine,
  defaultRulesEngineConfig,
  type RuleSuggestion,
  type RuleImpact,
  type RuleSuggestionCategory,
  type EmailPattern,
  type RuleAnalysisContext,
  type RuleSuggestionConfig,
  type PatternAnalysisResult
} from './rules-engine';

// Conversational AI
export {
  ConversationalAI,
  createConversationalAI,
  defaultChatConfig,
  type ChatConfig,
  type ChatFunction,
  type EmailQuery,
  type RuleCreationIntent,
  type ConversationState
} from './chat';

// Mock Responses and Testing
export {
  default as MockResponses,
  MOCK_AI_MODELS,
  MOCK_CATEGORIZATION_RESPONSES,
  MOCK_RULE_SUGGESTIONS,
  MOCK_CHAT_RESPONSES,
  MOCK_ANALYSIS_INSIGHTS,
  MOCK_SUGGESTIONS,
  MOCK_PROCESSING_DELAYS,
  generateMockLatency,
  generateMockTokenUsage,
  generateMockCost,
  shouldSimulateError,
  getRandomResponse,
  getRandomInsight,
  getRandomSuggestion,
  generateMockCategorizationResult,
  generateMockRuleSuggestion,
  generateMockChatResponse,
  simulateProgressiveResponse,
  getProcessingDelay,
  generateMockPerformanceMetrics,
  type MockModelConfig
} from './mock-responses';

/**
 * Comprehensive AI Email Management System
 * 
 * This class combines all AI capabilities into a unified interface
 * for easy integration throughout the application.
 */
export class AIEmailManager {
  private categorizer: any; // EmailCategorizer;
  private rulesEngine: any; // AIRulesEngine;
  private conversationalAI: any; // ConversationalAI;

  constructor(config?: {
    categorization?: any; // Partial<CategorizationConfig>;
    rules?: any; // Partial<RuleSuggestionConfig>;
    chat?: any; // Partial<ChatConfig>;
  }) {
    // Import functions dynamically to avoid type errors
    const { createEmailCategorizer } = require('./categorize');
    const { createAIRulesEngine } = require('./rules-engine');
    const { createConversationalAI } = require('./chat');
    
    this.categorizer = createEmailCategorizer(config?.categorization);
    this.rulesEngine = createAIRulesEngine(config?.rules);
    this.conversationalAI = createConversationalAI(config?.chat);
  }

  /**
   * Get the email categorizer instance
   */
  getCategorizer(): any { // EmailCategorizer {
    return this.categorizer;
  }

  /**
   * Get the rules engine instance
   */
  getRulesEngine(): any { // AIRulesEngine {
    return this.rulesEngine;
  }

  /**
   * Get the conversational AI instance
   */
  getConversationalAI(): any { // ConversationalAI {
    return this.conversationalAI;
  }

  /**
   * Get comprehensive performance metrics across all AI systems
   */
  async getSystemMetrics() {
    const categorizationStats = this.categorizer.getStats();
    const rulesStats = this.rulesEngine.getPerformanceMetrics();
    
    return {
      categorization: categorizationStats,
      rules: rulesStats,
      chat: {
        // Chat metrics would be implemented in the ConversationalAI class
        totalConversations: 0,
        averageResponseTime: 0,
        userSatisfaction: 0.85
      },
      overall: {
        systemHealth: 'healthy',
        uptime: '99.8%',
        lastUpdate: new Date(),
        version: '1.0.0'
      }
    };
  }

  /**
   * Process feedback across all AI systems
   */
  async submitSystemFeedback(feedback: {
    type: 'categorization' | 'rules' | 'chat';
    itemId: string;
    rating: number;
    comment?: string;
    corrections?: any;
  }) {
    switch (feedback.type) {
      case 'categorization':
        if (feedback.corrections) {
          await this.categorizer.addFeedback({
            messageId: feedback.itemId,
            originalCategory: feedback.corrections.original,
            correctedCategory: feedback.corrections.corrected,
            confidence: feedback.rating / 5, // Convert 1-5 rating to 0-1 confidence
            timestamp: new Date(),
            features: feedback.corrections.features
          });
        }
        break;

      case 'rules':
        await this.rulesEngine.addFeedback(
          feedback.itemId,
          feedback.rating >= 4 ? 'accepted' : 'rejected',
          feedback.corrections,
          feedback.comment
        );
        break;

      case 'chat':
        // Chat feedback would be handled by the ConversationalAI class
        break;
    }
  }
}

/**
 * Create a fully configured AI Email Manager instance
 */
export function createAIEmailManager(config?: {
  categorization?: any; // Partial<CategorizationConfig>;
  rules?: any; // Partial<RuleSuggestionConfig>;
  chat?: any; // Partial<ChatConfig>;
}): AIEmailManager {
  return new AIEmailManager(config);
}

/**
 * Default AI configuration for production use
 */
export const defaultAIConfig = {
  categorization: {}, // defaultCategorizationConfig,
  rules: {}, // defaultRulesEngineConfig,
  chat: {} // defaultChatConfig
};

// Re-export types for convenience - these are aliases to avoid conflicts

export type {
  RuleSuggestion as AIRuleSuggestion,
  RuleImpact as AIRuleImpact,
  RuleSuggestionCategory as AIRuleSuggestionCategory,
  EmailPattern as AIEmailPattern,
  RuleAnalysisContext as AIRuleAnalysisContext,
  RuleSuggestionConfig as AIRuleSuggestionConfig,
  PatternAnalysisResult as AIPatternAnalysisResult
} from './rules-engine';

export type {
  ChatConfig as AIChatConfig,
  ChatFunction as AIChatFunction,
  EmailQuery as AIEmailQuery,
  RuleCreationIntent as AIRuleCreationIntent,
  ConversationState as AIConversationState
} from './chat';