import { EmailCategory, ParsedMessage } from '../../types/email';
import { Rule, RuleCondition, RuleAction } from '../../types/rules';
import { AIResponse, ChatMessage, Suggestion } from '../../types/assistant';
import { CategorizationResult } from './categorize';
import { RuleSuggestion } from './rules-engine';

/**
 * Mock AI model responses for development and testing
 */

export interface MockModelConfig {
  name: string;
  baseLatency: number; // ms
  variabilityFactor: number; // 0-1
  errorRate: number; // 0-1
  costPerToken: number;
  features: string[];
}

export const MOCK_AI_MODELS: Record<string, MockModelConfig> = {
  'gpt-4-turbo': {
    name: 'GPT-4 Turbo',
    baseLatency: 800,
    variabilityFactor: 0.3,
    errorRate: 0.02,
    costPerToken: 0.00003,
    features: ['chat', 'classification', 'analysis', 'function-calling']
  },
  'gpt-3.5-turbo': {
    name: 'GPT-3.5 Turbo',
    baseLatency: 400,
    variabilityFactor: 0.2,
    errorRate: 0.05,
    costPerToken: 0.000002,
    features: ['chat', 'classification']
  },
  'claude-3-opus': {
    name: 'Claude 3 Opus',
    baseLatency: 1200,
    variabilityFactor: 0.4,
    errorRate: 0.01,
    costPerToken: 0.000075,
    features: ['chat', 'analysis', 'reasoning', 'long-context']
  },
  'claude-3-haiku': {
    name: 'Claude 3 Haiku',
    baseLatency: 300,
    variabilityFactor: 0.15,
    errorRate: 0.03,
    costPerToken: 0.00000025,
    features: ['chat', 'classification', 'speed']
  }
};

export const MOCK_CATEGORIZATION_RESPONSES: Record<string, Partial<CategorizationResult>[]> = {
  newsletter: [
    {
      category: 'Newsletter',
      confidence: 0.92,
      reasoning: 'Email contains unsubscribe link, newsletter keywords, and is from a no-reply address',
      alternatives: [
        { category: 'Marketing', confidence: 0.15 },
        { category: 'Notification', confidence: 0.08 }
      ]
    },
    {
      category: 'Newsletter',
      confidence: 0.87,
      reasoning: 'Subject contains "weekly digest" and sender has newsletter patterns',
      alternatives: [
        { category: 'Marketing', confidence: 0.18 },
        { category: 'Other', confidence: 0.12 }
      ]
    }
  ],
  marketing: [
    {
      category: 'Marketing',
      confidence: 0.89,
      reasoning: 'Email contains promotional language, discount offers, and marketing headers',
      alternatives: [
        { category: 'Newsletter', confidence: 0.22 },
        { category: 'Spam', confidence: 0.05 }
      ]
    },
    {
      category: 'Marketing',
      confidence: 0.94,
      reasoning: 'Subject line contains "50% OFF" and sender is from marketing domain',
      alternatives: [
        { category: 'Newsletter', confidence: 0.08 },
        { category: 'Spam', confidence: 0.03 }
      ]
    }
  ],
  work: [
    {
      category: 'Work',
      confidence: 0.95,
      reasoning: 'Email is from corporate domain, contains meeting request, and has formal language',
      alternatives: [
        { category: 'Personal', confidence: 0.08 },
        { category: 'Notification', confidence: 0.05 }
      ]
    },
    {
      category: 'Work',
      confidence: 0.88,
      reasoning: 'Sender is from company domain and subject contains project keywords',
      alternatives: [
        { category: 'Personal', confidence: 0.15 },
        { category: 'Other', confidence: 0.07 }
      ]
    }
  ],
  personal: [
    {
      category: 'Personal',
      confidence: 0.91,
      reasoning: 'Email is from personal domain, uses informal language, and contains personal keywords',
      alternatives: [
        { category: 'Work', confidence: 0.12 },
        { category: 'Other', confidence: 0.08 }
      ]
    }
  ],
  receipt: [
    {
      category: 'Receipt',
      confidence: 0.96,
      reasoning: 'Email contains order confirmation, transaction details, and receipt keywords',
      alternatives: [
        { category: 'Notification', confidence: 0.08 },
        { category: 'Marketing', confidence: 0.03 }
      ]
    }
  ],
  spam: [
    {
      category: 'Spam',
      confidence: 0.85,
      reasoning: 'Email contains suspicious patterns, urgent language, and potential phishing indicators',
      alternatives: [
        { category: 'Marketing', confidence: 0.25 },
        { category: 'Other', confidence: 0.10 }
      ]
    }
  ]
};

export const MOCK_RULE_SUGGESTIONS: RuleSuggestion[] = [
  {
    id: 'suggestion_newsletter_archive',
    name: 'Auto-archive Newsletter Emails',
    description: 'Automatically archive newsletter emails older than 7 days to keep inbox clean',
    confidence: 0.88,
    priority: 'high',
    impact: {
      emailsAffected: 45,
      timesSaved: 15, // minutes per week
      clutterReduced: 25, // percentage
      automationLevel: 0.9,
      riskLevel: 'low',
      reversibility: true
    },
    rule: {
      name: 'Auto-archive Newsletter Emails',
      description: 'Archive newsletter emails older than 7 days',
      enabled: true,
      priority: 1,
      conditions: [
        {
          id: 'cond_1',
          type: 'category',
          operator: 'equals',
          value: 'Newsletter'
        },
        {
          id: 'cond_2',
          type: 'age',
          operator: 'greaterThan',
          value: 7
        }
      ],
      conditionLogic: 'all',
      actions: [
        {
          id: 'action_1',
          type: 'archive',
          order: 1
        },
        {
          id: 'action_2',
          type: 'markRead',
          order: 2
        }
      ]
    },
    reasoning: 'You have 45 newsletter emails older than 7 days taking up inbox space. Most users rarely revisit old newsletters.',
    examples: [
      '<EMAIL>: TechCrunch Daily - Week in Review',
      '<EMAIL>: GitHub Weekly Digest',
      '<EMAIL>: The Morning Brew - Tech Edition'
    ],
    category: {
      type: 'organization',
      subcategory: 'newsletter-management',
      tags: ['newsletter', 'archive', 'cleanup']
    },
    complexity: 'simple',
    estimatedTimeToImplement: 5
  },
  {
    id: 'suggestion_marketing_label',
    name: 'Label Marketing Emails',
    description: 'Automatically label promotional and marketing emails for easy identification',
    confidence: 0.82,
    priority: 'medium',
    impact: {
      emailsAffected: 32,
      timesSaved: 8,
      clutterReduced: 15,
      automationLevel: 0.8,
      riskLevel: 'low',
      reversibility: true
    },
    rule: {
      name: 'Label Marketing Emails',
      description: 'Add "Marketing" label to promotional emails',
      enabled: true,
      priority: 2,
      conditions: [
        {
          id: 'cond_1',
          type: 'category',
          operator: 'equals',
          value: 'Marketing'
        }
      ],
      conditionLogic: 'all',
      actions: [
        {
          id: 'action_1',
          type: 'label',
          order: 1,
          params: { label: 'Marketing' }
        }
      ]
    },
    reasoning: 'You receive frequent marketing emails that could be better organized with labels for easier management.',
    examples: [
      '<EMAIL>: Prime Day Deals - Up to 50% Off',
      '<EMAIL>: Black Friday Sale - Limited Time',
      '<EMAIL>: New Arrivals - Shop Now'
    ],
    category: {
      type: 'organization',
      subcategory: 'labeling',
      tags: ['marketing', 'label', 'organization']
    },
    complexity: 'simple',
    estimatedTimeToImplement: 3
  },
  {
    id: 'suggestion_work_priority',
    name: 'Prioritize Work Emails',
    description: 'Mark emails from colleagues and important work domains as high priority',
    confidence: 0.91,
    priority: 'high',
    impact: {
      emailsAffected: 28,
      timesSaved: 20,
      clutterReduced: 10,
      automationLevel: 0.85,
      riskLevel: 'low',
      reversibility: true
    },
    rule: {
      name: 'Prioritize Work Emails',
      description: 'Mark important work emails as high priority',
      enabled: true,
      priority: 1,
      conditions: [
        {
          id: 'cond_1',
          type: 'from',
          operator: 'contains',
          value: '@company.com'
        },
        {
          id: 'cond_2',
          type: 'subject',
          operator: 'contains',
          value: 'urgent'
        }
      ],
      conditionLogic: 'any',
      actions: [
        {
          id: 'action_1',
          type: 'markImportant',
          order: 1
        },
        {
          id: 'action_2',
          type: 'label',
          order: 2,
          params: { label: 'Work-Priority' }
        }
      ]
    },
    reasoning: 'Important work emails should be easily identifiable to ensure timely responses and prevent missing critical communications.',
    examples: [
      '<EMAIL>: URGENT: Project Deadline Update',
      '<EMAIL>: Important Meeting Tomorrow',
      '<EMAIL>: Action Required: Benefits Enrollment'
    ],
    category: {
      type: 'productivity',
      subcategory: 'prioritization',
      tags: ['work', 'priority', 'urgent']
    },
    complexity: 'moderate',
    estimatedTimeToImplement: 8
  },
  {
    id: 'suggestion_receipt_organize',
    name: 'Organize Receipt Emails',
    description: 'Automatically categorize and archive receipt emails for record keeping',
    confidence: 0.86,
    priority: 'medium',
    impact: {
      emailsAffected: 18,
      timesSaved: 12,
      clutterReduced: 8,
      automationLevel: 0.9,
      riskLevel: 'low',
      reversibility: true
    },
    rule: {
      name: 'Organize Receipt Emails',
      description: 'Label and organize receipt emails',
      enabled: true,
      priority: 3,
      conditions: [
        {
          id: 'cond_1',
          type: 'category',
          operator: 'equals',
          value: 'Receipt'
        }
      ],
      conditionLogic: 'all',
      actions: [
        {
          id: 'action_1',
          type: 'label',
          order: 1,
          params: { label: 'Receipts' }
        },
        {
          id: 'action_2',
          type: 'markRead',
          order: 2
        }
      ]
    },
    reasoning: 'Receipt emails are important for record keeping but don\'t need immediate attention. Organizing them helps with expense tracking.',
    examples: [
      '<EMAIL>: Your Order Confirmation',
      '<EMAIL>: Payment Receipt',
      '<EMAIL>: Payment Successful'
    ],
    category: {
      type: 'organization',
      subcategory: 'financial',
      tags: ['receipt', 'financial', 'records']
    },
    complexity: 'simple',
    estimatedTimeToImplement: 4
  }
];

export const MOCK_CHAT_RESPONSES: Record<string, string[]> = {
  greeting: [
    "Hello! I'm your AI email assistant. I can help you organize your inbox, create automation rules, and analyze your email patterns. What would you like to work on today?",
    "Hi there! I'm here to help make your email management more efficient. I can create rules, analyze patterns, or help organize your inbox. How can I assist you?",
    "Welcome! I specialize in email automation and organization. Whether you want to create rules, get insights, or clean up your inbox, I'm here to help!"
  ],
  rule_creation: [
    "I'd be happy to help you create an email rule! Could you describe what you want to automate? For example, 'Archive all newsletters' or 'Label emails from my bank as Finance'.",
    "Let's create a rule together! What type of emails would you like to automate? I can help with organizing, labeling, archiving, or any other email action.",
    "Creating automation rules is a great way to save time! Tell me what you want to automate - I can handle conditions like sender, subject, content, or timing."
  ],
  analysis: [
    "I can analyze your email patterns to help you understand your habits and identify optimization opportunities. What aspect would you like me to examine?",
    "Email analysis can reveal interesting insights about your communication patterns. I can look at volume trends, top senders, categories, or response patterns.",
    "Let me analyze your emails to find patterns and suggest improvements. I can examine sender behavior, content types, timing, and more."
  ],
  organization: [
    "I can help organize your inbox in several ways: creating rules for automatic sorting, suggesting bulk actions for cleanup, or analyzing patterns for optimization strategies.",
    "Email organization is key to productivity! I can suggest automated rules, help with bulk actions, or provide personalized organization strategies based on your patterns.",
    "There are many ways to organize your emails effectively. I can create automation rules, suggest labeling systems, or help with inbox cleanup strategies."
  ],
  help: [
    "I'm here to help with all your email management needs! I can create automation rules, analyze email patterns, help organize your inbox, or answer questions about email productivity.",
    "I can assist with: creating smart automation rules, analyzing your email habits, organizing and cleaning your inbox, and providing productivity tips.",
    "My capabilities include: rule creation and management, email pattern analysis, inbox organization strategies, and personalized productivity recommendations."
  ],
  error: [
    "I apologize, but I encountered an issue processing your request. Could you please try rephrasing or providing more details?",
    "Sorry, I had trouble understanding that request. Could you clarify what you'd like me to help you with?",
    "I'm having difficulty with that request. Could you provide more specific details about what you want to accomplish?"
  ]
};

export const MOCK_ANALYSIS_INSIGHTS: Record<string, string[]> = {
  volume: [
    "You receive an average of 47 emails per day, with Tuesday and Wednesday being your busiest days.",
    "Your email volume has increased 23% compared to last month, mainly due to newsletter subscriptions.",
    "You process emails most efficiently between 9 AM and 11 AM, handling 65% of your daily email volume during this time."
  ],
  senders: [
    "Your top 5 senders account for 35% of your total email volume - creating rules for these could significantly reduce manual sorting.",
    "You receive emails from 127 unique senders per week, with 23% being one-time senders that could benefit from spam filtering.",
    "Newsletter senders dominate your inbox, representing 8 of your top 15 most frequent contacts."
  ],
  categories: [
    "Newsletter emails make up 28% of your inbox but only 8% get opened - consider automatic archiving for older newsletters.",
    "Work emails peak on Monday mornings and Thursday afternoons, suggesting optimal times for email automation rules.",
    "You have a 92% archive rate for marketing emails, indicating these could be automatically organized."
  ],
  behavior: [
    "You respond to emails within 2 hours 78% of the time during work hours, showing excellent responsiveness.",
    "Your longest email threads average 12 messages and are primarily work-related project discussions.",
    "You use 'Archive' 3x more than 'Delete', suggesting a preference for keeping records rather than permanent removal."
  ],
  productivity: [
    "You could save approximately 25 minutes per day with 3 well-designed automation rules based on your patterns.",
    "Your peak email processing efficiency is 2.3 emails per minute, typically achieved during focused email sessions.",
    "Batch processing emails (rather than checking throughout the day) could improve your response quality by 40%."
  ]
};

export const MOCK_SUGGESTIONS: Record<string, Suggestion[]> = {
  unsubscribe: [
    {
      id: 'unsubscribe_1',
      type: 'unsubscribe',
      title: 'Unsubscribe from unused newsletters',
      description: 'You have 12 newsletters with 0% open rate in the last 30 days',
      confidence: 0.92,
      priority: 'high',
      action: {
        type: 'bulk_unsubscribe',
        params: { newsletters: ['<EMAIL>', '<EMAIL>'] }
      },
      reasoning: 'These newsletters consume inbox space without providing value',
      impact: {
        timesSaved: 15,
        emailsAffected: 48,
        clutterReduced: 20
      }
    }
  ],
  organization: [
    {
      id: 'organize_1',
      type: 'organize',
      title: 'Create folders for project emails',
      description: 'Organize work emails by project for better tracking',
      confidence: 0.85,
      priority: 'medium',
      action: {
        type: 'create_folders',
        params: { 
          folders: ['Project Alpha', 'Project Beta', 'Team Updates'],
          auto_sort: true
        }
      },
      reasoning: 'Your work emails show clear project groupings that would benefit from folder organization',
      impact: {
        timesSaved: 20,
        emailsAffected: 156,
        clutterReduced: 35
      }
    }
  ],
  automation: [
    {
      id: 'automation_1',
      type: 'createRule',
      title: 'Auto-label financial emails',
      description: 'Automatically label emails from banks and payment services',
      confidence: 0.89,
      priority: 'high',
      action: {
        type: 'create_rule',
        params: {
          conditions: [{ type: 'from', value: '@bank.com|@paypal.com|@stripe.com' }],
          actions: [{ type: 'label', value: 'Financial' }]
        }
      },
      reasoning: 'Financial emails need special attention and organization for record-keeping',
      impact: {
        timesSaved: 10,
        emailsAffected: 24,
        clutterReduced: 8
      }
    }
  ]
};

/**
 * Utility functions for generating realistic mock responses
 */

export function generateMockLatency(model: string = 'gpt-4-turbo'): number {
  const config = MOCK_AI_MODELS[model] || MOCK_AI_MODELS['gpt-4-turbo'];
  const baseLatency = config.baseLatency;
  const variability = config.variabilityFactor;
  
  // Add random variability
  const variation = (Math.random() - 0.5) * 2 * variability;
  return Math.max(100, Math.round(baseLatency * (1 + variation)));
}

export function generateMockTokenUsage(prompt: string, response: string, model: string = 'gpt-4-turbo') {
  // Rough token estimation (4 characters per token)
  const promptTokens = Math.ceil(prompt.length / 4);
  const completionTokens = Math.ceil(response.length / 4);
  
  return {
    prompt: promptTokens,
    completion: completionTokens,
    total: promptTokens + completionTokens
  };
}

export function generateMockCost(tokens: { total: number }, model: string = 'gpt-4-turbo'): number {
  const config = MOCK_AI_MODELS[model] || MOCK_AI_MODELS['gpt-4-turbo'];
  return tokens.total * config.costPerToken;
}

export function shouldSimulateError(model: string = 'gpt-4-turbo'): boolean {
  const config = MOCK_AI_MODELS[model] || MOCK_AI_MODELS['gpt-4-turbo'];
  return Math.random() < config.errorRate;
}

export function getRandomResponse(category: string, responses: Record<string, string[]>): string {
  const categoryResponses = responses[category] || responses.error || ['I apologize, but I need more information to help you.'];
  return categoryResponses[Math.floor(Math.random() * categoryResponses.length)];
}

export function getRandomInsight(category: string): string {
  const insights = MOCK_ANALYSIS_INSIGHTS[category] || MOCK_ANALYSIS_INSIGHTS.volume;
  return insights[Math.floor(Math.random() * insights.length)];
}

export function getRandomSuggestion(category: string): Suggestion | null {
  const suggestions = MOCK_SUGGESTIONS[category];
  if (!suggestions || suggestions.length === 0) return null;
  
  return suggestions[Math.floor(Math.random() * suggestions.length)];
}

export function generateMockCategorizationResult(
  email: Partial<ParsedMessage>,
  targetCategory?: EmailCategory
): CategorizationResult {
  // Determine category based on email content or use target
  let category: EmailCategory = targetCategory || 'Other';
  let responseSet = MOCK_CATEGORIZATION_RESPONSES.newsletter;

  if (!targetCategory) {
    // Simple heuristics to determine category
    const subject = email.subject?.toLowerCase() || '';
    const from = email.from?.email?.toLowerCase() || '';
    const body = (email.body?.text || email.body?.plain || '').toLowerCase();

    if (subject.includes('newsletter') || subject.includes('digest') || from.includes('newsletter')) {
      category = 'Newsletter';
      responseSet = MOCK_CATEGORIZATION_RESPONSES.newsletter;
    } else if (subject.includes('sale') || subject.includes('offer') || subject.includes('discount')) {
      category = 'Marketing';
      responseSet = MOCK_CATEGORIZATION_RESPONSES.marketing;
    } else if (from.includes('@company.com') || subject.includes('meeting') || subject.includes('project')) {
      category = 'Work';
      responseSet = MOCK_CATEGORIZATION_RESPONSES.work;
    } else if (subject.includes('receipt') || subject.includes('order') || subject.includes('payment')) {
      category = 'Receipt';
      responseSet = MOCK_CATEGORIZATION_RESPONSES.receipt;
    } else if (from.includes('gmail.com') || from.includes('yahoo.com')) {
      category = 'Personal';
      responseSet = MOCK_CATEGORIZATION_RESPONSES.personal;
    } else if (subject.includes('urgent') && body.includes('click')) {
      category = 'Spam';
      responseSet = MOCK_CATEGORIZATION_RESPONSES.spam;
    }
  }

  // Get random response from the appropriate set
  const mockResponse = responseSet[Math.floor(Math.random() * responseSet.length)];
  
  return {
    category,
    confidence: mockResponse.confidence || 0.8,
    reasoning: mockResponse.reasoning || 'Email classified based on content analysis',
    alternatives: mockResponse.alternatives || [],
    processingTime: generateMockLatency(),
    features: {
      senderDomain: email.from?.email?.split('@')[1] || 'unknown.com',
      senderType: (email.from?.email || '').includes('noreply') ? 'noreply' : 'known',
      hasUnsubscribe: (() => {
        const bodyText = typeof email.body === 'string' ? email.body : (email.body?.text || email.body?.html || email.body?.plain || '');
        return bodyText.includes('unsubscribe');
      })(),
      hasAttachments: (email.attachments?.length || 0) > 0,
      contentLength: (() => {
        const bodyText = typeof email.body === 'string' ? email.body : (email.body?.text || email.body?.html || email.body?.plain || '');
        return bodyText.length;
      })(),
      subjectKeywords: (email.subject || '').split(' ').slice(0, 5),
      bodyKeywords: (() => {
        const bodyText = typeof email.body === 'string' ? email.body : (email.body?.text || email.body?.html || email.body?.plain || '');
        return bodyText.split(' ').slice(0, 10);
      })(),
      headerAnalysis: {
        listHeaders: false,
        marketingHeaders: false,
        securityHeaders: false,
        receiptHeaders: false
      },
      languageFeatures: {
        sentiment: 'neutral',
        urgency: 'low',
        formality: 'formal',
        personalization: 0.5
      }
    }
  };
}

export function generateMockRuleSuggestion(
  pattern: any,
  priority: 'high' | 'medium' | 'low' = 'medium'
): RuleSuggestion {
  const suggestions = MOCK_RULE_SUGGESTIONS.filter(s => s.priority === priority);
  const baseSuggestion = suggestions[Math.floor(Math.random() * suggestions.length)] || MOCK_RULE_SUGGESTIONS[0];
  
  // Customize suggestion based on pattern
  return {
    ...baseSuggestion,
    id: `suggestion_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    confidence: Math.random() * 0.3 + 0.7, // 0.7 - 1.0
    impact: {
      ...baseSuggestion.impact,
      emailsAffected: Math.floor(Math.random() * 100) + 10, // 10-110 emails
      timesSaved: Math.floor(Math.random() * 30) + 5 // 5-35 minutes
    }
  };
}

export function generateMockChatResponse(
  message: string,
  context?: any
): AIResponse {
  const lowerMessage = message.toLowerCase();
  let responseCategory = 'help';
  
  // Determine response category
  if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
    responseCategory = 'greeting';
  } else if (lowerMessage.includes('rule') || lowerMessage.includes('automate')) {
    responseCategory = 'rule_creation';
  } else if (lowerMessage.includes('analyze') || lowerMessage.includes('insight')) {
    responseCategory = 'analysis';
  } else if (lowerMessage.includes('organize') || lowerMessage.includes('clean')) {
    responseCategory = 'organization';
  } else if (lowerMessage.includes('help') || lowerMessage.includes('how')) {
    responseCategory = 'help';
  }

  const response = getRandomResponse(responseCategory, MOCK_CHAT_RESPONSES);
  const tokens = generateMockTokenUsage(message, response);
  const latency = generateMockLatency();

  return {
    id: `response_${Date.now()}`,
    query: message,
    response,
    type: 'answer',
    model: 'gpt-4-turbo',
    tokens,
    latency,
    cost: generateMockCost(tokens),
    confidence: Math.random() * 0.2 + 0.8, // 0.8 - 1.0
    sources: [],
    citations: []
  };
}

/**
 * Progressive loading simulation for AI responses
 */
export function simulateProgressiveResponse(
  fullResponse: string,
  onChunk: (chunk: string, isComplete: boolean) => void,
  chunkDelay: number = 50
): Promise<void> {
  return new Promise((resolve) => {
    const words = fullResponse.split(' ');
    let currentIndex = 0;
    
    const sendNextChunk = () => {
      if (currentIndex >= words.length) {
        onChunk('', true);
        resolve();
        return;
      }
      
      // Send 1-3 words per chunk
      const chunkSize = Math.floor(Math.random() * 3) + 1;
      const chunk = words.slice(currentIndex, currentIndex + chunkSize).join(' ');
      
      onChunk(chunk + ' ', false);
      currentIndex += chunkSize;
      
      setTimeout(sendNextChunk, chunkDelay + Math.random() * chunkDelay);
    };
    
    sendNextChunk();
  });
}

/**
 * Generate realistic processing delays for different AI operations
 */
export const MOCK_PROCESSING_DELAYS = {
  categorization: { min: 200, max: 800 },
  rule_suggestion: { min: 1000, max: 3000 },
  chat_response: { min: 500, max: 2000 },
  email_analysis: { min: 800, max: 2500 },
  bulk_operation: { min: 2000, max: 8000 }
};

export function getProcessingDelay(operation: keyof typeof MOCK_PROCESSING_DELAYS): number {
  const { min, max } = MOCK_PROCESSING_DELAYS[operation];
  return Math.floor(Math.random() * (max - min)) + min;
}

/**
 * Generate mock performance metrics
 */
export function generateMockPerformanceMetrics() {
  return {
    accuracy: 0.85 + Math.random() * 0.1, // 85-95%
    averageLatency: 800 + Math.random() * 400, // 800-1200ms
    successRate: 0.95 + Math.random() * 0.04, // 95-99%
    costPerRequest: 0.002 + Math.random() * 0.003, // $0.002-0.005
    userSatisfaction: 0.8 + Math.random() * 0.15, // 80-95%
    totalRequests: Math.floor(Math.random() * 1000) + 500,
    tokensProcessed: Math.floor(Math.random() * 50000) + 10000
  };
}

export default {
  MOCK_AI_MODELS,
  MOCK_CATEGORIZATION_RESPONSES,
  MOCK_RULE_SUGGESTIONS,
  MOCK_CHAT_RESPONSES,
  MOCK_ANALYSIS_INSIGHTS,
  MOCK_SUGGESTIONS,
  generateMockLatency,
  generateMockTokenUsage,
  generateMockCost,
  shouldSimulateError,
  getRandomResponse,
  getRandomInsight,
  getRandomSuggestion,
  generateMockCategorizationResult,
  generateMockRuleSuggestion,
  generateMockChatResponse,
  simulateProgressiveResponse,
  getProcessingDelay,
  generateMockPerformanceMetrics
};