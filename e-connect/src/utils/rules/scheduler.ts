import { Rule, RuleSchedule } from '@/types/rules';

export interface ScheduledTask {
  id: string;
  ruleId: string;
  scheduledFor: Date;
  type: 'immediate' | 'delayed' | 'scheduled' | 'recurring';
  attempts: number;
  maxAttempts: number;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  lastAttempt?: Date;
  error?: string;
  metadata?: Record<string, any>;
}

export class RuleScheduler {
  private tasks: Map<string, ScheduledTask> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private isRunning: boolean = false;

  constructor() {
    this.startScheduler();
  }

  /**
   * Schedule rule execution based on rule schedule configuration
   */
  scheduleRule(rule: Rule, emailContext?: any): string[] {
    if (!rule.schedule || !rule.enabled) {
      return [];
    }

    const taskIds: string[] = [];

    switch (rule.schedule.type) {
      case 'immediate':
        taskIds.push(this.scheduleImmediate(rule, emailContext));
        break;
      
      case 'delayed':
        if (rule.schedule.delay) {
          taskIds.push(this.scheduleDelayed(rule, rule.schedule.delay, emailContext));
        }
        break;
      
      case 'scheduled':
        if (rule.schedule.time && rule.schedule.days) {
          taskIds.push(...this.scheduleRecurring(rule, emailContext));
        }
        break;
      
      case 'recurring':
        if (rule.schedule.time && rule.schedule.days) {
          taskIds.push(...this.scheduleRecurring(rule, emailContext));
        }
        break;
    }

    return taskIds;
  }

  /**
   * Schedule immediate execution
   */
  private scheduleImmediate(rule: Rule, emailContext?: any): string {
    const task: ScheduledTask = {
      id: this.generateTaskId(),
      ruleId: rule.id,
      scheduledFor: new Date(),
      type: 'immediate',
      attempts: 0,
      maxAttempts: 3,
      status: 'pending',
      metadata: { emailContext }
    };

    this.tasks.set(task.id, task);
    return task.id;
  }

  /**
   * Schedule delayed execution
   */
  private scheduleDelayed(rule: Rule, delayMinutes: number, emailContext?: any): string {
    const scheduledFor = new Date(Date.now() + delayMinutes * 60 * 1000);
    
    const task: ScheduledTask = {
      id: this.generateTaskId(),
      ruleId: rule.id,
      scheduledFor,
      type: 'delayed',
      attempts: 0,
      maxAttempts: 3,
      status: 'pending',
      metadata: { emailContext, delayMinutes }
    };

    this.tasks.set(task.id, task);
    return task.id;
  }

  /**
   * Schedule recurring execution
   */
  private scheduleRecurring(rule: Rule, emailContext?: any): string[] {
    const schedule = rule.schedule!;
    const taskIds: string[] = [];

    if (!schedule.time || !schedule.days) {
      return taskIds;
    }

    // Parse time (HH:MM format)
    const [hours, minutes] = schedule.time.split(':').map(Number);
    
    // Get timezone or default to UTC
    const timezone = schedule.timezone || 'UTC';

    // Schedule for each specified day
    schedule.days.forEach(dayOfWeek => {
      const task: ScheduledTask = {
        id: this.generateTaskId(),
        ruleId: rule.id,
        scheduledFor: this.getNextScheduledTime(dayOfWeek, hours, minutes, timezone),
        type: 'recurring',
        attempts: 0,
        maxAttempts: 3,
        status: 'pending',
        metadata: { 
          emailContext, 
          dayOfWeek, 
          hours, 
          minutes, 
          timezone,
          recurring: true 
        }
      };

      this.tasks.set(task.id, task);
      taskIds.push(task.id);
    });

    return taskIds;
  }

  /**
   * Cancel scheduled task
   */
  cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task) return false;

    task.status = 'cancelled';
    this.tasks.delete(taskId);
    
    // Clear any associated intervals
    const interval = this.intervals.get(taskId);
    if (interval) {
      clearTimeout(interval);
      this.intervals.delete(taskId);
    }

    return true;
  }

  /**
   * Cancel all tasks for a specific rule
   */
  cancelRuleTasks(ruleId: string): number {
    let cancelledCount = 0;
    
    for (const [taskId, task] of this.tasks) {
      if (task.ruleId === ruleId) {
        this.cancelTask(taskId);
        cancelledCount++;
      }
    }

    return cancelledCount;
  }

  /**
   * Get scheduled tasks for a rule
   */
  getRuleTasks(ruleId: string): ScheduledTask[] {
    return Array.from(this.tasks.values()).filter(task => task.ruleId === ruleId);
  }

  /**
   * Get all pending tasks
   */
  getPendingTasks(): ScheduledTask[] {
    return Array.from(this.tasks.values()).filter(task => task.status === 'pending');
  }

  /**
   * Update rule schedule
   */
  updateRuleSchedule(rule: Rule): void {
    // Cancel existing tasks
    this.cancelRuleTasks(rule.id);
    
    // Schedule new tasks if rule is enabled
    if (rule.enabled) {
      this.scheduleRule(rule);
    }
  }

  /**
   * Start the scheduler
   */
  private startScheduler(): void {
    if (this.isRunning) return;

    this.isRunning = true;
    
    // Check for due tasks every minute
    const schedulerInterval = setInterval(() => {
      this.processDueTasks();
    }, 60000); // 1 minute

    // Store the main scheduler interval
    this.intervals.set('main', schedulerInterval);
  }

  /**
   * Stop the scheduler
   */
  stopScheduler(): void {
    this.isRunning = false;
    
    // Clear all intervals
    for (const interval of this.intervals.values()) {
      clearInterval(interval);
    }
    this.intervals.clear();

    // Cancel all pending tasks
    for (const task of this.tasks.values()) {
      if (task.status === 'pending') {
        task.status = 'cancelled';
      }
    }
  }

  /**
   * Process tasks that are due for execution
   */
  private async processDueTasks(): Promise<void> {
    const now = new Date();
    const dueTasks = Array.from(this.tasks.values()).filter(
      task => task.status === 'pending' && task.scheduledFor <= now
    );

    for (const task of dueTasks) {
      await this.executeTask(task);
    }
  }

  /**
   * Execute a scheduled task
   */
  private async executeTask(task: ScheduledTask): Promise<void> {
    try {
      task.status = 'running';
      task.attempts++;
      task.lastAttempt = new Date();

      // Here you would integrate with the RuleEngine to execute the rule
      // For now, we'll simulate execution
      await this.simulateTaskExecution(task);

      task.status = 'completed';

      // If it's a recurring task, schedule the next occurrence
      if (task.type === 'recurring' && task.metadata?.recurring) {
        this.scheduleNextRecurrence(task);
      } else {
        // Remove non-recurring completed tasks
        this.tasks.delete(task.id);
      }

    } catch (error) {
      task.error = error instanceof Error ? error.message : 'Unknown error';
      
      if (task.attempts >= task.maxAttempts) {
        task.status = 'failed';
        console.error(`Task ${task.id} failed after ${task.attempts} attempts:`, task.error);
      } else {
        // Retry with exponential backoff
        const retryDelay = Math.pow(2, task.attempts) * 60000; // 2^n minutes
        task.scheduledFor = new Date(Date.now() + retryDelay);
        task.status = 'pending';
      }
    }
  }

  /**
   * Simulate task execution (replace with actual rule execution)
   */
  private async simulateTaskExecution(task: ScheduledTask): Promise<void> {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 500));
    
    // Simulate occasional failures
    if (Math.random() < 0.05) { // 5% failure rate
      throw new Error('Simulated execution failure');
    }

    console.log(`Executed scheduled task ${task.id} for rule ${task.ruleId}`);
  }

  /**
   * Schedule next recurrence for a recurring task
   */
  private scheduleNextRecurrence(task: ScheduledTask): void {
    if (!task.metadata) return;

    const { dayOfWeek, hours, minutes, timezone } = task.metadata;
    const nextScheduledTime = this.getNextScheduledTime(dayOfWeek, hours, minutes, timezone);

    // Create new task for next occurrence
    const newTask: ScheduledTask = {
      ...task,
      id: this.generateTaskId(),
      scheduledFor: nextScheduledTime,
      attempts: 0,
      status: 'pending',
      lastAttempt: undefined,
      error: undefined
    };

    this.tasks.set(newTask.id, newTask);
    
    // Remove the current task
    this.tasks.delete(task.id);
  }

  /**
   * Calculate next scheduled time for a recurring task
   */
  private getNextScheduledTime(
    dayOfWeek: number, 
    hours: number, 
    minutes: number, 
    timezone: string = 'UTC'
  ): Date {
    const now = new Date();
    const scheduledTime = new Date();
    
    // Set the time
    scheduledTime.setHours(hours, minutes, 0, 0);
    
    // Calculate days until next occurrence
    const currentDay = now.getDay();
    let daysUntilNext = dayOfWeek - currentDay;
    
    // If the day has passed this week, or it's today but time has passed, schedule for next week
    if (daysUntilNext < 0 || (daysUntilNext === 0 && now >= scheduledTime)) {
      daysUntilNext += 7;
    }
    
    scheduledTime.setDate(now.getDate() + daysUntilNext);
    
    // TODO: Handle timezone conversion properly
    // For now, assuming UTC
    
    return scheduledTime;
  }

  /**
   * Get scheduler statistics
   */
  getStats(): {
    totalTasks: number;
    pendingTasks: number;
    runningTasks: number;
    completedTasks: number;
    failedTasks: number;
    tasksByType: Record<string, number>;
  } {
    const tasks = Array.from(this.tasks.values());
    
    const stats = {
      totalTasks: tasks.length,
      pendingTasks: tasks.filter(t => t.status === 'pending').length,
      runningTasks: tasks.filter(t => t.status === 'running').length,
      completedTasks: tasks.filter(t => t.status === 'completed').length,
      failedTasks: tasks.filter(t => t.status === 'failed').length,
      tasksByType: tasks.reduce((acc, task) => {
        acc[task.type] = (acc[task.type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>)
    };

    return stats;
  }

  /**
   * Generate unique task ID
   */
  private generateTaskId(): string {
    return `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Singleton instance
export const ruleScheduler = new RuleScheduler();

export default RuleScheduler;