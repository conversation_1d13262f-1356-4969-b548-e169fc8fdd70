import { Rule, RuleCondition, RuleAction, RuleExecution, RuleOperator, RuleConditionType } from '../../types/rules';
import { ParsedMessage } from '../../types/email';

interface ExecutionContext {
  email: ParsedMessage;
  startTime: number;
  debug?: boolean;
  metadata?: Record<string, any>;
}

interface ConditionResult {
  conditionId: string;
  matched: boolean;
  evaluationTime: number;
  details?: any;
  error?: string;
}

interface ActionResult {
  actionId: string;
  executed: boolean;
  success: boolean;
  error?: string;
  executionTime: number;
  result?: any;
}

export class RuleEngine {
  private debugMode: boolean = false;
  private executionTimeout: number = 30000; // 30 seconds

  constructor(options: { debug?: boolean; timeout?: number } = {}) {
    this.debugMode = options.debug || false;
    this.executionTimeout = options.timeout || 30000;
  }

  /**
   * Execute a single rule against an email
   */
  async executeRule(rule: Rule, email: ParsedMessage): Promise<RuleExecution> {
    const startTime = Date.now();
    const context: ExecutionContext = {
      email,
      startTime,
      debug: this.debugMode
    };

    const execution: RuleExecution = {
      id: this.generateExecutionId(),
      ruleId: rule.id,
      threadId: email.threadId || email.id,
      messageId: email.id,
      timestamp: new Date(),
      conditions: [],
      actions: [],
      totalTime: 0,
      success: false
    };

    try {
      if (!rule.enabled) {
        execution.success = false;
        execution.error = 'Rule is disabled';
        execution.totalTime = Date.now() - startTime;
        return execution;
      }

      // Evaluate conditions
      const conditionResults = await this.evaluateConditions(rule, context);
      execution.conditions = conditionResults;

      // Determine if rule should trigger based on condition logic
      const shouldTrigger = this.shouldRuleTrigger(rule, conditionResults);

      if (shouldTrigger) {
        // Execute actions
        const actionResults = await this.executeActions(rule, context);
        execution.actions = actionResults;
        execution.success = actionResults.every(action => action.success);
      } else {
        execution.success = true; // Rule evaluated correctly but didn't trigger
      }

    } catch (error) {
      execution.error = error instanceof Error ? error.message : 'Unknown error';
      execution.success = false;
    }

    execution.totalTime = Date.now() - startTime;
    return execution;
  }

  /**
   * Execute multiple rules against an email with priority ordering
   */
  async executeRules(rules: Rule[], email: ParsedMessage): Promise<RuleExecution[]> {
    // Sort rules by priority (lower number = higher priority)
    const sortedRules = [...rules]
      .filter(rule => rule.enabled)
      .sort((a, b) => a.priority - b.priority);

    const executions: RuleExecution[] = [];

    for (const rule of sortedRules) {
      try {
        const execution = await this.executeRule(rule, email);
        executions.push(execution);

        // Check if any action was destructive (like delete) and stop processing
        if (execution.success && this.hasDestructiveAction(rule)) {
          break;
        }
      } catch (error) {
        console.error(`Failed to execute rule ${rule.id}:`, error);
      }
    }

    return executions;
  }

  /**
   * Test a rule against sample data
   */
  async testRule(rule: Rule, testParsedMessages: Partial<ParsedMessage>[]): Promise<{
    results: Array<{
      email: Partial<ParsedMessage>;
      execution: RuleExecution;
      matched: boolean;
    }>;
    summary: {
      totalTests: number;
      matched: number;
      avgExecutionTime: number;
      errors: number;
    };
  }> {
    const results = [];
    let totalTime = 0;
    let errors = 0;

    for (const testParsedMessage of testParsedMessages) {
      try {
        // Convert partial email to full email for testing
        const fullParsedMessage = this.normalizeParsedMessageForTesting(testParsedMessage);
        const execution = await this.executeRule(rule, fullParsedMessage);
        
        const matched = this.shouldRuleTrigger(rule, execution.conditions);
        results.push({
          email: testParsedMessage,
          execution,
          matched
        });

        totalTime += execution.totalTime;
        if (!execution.success) errors++;
      } catch (error) {
        errors++;
        console.error('Test execution failed:', error);
      }
    }

    return {
      results,
      summary: {
        totalTests: testParsedMessages.length,
        matched: results.filter(r => r.matched).length,
        avgExecutionTime: testParsedMessages.length > 0 ? totalTime / testParsedMessages.length : 0,
        errors
      }
    };
  }

  /**
   * Evaluate all conditions for a rule
   */
  private async evaluateConditions(rule: Rule, context: ExecutionContext): Promise<ConditionResult[]> {
    const results: ConditionResult[] = [];

    for (const condition of rule.conditions) {
      const startTime = Date.now();
      
      try {
        const matched = await this.evaluateCondition(condition, context);
        
        results.push({
          conditionId: condition.id,
          matched: condition.negate ? !matched : matched,
          evaluationTime: Date.now() - startTime,
          details: this.getConditionDetails(condition, context.email)
        });
      } catch (error) {
        results.push({
          conditionId: condition.id,
          matched: false,
          evaluationTime: Date.now() - startTime,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return results;
  }

  /**
   * Evaluate a single condition
   */
  private async evaluateCondition(condition: RuleCondition, context: ExecutionContext): Promise<boolean> {
    const { email } = context;
    let fieldValue: any;

    // Extract field value based on condition type
    switch (condition.type) {
      case 'from':
        fieldValue = email.from?.email || email.from;
        break;
      case 'to':
        fieldValue = email.to?.map(t => typeof t === 'string' ? t : t.email).join(' ') || '';
        break;
      case 'subject':
        fieldValue = email.subject || '';
        break;
      case 'body':
        fieldValue = email.body || '';
        break;
      case 'hasAttachment':
        fieldValue = Boolean(email.attachments && email.attachments.length > 0);
        break;
      case 'isUnread':
        fieldValue = email.flags?.isUnread || false;
        break;
      case 'isImportant':
        fieldValue = email.flags?.isImportant || false;
        break;
      case 'isStarred':
        fieldValue = email.flags?.isStarred || false;
        break;
      case 'category':
        fieldValue = email.category || '';
        break;
      case 'label':
        fieldValue = email.labels?.join(' ') || '';
        break;
      case 'domain':
        const fromParsedMessage = typeof email.from === 'string' ? email.from : email.from?.email;
        fieldValue = fromParsedMessage ? fromParsedMessage.split('@')[1] : '';
        break;
      case 'size':
        fieldValue = email.size || 0;
        break;
      case 'age':
        const ageInMs = Date.now() - new Date(email.date || (email as any).createdAt || 0).getTime();
        fieldValue = Math.floor(ageInMs / (1000 * 60 * 60 * 24)); // days
        break;
      default:
        fieldValue = '';
    }

    // Apply operator
    return this.applyOperator(fieldValue, condition.operator, condition.value, condition.caseSensitive);
  }

  /**
   * Apply operator logic
   */
  private applyOperator(
    fieldValue: any, 
    operator: RuleOperator, 
    conditionValue: any, 
    caseSensitive: boolean = false
  ): boolean {
    // Normalize for case sensitivity
    const normalizeString = (val: any) => {
      if (typeof val !== 'string') return val;
      return caseSensitive ? val : val.toLowerCase();
    };

    const normalizedField = normalizeString(fieldValue);
    const normalizedCondition = Array.isArray(conditionValue) 
      ? conditionValue.map(normalizeString)
      : normalizeString(conditionValue);

    switch (operator) {
      case 'equals':
        return fieldValue === conditionValue;
      
      case 'notEquals':
        return fieldValue !== conditionValue;
      
      case 'contains':
        if (Array.isArray(normalizedCondition)) {
          return normalizedCondition.some(val => 
            String(normalizedField).includes(String(val))
          );
        }
        return String(normalizedField).includes(String(normalizedCondition));
      
      case 'notContains':
        if (Array.isArray(normalizedCondition)) {
          return !normalizedCondition.some(val => 
            String(normalizedField).includes(String(val))
          );
        }
        return !String(normalizedField).includes(String(normalizedCondition));
      
      case 'startsWith':
        return String(normalizedField).startsWith(String(normalizedCondition));
      
      case 'endsWith':
        return String(normalizedField).endsWith(String(normalizedCondition));
      
      case 'in':
        if (!Array.isArray(normalizedCondition)) return false;
        return normalizedCondition.includes(normalizedField);
      
      case 'notIn':
        if (!Array.isArray(normalizedCondition)) return true;
        return !normalizedCondition.includes(normalizedField);
      
      case 'greaterThan':
        return Number(fieldValue) > Number(conditionValue);
      
      case 'lessThan':
        return Number(fieldValue) < Number(conditionValue);
      
      case 'greaterThanOrEqual':
        return Number(fieldValue) >= Number(conditionValue);
      
      case 'lessThanOrEqual':
        return Number(fieldValue) <= Number(conditionValue);
      
      case 'between':
        if (!Array.isArray(conditionValue) || conditionValue.length !== 2) return false;
        const numField = Number(fieldValue);
        return numField >= Number(conditionValue[0]) && numField <= Number(conditionValue[1]);
      
      case 'exists':
        return fieldValue !== null && fieldValue !== undefined && fieldValue !== '';
      
      case 'notExists':
        return fieldValue === null || fieldValue === undefined || fieldValue === '';
      
      case 'matches':
        try {
          const regex = new RegExp(String(conditionValue), caseSensitive ? 'g' : 'gi');
          return regex.test(String(fieldValue));
        } catch {
          return false;
        }
      
      default:
        return false;
    }
  }

  /**
   * Determine if rule should trigger based on condition results and logic
   */
  private shouldRuleTrigger(rule: Rule, conditionResults: ConditionResult[]): boolean {
    if (conditionResults.length === 0) return false;

    const matchedConditions = conditionResults.filter(result => result.matched && !result.error);

    if (rule.conditionLogic === 'all') {
      return matchedConditions.length === conditionResults.length;
    } else { // 'any'
      return matchedConditions.length > 0;
    }
  }

  /**
   * Execute all actions for a rule
   */
  private async executeActions(rule: Rule, context: ExecutionContext): Promise<ActionResult[]> {
    const results: ActionResult[] = [];
    
    // Sort actions by order
    const sortedActions = [...rule.actions].sort((a, b) => a.order - b.order);

    for (const action of sortedActions) {
      const startTime = Date.now();
      
      try {
        // Check action condition if present
        if (action.condition && !this.evaluateActionCondition(action.condition, context)) {
          results.push({
            actionId: action.id,
            executed: false,
            success: true,
            executionTime: Date.now() - startTime,
            result: { skipped: 'Condition not met' }
          });
          continue;
        }

        const result = await this.executeAction(action, context);
        
        results.push({
          actionId: action.id,
          executed: true,
          success: true,
          executionTime: Date.now() - startTime,
          result
        });
      } catch (error) {
        results.push({
          actionId: action.id,
          executed: true,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          executionTime: Date.now() - startTime
        });
      }
    }

    return results;
  }

  /**
   * Execute a single action
   */
  private async executeAction(action: RuleAction, context: ExecutionContext): Promise<any> {
    // In a real implementation, these would perform actual email operations
    // For now, we'll simulate the actions
    
    const { email } = context;
    
    switch (action.type) {
      case 'archive':
        return { archived: true, emailId: email.id };
      
      case 'delete':
        return { deleted: true, emailId: email.id };
      
      case 'markRead':
        return { markedRead: true, emailId: email.id };
      
      case 'markUnread':
        return { markedUnread: true, emailId: email.id };
      
      case 'star':
        return { starred: true, emailId: email.id };
      
      case 'unstar':
        return { unstarred: true, emailId: email.id };
      
      case 'label':
        return { labelAdded: action.value, emailId: email.id };
      
      case 'removeLabel':
        return { labelRemoved: action.value, emailId: email.id };
      
      case 'categorize':
        return { categorized: action.value, emailId: email.id };
      
      case 'moveToFolder':
        return { movedToFolder: action.params?.folder, emailId: email.id };
      
      case 'forward':
        return { 
          forwarded: true, 
          to: action.params?.address,
          emailId: email.id 
        };
      
      case 'reply':
        return { 
          replied: true, 
          template: action.template,
          emailId: email.id 
        };
      
      case 'snooze':
        return { 
          snoozed: true, 
          duration: action.params?.duration,
          emailId: email.id 
        };
      
      case 'webhook':
        // Simulate webhook call
        return { 
          webhookCalled: true, 
          url: action.params?.url,
          response: { status: 200 }
        };
      
      case 'notification':
        return { 
          notificationSent: true, 
          priority: action.params?.priority,
          emailId: email.id 
        };
      
      default:
        return { executed: true, type: action.type };
    }
  }

  /**
   * Evaluate action condition
   */
  private evaluateActionCondition(condition: any, context: ExecutionContext): boolean {
    // Simplified action condition evaluation
    // In a real implementation, this would be more sophisticated
    return Math.random() > (condition.threshold || 0.5);
  }

  /**
   * Check if rule has destructive actions
   */
  private hasDestructiveAction(rule: Rule): boolean {
    return rule.actions.some(action => action.type === 'delete');
  }

  /**
   * Get condition evaluation details
   */
  private getConditionDetails(condition: RuleCondition, email: ParsedMessage): any {
    const details: any = {
      conditionType: condition.type,
      operator: condition.operator
    };

    switch (condition.type) {
      case 'from':
        details.emailFrom = email.from;
        details.expectedValue = condition.value;
        break;
      case 'subject':
        details.emailSubject = email.subject;
        details.expectedValue = condition.value;
        break;
      case 'hasAttachment':
        details.hasAttachments = Boolean(email.attachments?.length);
        details.expectedValue = condition.value;
        break;
    }

    return details;
  }

  /**
   * Normalize partial email for testing
   */
  private normalizeParsedMessageForTesting(partial: Partial<ParsedMessage>): ParsedMessage {
    return {
      id: partial.id || 'test-email',
      threadId: partial.threadId || 'test-thread',
      messageId: partial.messageId || 'test-message-id',
      subject: partial.subject || 'Test Subject',
      snippet: partial.snippet || 'Test snippet',
      body: partial.body || { text: 'Test body content' },
      headers: partial.headers || {},
      from: partial.from || { email: '<EMAIL>', name: 'Test User' },
      to: partial.to || [{ email: '<EMAIL>', name: 'User' }],
      date: partial.date || new Date(),
      receivedDate: partial.receivedDate || new Date(),
      attachments: partial.attachments || [],
      labels: partial.labels || [],
      flags: partial.flags || {
        isUnread: false,
        isImportant: false,
        isStarred: false,
        isDraft: false,
        isSpam: false,
        isTrash: false
      },
      size: partial.size || 1024,
      rawSize: partial.rawSize || 1024,
      historyId: partial.historyId || 'test-history',
      internalDate: partial.internalDate || new Date().toISOString(),
      category: partial.category || 'primary'
    } as ParsedMessage;
  }

  /**
   * Generate unique execution ID
   */
  private generateExecutionId(): string {
    return `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Helper functions for rule validation and optimization
export class RuleValidator {
  /**
   * Validate rule configuration
   */
  static validateRule(rule: Partial<Rule>): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!rule.name?.trim()) {
      errors.push('Rule name is required');
    }

    if (!rule.conditions || rule.conditions.length === 0) {
      errors.push('At least one condition is required');
    }

    if (!rule.actions || rule.actions.length === 0) {
      errors.push('At least one action is required');
    }

    if (rule.priority !== undefined && (rule.priority < 0 || rule.priority > 100)) {
      errors.push('Priority must be between 0 and 100');
    }

    // Validate conditions
    rule.conditions?.forEach((condition, index) => {
      if (!condition.type) {
        errors.push(`Condition ${index + 1}: Type is required`);
      }
      if (!condition.operator) {
        errors.push(`Condition ${index + 1}: Operator is required`);
      }
      if (condition.value === undefined || condition.value === '') {
        errors.push(`Condition ${index + 1}: Value is required`);
      }
    });

    // Validate actions
    rule.actions?.forEach((action, index) => {
      if (!action.type) {
        errors.push(`Action ${index + 1}: Type is required`);
      }
      if (action.order === undefined) {
        errors.push(`Action ${index + 1}: Order is required`);
      }
    });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Check for potential rule conflicts
   */
  static findConflicts(rules: Rule[]): Array<{
    type: string;
    severity: 'warning' | 'error';
    rules: string[];
    description: string;
  }> {
    const conflicts = [];
    const enabledRules = rules.filter(r => r.enabled);

    // Check priority conflicts
    const priorityGroups = enabledRules.reduce((groups, rule) => {
      if (!groups[rule.priority]) groups[rule.priority] = [];
      groups[rule.priority].push(rule);
      return groups;
    }, {} as Record<number, Rule[]>);

    Object.entries(priorityGroups).forEach(([priority, rulesInPriority]) => {
      if (rulesInPriority.length > 1) {
        conflicts.push({
          type: 'priority',
          severity: 'warning' as const,
          rules: rulesInPriority.map(r => r.id),
          description: `Multiple rules have the same priority (${priority})`
        });
      }
    });

    // Check for destructive action conflicts
    const destructiveRules = enabledRules.filter(rule => 
      rule.actions.some(action => action.type === 'delete')
    );

    if (destructiveRules.length > 1) {
      conflicts.push({
        type: 'destructive',
        severity: 'error' as const,
        rules: destructiveRules.map(r => r.id),
        description: 'Multiple rules contain destructive delete actions'
      });
    }

    return conflicts;
  }
}

export default RuleEngine;