import { z } from 'zod';
import type { ParsedMessage } from './types';

const emailSchema = z.string().email();

// Converts "<PERSON> <john.doe@gmail>" to "<PERSON>"
// Converts "<john.doe@gmail>" to "john.doe@gmail"
// Converts "john.doe@gmail" to "john.doe@gmail"
export function extractNameFromEmail(email: string) {
  if (!email) return "";
  const firstPart = email.split("<")[0]?.trim();
  if (firstPart) return firstPart;
  const secondPart = email.split("<")?.[1]?.trim();
  if (secondPart) return secondPart.split(">")[0];
  return email;
}

// Converts "John <PERSON> <john.doe@gmail>" to "john.doe@gmail"
export function extractEmailAddress(email: string): string {
  if (!email) return "";

  // Trim the input once at the start to handle leading/trailing spaces
  const trimmedEmail = email.trim();

  // Try to extract from angle brackets first
  const bracketMatch = trimmedEmail.match(/<([^<>]+)>$/);
  if (bracketMatch) {
    const candidate = bracketMatch[1].trim();
    if (isValidEmail(candidate)) {
      return candidate;
    }
  }

  // If no brackets or invalid email in brackets, try the whole string
  if (isValidEmail(trimmedEmail)) {
    return trimmedEmail;
  }

  // If still no valid email, try to extract from the beginning
  const spaceMatch = trimmedEmail.match(/^([^\s<>]+@[^\s<>]+)/);
  if (spaceMatch && isValidEmail(spaceMatch[1])) {
    return spaceMatch[1];
  }

  // Return original if no valid email found
  return trimmedEmail;
}

export function isValidEmail(email: string): boolean {
  return emailSchema.safeParse(email).success;
}

// Normalizes email addresses by:
// - Converting to lowercase
// - Removing all dots from local part
// - Removing all whitespace from local part
// - Preserving domain part unchanged
// Example: "<EMAIL>" -> "<EMAIL>"
export function normalizeEmailAddress(email: string) {
  const [localPart, domain] = email.toLowerCase().split("@");
  if (!domain) return email.toLowerCase();
  // Remove all dots and whitespace from local part
  const normalizedLocal = localPart.trim().replace(/[\s.]+/g, "");
  return `${normalizedLocal}@${domain}`;
}

// Converts "Name <<EMAIL>>" to "domain.com"
export function extractDomainFromEmail(email: string) {
  if (!email) return "";

  // Extract clean email address from formatted strings like "Name <<EMAIL>>"
  const emailAddress = email.includes("<") ? extractEmailAddress(email) : email;

  // Validate email has exactly one @ symbol
  if ((emailAddress.match(/@/g) || []).length !== 1) return "";

  // Extract domain using regex that supports:
  // - International characters (via \p{L})
  // - Multiple subdomains (e.g. sub1.sub2.domain.com)
  // - Common domain characters (letters, numbers, dots, hyphens)
  // - TLDs of 2 or more characters
  const domain = emailAddress.match(
    /@([\p{L}a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/u,
  )?.[1];
  return domain || "";
}

// Extract content from email for processing
export type EmailToContentOptions = {
  maxLength?: number;
  extractReply?: boolean;
  removeForwarded?: boolean;
};

export function emailToContent(
  email: Pick<ParsedMessage, "textHtml" | "textPlain" | "snippet">,
  {
    maxLength = 2000,
    extractReply = false,
    removeForwarded = false,
  }: EmailToContentOptions = {},
): string {
  let content = "";

  if (email.textHtml) {
    content = htmlToText(email.textHtml);
  } else if (email.textPlain) {
    content = email.textPlain;
  } else if (email.snippet) {
    content = email.snippet;
  }

  if (extractReply) {
    content = parseReply(content);
  }

  if (removeForwarded) {
    content = removeForwardedContent(content);
  }

  content = removeExcessiveWhitespace(content);

  return maxLength ? truncate(content, maxLength) : content;
}

// Simple HTML to text conversion
function htmlToText(html: string): string {
  return html
    .replace(/<[^>]*>/g, ' ')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");
}

// Extract reply content (remove quoted text)
function parseReply(content: string): string {
  const lines = content.split('\n');
  const replyLines = [];
  
  for (const line of lines) {
    // Stop at common reply indicators
    if (line.match(/^On .* wrote:$/i) || 
        line.match(/^From:.*$/i) ||
        line.match(/^>.*$/)) {
      break;
    }
    replyLines.push(line);
  }
  
  return replyLines.join('\n').trim();
}

// Remove forwarded content
function removeForwardedContent(content: string): string {
  const forwardedIndicators = [
    /^-+ ?Forwarded message ?-+/i,
    /^Begin forwarded message:/i,
    /^---------- Forwarded message ----------/i,
  ];
  
  const lines = content.split('\n');
  const cleanLines = [];
  
  for (const line of lines) {
    if (forwardedIndicators.some(indicator => indicator.test(line))) {
      break;
    }
    cleanLines.push(line);
  }
  
  return cleanLines.join('\n').trim();
}

// Remove excessive whitespace
function removeExcessiveWhitespace(content: string): string {
  return content
    .replace(/\s+/g, ' ')
    .replace(/\n\s*\n/g, '\n')
    .trim();
}

// Truncate text to specified length
function truncate(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}
