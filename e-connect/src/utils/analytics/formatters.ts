import { format, formatDistanceToNow, isToday, isYesterday, startOfDay, differenceInDays } from 'date-fns'

/**
 * Format file sizes in human-readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
}

/**
 * Format numbers with thousand separators
 */
export function formatNumber(num: number, decimals: number = 0): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(num)
}

/**
 * Format percentages
 */
export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`
}

/**
 * Format time durations in human-readable format
 */
export function formatDuration(hours: number): string {
  if (hours < 1) {
    const minutes = Math.round(hours * 60)
    return `${minutes}m`
  } else if (hours < 24) {
    const wholeHours = Math.floor(hours)
    const minutes = Math.round((hours - wholeHours) * 60)
    return minutes > 0 ? `${wholeHours}h ${minutes}m` : `${wholeHours}h`
  } else {
    const days = Math.floor(hours / 24)
    const remainingHours = Math.round(hours % 24)
    return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`
  }
}

/**
 * Format dates for display
 */
export function formatDate(date: Date, formatStr?: string): string {
  if (!date) return 'N/A'
  
  if (formatStr) {
    return format(date, formatStr)
  }
  
  if (isToday(date)) {
    return `Today ${format(date, 'HH:mm')}`
  } else if (isYesterday(date)) {
    return `Yesterday ${format(date, 'HH:mm')}`
  } else if (differenceInDays(new Date(), date) < 7) {
    return format(date, 'EEEE HH:mm')
  } else {
    return format(date, 'MMM d, yyyy')
  }
}

/**
 * Format relative time (e.g., "2 hours ago")
 */
export function formatRelativeTime(date: Date): string {
  if (!date) return 'N/A'
  return formatDistanceToNow(date, { addSuffix: true })
}

/**
 * Format email addresses for display
 */
export function formatEmailAddress(email: string, name?: string, maxLength: number = 30): string {
  if (!email) return 'N/A'
  
  if (name && name.trim()) {
    const displayName = name.length > maxLength ? `${name.substring(0, maxLength - 3)}...` : name
    return `${displayName} <${email}>`
  }
  
  return email.length > maxLength ? `${email.substring(0, maxLength - 3)}...` : email
}

/**
 * Format domain names
 */
export function formatDomain(domain: string): string {
  if (!domain) return 'N/A'
  
  // Remove www. prefix if present
  return domain.replace(/^www\./, '')
}

/**
 * Format response time categories
 */
export function formatResponseTimeCategory(hours: number): string {
  if (hours < 1) return 'Immediate'
  if (hours < 4) return 'Quick'
  if (hours < 24) return 'Same day'
  if (hours < 72) return 'Within 3 days'
  if (hours < 168) return 'Within a week'
  return 'Slow'
}

/**
 * Format trend indicators
 */
export function formatTrend(value: number, showDirection: boolean = true): string {
  const absValue = Math.abs(value)
  const direction = value > 0 ? '↗' : value < 0 ? '↘' : '→'
  const prefix = showDirection ? direction + ' ' : ''
  
  if (absValue < 0.1) return `${prefix}${formatPercentage(0, 0)}`
  return `${prefix}${formatPercentage(absValue, 1)}`
}

/**
 * Format trend descriptions
 */
export function formatTrendDescription(value: number): string {
  const absValue = Math.abs(value)
  
  if (absValue < 1) return 'Stable'
  if (absValue < 5) return value > 0 ? 'Slight increase' : 'Slight decrease'
  if (absValue < 15) return value > 0 ? 'Moderate increase' : 'Moderate decrease'
  if (absValue < 30) return value > 0 ? 'Strong increase' : 'Strong decrease'
  return value > 0 ? 'Very strong increase' : 'Very strong decrease'
}

/**
 * Format importance levels
 */
export function formatImportance(importance: 'high' | 'medium' | 'low'): {
  label: string
  color: string
  icon: string
} {
  const levels = {
    high: { label: 'High', color: '#ef4444', icon: '🔴' },
    medium: { label: 'Medium', color: '#f59e0b', icon: '🟡' },
    low: { label: 'Low', color: '#10b981', icon: '🟢' }
  }
  
  return levels[importance] || levels.low
}

/**
 * Format sentiment
 */
export function formatSentiment(sentiment: 'positive' | 'negative' | 'neutral'): {
  label: string
  color: string
  icon: string
} {
  const sentiments = {
    positive: { label: 'Positive', color: '#10b981', icon: '😊' },
    negative: { label: 'Negative', color: '#ef4444', icon: '😞' },
    neutral: { label: 'Neutral', color: '#6b7280', icon: '😐' }
  }
  
  return sentiments[sentiment] || sentiments.neutral
}

/**
 * Format category names
 */
export function formatCategoryName(category: string): string {
  return category
    .split(/[_-\s]+/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')
}

/**
 * Format time ranges
 */
export function formatTimeRange(start: Date, end: Date): string {
  const daysDiff = differenceInDays(end, start)
  
  if (daysDiff === 0) {
    return format(start, 'MMM d, yyyy')
  } else if (daysDiff === 1) {
    return `${format(start, 'MMM d')} - ${format(end, 'MMM d, yyyy')}`
  } else if (daysDiff < 7) {
    return `${format(start, 'MMM d')} - ${format(end, 'MMM d, yyyy')}`
  } else if (daysDiff < 31) {
    return `${format(start, 'MMM d')} - ${format(end, 'MMM d, yyyy')}`
  } else {
    return `${format(start, 'MMM d, yyyy')} - ${format(end, 'MMM d, yyyy')}`
  }
}

/**
 * Format metric values with appropriate units
 */
export function formatMetricValue(value: number, metric: string): string {
  switch (metric.toLowerCase()) {
    case 'size':
    case 'attachmentsize':
    case 'messagesize':
      return formatFileSize(value)
    
    case 'responsetime':
    case 'avgresponsetime':
    case 'processingtime':
      return formatDuration(value)
    
    case 'percentage':
    case 'rate':
    case 'accuracy':
      return formatPercentage(value)
    
    case 'count':
    case 'messages':
    case 'emails':
    case 'threads':
      return formatNumber(value)
    
    case 'trend':
    case 'change':
    case 'growth':
      return formatTrend(value)
    
    default:
      if (value > 1000) {
        return formatNumber(value)
      }
      return value.toString()
  }
}

/**
 * Format chart labels
 */
export function formatChartLabel(value: any, type: 'date' | 'number' | 'percentage' | 'duration'): string {
  switch (type) {
    case 'date':
      return formatDate(new Date(value), 'MMM d')
    
    case 'number':
      return formatNumber(value)
    
    case 'percentage':
      return formatPercentage(value)
    
    case 'duration':
      return formatDuration(value)
    
    default:
      return String(value)
  }
}

/**
 * Format error messages for user display
 */
export function formatErrorMessage(error: Error | string): string {
  const message = typeof error === 'string' ? error : error.message
  
  // Make error messages more user-friendly
  if (message.includes('fetch')) {
    return 'Unable to load data. Please check your connection and try again.'
  } else if (message.includes('timeout')) {
    return 'Request timed out. Please try again.'
  } else if (message.includes('unauthorized')) {
    return 'You are not authorized to access this data.'
  } else if (message.includes('not found')) {
    return 'The requested data was not found.'
  } else {
    return message || 'An unexpected error occurred.'
  }
}