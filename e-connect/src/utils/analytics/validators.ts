import { EmailStats, SenderStats, VolumeData, CategoryStats } from '@/types/analytics'

export interface ValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  severity: 'error' | 'warning' | 'info'
}

/**
 * Validate email statistics data
 */
export function validateEmailStats(emailStats: EmailStats): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (!emailStats.totals) {
    errors.push('Missing totals data')
  } else {
    const { totals } = emailStats
    
    if (totals.threads < 0 || totals.messages < 0) {
      errors.push('Negative values found in totals')
    }
    
    if (totals.unread > totals.threads) {
      errors.push('Unread count cannot exceed total threads')
    }
  }

  if (!emailStats.metrics) {
    errors.push('Missing metrics data')
  } else {
    const { metrics } = emailStats
    
    if (metrics.avgResponseTime < 0) {
      errors.push('Negative average response time')
    }
    
    if (metrics.emailsPerDay < 0) {
      errors.push('Negative emails per day')
    }
  }

  if (!emailStats.period) {
    errors.push('Missing period information')
  } else {
    if (emailStats.period.start >= emailStats.period.end) {
      errors.push('Period start date must be before end date')
    }
  }

  const severity = errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'info'

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    severity
  }
}

/**
 * Validate sender statistics data
 */
export function validateSenderStats(senders: SenderStats[]): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (!Array.isArray(senders)) {
    errors.push('Sender data must be an array')
    return { isValid: false, errors, warnings, severity: 'error' }
  }

  senders.forEach((sender, index) => {
    const prefix = `Sender ${index + 1}`

    if (!sender.email) {
      errors.push(`${prefix}: Missing email address`)
    }

    if (!sender.domain) {
      errors.push(`${prefix}: Missing domain`)
    }

    if (sender.messageCount < 0) {
      errors.push(`${prefix}: Negative message count`)
    }
    
    if (sender.threadCount < 0) {
      errors.push(`${prefix}: Negative thread count`)
    }
  })

  const severity = errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'info'

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    severity
  }
}

/**
 * Validate date range
 */
export function validateDateRange(start: Date, end: Date): ValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  if (!start || !end) {
    errors.push('Start and end dates are required')
  } else {
    if (start >= end) {
      errors.push('Start date must be before end date')
    }

    const now = new Date()
    if (start > now) {
      errors.push('Start date cannot be in the future')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    severity: errors.length > 0 ? 'error' : warnings.length > 0 ? 'warning' : 'info'
  }
}