import { VolumeData } from '@/types/analytics'
import { addDays, addWeeks, addMonths, format } from 'date-fns'

export interface Prediction {
  date: Date
  predicted: number
  confidence: number
  lowerBound: number
  upperBound: number
  method: string
}

export interface PredictionResult {
  predictions: Prediction[]
  accuracy: number
  method: string
  parameters: Record<string, any>
  insights: string[]
}

/**
 * Predict future email volume using multiple methods
 */
export function predictFutureVolume(
  historicalData: VolumeData[],
  options: {
    days: number
    method: 'linear' | 'exponential' | 'seasonal' | 'ensemble'
    confidence: number
  } = {
    days: 30,
    method: 'ensemble',
    confidence: 0.95
  }
): PredictionResult {
  if (historicalData.length < 3) {
    return {
      predictions: [],
      accuracy: 0,
      method: options.method,
      parameters: {},
      insights: ['Insufficient historical data for predictions']
    }
  }

  const sortedData = [...historicalData].sort((a, b) => a.date.getTime() - b.date.getTime())
  
  switch (options.method) {
    case 'linear':
      return predictLinearTrend(sortedData, options.days, options.confidence)
    case 'exponential':
      return predictExponentialTrend(sortedData, options.days, options.confidence)
    case 'seasonal':
      return predictSeasonalTrend(sortedData, options.days, options.confidence)
    case 'ensemble':
      return predictEnsemble(sortedData, options.days, options.confidence)
    default:
      return predictLinearTrend(sortedData, options.days, options.confidence)
  }
}

/**
 * Linear trend prediction
 */
function predictLinearTrend(
  data: VolumeData[],
  days: number,
  confidence: number
): PredictionResult {
  const values = data.map(d => d.received + d.sent)
  const n = values.length
  
  // Calculate linear regression
  const sumX = (n * (n - 1)) / 2
  const sumY = values.reduce((sum, val) => sum + val, 0)
  const sumXY = values.reduce((sum, val, index) => sum + (index * val), 0)
  const sumXX = values.reduce((sum, _, index) => sum + (index * index), 0)
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
  const intercept = (sumY - slope * sumX) / n
  
  // Calculate residuals for confidence intervals
  const residuals = values.map((val, index) => val - (slope * index + intercept))
  const residualVariance = residuals.reduce((sum, r) => sum + r * r, 0) / (n - 2)
  const standardError = Math.sqrt(residualVariance)
  
  // Generate predictions
  const predictions: Prediction[] = []
  const lastDate = data[data.length - 1].date
  
  for (let i = 1; i <= days; i++) {
    const x = n + i - 1
    const predicted = slope * x + intercept
    const predictionDate = addDays(lastDate, i)
    
    // Confidence interval calculation
    const tValue = 1.96 // Approximate for 95% confidence
    const predictionError = standardError * Math.sqrt(1 + 1/n + Math.pow(x - (n-1)/2, 2) / sumXX)
    const margin = tValue * predictionError
    
    predictions.push({
      date: predictionDate,
      predicted: Math.max(0, predicted),
      confidence: confidence,
      lowerBound: Math.max(0, predicted - margin),
      upperBound: predicted + margin,
      method: 'linear'
    })
  }
  
  // Calculate accuracy using historical data
  const accuracy = calculateHistoricalAccuracy(data, 'linear')
  
  const insights = [
    `Linear trend shows ${slope > 0 ? 'increasing' : 'decreasing'} email volume`,
    `Average daily change: ${slope.toFixed(1)} emails`,
    `Prediction accuracy: ${(accuracy * 100).toFixed(1)}%`
  ]
  
  return {
    predictions,
    accuracy,
    method: 'linear',
    parameters: { slope, intercept, standardError },
    insights
  }
}

/**
 * Exponential trend prediction
 */
function predictExponentialTrend(
  data: VolumeData[],
  days: number,
  confidence: number
): PredictionResult {
  const values = data.map(d => d.received + d.sent)
  
  // Filter out zero values for log transformation
  const nonZeroValues = values.filter(v => v > 0)
  if (nonZeroValues.length < 3) {
    return predictLinearTrend(data, days, confidence)
  }
  
  const logValues = nonZeroValues.map(v => Math.log(v))
  const n = logValues.length
  
  // Linear regression on log-transformed data
  const sumX = (n * (n - 1)) / 2
  const sumY = logValues.reduce((sum, val) => sum + val, 0)
  const sumXY = logValues.reduce((sum, val, index) => sum + (index * val), 0)
  const sumXX = (n * (n - 1) * (2 * n - 1)) / 6
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
  const intercept = (sumY - slope * sumX) / n
  
  // Calculate prediction error
  const residuals = logValues.map((val, index) => val - (slope * index + intercept))
  const standardError = Math.sqrt(residuals.reduce((sum, r) => sum + r * r, 0) / (n - 2))
  
  const predictions: Prediction[] = []
  const lastDate = data[data.length - 1].date
  
  for (let i = 1; i <= days; i++) {
    const x = n + i - 1
    const logPredicted = slope * x + intercept
    const predicted = Math.exp(logPredicted)
    const predictionDate = addDays(lastDate, i)
    
    // Confidence intervals (approximate)
    const logMargin = 1.96 * standardError
    const lowerBound = Math.exp(logPredicted - logMargin)
    const upperBound = Math.exp(logPredicted + logMargin)
    
    predictions.push({
      date: predictionDate,
      predicted: Math.max(0, predicted),
      confidence: confidence,
      lowerBound: Math.max(0, lowerBound),
      upperBound,
      method: 'exponential'
    })
  }
  
  const accuracy = calculateHistoricalAccuracy(data, 'exponential')
  const growthRate = (Math.exp(slope) - 1) * 100
  
  const insights = [
    `Exponential trend shows ${growthRate > 0 ? 'growth' : 'decline'} of ${Math.abs(growthRate).toFixed(1)}% per day`,
    `Compound growth rate: ${growthRate.toFixed(2)}%`,
    `Prediction accuracy: ${(accuracy * 100).toFixed(1)}%`
  ]
  
  return {
    predictions,
    accuracy,
    method: 'exponential',
    parameters: { slope, intercept, growthRate, standardError },
    insights
  }
}

/**
 * Seasonal trend prediction
 */
function predictSeasonalTrend(
  data: VolumeData[],
  days: number,
  confidence: number
): PredictionResult {
  const values = data.map(d => d.received + d.sent)
  
  if (data.length < 14) {
    return predictLinearTrend(data, days, confidence)
  }
  
  // Detect seasonality (weekly pattern)
  const seasonLength = 7
  const seasonalComponents = calculateSeasonalComponents(values, seasonLength)
  const deseasonalized = removeSeasonality(values, seasonalComponents)
  
  // Fit trend to deseasonalized data
  const trendSlope = calculateTrendSlope(deseasonalized)
  const trendIntercept = deseasonalized[0]
  
  // Calculate prediction error
  const residuals = values.map((val, index) => {
    const seasonalIndex = index % seasonLength
    const trend = trendSlope * index + trendIntercept
    const seasonal = seasonalComponents[seasonalIndex]
    return val - (trend + seasonal)
  })
  
  const standardError = Math.sqrt(residuals.reduce((sum, r) => sum + r * r, 0) / residuals.length)
  
  const predictions: Prediction[] = []
  const lastDate = data[data.length - 1].date
  
  for (let i = 1; i <= days; i++) {
    const x = values.length + i - 1
    const seasonalIndex = x % seasonLength
    const trend = trendSlope * x + trendIntercept
    const seasonal = seasonalComponents[seasonalIndex]
    const predicted = trend + seasonal
    const predictionDate = addDays(lastDate, i)
    
    const margin = 1.96 * standardError
    
    predictions.push({
      date: predictionDate,
      predicted: Math.max(0, predicted),
      confidence: confidence,
      lowerBound: Math.max(0, predicted - margin),
      upperBound: predicted + margin,
      method: 'seasonal'
    })
  }
  
  const accuracy = calculateHistoricalAccuracy(data, 'seasonal')
  const seasonalStrength = calculateSeasonalStrength(values, seasonalComponents)
  
  const insights = [
    `Seasonal pattern detected with ${seasonLength}-day cycle`,
    `Seasonal strength: ${(seasonalStrength * 100).toFixed(1)}%`,
    `Trend slope: ${trendSlope.toFixed(2)} emails/day`,
    `Prediction accuracy: ${(accuracy * 100).toFixed(1)}%`
  ]
  
  return {
    predictions,
    accuracy,
    method: 'seasonal',
    parameters: { seasonLength, seasonalComponents, trendSlope, seasonalStrength },
    insights
  }
}

/**
 * Ensemble prediction combining multiple methods
 */
function predictEnsemble(
  data: VolumeData[],
  days: number,
  confidence: number
): PredictionResult {
  const linearPred = predictLinearTrend(data, days, confidence)
  const exponentialPred = predictExponentialTrend(data, days, confidence)
  const seasonalPred = predictSeasonalTrend(data, days, confidence)
  
  // Weight models based on their historical accuracy
  const weights = {
    linear: linearPred.accuracy,
    exponential: exponentialPred.accuracy,
    seasonal: seasonalPred.accuracy
  }
  
  const totalWeight = weights.linear + weights.exponential + weights.seasonal
  
  if (totalWeight === 0) {
    return linearPred // Fallback to linear if all weights are zero
  }
  
  // Normalize weights
  weights.linear /= totalWeight
  weights.exponential /= totalWeight
  weights.seasonal /= totalWeight
  
  const predictions: Prediction[] = []
  
  for (let i = 0; i < days; i++) {
    const linearValue = linearPred.predictions[i]?.predicted || 0
    const exponentialValue = exponentialPred.predictions[i]?.predicted || 0
    const seasonalValue = seasonalPred.predictions[i]?.predicted || 0
    
    const ensemblePredicted = 
      weights.linear * linearValue +
      weights.exponential * exponentialValue +
      weights.seasonal * seasonalValue
    
    const ensembleLower = 
      weights.linear * (linearPred.predictions[i]?.lowerBound || 0) +
      weights.exponential * (exponentialPred.predictions[i]?.lowerBound || 0) +
      weights.seasonal * (seasonalPred.predictions[i]?.lowerBound || 0)
    
    const ensembleUpper = 
      weights.linear * (linearPred.predictions[i]?.upperBound || 0) +
      weights.exponential * (exponentialPred.predictions[i]?.upperBound || 0) +
      weights.seasonal * (seasonalPred.predictions[i]?.upperBound || 0)
    
    predictions.push({
      date: linearPred.predictions[i]?.date || addDays(data[data.length - 1].date, i + 1),
      predicted: Math.max(0, ensemblePredicted),
      confidence: confidence,
      lowerBound: Math.max(0, ensembleLower),
      upperBound: ensembleUpper,
      method: 'ensemble'
    })
  }
  
  const averageAccuracy = (linearPred.accuracy + exponentialPred.accuracy + seasonalPred.accuracy) / 3
  
  const insights = [
    `Ensemble combines linear (${(weights.linear * 100).toFixed(1)}%), exponential (${(weights.exponential * 100).toFixed(1)}%), and seasonal (${(weights.seasonal * 100).toFixed(1)}%) models`,
    `Average component accuracy: ${(averageAccuracy * 100).toFixed(1)}%`,
    ...linearPred.insights.slice(0, 1),
    ...seasonalPred.insights.slice(0, 1)
  ]
  
  return {
    predictions,
    accuracy: averageAccuracy,
    method: 'ensemble',
    parameters: { weights, componentAccuracies: { linear: linearPred.accuracy, exponential: exponentialPred.accuracy, seasonal: seasonalPred.accuracy } },
    insights
  }
}

/**
 * Calculate seasonal components using simple moving average
 */
function calculateSeasonalComponents(values: number[], seasonLength: number): number[] {
  const seasonalComponents = new Array(seasonLength).fill(0)
  const counts = new Array(seasonLength).fill(0)
  
  values.forEach((value, index) => {
    const seasonIndex = index % seasonLength
    seasonalComponents[seasonIndex] += value
    counts[seasonIndex]++
  })
  
  // Calculate averages
  for (let i = 0; i < seasonLength; i++) {
    if (counts[i] > 0) {
      seasonalComponents[i] /= counts[i]
    }
  }
  
  // Remove overall mean to center seasonal components
  const overallMean = values.reduce((sum, val) => sum + val, 0) / values.length
  for (let i = 0; i < seasonLength; i++) {
    seasonalComponents[i] -= overallMean
  }
  
  return seasonalComponents
}

/**
 * Remove seasonality from data
 */
function removeSeasonality(values: number[], seasonalComponents: number[]): number[] {
  return values.map((value, index) => {
    const seasonIndex = index % seasonalComponents.length
    return value - seasonalComponents[seasonIndex]
  })
}

/**
 * Calculate trend slope from deseasonalized data
 */
function calculateTrendSlope(deseasonalizedValues: number[]): number {
  const n = deseasonalizedValues.length
  const sumX = (n * (n - 1)) / 2
  const sumY = deseasonalizedValues.reduce((sum, val) => sum + val, 0)
  const sumXY = deseasonalizedValues.reduce((sum, val, index) => sum + (index * val), 0)
  const sumXX = (n * (n - 1) * (2 * n - 1)) / 6
  
  return (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
}

/**
 * Calculate seasonal strength
 */
function calculateSeasonalStrength(values: number[], seasonalComponents: number[]): number {
  const overallVariance = calculateVariance(values)
  if (overallVariance === 0) return 0
  
  const seasonalVariance = calculateVariance(seasonalComponents)
  return seasonalVariance / overallVariance
}

/**
 * Calculate variance of an array
 */
function calculateVariance(values: number[]): number {
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  return values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
}

/**
 * Calculate historical accuracy using cross-validation
 */
function calculateHistoricalAccuracy(data: VolumeData[], method: string): number {
  if (data.length < 6) return 0.5 // Default accuracy for insufficient data
  
  const testSize = Math.min(7, Math.floor(data.length * 0.3))
  const trainSize = data.length - testSize
  
  if (trainSize < 3) return 0.5
  
  const trainData = data.slice(0, trainSize)
  const testData = data.slice(trainSize)
  
  // Make predictions for test period
  let predictions: number[] = []
  
  switch (method) {
    case 'linear':
      predictions = predictWithLinear(trainData, testSize)
      break
    case 'exponential':
      predictions = predictWithExponential(trainData, testSize)
      break
    case 'seasonal':
      predictions = predictWithSeasonal(trainData, testSize)
      break
    default:
      predictions = predictWithLinear(trainData, testSize)
  }
  
  // Calculate accuracy metrics
  const actualValues = testData.map(d => d.received + d.sent)
  const errors = predictions.map((pred, index) => Math.abs(pred - actualValues[index]))
  const meanError = errors.reduce((sum, err) => sum + err, 0) / errors.length
  const meanActual = actualValues.reduce((sum, val) => sum + val, 0) / actualValues.length
  
  // Calculate accuracy as 1 - (mean_error / mean_actual)
  return meanActual > 0 ? Math.max(0, 1 - meanError / meanActual) : 0.5
}

/**
 * Helper functions for accuracy calculation
 */
function predictWithLinear(data: VolumeData[], steps: number): number[] {
  const values = data.map(d => d.received + d.sent)
  const result = predictLinearTrend(data, steps, 0.95)
  return result.predictions.map(p => p.predicted)
}

function predictWithExponential(data: VolumeData[], steps: number): number[] {
  const result = predictExponentialTrend(data, steps, 0.95)
  return result.predictions.map(p => p.predicted)
}

function predictWithSeasonal(data: VolumeData[], steps: number): number[] {
  const result = predictSeasonalTrend(data, steps, 0.95)
  return result.predictions.map(p => p.predicted)
}

/**
 * Predict email categories
 */
export function predictCategoryDistribution(
  historicalData: VolumeData[],
  days: number
): Record<string, Prediction[]> {
  const categoryPredictions: Record<string, Prediction[]> = {}
  
  if (historicalData.length < 3) return categoryPredictions
  
  const categories = Object.keys(historicalData[0]?.categories || {})
  
  categories.forEach(category => {
    const categoryData = historicalData.map(d => ({
      ...d,
      received: d.categories[category] || 0,
      sent: 0
    }))
    
    const result = predictLinearTrend(categoryData, days, 0.95)
    categoryPredictions[category] = result.predictions
  })
  
  return categoryPredictions
}

/**
 * Generate prediction insights
 */
export function generatePredictionInsights(result: PredictionResult): {
  summary: string
  trends: string[]
  recommendations: string[]
  confidence: string
} {
  const { predictions, method, accuracy, insights } = result
  
  if (predictions.length === 0) {
    return {
      summary: 'No predictions available due to insufficient data',
      trends: [],
      recommendations: ['Collect more historical data for better predictions'],
      confidence: 'Low'
    }
  }
  
  const firstPrediction = predictions[0]
  const lastPrediction = predictions[predictions.length - 1]
  const totalChange = lastPrediction.predicted - firstPrediction.predicted
  const changePercent = firstPrediction.predicted > 0 ? 
    (totalChange / firstPrediction.predicted) * 100 : 0
  
  const summary = `${method} model predicts ${changePercent >= 0 ? 'increase' : 'decrease'} of ${Math.abs(changePercent).toFixed(1)}% over ${predictions.length} days`
  
  const trends = [
    `Email volume trending ${totalChange >= 0 ? 'upward' : 'downward'}`,
    `Average daily volume: ${predictions.reduce((sum, p) => sum + p.predicted, 0) / predictions.length / 1}.toFixed(1)} emails`,
    ...insights
  ]
  
  const recommendations = []
  if (changePercent > 20) {
    recommendations.push('Prepare for significant increase in email volume')
    recommendations.push('Consider scaling email processing capacity')
  } else if (changePercent < -20) {
    recommendations.push('Monitor for potential issues causing volume decrease')
    recommendations.push('Review sender engagement patterns')
  } else {
    recommendations.push('Email volume expected to remain stable')
  }
  
  if (accuracy < 0.7) {
    recommendations.push('Prediction accuracy is moderate - continue monitoring actual vs predicted')
  }
  
  const confidenceLevel = accuracy > 0.8 ? 'High' : accuracy > 0.6 ? 'Medium' : 'Low'
  
  return {
    summary,
    trends,
    recommendations,
    confidence: confidenceLevel
  }
}