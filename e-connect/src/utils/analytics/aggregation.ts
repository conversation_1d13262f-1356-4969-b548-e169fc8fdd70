import { VolumeData } from '@/types/analytics'
import { 
  startOfDay, 
  startOfWeek, 
  startOfMonth, 
  startOfHour,
  format,
  parseISO,
  addDays,
  addWeeks,
  addMonths,
  addHours,
  isSameDay,
  isSameWeek,
  isSameMonth,
  isSameHour
} from 'date-fns'

export type AggregationPeriod = 'hourly' | 'daily' | 'weekly' | 'monthly'

export interface AggregatedData {
  period: string
  received: number
  sent: number
  archived: number
  deleted: number
  categories: Record<string, number>
  timestamp: Date
}

/**
 * Aggregate data by time period
 */
export function aggregateDataByPeriod(
  data: VolumeData[],
  period: AggregationPeriod
): AggregatedData[] {
  if (!data.length) return []

  const aggregated = new Map<string, AggregatedData>()

  // Helper functions for period grouping
  const getPeriodStart = (date: Date): Date => {
    switch (period) {
      case 'hourly':
        return startOfHour(date)
      case 'daily':
        return startOfDay(date)
      case 'weekly':
        return startOfWeek(date, { weekStartsOn: 1 }) // Monday
      case 'monthly':
        return startOfMonth(date)
      default:
        return startOfDay(date)
    }
  }

  const formatPeriod = (date: Date): string => {
    switch (period) {
      case 'hourly':
        return format(date, 'yyyy-MM-dd HH:00')
      case 'daily':
        return format(date, 'yyyy-MM-dd')
      case 'weekly':
        return format(date, 'yyyy-\\WwW')
      case 'monthly':
        return format(date, 'yyyy-MM')
      default:
        return format(date, 'yyyy-MM-dd')
    }
  }

  // Aggregate data points
  data.forEach(point => {
    const periodStart = getPeriodStart(point.date)
    const periodKey = formatPeriod(periodStart)

    if (!aggregated.has(periodKey)) {
      aggregated.set(periodKey, {
        period: periodKey,
        received: 0,
        sent: 0,
        archived: 0,
        deleted: 0,
        categories: {},
        timestamp: periodStart
      })
    }

    const agg = aggregated.get(periodKey)!
    agg.received += point.received
    agg.sent += point.sent
    agg.archived += point.archived
    agg.deleted += point.deleted

    // Aggregate categories
    Object.entries(point.categories).forEach(([category, count]) => {
      agg.categories[category] = (agg.categories[category] || 0) + count
    })
  })

  return Array.from(aggregated.values()).sort((a, b) => 
    a.timestamp.getTime() - b.timestamp.getTime()
  )
}

/**
 * Fill gaps in time series data
 */
export function fillTimeSeriesGaps(
  data: AggregatedData[],
  period: AggregationPeriod,
  startDate: Date,
  endDate: Date
): AggregatedData[] {
  if (!data.length) return []

  const filled: AggregatedData[] = []
  const dataMap = new Map(data.map(d => [d.period, d]))

  const addPeriod = (date: Date): Date => {
    switch (period) {
      case 'hourly':
        return addHours(date, 1)
      case 'daily':
        return addDays(date, 1)
      case 'weekly':
        return addWeeks(date, 1)
      case 'monthly':
        return addMonths(date, 1)
      default:
        return addDays(date, 1)
    }
  }

  const formatPeriod = (date: Date): string => {
    switch (period) {
      case 'hourly':
        return format(date, 'yyyy-MM-dd HH:00')
      case 'daily':
        return format(date, 'yyyy-MM-dd')
      case 'weekly':
        return format(date, 'yyyy-\\WwW')
      case 'monthly':
        return format(date, 'yyyy-MM')
      default:
        return format(date, 'yyyy-MM-dd')
    }
  }

  let current = startDate
  while (current <= endDate) {
    const periodKey = formatPeriod(current)
    
    if (dataMap.has(periodKey)) {
      filled.push(dataMap.get(periodKey)!)
    } else {
      // Fill with zero values
      filled.push({
        period: periodKey,
        received: 0,
        sent: 0,
        archived: 0,
        deleted: 0,
        categories: {},
        timestamp: new Date(current)
      })
    }
    
    current = addPeriod(current)
  }

  return filled
}

/**
 * Group data by category
 */
export function groupByCategory(data: VolumeData[]): Record<string, {
  total: number
  average: number
  peak: number
  trend: number
}> {
  const categories: Record<string, number[]> = {}

  // Collect all category values
  data.forEach(point => {
    Object.entries(point.categories).forEach(([category, count]) => {
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(count)
    })
  })

  // Calculate statistics for each category
  const result: Record<string, {
    total: number
    average: number
    peak: number
    trend: number
  }> = {}

  Object.entries(categories).forEach(([category, values]) => {
    const total = values.reduce((sum, val) => sum + val, 0)
    const average = total / values.length
    const peak = Math.max(...values)
    
    // Simple trend calculation (linear regression slope)
    const trend = calculateTrend(values)

    result[category] = {
      total,
      average,
      peak,
      trend
    }
  })

  return result
}

/**
 * Calculate rolling averages
 */
export function calculateRollingAverage(
  data: number[],
  windowSize: number
): number[] {
  if (windowSize <= 0 || windowSize > data.length) {
    return data.slice()
  }

  const result: number[] = []
  
  for (let i = 0; i < data.length; i++) {
    const start = Math.max(0, i - windowSize + 1)
    const window = data.slice(start, i + 1)
    const average = window.reduce((sum, val) => sum + val, 0) / window.length
    result.push(average)
  }

  return result
}

/**
 * Calculate percentage changes between periods
 */
export function calculatePeriodChanges(data: AggregatedData[]): Array<{
  period: string
  receivedChange: number
  sentChange: number
  totalChange: number
}> {
  if (data.length < 2) return []

  const changes: Array<{
    period: string
    receivedChange: number
    sentChange: number
    totalChange: number
  }> = []

  for (let i = 1; i < data.length; i++) {
    const current = data[i]
    const previous = data[i - 1]

    const receivedChange = previous.received === 0 ? 0 : 
      ((current.received - previous.received) / previous.received) * 100

    const sentChange = previous.sent === 0 ? 0 : 
      ((current.sent - previous.sent) / previous.sent) * 100

    const currentTotal = current.received + current.sent
    const previousTotal = previous.received + previous.sent
    const totalChange = previousTotal === 0 ? 0 : 
      ((currentTotal - previousTotal) / previousTotal) * 100

    changes.push({
      period: current.period,
      receivedChange,
      sentChange,
      totalChange
    })
  }

  return changes
}

/**
 * Find peak periods
 */
export function findPeakPeriods(
  data: AggregatedData[],
  metric: 'received' | 'sent' | 'total' = 'received',
  topN: number = 10
): Array<{
  period: string
  value: number
  timestamp: Date
  percentile: number
}> {
  if (!data.length) return []

  const values = data.map(d => {
    switch (metric) {
      case 'received':
        return d.received
      case 'sent':
        return d.sent
      case 'total':
        return d.received + d.sent
      default:
        return d.received
    }
  })

  const maxValue = Math.max(...values)
  
  const peaks = data
    .map((d, index) => ({
      period: d.period,
      value: values[index],
      timestamp: d.timestamp,
      percentile: (values[index] / maxValue) * 100
    }))
    .sort((a, b) => b.value - a.value)
    .slice(0, topN)

  return peaks
}

/**
 * Calculate cumulative data
 */
export function calculateCumulative(data: AggregatedData[]): AggregatedData[] {
  let cumulativeReceived = 0
  let cumulativeSent = 0
  let cumulativeArchived = 0
  let cumulativeDeleted = 0
  const cumulativeCategories: Record<string, number> = {}

  return data.map(point => {
    cumulativeReceived += point.received
    cumulativeSent += point.sent
    cumulativeArchived += point.archived
    cumulativeDeleted += point.deleted

    Object.entries(point.categories).forEach(([category, count]) => {
      cumulativeCategories[category] = (cumulativeCategories[category] || 0) + count
    })

    return {
      ...point,
      received: cumulativeReceived,
      sent: cumulativeSent,
      archived: cumulativeArchived,
      deleted: cumulativeDeleted,
      categories: { ...cumulativeCategories }
    }
  })
}

/**
 * Simple linear trend calculation
 */
function calculateTrend(values: number[]): number {
  if (values.length < 2) return 0

  const n = values.length
  const sumX = (n * (n - 1)) / 2 // Sum of indices
  const sumY = values.reduce((sum, val) => sum + val, 0)
  const sumXY = values.reduce((sum, val, index) => sum + (index * val), 0)
  const sumXX = values.reduce((sum, _, index) => sum + (index * index), 0)

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
  return slope
}

/**
 * Detect seasonal patterns
 */
export function detectSeasonalPatterns(
  data: AggregatedData[],
  period: AggregationPeriod
): {
  hasSeasonality: boolean
  seasonalStrength: number
  patterns: Record<string, number>
} {
  if (data.length < 4) {
    return {
      hasSeasonality: false,
      seasonalStrength: 0,
      patterns: {}
    }
  }

  // Group by season (hour of day, day of week, etc.)
  const patterns: Record<string, number[]> = {}

  data.forEach(point => {
    let seasonKey: string

    switch (period) {
      case 'hourly':
        seasonKey = format(point.timestamp, 'HH')
        break
      case 'daily':
        seasonKey = format(point.timestamp, 'EEEE') // Day of week
        break
      case 'weekly':
        seasonKey = format(point.timestamp, 'w') // Week of year
        break
      case 'monthly':
        seasonKey = format(point.timestamp, 'MM') // Month
        break
      default:
        seasonKey = format(point.timestamp, 'EEEE')
    }

    if (!patterns[seasonKey]) {
      patterns[seasonKey] = []
    }
    patterns[seasonKey].push(point.received + point.sent)
  })

  // Calculate seasonal strength
  const allValues = Object.values(patterns).flat()
  const overallMean = allValues.reduce((sum, val) => sum + val, 0) / allValues.length
  
  const seasonalMeans = Object.fromEntries(
    Object.entries(patterns).map(([key, values]) => [
      key,
      values.reduce((sum, val) => sum + val, 0) / values.length
    ])
  )

  const seasonalVariance = Object.values(seasonalMeans).reduce(
    (sum, mean) => sum + Math.pow(mean - overallMean, 2),
    0
  ) / Object.keys(seasonalMeans).length

  const totalVariance = allValues.reduce(
    (sum, val) => sum + Math.pow(val - overallMean, 2),
    0
  ) / allValues.length

  const seasonalStrength = totalVariance > 0 ? seasonalVariance / totalVariance : 0

  return {
    hasSeasonality: seasonalStrength > 0.1, // Threshold for seasonality
    seasonalStrength,
    patterns: seasonalMeans
  }
}