import { EmailStats, SenderStats, VolumeData, CategoryStats } from '@/types/analytics'
import { format } from 'date-fns'

export interface ExportOptions {
  format: 'csv' | 'json' | 'xlsx' | 'pdf'
  includeHeaders: boolean
  dateFormat: string
  delimiter: string
  encoding: 'utf-8' | 'utf-16'
  compressed: boolean
}

export interface ExportResult {
  data: string | Blob | ArrayBuffer
  filename: string
  mimeType: string
  size: number
}

/**
 * Export email statistics to various formats
 */
export async function exportEmailStats(
  emailStats: EmailStats,
  options: Partial<ExportOptions> = {}
): Promise<ExportResult> {
  const opts: ExportOptions = {
    format: 'csv',
    includeHeaders: true,
    dateFormat: 'yyyy-MM-dd',
    delimiter: ',',
    encoding: 'utf-8',
    compressed: false,
    ...options
  }

  switch (opts.format) {
    case 'csv':
      return exportEmailStatsToCSV(emailStats, opts)
    case 'json':
      return exportEmailStatsToJSON(emailStats, opts)
    default:
      throw new Error(`Unsupported export format: ${opts.format}`)
  }
}

/**
 * Export sender analytics
 */
export async function exportSenderAnalytics(
  senders: SenderStats[],
  options: Partial<ExportOptions> = {}
): Promise<ExportResult> {
  const opts: ExportOptions = {
    format: 'csv',
    includeHeaders: true,
    dateFormat: 'yyyy-MM-dd',
    delimiter: ',',
    encoding: 'utf-8',
    compressed: false,
    ...options
  }

  switch (opts.format) {
    case 'csv':
      return exportSendersToCSV(senders, opts)
    case 'json':
      return exportSendersToJSON(senders, opts)
    default:
      throw new Error(`Unsupported export format: ${opts.format}`)
  }
}

function exportEmailStatsToCSV(emailStats: EmailStats, options: ExportOptions): ExportResult {
  const lines: string[] = []
  
  if (options.includeHeaders) {
    lines.push('Metric,Value')
  }
  
  Object.entries(emailStats.totals).forEach(([key, value]) => {
    lines.push(`${key},${value}`)
  })
  
  Object.entries(emailStats.metrics).forEach(([key, value]) => {
    if (Array.isArray(value)) {
      lines.push(`${key},"${value.join(';')}"`)
    } else {
      lines.push(`${key},${value}`)
    }
  })
  
  const csvContent = lines.join('\n')
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  
  return {
    data: blob,
    filename: `email_stats_${format(new Date(), 'yyyy-MM-dd')}.csv`,
    mimeType: 'text/csv',
    size: blob.size
  }
}

function exportSendersToCSV(senders: SenderStats[], options: ExportOptions): ExportResult {
  const lines: string[] = []
  
  if (options.includeHeaders) {
    lines.push([
      'Email',
      'Name',
      'Domain',
      'Message Count',
      'Thread Count',
      'Unread Count',
      'Avg Response Time',
      'Importance',
      'Categories'
    ].join(options.delimiter))
  }
  
  senders.forEach(sender => {
    const row = [
      sender.email,
      sender.name || '',
      sender.domain,
      sender.messageCount,
      sender.threadCount,
      sender.unreadCount,
      sender.avgResponseTime || '',
      sender.importance,
      sender.categories.join(';')
    ]
    lines.push(row.join(options.delimiter))
  })
  
  const csvContent = lines.join('\n')
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  
  return {
    data: blob,
    filename: `sender_analytics_${format(new Date(), 'yyyy-MM-dd')}.csv`,
    mimeType: 'text/csv',
    size: blob.size
  }
}

function exportEmailStatsToJSON(emailStats: EmailStats, options: ExportOptions): ExportResult {
  const jsonData = {
    exportDate: new Date().toISOString(),
    exportOptions: options,
    data: emailStats
  }
  
  const jsonString = JSON.stringify(jsonData, null, 2)
  const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8;' })
  
  return {
    data: blob,
    filename: `email_stats_${format(new Date(), 'yyyy-MM-dd')}.json`,
    mimeType: 'application/json',
    size: blob.size
  }
}

function exportSendersToJSON(senders: SenderStats[], options: ExportOptions): ExportResult {
  const jsonData = {
    exportDate: new Date().toISOString(),
    exportOptions: options,
    count: senders.length,
    data: senders
  }
  
  const jsonString = JSON.stringify(jsonData, null, 2)
  const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8;' })
  
  return {
    data: blob,
    filename: `sender_analytics_${format(new Date(), 'yyyy-MM-dd')}.json`,
    mimeType: 'application/json',
    size: blob.size
  }
}

/**
 * Download exported data
 */
export function downloadExportResult(result: ExportResult): void {
  const url = URL.createObjectURL(result.data as Blob)
  const link = document.createElement('a')
  link.href = url
  link.download = result.filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}