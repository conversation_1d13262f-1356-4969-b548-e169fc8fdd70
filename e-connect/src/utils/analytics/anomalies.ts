import { VolumeData } from '@/types/analytics'

export interface Anomaly {
  date: Date
  type: 'spike' | 'drop' | 'unusual_pattern'
  metric: 'received' | 'sent' | 'total' | 'category'
  value: number
  expectedValue: number
  severity: 'low' | 'medium' | 'high'
  confidence: number
  description: string
  category?: string
}

/**
 * Detect anomalies in email volume data using statistical methods
 */
export function detectAnomalies(
  volumeData: VolumeData[],
  options: {
    sensitivity: number // 1-5, higher = more sensitive
    methods: ('zscore' | 'iqr' | 'isolation')[]
    minDataPoints: number
  } = {
    sensitivity: 3,
    methods: ['zscore', 'iqr'],
    minDataPoints: 7
  }
): Anomaly[] {
  if (volumeData.length < options.minDataPoints) {
    return []
  }

  const anomalies: Anomaly[] = []
  const sortedData = [...volumeData].sort((a, b) => a.date.getTime() - b.date.getTime())

  // Detect anomalies using different methods
  if (options.methods.includes('zscore')) {
    anomalies.push(...detectZScoreAnomalies(sortedData, options.sensitivity))
  }

  if (options.methods.includes('iqr')) {
    anomalies.push(...detectIQRAnomalies(sortedData, options.sensitivity))
  }

  if (options.methods.includes('isolation')) {
    anomalies.push(...detectIsolationAnomalies(sortedData, options.sensitivity))
  }

  // Remove duplicates and sort by severity
  const uniqueAnomalies = removeDuplicateAnomalies(anomalies)
  return uniqueAnomalies.sort((a, b) => {
    const severityOrder = { high: 3, medium: 2, low: 1 }
    return severityOrder[b.severity] - severityOrder[a.severity]
  })
}

/**
 * Detect anomalies using Z-Score method
 */
function detectZScoreAnomalies(data: VolumeData[], sensitivity: number): Anomaly[] {
  const anomalies: Anomaly[] = []
  const threshold = Math.max(1.5, 4 - sensitivity * 0.5) // Adjust threshold based on sensitivity

  // Check received emails
  const receivedValues = data.map(d => d.received)
  const receivedStats = calculateStats(receivedValues)
  
  data.forEach((point, index) => {
    const zScore = Math.abs((point.received - receivedStats.mean) / receivedStats.stdDev)
    if (zScore > threshold) {
      anomalies.push({
        date: point.date,
        type: point.received > receivedStats.mean ? 'spike' : 'drop',
        metric: 'received',
        value: point.received,
        expectedValue: receivedStats.mean,
        severity: getSeverity(zScore, threshold),
        confidence: Math.min(0.95, zScore / (threshold * 2)),
        description: `Unusual received email volume: ${point.received} (expected ~${Math.round(receivedStats.mean)})`
      })
    }
  })

  // Check sent emails
  const sentValues = data.map(d => d.sent)
  const sentStats = calculateStats(sentValues)
  
  data.forEach((point, index) => {
    const zScore = Math.abs((point.sent - sentStats.mean) / sentStats.stdDev)
    if (zScore > threshold) {
      anomalies.push({
        date: point.date,
        type: point.sent > sentStats.mean ? 'spike' : 'drop',
        metric: 'sent',
        value: point.sent,
        expectedValue: sentStats.mean,
        severity: getSeverity(zScore, threshold),
        confidence: Math.min(0.95, zScore / (threshold * 2)),
        description: `Unusual sent email volume: ${point.sent} (expected ~${Math.round(sentStats.mean)})`
      })
    }
  })

  // Check categories
  const categories = Object.keys(data[0]?.categories || {})
  categories.forEach(category => {
    const categoryValues = data.map(d => d.categories[category] || 0)
    const categoryStats = calculateStats(categoryValues)
    
    data.forEach((point, index) => {
      const value = point.categories[category] || 0
      const zScore = Math.abs((value - categoryStats.mean) / categoryStats.stdDev)
      if (zScore > threshold) {
        anomalies.push({
          date: point.date,
          type: value > categoryStats.mean ? 'spike' : 'drop',
          metric: 'category',
          category,
          value,
          expectedValue: categoryStats.mean,
          severity: getSeverity(zScore, threshold),
          confidence: Math.min(0.95, zScore / (threshold * 2)),
          description: `Unusual ${category} volume: ${value} (expected ~${Math.round(categoryStats.mean)})`
        })
      }
    })
  })

  return anomalies
}

/**
 * Detect anomalies using Interquartile Range (IQR) method
 */
function detectIQRAnomalies(data: VolumeData[], sensitivity: number): Anomaly[] {
  const anomalies: Anomaly[] = []
  const multiplier = Math.max(1, 2.5 - sensitivity * 0.3) // Adjust multiplier based on sensitivity

  // Function to detect outliers for a given metric
  const detectOutliers = (values: number[], metric: 'received' | 'sent' | 'total', category?: string) => {
    const sorted = [...values].sort((a, b) => a - b)
    const q1 = sorted[Math.floor(sorted.length * 0.25)]
    const q3 = sorted[Math.floor(sorted.length * 0.75)]
    const iqr = q3 - q1
    const lowerBound = q1 - multiplier * iqr
    const upperBound = q3 + multiplier * iqr
    const median = sorted[Math.floor(sorted.length * 0.5)]

    data.forEach((point, index) => {
      const value = category ? (point.categories[category] || 0) : 
                    metric === 'received' ? point.received :
                    metric === 'sent' ? point.sent :
                    point.received + point.sent

      if (value < lowerBound || value > upperBound) {
        const distance = Math.min(
          Math.abs(value - lowerBound),
          Math.abs(value - upperBound)
        )
        const severity = distance > iqr * 2 ? 'high' : distance > iqr ? 'medium' : 'low'

        anomalies.push({
          date: point.date,
          type: value > upperBound ? 'spike' : 'drop',
          metric: category ? 'category' : metric,
          category,
          value,
          expectedValue: median,
          severity,
          confidence: Math.min(0.9, distance / (iqr * 2)),
          description: category 
            ? `${category} volume outside normal range: ${value} (normal range: ${Math.round(lowerBound)}-${Math.round(upperBound)})`
            : `${metric} volume outside normal range: ${value} (normal range: ${Math.round(lowerBound)}-${Math.round(upperBound)})`
        })
      }
    })
  }

  // Detect outliers for different metrics
  detectOutliers(data.map(d => d.received), 'received')
  detectOutliers(data.map(d => d.sent), 'sent')
  detectOutliers(data.map(d => d.received + d.sent), 'total')

  // Detect outliers for categories
  const categories = Object.keys(data[0]?.categories || {})
  categories.forEach(category => {
    const values = data.map(d => d.categories[category] || 0)
    detectOutliers(values, 'total', category)
  })

  return anomalies
}

/**
 * Detect anomalies using a simplified isolation forest approach
 */
function detectIsolationAnomalies(data: VolumeData[], sensitivity: number): Anomaly[] {
  const anomalies: Anomaly[] = []
  const threshold = 0.7 - sensitivity * 0.1 // Lower threshold = more sensitive

  // Create feature vectors for each data point
  const features = data.map(point => [
    point.received,
    point.sent,
    point.received + point.sent,
    ...Object.values(point.categories)
  ])

  // Simple isolation score calculation
  features.forEach((feature, index) => {
    const isolationScore = calculateIsolationScore(feature, features)
    
    if (isolationScore < threshold) {
      const point = data[index]
      const totalVolume = point.received + point.sent
      const avgVolume = data.reduce((sum, d) => sum + d.received + d.sent, 0) / data.length

      anomalies.push({
        date: point.date,
        type: 'unusual_pattern',
        metric: 'total',
        value: totalVolume,
        expectedValue: avgVolume,
        severity: isolationScore < threshold * 0.7 ? 'high' : isolationScore < threshold * 0.85 ? 'medium' : 'low',
        confidence: 1 - isolationScore,
        description: `Unusual email pattern detected (isolation score: ${isolationScore.toFixed(3)})`
      })
    }
  })

  return anomalies
}

/**
 * Calculate basic statistics for a dataset
 */
function calculateStats(values: number[]): {
  mean: number
  stdDev: number
  min: number
  max: number
  median: number
} {
  const mean = values.reduce((sum, val) => sum + val, 0) / values.length
  const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length
  const stdDev = Math.sqrt(variance)
  
  const sorted = [...values].sort((a, b) => a - b)
  const median = sorted.length % 2 === 0
    ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
    : sorted[Math.floor(sorted.length / 2)]

  return {
    mean,
    stdDev,
    min: Math.min(...values),
    max: Math.max(...values),
    median
  }
}

/**
 * Get severity level based on Z-score
 */
function getSeverity(zScore: number, threshold: number): 'low' | 'medium' | 'high' {
  if (zScore > threshold * 2) return 'high'
  if (zScore > threshold * 1.5) return 'medium'
  return 'low'
}

/**
 * Calculate a simple isolation score for anomaly detection
 */
function calculateIsolationScore(target: number[], dataset: number[][]): number {
  let isolationPath = 0
  let maxDepth = Math.ceil(Math.log2(dataset.length))
  
  // Simplified isolation calculation
  for (let depth = 0; depth < maxDepth; depth++) {
    // Random feature selection and split
    const featureIndex = Math.floor(Math.random() * target.length)
    const featureValues = dataset.map(point => point[featureIndex])
    const splitValue = (Math.min(...featureValues) + Math.max(...featureValues)) / 2
    
    // Check which side of the split our target falls on
    if (target[featureIndex] < splitValue) {
      dataset = dataset.filter(point => point[featureIndex] < splitValue)
    } else {
      dataset = dataset.filter(point => point[featureIndex] >= splitValue)
    }
    
    isolationPath++
    
    // If we've isolated the point or run out of data, stop
    if (dataset.length <= 1) break
  }
  
  // Normalize the isolation path length
  return isolationPath / maxDepth
}

/**
 * Remove duplicate anomalies (same date and metric)
 */
function removeDuplicateAnomalies(anomalies: Anomaly[]): Anomaly[] {
  const seen = new Set<string>()
  return anomalies.filter(anomaly => {
    const key = `${anomaly.date.toISOString()}_${anomaly.metric}_${anomaly.category || ''}`
    if (seen.has(key)) return false
    seen.add(key)
    return true
  })
}

/**
 * Detect pattern anomalies (day-of-week, time-of-day patterns)
 */
export function detectPatternAnomalies(volumeData: VolumeData[]): Anomaly[] {
  const anomalies: Anomaly[] = []
  
  if (volumeData.length < 14) return anomalies // Need at least 2 weeks of data

  // Group by day of week
  const dayOfWeekPatterns: Record<number, number[]> = {}
  
  volumeData.forEach(point => {
    const dayOfWeek = point.date.getDay()
    const total = point.received + point.sent
    
    if (!dayOfWeekPatterns[dayOfWeek]) {
      dayOfWeekPatterns[dayOfWeek] = []
    }
    dayOfWeekPatterns[dayOfWeek].push(total)
  })

  // Check for day-of-week anomalies
  Object.entries(dayOfWeekPatterns).forEach(([day, values]) => {
    if (values.length < 2) return
    
    const stats = calculateStats(values)
    const dayName = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][parseInt(day)]
    
    values.forEach((value, index) => {
      const zScore = Math.abs((value - stats.mean) / stats.stdDev)
      if (zScore > 2.5) {
        // Find the corresponding date for this value
        const correspondingDates = volumeData
          .filter(d => d.date.getDay() === parseInt(day))
          .map(d => d.date)
          .sort((a, b) => a.getTime() - b.getTime())
        
        if (correspondingDates[index]) {
          anomalies.push({
            date: correspondingDates[index],
            type: value > stats.mean ? 'spike' : 'drop',
            metric: 'total',
            value,
            expectedValue: stats.mean,
            severity: zScore > 3 ? 'high' : 'medium',
            confidence: Math.min(0.9, zScore / 4),
            description: `Unusual ${dayName} email volume: ${value} (typical ${dayName}: ~${Math.round(stats.mean)})`
          })
        }
      }
    })
  })

  return anomalies
}

/**
 * Detect seasonal anomalies
 */
export function detectSeasonalAnomalies(
  volumeData: VolumeData[],
  seasonLength: number = 7 // Weekly seasonality by default
): Anomaly[] {
  const anomalies: Anomaly[] = []
  
  if (volumeData.length < seasonLength * 3) return anomalies

  const values = volumeData.map(d => d.received + d.sent)
  
  // Calculate seasonal components
  for (let i = seasonLength * 2; i < values.length; i++) {
    const currentValue = values[i]
    
    // Get historical values for the same seasonal position
    const seasonalValues: number[] = []
    for (let j = i - seasonLength; j >= 0; j -= seasonLength) {
      seasonalValues.push(values[j])
    }
    
    if (seasonalValues.length < 2) continue
    
    const seasonalStats = calculateStats(seasonalValues)
    const zScore = Math.abs((currentValue - seasonalStats.mean) / seasonalStats.stdDev)
    
    if (zScore > 2.0) {
      anomalies.push({
        date: volumeData[i].date,
        type: currentValue > seasonalStats.mean ? 'spike' : 'drop',
        metric: 'total',
        value: currentValue,
        expectedValue: seasonalStats.mean,
        severity: zScore > 3 ? 'high' : 'medium',
        confidence: Math.min(0.9, zScore / 4),
        description: `Seasonal anomaly detected: ${currentValue} (seasonal average: ~${Math.round(seasonalStats.mean)})`
      })
    }
  }

  return anomalies
}

/**
 * Get anomaly insights and recommendations
 */
export function getAnomalyInsights(anomalies: Anomaly[]): {
  summary: string
  totalAnomalies: number
  severityBreakdown: Record<string, number>
  recommendations: string[]
  patterns: string[]
} {
  const totalAnomalies = anomalies.length
  
  const severityBreakdown = anomalies.reduce((acc, anomaly) => {
    acc[anomaly.severity] = (acc[anomaly.severity] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // Generate recommendations based on anomaly patterns
  const recommendations: string[] = []
  const patterns: string[] = []

  const spikes = anomalies.filter(a => a.type === 'spike')
  const drops = anomalies.filter(a => a.type === 'drop')
  const highSeverity = anomalies.filter(a => a.severity === 'high')

  if (spikes.length > drops.length * 2) {
    patterns.push('Frequent email volume spikes detected')
    recommendations.push('Consider setting up filters or rules to manage high-volume periods')
  }

  if (drops.length > spikes.length * 2) {
    patterns.push('Frequent email volume drops detected')
    recommendations.push('Monitor for potential delivery issues or sender problems')
  }

  if (highSeverity.length > totalAnomalies * 0.3) {
    patterns.push('High number of severe anomalies')
    recommendations.push('Review email system configuration and monitoring')
  }

  const categoryAnomalies = anomalies.filter(a => a.metric === 'category')
  if (categoryAnomalies.length > totalAnomalies * 0.4) {
    patterns.push('Category-specific anomalies detected')
    recommendations.push('Review email categorization rules and filters')
  }

  // Default recommendations if no specific patterns found
  if (recommendations.length === 0) {
    recommendations.push('No significant patterns detected - continue monitoring')
  }

  const summary = totalAnomalies === 0 
    ? 'No anomalies detected in the current period'
    : `${totalAnomalies} anomalies detected with ${highSeverity.length} high-severity issues`

  return {
    summary,
    totalAnomalies,
    severityBreakdown,
    recommendations,
    patterns
  }
}