import { EmailStats, SenderStats, VolumeData } from '@/types/analytics'

export interface CorrelationResult {
  variables: [string, string]
  coefficient: number
  strength: 'very_weak' | 'weak' | 'moderate' | 'strong' | 'very_strong'
  significance: number
  pValue: number
  interpretation: string
}

/**
 * Calculate correlations between various email metrics
 */
export function calculateCorrelations(
  emailStats: EmailStats,
  senderStats: SenderStats[]
): Record<string, number> {
  const correlations: Record<string, number> = {}
  
  if (!emailStats.volumeData || emailStats.volumeData.length < 3) {
    return correlations
  }

  const volumeData = emailStats.volumeData
  
  // Extract time series data
  const received = volumeData.map(d => d.received)
  const sent = volumeData.map(d => d.sent)
  const archived = volumeData.map(d => d.archived)
  const deleted = volumeData.map(d => d.deleted)
  
  // Calculate basic correlations
  correlations.receivedVsSent = calculatePearsonCorrelation(received, sent)
  correlations.receivedVsArchived = calculatePearsonCorrelation(received, archived)
  correlations.receivedVsDeleted = calculatePearsonCorrelation(received, deleted)
  correlations.sentVsArchived = calculatePearsonCorrelation(sent, archived)
  
  // Category correlations
  const categories = Object.keys(volumeData[0]?.categories || {})
  categories.forEach(category => {
    const categoryValues = volumeData.map(d => d.categories[category] || 0)
    correlations[`${category}VsReceived`] = calculatePearsonCorrelation(categoryValues, received)
    correlations[`${category}VsSent`] = calculatePearsonCorrelation(categoryValues, sent)
  })

  // Day of week correlations
  const dayOfWeekVolumes = calculateDayOfWeekCorrelations(volumeData)
  Object.entries(dayOfWeekVolumes).forEach(([key, value]) => {
    correlations[key] = value
  })

  // Sender correlations
  if (senderStats.length > 0) {
    const senderCorrelations = calculateSenderCorrelations(senderStats)
    Object.entries(senderCorrelations).forEach(([key, value]) => {
      correlations[key] = value
    })
  }

  return correlations
}

/**
 * Calculate Pearson correlation coefficient
 */
export function calculatePearsonCorrelation(x: number[], y: number[]): number {
  if (x.length !== y.length || x.length < 2) {
    return 0
  }

  const n = x.length
  const sumX = x.reduce((sum, val) => sum + val, 0)
  const sumY = y.reduce((sum, val) => sum + val, 0)
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0)
  const sumXX = x.reduce((sum, val) => sum + val * val, 0)
  const sumYY = y.reduce((sum, val) => sum + val * val, 0)

  const numerator = n * sumXY - sumX * sumY
  const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY))

  return denominator === 0 ? 0 : numerator / denominator
}

/**
 * Calculate Spearman rank correlation coefficient
 */
export function calculateSpearmanCorrelation(x: number[], y: number[]): number {
  if (x.length !== y.length || x.length < 2) {
    return 0
  }

  // Convert to ranks
  const xRanks = getRanks(x)
  const yRanks = getRanks(y)

  // Calculate Pearson correlation on ranks
  return calculatePearsonCorrelation(xRanks, yRanks)
}

/**
 * Get ranks for Spearman correlation
 */
function getRanks(values: number[]): number[] {
  const sorted = values.map((val, index) => ({ val, index }))
    .sort((a, b) => a.val - b.val)
  
  const ranks = new Array(values.length)
  
  sorted.forEach((item, rank) => {
    ranks[item.index] = rank + 1
  })
  
  return ranks
}

/**
 * Calculate day-of-week correlations
 */
function calculateDayOfWeekCorrelations(volumeData: VolumeData[]): Record<string, number> {
  const correlations: Record<string, number> = {}
  
  // Group by day of week
  const dayGroups: Record<number, number[]> = {}
  
  volumeData.forEach(data => {
    const dayOfWeek = data.date.getDay()
    const total = data.received + data.sent
    
    if (!dayGroups[dayOfWeek]) {
      dayGroups[dayOfWeek] = []
    }
    dayGroups[dayOfWeek].push(total)
  })

  // Calculate correlations between different days
  const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
  
  for (let i = 0; i < 7; i++) {
    for (let j = i + 1; j < 7; j++) {
      const day1Values = dayGroups[i] || []
      const day2Values = dayGroups[j] || []
      
      if (day1Values.length > 1 && day2Values.length > 1) {
        // Pad arrays to same length for correlation
        const maxLength = Math.max(day1Values.length, day2Values.length)
        const paddedDay1 = [...day1Values, ...Array(maxLength - day1Values.length).fill(0)]
        const paddedDay2 = [...day2Values, ...Array(maxLength - day2Values.length).fill(0)]
        
        const correlation = calculatePearsonCorrelation(
          paddedDay1.slice(0, Math.min(day1Values.length, day2Values.length)),
          paddedDay2.slice(0, Math.min(day1Values.length, day2Values.length))
        )
        
        correlations[`${dayNames[i]}Vs${dayNames[j]}`] = correlation
      }
    }
  }

  return correlations
}

/**
 * Calculate sender-related correlations
 */
function calculateSenderCorrelations(senderStats: SenderStats[]): Record<string, number> {
  const correlations: Record<string, number> = {}
  
  if (senderStats.length < 3) return correlations

  // Extract sender metrics
  const messageCounts = senderStats.map(s => s.messageCount)
  const threadCounts = senderStats.map(s => s.threadCount)
  const unreadCounts = senderStats.map(s => s.unreadCount)
  const responseTimes = senderStats
    .filter(s => s.avgResponseTime !== undefined)
    .map(s => s.avgResponseTime!)

  // Calculate correlations between sender metrics
  correlations.messageVsThread = calculatePearsonCorrelation(messageCounts, threadCounts)
  correlations.messageVsUnread = calculatePearsonCorrelation(messageCounts, unreadCounts)
  correlations.threadVsUnread = calculatePearsonCorrelation(threadCounts, unreadCounts)
  
  if (responseTimes.length > 2) {
    const responseMessageCounts = senderStats
      .filter(s => s.avgResponseTime !== undefined)
      .map(s => s.messageCount)
    
    correlations.responseTimeVsMessages = calculatePearsonCorrelation(responseTimes, responseMessageCounts)
  }

  // Domain-based correlations
  const domainStats = calculateDomainStats(senderStats)
  correlations.domainVsVolume = domainStats.domainVolumeCorrelation
  correlations.domainVsResponseTime = domainStats.domainResponseTimeCorrelation

  return correlations
}

/**
 * Calculate domain-based statistics
 */
function calculateDomainStats(senderStats: SenderStats[]): {
  domainVolumeCorrelation: number
  domainResponseTimeCorrelation: number
} {
  // Group by domain
  const domainGroups: Record<string, SenderStats[]> = {}
  
  senderStats.forEach(sender => {
    if (!domainGroups[sender.domain]) {
      domainGroups[sender.domain] = []
    }
    domainGroups[sender.domain].push(sender)
  })

  // Calculate domain averages
  const domainAverages = Object.entries(domainGroups).map(([domain, senders]) => ({
    domain,
    avgMessages: senders.reduce((sum, s) => sum + s.messageCount, 0) / senders.length,
    avgResponseTime: senders
      .filter(s => s.avgResponseTime !== undefined)
      .reduce((sum, s) => sum + s.avgResponseTime!, 0) / 
      senders.filter(s => s.avgResponseTime !== undefined).length,
    senderCount: senders.length
  }))

  if (domainAverages.length < 3) {
    return {
      domainVolumeCorrelation: 0,
      domainResponseTimeCorrelation: 0
    }
  }

  // Calculate correlations
  const volumes = domainAverages.map(d => d.avgMessages)
  const senderCounts = domainAverages.map(d => d.senderCount)
  const responseTimes = domainAverages
    .filter(d => !isNaN(d.avgResponseTime))
    .map(d => d.avgResponseTime)

  const domainVolumeCorrelation = calculatePearsonCorrelation(volumes, senderCounts)
  
  let domainResponseTimeCorrelation = 0
  if (responseTimes.length > 2) {
    const responseVolumes = domainAverages
      .filter(d => !isNaN(d.avgResponseTime))
      .map(d => d.avgMessages)
    domainResponseTimeCorrelation = calculatePearsonCorrelation(responseTimes, responseVolumes)
  }

  return {
    domainVolumeCorrelation,
    domainResponseTimeCorrelation
  }
}

/**
 * Perform comprehensive correlation analysis
 */
export function performCorrelationAnalysis(
  emailStats: EmailStats,
  senderStats: SenderStats[]
): CorrelationResult[] {
  const results: CorrelationResult[] = []
  
  if (!emailStats.volumeData || emailStats.volumeData.length < 3) {
    return results
  }

  const volumeData = emailStats.volumeData
  
  // Define variable pairs to analyze
  const variablePairs: Array<{
    name1: string
    name2: string
    data1: number[]
    data2: number[]
  }> = []

  // Volume metrics
  const received = volumeData.map(d => d.received)
  const sent = volumeData.map(d => d.sent)
  const archived = volumeData.map(d => d.archived)
  const deleted = volumeData.map(d => d.deleted)

  variablePairs.push(
    { name1: 'Received', name2: 'Sent', data1: received, data2: sent },
    { name1: 'Received', name2: 'Archived', data1: received, data2: archived },
    { name1: 'Received', name2: 'Deleted', data1: received, data2: deleted },
    { name1: 'Sent', name2: 'Archived', data1: sent, data2: archived }
  )

  // Category correlations
  const categories = Object.keys(volumeData[0]?.categories || {})
  categories.forEach(category => {
    const categoryValues = volumeData.map(d => d.categories[category] || 0)
    variablePairs.push(
      { name1: category, name2: 'Received', data1: categoryValues, data2: received },
      { name1: category, name2: 'Sent', data1: categoryValues, data2: sent }
    )
  })

  // Calculate correlations for each pair
  variablePairs.forEach(pair => {
    const coefficient = calculatePearsonCorrelation(pair.data1, pair.data2)
    const strength = getCorrelationStrength(Math.abs(coefficient))
    const significance = calculateSignificance(coefficient, pair.data1.length)
    const pValue = calculatePValue(coefficient, pair.data1.length)
    
    results.push({
      variables: [pair.name1, pair.name2],
      coefficient,
      strength,
      significance,
      pValue,
      interpretation: generateCorrelationInterpretation(pair.name1, pair.name2, coefficient, strength)
    })
  })

  // Sort by absolute correlation coefficient (strongest correlations first)
  results.sort((a, b) => Math.abs(b.coefficient) - Math.abs(a.coefficient))

  return results
}

/**
 * Determine correlation strength category
 */
function getCorrelationStrength(absCoefficient: number): 'very_weak' | 'weak' | 'moderate' | 'strong' | 'very_strong' {
  if (absCoefficient < 0.2) return 'very_weak'
  if (absCoefficient < 0.4) return 'weak'
  if (absCoefficient < 0.6) return 'moderate'
  if (absCoefficient < 0.8) return 'strong'
  return 'very_strong'
}

/**
 * Calculate statistical significance (simplified)
 */
function calculateSignificance(coefficient: number, sampleSize: number): number {
  if (sampleSize < 3) return 0
  
  const t = coefficient * Math.sqrt((sampleSize - 2) / (1 - coefficient * coefficient))
  const df = sampleSize - 2
  
  // Simplified significance calculation
  return Math.abs(t) > 2.0 ? 0.95 : Math.abs(t) > 1.5 ? 0.8 : 0.5
}

/**
 * Calculate p-value (simplified approximation)
 */
function calculatePValue(coefficient: number, sampleSize: number): number {
  if (sampleSize < 3) return 1
  
  const t = Math.abs(coefficient) * Math.sqrt((sampleSize - 2) / (1 - coefficient * coefficient))
  
  // Very simplified p-value approximation
  if (t > 2.5) return 0.01
  if (t > 2.0) return 0.05
  if (t > 1.5) return 0.1
  return 0.2
}

/**
 * Generate human-readable interpretation
 */
function generateCorrelationInterpretation(
  var1: string,
  var2: string,
  coefficient: number,
  strength: string
): string {
  const direction = coefficient > 0 ? 'positive' : 'negative'
  const absCoeff = Math.abs(coefficient)
  
  let strengthDesc = ''
  switch (strength) {
    case 'very_strong':
      strengthDesc = 'very strong'
      break
    case 'strong':
      strengthDesc = 'strong'
      break
    case 'moderate':
      strengthDesc = 'moderate'
      break
    case 'weak':
      strengthDesc = 'weak'
      break
    case 'very_weak':
      strengthDesc = 'very weak'
      break
  }

  if (absCoeff < 0.1) {
    return `No meaningful correlation between ${var1} and ${var2}.`
  }

  const relationshipDesc = coefficient > 0 
    ? `As ${var1} increases, ${var2} tends to increase.`
    : `As ${var1} increases, ${var2} tends to decrease.`

  return `${strengthDesc} ${direction} correlation (${coefficient.toFixed(3)}). ${relationshipDesc}`
}

/**
 * Calculate time-lag correlations
 */
export function calculateTimeLagCorrelations(
  data1: number[],
  data2: number[],
  maxLag: number = 7
): Array<{ lag: number; correlation: number }> {
  const results: Array<{ lag: number; correlation: number }> = []
  
  for (let lag = -maxLag; lag <= maxLag; lag++) {
    let shiftedData1: number[]
    let shiftedData2: number[]
    
    if (lag > 0) {
      // data1 leads data2 by 'lag' periods
      shiftedData1 = data1.slice(0, -lag)
      shiftedData2 = data2.slice(lag)
    } else if (lag < 0) {
      // data2 leads data1 by abs(lag) periods
      shiftedData1 = data1.slice(-lag)
      shiftedData2 = data2.slice(0, lag)
    } else {
      // No lag
      shiftedData1 = data1
      shiftedData2 = data2
    }
    
    if (shiftedData1.length > 2 && shiftedData2.length > 2) {
      const correlation = calculatePearsonCorrelation(shiftedData1, shiftedData2)
      results.push({ lag, correlation })
    }
  }
  
  return results.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation))
}

/**
 * Calculate partial correlations (simplified)
 */
export function calculatePartialCorrelation(
  x: number[],
  y: number[],
  z: number[]
): number {
  if (x.length !== y.length || y.length !== z.length || x.length < 4) {
    return 0
  }

  const rxy = calculatePearsonCorrelation(x, y)
  const rxz = calculatePearsonCorrelation(x, z)
  const ryz = calculatePearsonCorrelation(y, z)
  
  const numerator = rxy - rxz * ryz
  const denominator = Math.sqrt((1 - rxz * rxz) * (1 - ryz * ryz))
  
  return denominator === 0 ? 0 : numerator / denominator
}

/**
 * Generate correlation insights and recommendations
 */
export function generateCorrelationInsights(results: CorrelationResult[]): {
  keyFindings: string[]
  strongCorrelations: CorrelationResult[]
  recommendations: string[]
  warnings: string[]
} {
  const keyFindings: string[] = []
  const strongCorrelations = results.filter(r => 
    r.strength === 'strong' || r.strength === 'very_strong'
  )
  const recommendations: string[] = []
  const warnings: string[] = []

  // Analyze strong correlations
  strongCorrelations.forEach(result => {
    keyFindings.push(
      `Strong ${result.coefficient > 0 ? 'positive' : 'negative'} correlation between ${result.variables.join(' and ')}`
    )
  })

  // Generate recommendations based on findings
  const receivedSentCorr = results.find(r => 
    r.variables.includes('Received') && r.variables.includes('Sent')
  )
  
  if (receivedSentCorr && Math.abs(receivedSentCorr.coefficient) > 0.7) {
    recommendations.push('High correlation between received and sent emails suggests consistent communication patterns')
  }

  const categoryCorrelations = results.filter(r => 
    r.variables.some(v => !['Received', 'Sent', 'Archived', 'Deleted'].includes(v))
  )
  
  if (categoryCorrelations.some(c => Math.abs(c.coefficient) > 0.6)) {
    recommendations.push('Review email categorization rules - some categories show strong correlations')
  }

  // Add warnings for unexpected correlations
  const unexpectedHighCorr = results.filter(r => 
    Math.abs(r.coefficient) > 0.8 && r.significance < 0.8
  )
  
  if (unexpectedHighCorr.length > 0) {
    warnings.push('Some high correlations may not be statistically significant')
  }

  if (keyFindings.length === 0) {
    keyFindings.push('No strong correlations detected in the current data')
  }

  if (recommendations.length === 0) {
    recommendations.push('Continue monitoring email patterns for emerging correlations')
  }

  return {
    keyFindings,
    strongCorrelations,
    recommendations,
    warnings
  }
}