import { EmailStats, SenderStats, VolumeData, CategoryStats, ChartData, ChartDataset } from '@/types/analytics'
import { format } from 'date-fns'

/**
 * Transform email volume data for chart visualization
 */
export function transformVolumeDataForChart(
  volumeData: VolumeData[],
  options: {
    chartType: 'line' | 'bar' | 'area'
    metrics: ('received' | 'sent' | 'total')[]
    maxDataPoints?: number
  }
): ChartData {
  if (!volumeData.length) {
    return { labels: [], datasets: [] }
  }

  const sortedData = [...volumeData].sort((a, b) => a.date.getTime() - b.date.getTime())
  
  let processedData = sortedData
  if (options.maxDataPoints && processedData.length > options.maxDataPoints) {
    const step = Math.ceil(processedData.length / options.maxDataPoints)
    processedData = processedData.filter((_, index) => index % step === 0)
  }

  const labels = processedData.map(data => format(data.date, 'MMM d'))

  const datasets: ChartDataset[] = []
  const colors = {
    received: '#3b82f6',
    sent: '#10b981',
    total: '#8b5cf6'
  }

  options.metrics.forEach(metric => {
    const data = processedData.map(point => {
      switch (metric) {
        case 'received':
          return point.received
        case 'sent':
          return point.sent
        case 'total':
          return point.received + point.sent
        default:
          return 0
      }
    })

    datasets.push({
      label: metric.charAt(0).toUpperCase() + metric.slice(1),
      data,
      borderColor: colors[metric],
      backgroundColor: options.chartType === 'area' ? colors[metric] + '20' : colors[metric],
      fill: options.chartType === 'area',
      tension: 0.4,
      type: options.chartType === 'bar' ? 'bar' : 'line'
    })
  })

  return { labels, datasets }
}

/**
 * Transform category data for pie/doughnut charts
 */
export function transformCategoryDataForChart(
  categories: CategoryStats[],
  options: {
    chartType: 'pie' | 'doughnut'
    metric: 'count' | 'percentage'
    maxCategories?: number
  }
): ChartData {
  if (!categories.length) {
    return { labels: [], datasets: [] }
  }

  let processedCategories = [...categories].sort((a, b) => b.count - a.count)
  
  if (options.maxCategories && processedCategories.length > options.maxCategories) {
    const topCategories = processedCategories.slice(0, options.maxCategories - 1)
    const others = processedCategories.slice(options.maxCategories - 1)
    const othersSum = others.reduce((sum, cat) => sum + cat.count, 0)
    const othersPercentage = others.reduce((sum, cat) => sum + cat.percentage, 0)
    
    topCategories.push({
      category: 'Others',
      count: othersSum,
      percentage: othersPercentage,
      unreadCount: others.reduce((sum, cat) => sum + cat.unreadCount, 0),
      trend: 'stable',
      trendPercentage: 0
    })
    
    processedCategories = topCategories
  }

  const labels = processedCategories.map(cat => cat.category)
  const data = processedCategories.map(cat => 
    options.metric === 'percentage' ? cat.percentage : cat.count
  )

  const colors = generateColors(processedCategories.length)

  const datasets: ChartDataset[] = [{
    label: options.metric === 'percentage' ? 'Percentage' : 'Count',
    data,
    backgroundColor: colors,
    borderColor: colors.map(color => color.replace('0.8', '1')),
    borderWidth: 2
  }]

  return { labels, datasets }
}

/**
 * Transform data for export (normalize for different formats)
 */
export function transformDataForExport(
  data: any,
  format: 'csv' | 'json' | 'xlsx'
): any {
  if (format === 'csv') {
    return flattenForCSV(data)
  } else if (format === 'json') {
    return cleanForJSON(data)
  } else {
    return data
  }
}

function flattenForCSV(obj: any, prefix = ''): any {
  const flattened: any = {}
  
  Object.keys(obj).forEach(key => {
    const value = obj[key]
    const newKey = prefix ? `${prefix}_${key}` : key
    
    if (value !== null && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
      Object.assign(flattened, flattenForCSV(value, newKey))
    } else if (Array.isArray(value)) {
      flattened[newKey] = value.join(';')
    } else {
      flattened[newKey] = value
    }
  })
  
  return flattened
}

function cleanForJSON(obj: any): any {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (Array.isArray(obj)) {
    return obj.map(cleanForJSON)
  }
  
  const cleaned: any = {}
  Object.keys(obj).forEach(key => {
    const value = obj[key]
    if (typeof value !== 'function') {
      cleaned[key] = cleanForJSON(value)
    }
  })
  
  return cleaned
}

function generateColors(count: number): string[] {
  const baseColors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
    '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
  ]

  const colors: string[] = []
  for (let i = 0; i < count; i++) {
    if (i < baseColors.length) {
      colors.push(baseColors[i] + '80')
    } else {
      const hue = (i * 137.508) % 360
      colors.push(`hsla(${hue}, 70%, 60%, 0.8)`)
    }
  }

  return colors
}