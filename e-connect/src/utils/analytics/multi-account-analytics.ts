import type { 
  EmailAccount,
  AccountAnalytics,
  UnifiedThread 
} from '../../types/multi-account'
import type { Thread } from '../../types/email'

export interface UnifiedAnalytics {
  // Overview metrics
  overview: {
    totalAccounts: number
    activeAccounts: number
    totalEmails: number
    totalUnread: number
    totalStorage: number
    avgResponseTime: number
    overallResponseRate: number
  }

  // Account breakdown
  accountBreakdown: Array<{
    accountId: string
    accountName: string
    accountColor: string
    provider: string
    metrics: AccountAnalytics
    contribution: {
      emailsPercentage: number
      storagePercentage: number
      activityPercentage: number
    }
  }>

  // Aggregated metrics
  aggregatedMetrics: {
    totalSent: number
    totalReceived: number
    totalReplied: number
    totalForwarded: number
    totalDeleted: number
    totalArchived: number
  }

  // Time-based aggregation
  activityAggregation: {
    byHour: Record<string, number>
    byDay: Record<string, number>
    byWeek: Record<string, number>
    byMonth: Record<string, number>
  }

  // Cross-account insights
  insights: {
    mostActiveAccount: string
    mostProductiveHour: string
    commonSenders: Array<{
      email: string
      name?: string
      accountsFound: string[]
      totalCount: number
    }>
    categoryDistribution: Record<string, {
      total: number
      byAccount: Record<string, number>
    }>
    syncPerformance: {
      bestPerformingAccount: string
      worstPerformingAccount: string
      avgSyncTime: number
      totalSyncErrors: number
    }
  }

  // Comparative analysis
  comparative: {
    emailVolumeComparison: Array<{
      accountId: string
      accountName: string
      volume: number
      trend: 'increasing' | 'decreasing' | 'stable'
      percentageChange: number
    }>
    responseTimeComparison: Array<{
      accountId: string
      accountName: string
      avgResponseTime: number
      ranking: number
    }>
    storageUtilization: Array<{
      accountId: string
      accountName: string
      storageUsed: number
      storageEfficiency: number
    }>
  }

  // Trends and predictions
  trends: {
    emailVolumeTrend: {
      direction: 'up' | 'down' | 'stable'
      percentage: number
      prediction: number
    }
    responseTimeTrend: {
      direction: 'improving' | 'declining' | 'stable'
      percentage: number
    }
    storageGrowthTrend: {
      dailyGrowth: number
      projectedFullDate?: Date
    }
  }

  // Period information
  period: {
    start: Date
    end: Date
    duration: number // days
  }
}

export interface AnalyticsAggregator {
  // Main aggregation methods
  aggregateAccountAnalytics(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>,
    period?: { start: Date; end: Date }
  ): UnifiedAnalytics

  // Specific aggregations
  aggregateEmailMetrics(analyticsData: Record<string, AccountAnalytics>): any
  aggregateActivityData(analyticsData: Record<string, AccountAnalytics>): any
  aggregateTopContacts(analyticsData: Record<string, AccountAnalytics>): any
  aggregateCategoryData(analyticsData: Record<string, AccountAnalytics>): any
  aggregateSyncPerformance(accounts: EmailAccount[]): any

  // Cross-account insights
  generateInsights(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>
  ): UnifiedAnalytics['insights']

  // Comparative analysis
  generateComparativeAnalysis(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>
  ): UnifiedAnalytics['comparative']

  // Trend analysis
  generateTrendAnalysis(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>,
    historicalData?: Record<string, AccountAnalytics[]>
  ): UnifiedAnalytics['trends']
}

class MultiAccountAnalyticsAggregator implements AnalyticsAggregator {
  aggregateAccountAnalytics(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>,
    period?: { start: Date; end: Date }
  ): UnifiedAnalytics {
    const activeAccounts = accounts.filter(acc => acc.status === 'active')
    
    // Calculate overview metrics
    const overview = this.calculateOverviewMetrics(accounts, analyticsData)
    
    // Create account breakdown
    const accountBreakdown = this.createAccountBreakdown(accounts, analyticsData, overview)
    
    // Aggregate metrics
    const aggregatedMetrics = this.aggregateEmailMetrics(analyticsData)
    
    // Aggregate activity data
    const activityAggregation = this.aggregateActivityData(analyticsData)
    
    // Generate insights
    const insights = this.generateInsights(accounts, analyticsData)
    
    // Generate comparative analysis
    const comparative = this.generateComparativeAnalysis(accounts, analyticsData)
    
    // Generate trends
    const trends = this.generateTrendAnalysis(accounts, analyticsData)
    
    // Determine period
    const periods = Object.values(analyticsData).map(data => data.period)
    const defaultPeriod = {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      end: new Date()
    }
    const actualPeriod = period || (periods.length > 0 ? {
      start: new Date(Math.min(...periods.map(p => p.start.getTime()))),
      end: new Date(Math.max(...periods.map(p => p.end.getTime())))
    } : defaultPeriod)

    return {
      overview,
      accountBreakdown,
      aggregatedMetrics,
      activityAggregation,
      insights,
      comparative,
      trends,
      period: {
        ...actualPeriod,
        duration: Math.ceil((actualPeriod.end.getTime() - actualPeriod.start.getTime()) / (24 * 60 * 60 * 1000))
      }
    }
  }

  private calculateOverviewMetrics(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>
  ) {
    const activeAccounts = accounts.filter(acc => acc.status === 'active')
    
    const totalEmails = accounts.reduce((sum, acc) => sum + acc.stats.totalEmails, 0)
    const totalUnread = accounts.reduce((sum, acc) => sum + acc.stats.unreadEmails, 0)
    const totalStorage = accounts.reduce((sum, acc) => sum + acc.stats.storageUsed, 0)
    
    // Calculate weighted average response time
    const responseTimes = Object.values(analyticsData)
      .map(data => ({ time: data.averageResponseTime, weight: data.emailMetrics.received }))
      .filter(item => item.weight > 0)
    
    const avgResponseTime = responseTimes.length > 0
      ? responseTimes.reduce((sum, item) => sum + item.time * item.weight, 0) /
        responseTimes.reduce((sum, item) => sum + item.weight, 0)
      : 0

    // Calculate overall response rate
    const totalReceived = Object.values(analyticsData)
      .reduce((sum, data) => sum + data.emailMetrics.received, 0)
    const totalReplied = Object.values(analyticsData)
      .reduce((sum, data) => sum + data.emailMetrics.replied, 0)
    
    const overallResponseRate = totalReceived > 0 ? totalReplied / totalReceived : 0

    return {
      totalAccounts: accounts.length,
      activeAccounts: activeAccounts.length,
      totalEmails,
      totalUnread,
      totalStorage,
      avgResponseTime,
      overallResponseRate
    }
  }

  private createAccountBreakdown(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>,
    overview: any
  ) {
    return accounts.map(account => {
      const analytics = analyticsData[account.id]
      
      if (!analytics) {
        return {
          accountId: account.id,
          accountName: account.name,
          accountColor: account.color,
          provider: account.provider,
          metrics: this.createEmptyAnalytics(account.id),
          contribution: {
            emailsPercentage: 0,
            storagePercentage: 0,
            activityPercentage: 0
          }
        }
      }

      const emailsPercentage = overview.totalEmails > 0 
        ? (account.stats.totalEmails / overview.totalEmails) * 100 
        : 0
      
      const storagePercentage = overview.totalStorage > 0 
        ? (account.stats.storageUsed / overview.totalStorage) * 100 
        : 0
      
      const totalActivity = analytics.emailMetrics.sent + analytics.emailMetrics.received
      const overallActivity = Object.values(analyticsData)
        .reduce((sum, data) => sum + data.emailMetrics.sent + data.emailMetrics.received, 0)
      
      const activityPercentage = overallActivity > 0 
        ? (totalActivity / overallActivity) * 100 
        : 0

      return {
        accountId: account.id,
        accountName: account.name,
        accountColor: account.color,
        provider: account.provider,
        metrics: analytics,
        contribution: {
          emailsPercentage: Math.round(emailsPercentage * 100) / 100,
          storagePercentage: Math.round(storagePercentage * 100) / 100,
          activityPercentage: Math.round(activityPercentage * 100) / 100
        }
      }
    })
  }

  aggregateEmailMetrics(analyticsData: Record<string, AccountAnalytics>) {
    return Object.values(analyticsData).reduce(
      (totals, data) => ({
        totalSent: totals.totalSent + data.emailMetrics.sent,
        totalReceived: totals.totalReceived + data.emailMetrics.received,
        totalReplied: totals.totalReplied + data.emailMetrics.replied,
        totalForwarded: totals.totalForwarded + data.emailMetrics.forwarded,
        totalDeleted: totals.totalDeleted + data.emailMetrics.deleted,
        totalArchived: totals.totalArchived + data.emailMetrics.archived
      }),
      {
        totalSent: 0,
        totalReceived: 0,
        totalReplied: 0,
        totalForwarded: 0,
        totalDeleted: 0,
        totalArchived: 0
      }
    )
  }

  aggregateActivityData(analyticsData: Record<string, AccountAnalytics>) {
    const aggregated = {
      byHour: {} as Record<string, number>,
      byDay: {} as Record<string, number>,
      byWeek: {} as Record<string, number>,
      byMonth: {} as Record<string, number>
    }

    Object.values(analyticsData).forEach(data => {
      // Aggregate by hour
      Object.entries(data.activityByHour).forEach(([hour, count]) => {
        aggregated.byHour[hour] = (aggregated.byHour[hour] || 0) + count
      })

      // Aggregate by day
      Object.entries(data.activityByDay).forEach(([day, count]) => {
        aggregated.byDay[day] = (aggregated.byDay[day] || 0) + count
      })

      // Aggregate by week
      Object.entries(data.activityByWeek).forEach(([week, count]) => {
        aggregated.byWeek[week] = (aggregated.byWeek[week] || 0) + count
      })
    })

    // Generate monthly data from weekly data
    Object.entries(aggregated.byWeek).forEach(([week, count]) => {
      const month = `Month ${Math.ceil(parseInt(week.split(' ')[1]) / 4)}`
      aggregated.byMonth[month] = (aggregated.byMonth[month] || 0) + count
    })

    return aggregated
  }

  aggregateTopContacts(analyticsData: Record<string, AccountAnalytics>) {
    const senderMap = new Map<string, {
      email: string
      name?: string
      accountsFound: string[]
      totalCount: number
    }>()

    Object.entries(analyticsData).forEach(([accountId, data]) => {
      data.topSenders.forEach(sender => {
        const key = sender.email.toLowerCase()
        const existing = senderMap.get(key)
        
        if (existing) {
          existing.accountsFound.push(accountId)
          existing.totalCount += sender.count
          if (sender.name && !existing.name) {
            existing.name = sender.name
          }
        } else {
          senderMap.set(key, {
            email: sender.email,
            name: sender.name,
            accountsFound: [accountId],
            totalCount: sender.count
          })
        }
      })
    })

    return Array.from(senderMap.values())
      .sort((a, b) => b.totalCount - a.totalCount)
      .slice(0, 10)
  }

  aggregateCategoryData(analyticsData: Record<string, AccountAnalytics>) {
    const categoryMap = new Map<string, {
      total: number
      byAccount: Record<string, number>
    }>()

    Object.entries(analyticsData).forEach(([accountId, data]) => {
      Object.entries(data.categories).forEach(([category, count]) => {
        const existing = categoryMap.get(category)
        
        if (existing) {
          existing.total += count
          existing.byAccount[accountId] = count
        } else {
          categoryMap.set(category, {
            total: count,
            byAccount: { [accountId]: count }
          })
        }
      })
    })

    return Object.fromEntries(categoryMap.entries())
  }

  aggregateSyncPerformance(accounts: EmailAccount[]) {
    const syncData = accounts.map(account => ({
      accountId: account.id,
      accountName: account.name,
      avgSyncTime: account.stats.averageSyncDuration,
      syncErrors: account.stats.syncErrors,
      uptime: account.stats.consecutiveFailures === 0 ? 100 : Math.max(0, 100 - (account.stats.consecutiveFailures * 10))
    }))

    const bestPerforming = syncData.reduce((best, current) => 
      current.avgSyncTime < best.avgSyncTime ? current : best
    )

    const worstPerforming = syncData.reduce((worst, current) => 
      current.avgSyncTime > worst.avgSyncTime ? current : worst
    )

    const avgSyncTime = syncData.reduce((sum, data) => sum + data.avgSyncTime, 0) / syncData.length
    const totalSyncErrors = syncData.reduce((sum, data) => sum + data.syncErrors, 0)

    return {
      bestPerformingAccount: bestPerforming.accountId,
      worstPerformingAccount: worstPerforming.accountId,
      avgSyncTime: Math.round(avgSyncTime),
      totalSyncErrors
    }
  }

  generateInsights(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>
  ): UnifiedAnalytics['insights'] {
    // Find most active account
    const activityScores = accounts.map(account => {
      const analytics = analyticsData[account.id]
      if (!analytics) return { accountId: account.id, score: 0 }
      
      const score = analytics.emailMetrics.sent + analytics.emailMetrics.received
      return { accountId: account.id, score }
    })
    
    const mostActiveAccount = activityScores.reduce((max, current) => 
      current.score > max.score ? current : max
    ).accountId

    // Find most productive hour
    const hourlyActivity = this.aggregateActivityData(analyticsData).byHour
    const mostProductiveHour = Object.entries(hourlyActivity)
      .reduce((max, [hour, count]) => count > max.count ? { hour, count } : max, { hour: '9', count: 0 })
      .hour

    // Find common senders
    const commonSenders = this.aggregateTopContacts(analyticsData)
      .filter(sender => sender.accountsFound.length > 1)
      .slice(0, 5)

    // Aggregate category distribution
    const categoryDistribution = this.aggregateCategoryData(analyticsData)

    // Calculate sync performance
    const syncPerformance = this.aggregateSyncPerformance(accounts)

    return {
      mostActiveAccount,
      mostProductiveHour,
      commonSenders,
      categoryDistribution,
      syncPerformance
    }
  }

  generateComparativeAnalysis(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>
  ): UnifiedAnalytics['comparative'] {
    // Email volume comparison
    const emailVolumeComparison = accounts.map(account => {
      const analytics = analyticsData[account.id]
      const volume = analytics ? analytics.emailMetrics.sent + analytics.emailMetrics.received : 0
      
      // Mock trend calculation (would need historical data in real implementation)
      const trend: 'increasing' | 'decreasing' | 'stable' = Math.random() > 0.6 ? 'increasing' : Math.random() > 0.3 ? 'stable' : 'decreasing'
      const percentageChange = (Math.random() - 0.5) * 40 // -20% to +20%

      return {
        accountId: account.id,
        accountName: account.name,
        volume,
        trend,
        percentageChange: Math.round(percentageChange * 100) / 100
      }
    }).sort((a, b) => b.volume - a.volume)

    // Response time comparison
    const responseTimeComparison = accounts
      .map(account => {
        const analytics = analyticsData[account.id]
        return {
          accountId: account.id,
          accountName: account.name,
          avgResponseTime: analytics ? analytics.averageResponseTime : 0
        }
      })
      .sort((a, b) => a.avgResponseTime - b.avgResponseTime)
      .map((item, index) => ({ ...item, ranking: index + 1 }))

    // Storage utilization
    const storageUtilization = accounts.map(account => {
      const storageUsed = account.stats.storageUsed
      const totalEmails = account.stats.totalEmails
      const storageEfficiency = totalEmails > 0 ? storageUsed / totalEmails : 0

      return {
        accountId: account.id,
        accountName: account.name,
        storageUsed,
        storageEfficiency: Math.round(storageEfficiency)
      }
    }).sort((a, b) => b.storageUsed - a.storageUsed)

    return {
      emailVolumeComparison,
      responseTimeComparison,
      storageUtilization
    }
  }

  generateTrendAnalysis(
    accounts: EmailAccount[],
    analyticsData: Record<string, AccountAnalytics>,
    historicalData?: Record<string, AccountAnalytics[]>
  ): UnifiedAnalytics['trends'] {
    // In a real implementation, this would analyze historical data
    // For now, we'll generate mock trends
    
    const emailVolumeTrend = {
      direction: Math.random() > 0.5 ? 'up' : Math.random() > 0.5 ? 'stable' : 'down',
      percentage: Math.round((Math.random() - 0.5) * 30 * 100) / 100,
      prediction: Math.round(Math.random() * 1000 + 500)
    } as const

    const responseTimeTrend = {
      direction: Math.random() > 0.5 ? 'improving' : Math.random() > 0.5 ? 'stable' : 'declining',
      percentage: Math.round((Math.random()) * 15 * 100) / 100
    } as const

    const totalStorage = accounts.reduce((sum, acc) => sum + acc.stats.storageUsed, 0)
    const dailyGrowth = Math.random() * ******** // bytes per day
    const projectedFullDate = totalStorage > 0 && dailyGrowth > 0 
      ? new Date(Date.now() + ((********00 - totalStorage) / dailyGrowth) * 24 * 60 * 60 * 1000)
      : undefined

    const storageGrowthTrend = {
      dailyGrowth: Math.round(dailyGrowth),
      projectedFullDate
    }

    return {
      emailVolumeTrend,
      responseTimeTrend,
      storageGrowthTrend
    }
  }

  private createEmptyAnalytics(accountId: string): AccountAnalytics {
    return {
      accountId,
      period: {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        end: new Date()
      },
      emailMetrics: {
        sent: 0,
        received: 0,
        replied: 0,
        forwarded: 0,
        deleted: 0,
        archived: 0
      },
      activityByHour: {},
      activityByDay: {},
      activityByWeek: {},
      topSenders: [],
      topRecipients: [],
      categories: {},
      averageResponseTime: 0,
      responseRate: 0,
      syncPerformance: {
        averageSyncTime: 0,
        syncSuccess: 0,
        syncFailures: 0,
        uptime: 0
      }
    }
  }
}

// Factory function
export function createAnalyticsAggregator(): AnalyticsAggregator {
  return new MultiAccountAnalyticsAggregator()
}

// Utility functions for analytics processing
export const analyticsUtils = {
  // Format numbers for display
  formatNumber: (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  },

  // Format storage sizes
  formatStorage: (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`
  },

  // Format time durations
  formatDuration: (milliseconds: number): string => {
    const seconds = Math.floor(milliseconds / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    }
    if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    }
    return `${seconds}s`
  },

  // Calculate percentage change
  calculatePercentageChange: (current: number, previous: number): number => {
    if (previous === 0) return current > 0 ? 100 : 0
    return ((current - previous) / previous) * 100
  },

  // Generate color palette for accounts
  generateAccountColors: (count: number): string[] => {
    const baseColors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B', 
      '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'
    ]
    
    if (count <= baseColors.length) {
      return baseColors.slice(0, count)
    }
    
    // Generate additional colors if needed
    const colors = [...baseColors]
    for (let i = baseColors.length; i < count; i++) {
      const hue = (i * 137.508) % 360 // Golden angle approximation
      colors.push(`hsl(${hue}, 70%, 50%)`)
    }
    
    return colors
  }
}