import { VolumeData, EmailStats, SenderStats } from '@/types/analytics'

/**
 * Calculate trends from volume data
 */
export function calculateTrends(volumeData: VolumeData[]): Record<string, number> {
  if (volumeData.length < 2) {
    return {
      receivedTrend: 0,
      sentTrend: 0,
      totalTrend: 0,
      categoryTrends: 0
    }
  }

  // Sort by date to ensure proper order
  const sortedData = [...volumeData].sort((a, b) => a.date.getTime() - b.date.getTime())
  
  // Calculate linear trends using least squares regression
  const receivedTrend = calculateLinearTrend(sortedData.map(d => d.received))
  const sentTrend = calculateLinearTrend(sortedData.map(d => d.sent))
  const totalTrend = calculateLinearTrend(sortedData.map(d => d.received + d.sent))

  // Calculate category trends
  const categories = Object.keys(sortedData[0]?.categories || {})
  const categoryTrends = categories.reduce((acc, category) => {
    const values = sortedData.map(d => d.categories[category] || 0)
    acc[`${category}Trend`] = calculateLinearTrend(values)
    return acc
  }, {} as Record<string, number>)

  return {
    receivedTrend,
    sentTrend,
    totalTrend,
    ...categoryTrends
  }
}

/**
 * Calculate linear trend using least squares regression
 */
function calculateLinearTrend(values: number[]): number {
  if (values.length < 2) return 0

  const n = values.length
  const sumX = (n * (n - 1)) / 2 // Sum of indices 0, 1, 2, ...
  const sumY = values.reduce((sum, val) => sum + val, 0)
  const sumXY = values.reduce((sum, val, index) => sum + (index * val), 0)
  const sumXX = values.reduce((sum, _, index) => sum + (index * index), 0)

  // Calculate slope (trend)
  const denominator = n * sumXX - sumX * sumX
  if (denominator === 0) return 0

  const slope = (n * sumXY - sumX * sumY) / denominator
  
  // Normalize slope as percentage change per period
  const avgValue = sumY / n
  if (avgValue === 0) return 0

  return (slope / avgValue) * 100
}

/**
 * Calculate growth rates between periods
 */
export function calculateGrowthRates(volumeData: VolumeData[]): {
  daily: number[]
  weekly: number[]
  monthly: number[]
  overall: number
} {
  if (volumeData.length < 2) {
    return { daily: [], weekly: [], monthly: [], overall: 0 }
  }

  const sortedData = [...volumeData].sort((a, b) => a.date.getTime() - b.date.getTime())
  
  // Daily growth rates
  const dailyRates: number[] = []
  for (let i = 1; i < sortedData.length; i++) {
    const current = sortedData[i].received + sortedData[i].sent
    const previous = sortedData[i - 1].received + sortedData[i - 1].sent
    const rate = previous === 0 ? 0 : ((current - previous) / previous) * 100
    dailyRates.push(rate)
  }

  // Weekly growth rates (comparing week to previous week)
  const weeklyRates: number[] = []
  for (let i = 7; i < sortedData.length; i++) {
    const currentWeek = sortedData.slice(i - 6, i + 1)
    const previousWeek = sortedData.slice(i - 13, i - 6)
    
    const currentTotal = currentWeek.reduce((sum, d) => sum + d.received + d.sent, 0)
    const previousTotal = previousWeek.reduce((sum, d) => sum + d.received + d.sent, 0)
    
    const rate = previousTotal === 0 ? 0 : ((currentTotal - previousTotal) / previousTotal) * 100
    weeklyRates.push(rate)
  }

  // Monthly growth rates (approximate with 30-day periods)
  const monthlyRates: number[] = []
  for (let i = 30; i < sortedData.length; i++) {
    const currentMonth = sortedData.slice(i - 29, i + 1)
    const previousMonth = sortedData.slice(i - 59, i - 29)
    
    const currentTotal = currentMonth.reduce((sum, d) => sum + d.received + d.sent, 0)
    const previousTotal = previousMonth.reduce((sum, d) => sum + d.received + d.sent, 0)
    
    const rate = previousTotal === 0 ? 0 : ((currentTotal - previousTotal) / previousTotal) * 100
    monthlyRates.push(rate)
  }

  // Overall growth rate (first to last period)
  const firstTotal = sortedData[0].received + sortedData[0].sent
  const lastTotal = sortedData[sortedData.length - 1].received + sortedData[sortedData.length - 1].sent
  const overall = firstTotal === 0 ? 0 : ((lastTotal - firstTotal) / firstTotal) * 100

  return {
    daily: dailyRates,
    weekly: weeklyRates,
    monthly: monthlyRates,
    overall
  }
}

/**
 * Calculate email velocity metrics
 */
export function calculateVelocityMetrics(volumeData: VolumeData[]): {
  averageDaily: number
  peakDaily: number
  minDaily: number
  volatility: number
  consistency: number
} {
  if (!volumeData.length) {
    return {
      averageDaily: 0,
      peakDaily: 0,
      minDaily: 0,
      volatility: 0,
      consistency: 0
    }
  }

  const dailyTotals = volumeData.map(d => d.received + d.sent)
  
  const averageDaily = dailyTotals.reduce((sum, total) => sum + total, 0) / dailyTotals.length
  const peakDaily = Math.max(...dailyTotals)
  const minDaily = Math.min(...dailyTotals)
  
  // Calculate volatility (standard deviation)
  const variance = dailyTotals.reduce((sum, total) => {
    return sum + Math.pow(total - averageDaily, 2)
  }, 0) / dailyTotals.length
  
  const volatility = Math.sqrt(variance)
  
  // Calculate consistency (inverse of coefficient of variation)
  const coefficientOfVariation = averageDaily === 0 ? 0 : volatility / averageDaily
  const consistency = Math.max(0, 1 - coefficientOfVariation)

  return {
    averageDaily,
    peakDaily,
    minDaily,
    volatility,
    consistency
  }
}

/**
 * Calculate response time analytics
 */
export function calculateResponseTimeAnalytics(senderStats: SenderStats[]): {
  averageResponseTime: number
  medianResponseTime: number
  responseTimeDistribution: Record<string, number>
  fastestResponders: SenderStats[]
  slowestResponders: SenderStats[]
} {
  const sendersWithResponseTime = senderStats.filter(s => s.avgResponseTime !== undefined)
  
  if (!sendersWithResponseTime.length) {
    return {
      averageResponseTime: 0,
      medianResponseTime: 0,
      responseTimeDistribution: {},
      fastestResponders: [],
      slowestResponders: []
    }
  }

  const responseTimes = sendersWithResponseTime.map(s => s.avgResponseTime!)
  
  // Calculate average
  const averageResponseTime = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
  
  // Calculate median
  const sortedTimes = [...responseTimes].sort((a, b) => a - b)
  const medianResponseTime = sortedTimes.length % 2 === 0
    ? (sortedTimes[sortedTimes.length / 2 - 1] + sortedTimes[sortedTimes.length / 2]) / 2
    : sortedTimes[Math.floor(sortedTimes.length / 2)]

  // Distribution buckets (in hours)
  const buckets = {
    'Under 1 hour': 0,
    '1-4 hours': 0,
    '4-24 hours': 0,
    '1-3 days': 0,
    'Over 3 days': 0
  }

  responseTimes.forEach(time => {
    if (time < 1) buckets['Under 1 hour']++
    else if (time < 4) buckets['1-4 hours']++
    else if (time < 24) buckets['4-24 hours']++
    else if (time < 72) buckets['1-3 days']++
    else buckets['Over 3 days']++
  })

  // Top 5 fastest and slowest responders
  const sortedSenders = [...sendersWithResponseTime].sort((a, b) => a.avgResponseTime! - b.avgResponseTime!)
  const fastestResponders = sortedSenders.slice(0, 5)
  const slowestResponders = sortedSenders.slice(-5).reverse()

  return {
    averageResponseTime,
    medianResponseTime,
    responseTimeDistribution: buckets,
    fastestResponders,
    slowestResponders
  }
}

/**
 * Calculate productivity metrics
 */
export function calculateProductivityMetrics(emailStats: EmailStats): {
  inboxZeroRate: number
  processingEfficiency: number
  archivalRate: number
  deletionRate: number
  actionRate: number
  emailsPerDay: number
  timeSpentDaily: number // estimated
} {
  const { totals, metrics, period } = emailStats
  
  // Inbox Zero rate
  const inboxZeroRate = totals.threads === 0 ? 100 : 
    ((totals.threads - totals.unread) / totals.threads) * 100

  // Processing efficiency (acted upon vs received)
  const actedUpon = totals.archived + totals.deleted + totals.sent
  const processingEfficiency = totals.received === 0 ? 0 :
    (actedUpon / totals.received) * 100

  // Archival and deletion rates
  const archivalRate = totals.received === 0 ? 0 : (totals.archived / totals.received) * 100
  const deletionRate = totals.received === 0 ? 0 : (totals.deleted / totals.received) * 100
  
  // Action rate (sent/received ratio)
  const actionRate = totals.received === 0 ? 0 : (totals.sent / totals.received) * 100

  // Calculate period length in days
  const periodLengthMs = period.end.getTime() - period.start.getTime()
  const periodLengthDays = periodLengthMs / (1000 * 60 * 60 * 24)
  
  const emailsPerDay = periodLengthDays === 0 ? 0 : totals.received / periodLengthDays

  // Estimate time spent (rough calculation based on email counts and response times)
  const avgTimePerEmail = 2 // minutes
  const timeSpentDaily = emailsPerDay * avgTimePerEmail

  return {
    inboxZeroRate,
    processingEfficiency,
    archivalRate,
    deletionRate,
    actionRate,
    emailsPerDay,
    timeSpentDaily
  }
}

/**
 * Calculate category distribution changes
 */
export function calculateCategoryChanges(
  volumeData: VolumeData[],
  comparisonPeriodDays: number = 7
): Record<string, {
  current: number
  previous: number
  change: number
  changePercent: number
}> {
  if (volumeData.length < comparisonPeriodDays * 2) {
    return {}
  }

  const sortedData = [...volumeData].sort((a, b) => a.date.getTime() - b.date.getTime())
  
  // Split into current and previous periods
  const splitIndex = sortedData.length - comparisonPeriodDays
  const currentPeriod = sortedData.slice(splitIndex)
  const previousPeriod = sortedData.slice(splitIndex - comparisonPeriodDays, splitIndex)

  // Aggregate categories for each period
  const currentTotals: Record<string, number> = {}
  const previousTotals: Record<string, number> = {}

  currentPeriod.forEach(data => {
    Object.entries(data.categories).forEach(([category, count]) => {
      currentTotals[category] = (currentTotals[category] || 0) + count
    })
  })

  previousPeriod.forEach(data => {
    Object.entries(data.categories).forEach(([category, count]) => {
      previousTotals[category] = (previousTotals[category] || 0) + count
    })
  })

  // Calculate changes
  const allCategories = new Set([
    ...Object.keys(currentTotals),
    ...Object.keys(previousTotals)
  ])

  const changes: Record<string, {
    current: number
    previous: number
    change: number
    changePercent: number
  }> = {}

  allCategories.forEach(category => {
    const current = currentTotals[category] || 0
    const previous = previousTotals[category] || 0
    const change = current - previous
    const changePercent = previous === 0 ? (current > 0 ? 100 : 0) : (change / previous) * 100

    changes[category] = {
      current,
      previous,
      change,
      changePercent
    }
  })

  return changes
}

/**
 * Calculate peak activity patterns
 */
export function calculatePeakPatterns(volumeData: VolumeData[]): {
  peakHours: number[]
  peakDays: string[]
  peakWeeks: string[]
  activityHeatmap: number[][]
} {
  if (!volumeData.length) {
    return {
      peakHours: [],
      peakDays: [],
      peakWeeks: [],
      activityHeatmap: []
    }
  }

  // Calculate hourly distribution
  const hourlyTotals = new Array(24).fill(0)
  const dailyTotals: Record<string, number> = {}
  const weeklyTotals: Record<string, number> = {}

  volumeData.forEach(data => {
    const date = new Date(data.date)
    const hour = date.getHours()
    const dayName = date.toLocaleDateString('en-US', { weekday: 'long' })
    const weekKey = `${date.getFullYear()}-W${Math.ceil(date.getDate() / 7)}`
    
    const total = data.received + data.sent
    
    hourlyTotals[hour] += total
    dailyTotals[dayName] = (dailyTotals[dayName] || 0) + total
    weeklyTotals[weekKey] = (weeklyTotals[weekKey] || 0) + total

    // Add hourly distribution if available
    if (data.hourlyDistribution) {
      data.hourlyDistribution.forEach((count, hourIndex) => {
        hourlyTotals[hourIndex] += count
      })
    }
  })

  // Find peak hours (top 25% of hours)
  const hourlyPairs = hourlyTotals.map((total, hour) => ({ hour, total }))
  hourlyPairs.sort((a, b) => b.total - a.total)
  const peakHours = hourlyPairs.slice(0, 6).map(p => p.hour).sort((a, b) => a - b)

  // Find peak days
  const dailyPairs = Object.entries(dailyTotals).map(([day, total]) => ({ day, total }))
  dailyPairs.sort((a, b) => b.total - a.total)
  const peakDays = dailyPairs.slice(0, 3).map(p => p.day)

  // Find peak weeks
  const weeklyPairs = Object.entries(weeklyTotals).map(([week, total]) => ({ week, total }))
  weeklyPairs.sort((a, b) => b.total - a.total)
  const peakWeeks = weeklyPairs.slice(0, 4).map(p => p.week)

  // Create activity heatmap (24 hours x 7 days)
  const heatmap = Array.from({ length: 7 }, () => new Array(24).fill(0))
  
  volumeData.forEach(data => {
    const date = new Date(data.date)
    const dayOfWeek = date.getDay()
    const hour = date.getHours()
    const total = data.received + data.sent
    
    heatmap[dayOfWeek][hour] += total
  })

  return {
    peakHours,
    peakDays,
    peakWeeks,
    activityHeatmap: heatmap
  }
}

/**
 * Calculate efficiency metrics
 */
export function calculateEfficiencyMetrics(emailStats: EmailStats): {
  responseEfficiency: number
  organizationScore: number
  automationBenefit: number
  timeManagementScore: number
  overallEfficiency: number
} {
  const { totals, metrics } = emailStats

  // Response efficiency (how quickly emails are responded to)
  const responseEfficiency = Math.max(0, 100 - (metrics.avgResponseTime || 0) * 2)

  // Organization score (how well emails are categorized and filed)
  const organizedEmails = totals.archived + totals.deleted
  const organizationScore = totals.received === 0 ? 100 : 
    (organizedEmails / totals.received) * 100

  // Automation benefit (estimated time saved)
  const automationBenefit = Math.min(100, (totals.spam || 0) * 2) // Rough estimate

  // Time management score (based on peak hours utilization)
  const peakHoursCount = metrics.peakHours?.length || 0
  const timeManagementScore = Math.min(100, (peakHoursCount / 8) * 100) // Optimal is 8 peak hours

  // Overall efficiency (weighted average)
  const overallEfficiency = (
    responseEfficiency * 0.3 +
    organizationScore * 0.3 +
    automationBenefit * 0.2 +
    timeManagementScore * 0.2
  )

  return {
    responseEfficiency,
    organizationScore,
    automationBenefit,
    timeManagementScore,
    overallEfficiency
  }
}

/**
 * Calculate comparative metrics between two periods
 */
export function calculateComparativeMetrics(
  currentStats: EmailStats,
  previousStats: EmailStats
): {
  volumeChange: number
  responseTimeChange: number
  efficiencyChange: number
  categoryChanges: Record<string, number>
  overallTrend: 'improving' | 'declining' | 'stable'
} {
  // Volume change
  const currentVolume = currentStats.totals.received + currentStats.totals.sent
  const previousVolume = previousStats.totals.received + previousStats.totals.sent
  const volumeChange = previousVolume === 0 ? 0 : 
    ((currentVolume - previousVolume) / previousVolume) * 100

  // Response time change
  const responseTimeChange = (previousStats.metrics.avgResponseTime || 0) === 0 ? 0 :
    ((currentStats.metrics.avgResponseTime || 0) - (previousStats.metrics.avgResponseTime || 0)) / 
    (previousStats.metrics.avgResponseTime || 0) * 100

  // Efficiency change (inbox zero rate)
  const currentEfficiency = currentStats.totals.threads === 0 ? 100 :
    ((currentStats.totals.threads - currentStats.totals.unread) / currentStats.totals.threads) * 100
  const previousEfficiency = previousStats.totals.threads === 0 ? 100 :
    ((previousStats.totals.threads - previousStats.totals.unread) / previousStats.totals.threads) * 100
  const efficiencyChange = currentEfficiency - previousEfficiency

  // Category changes
  const categoryChanges: Record<string, number> = {}
  const currentCategories = currentStats.categoryBreakdown || []
  const previousCategories = previousStats.categoryBreakdown || []
  
  const categoryMap = new Map(previousCategories.map(cat => [cat.category, cat.count]))
  
  currentCategories.forEach(currentCat => {
    const previousCount = categoryMap.get(currentCat.category) || 0
    const change = previousCount === 0 ? 0 : 
      ((currentCat.count - previousCount) / previousCount) * 100
    categoryChanges[currentCat.category] = change
  })

  // Overall trend assessment
  let overallTrend: 'improving' | 'declining' | 'stable' = 'stable'
  
  const improvementScore = 
    (volumeChange > 0 ? 1 : 0) +
    (responseTimeChange < 0 ? 1 : 0) + // Lower response time is better
    (efficiencyChange > 0 ? 1 : 0)

  if (improvementScore >= 2) overallTrend = 'improving'
  else if (improvementScore <= 0) overallTrend = 'declining'

  return {
    volumeChange,
    responseTimeChange,
    efficiencyChange,
    categoryChanges,
    overallTrend
  }
}