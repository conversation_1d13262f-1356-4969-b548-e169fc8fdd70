/**
 * Statistical utility functions for analytics calculations
 */

export interface StatisticalSummary {
  count: number
  sum: number
  mean: number
  median: number
  mode: number[]
  min: number
  max: number
  range: number
  variance: number
  standardDeviation: number
  skewness: number
  kurtosis: number
  percentiles: {
    p5: number
    p10: number
    p25: number
    p50: number
    p75: number
    p90: number
    p95: number
    p99: number
  }
}

/**
 * Calculate comprehensive statistics for a dataset
 */
export function calculateStatistics(data: number[]): StatisticalSummary {
  if (data.length === 0) {
    return {
      count: 0,
      sum: 0,
      mean: 0,
      median: 0,
      mode: [],
      min: 0,
      max: 0,
      range: 0,
      variance: 0,
      standardDeviation: 0,
      skewness: 0,
      kurtosis: 0,
      percentiles: {
        p5: 0, p10: 0, p25: 0, p50: 0, p75: 0, p90: 0, p95: 0, p99: 0
      }
    }
  }

  const sorted = [...data].sort((a, b) => a - b)
  const count = data.length
  const sum = data.reduce((acc, val) => acc + val, 0)
  const mean = sum / count

  // Basic statistics
  const min = sorted[0]
  const max = sorted[count - 1]
  const range = max - min
  const median = calculateMedian(sorted)
  const mode = calculateMode(data)

  // Variance and standard deviation
  const variance = data.reduce((acc, val) => acc + Math.pow(val - mean, 2), 0) / count
  const standardDeviation = Math.sqrt(variance)

  // Skewness and kurtosis
  const skewness = calculateSkewness(data, mean, standardDeviation)
  const kurtosis = calculateKurtosis(data, mean, standardDeviation)

  // Percentiles
  const percentiles = {
    p5: calculatePercentile(sorted, 5),
    p10: calculatePercentile(sorted, 10),
    p25: calculatePercentile(sorted, 25),
    p50: calculatePercentile(sorted, 50),
    p75: calculatePercentile(sorted, 75),
    p90: calculatePercentile(sorted, 90),
    p95: calculatePercentile(sorted, 95),
    p99: calculatePercentile(sorted, 99)
  }

  return {
    count,
    sum,
    mean,
    median,
    mode,
    min,
    max,
    range,
    variance,
    standardDeviation,
    skewness,
    kurtosis,
    percentiles
  }
}

/**
 * Calculate median value
 */
export function calculateMedian(sortedData: number[]): number {
  const n = sortedData.length
  if (n === 0) return 0
  
  if (n % 2 === 0) {
    return (sortedData[n / 2 - 1] + sortedData[n / 2]) / 2
  } else {
    return sortedData[Math.floor(n / 2)]
  }
}

/**
 * Calculate mode (most frequent values)
 */
export function calculateMode(data: number[]): number[] {
  if (data.length === 0) return []

  const frequency: Record<number, number> = {}
  let maxFreq = 0

  // Count frequencies
  data.forEach(val => {
    frequency[val] = (frequency[val] || 0) + 1
    maxFreq = Math.max(maxFreq, frequency[val])
  })

  // Return all values with maximum frequency
  return Object.entries(frequency)
    .filter(([, freq]) => freq === maxFreq)
    .map(([val]) => Number(val))
}

/**
 * Calculate percentile
 */
export function calculatePercentile(sortedData: number[], percentile: number): number {
  if (sortedData.length === 0) return 0
  if (percentile < 0 || percentile > 100) return 0

  const index = (percentile / 100) * (sortedData.length - 1)
  const lower = Math.floor(index)
  const upper = Math.ceil(index)
  const weight = index % 1

  if (upper >= sortedData.length) return sortedData[sortedData.length - 1]
  if (lower < 0) return sortedData[0]

  return sortedData[lower] * (1 - weight) + sortedData[upper] * weight
}

/**
 * Calculate skewness (measure of asymmetry)
 */
export function calculateSkewness(data: number[], mean: number, stdDev: number): number {
  if (data.length < 3 || stdDev === 0) return 0

  const n = data.length
  const m3 = data.reduce((acc, val) => acc + Math.pow((val - mean) / stdDev, 3), 0) / n

  return m3
}

/**
 * Calculate kurtosis (measure of tail heaviness)
 */
export function calculateKurtosis(data: number[], mean: number, stdDev: number): number {
  if (data.length < 4 || stdDev === 0) return 0

  const n = data.length
  const m4 = data.reduce((acc, val) => acc + Math.pow((val - mean) / stdDev, 4), 0) / n

  return m4 - 3 // Excess kurtosis (normal distribution has kurtosis of 3)
}

/**
 * Calculate quartiles and interquartile range
 */
export function calculateQuartiles(data: number[]): {
  q1: number
  q2: number
  q3: number
  iqr: number
  outlierBounds: { lower: number; upper: number }
} {
  const sorted = [...data].sort((a, b) => a - b)
  
  const q1 = calculatePercentile(sorted, 25)
  const q2 = calculatePercentile(sorted, 50)
  const q3 = calculatePercentile(sorted, 75)
  const iqr = q3 - q1

  // Outlier bounds using 1.5 * IQR rule
  const outlierBounds = {
    lower: q1 - 1.5 * iqr,
    upper: q3 + 1.5 * iqr
  }

  return { q1, q2, q3, iqr, outlierBounds }
}

/**
 * Identify outliers using IQR method
 */
export function identifyOutliers(data: number[]): {
  outliers: number[]
  indices: number[]
  method: string
} {
  const { outlierBounds } = calculateQuartiles(data)
  const outliers: number[] = []
  const indices: number[] = []

  data.forEach((val, index) => {
    if (val < outlierBounds.lower || val > outlierBounds.upper) {
      outliers.push(val)
      indices.push(index)
    }
  })

  return { outliers, indices, method: 'IQR' }
}

/**
 * Calculate z-scores for a dataset
 */
export function calculateZScores(data: number[]): number[] {
  if (data.length === 0) return []

  const mean = data.reduce((sum, val) => sum + val, 0) / data.length
  const variance = data.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / data.length
  const stdDev = Math.sqrt(variance)

  if (stdDev === 0) return data.map(() => 0)

  return data.map(val => (val - mean) / stdDev)
}

/**
 * Calculate moving averages
 */
export function calculateMovingAverage(data: number[], windowSize: number): number[] {
  if (windowSize <= 0 || windowSize > data.length) return data.slice()

  const result: number[] = []
  
  for (let i = 0; i < data.length; i++) {
    const start = Math.max(0, i - windowSize + 1)
    const window = data.slice(start, i + 1)
    const average = window.reduce((sum, val) => sum + val, 0) / window.length
    result.push(average)
  }

  return result
}

/**
 * Calculate exponential moving average
 */
export function calculateExponentialMovingAverage(data: number[], alpha: number): number[] {
  if (data.length === 0 || alpha <= 0 || alpha > 1) return data.slice()

  const result: number[] = [data[0]]
  
  for (let i = 1; i < data.length; i++) {
    const ema = alpha * data[i] + (1 - alpha) * result[i - 1]
    result.push(ema)
  }

  return result
}

/**
 * Calculate correlation coefficient between two datasets
 */
export function calculateCorrelation(x: number[], y: number[]): number {
  if (x.length !== y.length || x.length < 2) return 0

  const n = x.length
  const sumX = x.reduce((sum, val) => sum + val, 0)
  const sumY = y.reduce((sum, val) => sum + val, 0)
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0)
  const sumXX = x.reduce((sum, val) => sum + val * val, 0)
  const sumYY = y.reduce((sum, val) => sum + val * val, 0)

  const numerator = n * sumXY - sumX * sumY
  const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY))

  return denominator === 0 ? 0 : numerator / denominator
}

/**
 * Perform linear regression
 */
export function calculateLinearRegression(x: number[], y: number[]): {
  slope: number
  intercept: number
  rSquared: number
  equation: string
} {
  if (x.length !== y.length || x.length < 2) {
    return { slope: 0, intercept: 0, rSquared: 0, equation: 'y = 0' }
  }

  const n = x.length
  const sumX = x.reduce((sum, val) => sum + val, 0)
  const sumY = y.reduce((sum, val) => sum + val, 0)
  const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0)
  const sumXX = x.reduce((sum, val) => sum + val * val, 0)

  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX)
  const intercept = (sumY - slope * sumX) / n

  // Calculate R-squared
  const meanY = sumY / n
  const totalSumSquares = y.reduce((sum, val) => sum + Math.pow(val - meanY, 2), 0)
  const residualSumSquares = y.reduce((sum, val, i) => {
    const predicted = slope * x[i] + intercept
    return sum + Math.pow(val - predicted, 2)
  }, 0)

  const rSquared = totalSumSquares === 0 ? 0 : 1 - (residualSumSquares / totalSumSquares)

  const equation = `y = ${slope.toFixed(3)}x + ${intercept.toFixed(3)}`

  return { slope, intercept, rSquared, equation }
}

/**
 * Calculate confidence intervals
 */
export function calculateConfidenceInterval(
  data: number[],
  confidenceLevel: number = 0.95
): { lower: number; upper: number; margin: number } {
  if (data.length === 0) {
    return { lower: 0, upper: 0, margin: 0 }
  }

  const stats = calculateStatistics(data)
  const { mean, standardDeviation, count } = stats

  // Use t-distribution for small samples, normal for large samples
  const degreesOfFreedom = count - 1
  const alpha = 1 - confidenceLevel
  
  // Simplified t-value calculation (approximate)
  let tValue: number
  if (count > 30) {
    // Use normal distribution approximation
    tValue = confidenceLevel === 0.95 ? 1.96 : confidenceLevel === 0.99 ? 2.576 : 1.645
  } else {
    // Use simplified t-distribution values
    const tValues: Record<number, number> = {
      0.90: 1.645,
      0.95: 1.96,
      0.99: 2.576
    }
    tValue = tValues[confidenceLevel] || 1.96
  }

  const standardError = standardDeviation / Math.sqrt(count)
  const margin = tValue * standardError

  return {
    lower: mean - margin,
    upper: mean + margin,
    margin
  }
}

/**
 * Calculate coefficient of variation
 */
export function calculateCoefficientOfVariation(data: number[]): number {
  const stats = calculateStatistics(data)
  return stats.mean === 0 ? 0 : (stats.standardDeviation / Math.abs(stats.mean)) * 100
}

/**
 * Normalize data to 0-1 range
 */
export function normalizeData(data: number[]): number[] {
  if (data.length === 0) return []

  const min = Math.min(...data)
  const max = Math.max(...data)
  const range = max - min

  if (range === 0) return data.map(() => 0)

  return data.map(val => (val - min) / range)
}

/**
 * Standardize data (z-score normalization)
 */
export function standardizeData(data: number[]): number[] {
  const zScores = calculateZScores(data)
  return zScores
}

/**
 * Calculate running statistics (for streaming data)
 */
export class RunningStatistics {
  private count = 0
  private mean = 0
  private m2 = 0 // For variance calculation
  private min = Infinity
  private max = -Infinity

  add(value: number): void {
    this.count++
    this.min = Math.min(this.min, value)
    this.max = Math.max(this.max, value)

    // Welford's online algorithm for mean and variance
    const delta = value - this.mean
    this.mean += delta / this.count
    const delta2 = value - this.mean
    this.m2 += delta * delta2
  }

  getStatistics(): {
    count: number
    mean: number
    variance: number
    standardDeviation: number
    min: number
    max: number
  } {
    const variance = this.count < 2 ? 0 : this.m2 / this.count
    const standardDeviation = Math.sqrt(variance)

    return {
      count: this.count,
      mean: this.mean,
      variance,
      standardDeviation,
      min: this.count === 0 ? 0 : this.min,
      max: this.count === 0 ? 0 : this.max
    }
  }

  reset(): void {
    this.count = 0
    this.mean = 0
    this.m2 = 0
    this.min = Infinity
    this.max = -Infinity
  }
}

/**
 * Calculate entropy (measure of randomness/uncertainty)
 */
export function calculateEntropy(data: number[]): number {
  if (data.length === 0) return 0

  // Calculate frequencies
  const frequencies: Record<number, number> = {}
  data.forEach(val => {
    frequencies[val] = (frequencies[val] || 0) + 1
  })

  const total = data.length
  let entropy = 0

  Object.values(frequencies).forEach(freq => {
    const probability = freq / total
    if (probability > 0) {
      entropy -= probability * Math.log2(probability)
    }
  })

  return entropy
}

/**
 * Calculate Gini coefficient (measure of inequality)
 */
export function calculateGiniCoefficient(data: number[]): number {
  if (data.length === 0) return 0

  const sorted = [...data].sort((a, b) => a - b)
  const n = sorted.length
  const mean = sorted.reduce((sum, val) => sum + val, 0) / n

  if (mean === 0) return 0

  let gini = 0
  for (let i = 0; i < n; i++) {
    gini += (2 * (i + 1) - n - 1) * sorted[i]
  }

  return gini / (n * n * mean)
}

/**
 * Statistical test for normality (Shapiro-Wilk approximation)
 */
export function testNormality(data: number[]): {
  isNormal: boolean
  pValue: number
  statistic: number
} {
  if (data.length < 3) {
    return { isNormal: false, pValue: 0, statistic: 0 }
  }

  // Simplified normality test
  const stats = calculateStatistics(data)
  const { skewness, kurtosis } = stats

  // Use Jarque-Bera test approximation
  const n = data.length
  const jb = (n / 6) * (skewness * skewness + (kurtosis * kurtosis) / 4)
  
  // Approximate p-value (very simplified)
  const pValue = jb > 5.99 ? 0.01 : jb > 2.71 ? 0.05 : 0.2

  return {
    isNormal: pValue > 0.05,
    pValue,
    statistic: jb
  }
}

/**
 * Calculate seasonal decomposition components
 */
export function seasonalDecomposition(
  data: number[],
  seasonLength: number
): {
  trend: number[]
  seasonal: number[]
  residual: number[]
  strength: number
} {
  if (data.length < seasonLength * 2) {
    return {
      trend: data.slice(),
      seasonal: new Array(data.length).fill(0),
      residual: new Array(data.length).fill(0),
      strength: 0
    }
  }

  // Calculate trend using moving average
  const trend = calculateMovingAverage(data, seasonLength)
  
  // Remove trend to get detrended data
  const detrended = data.map((val, i) => val - trend[i])
  
  // Calculate seasonal components
  const seasonal = new Array(data.length).fill(0)
  const seasonalSums = new Array(seasonLength).fill(0)
  const seasonalCounts = new Array(seasonLength).fill(0)
  
  detrended.forEach((val, i) => {
    const seasonIndex = i % seasonLength
    seasonalSums[seasonIndex] += val
    seasonalCounts[seasonIndex]++
  })
  
  // Average seasonal components
  const seasonalPattern = seasonalSums.map((sum, i) => 
    seasonalCounts[i] > 0 ? sum / seasonalCounts[i] : 0
  )
  
  // Apply seasonal pattern to all data points
  data.forEach((_, i) => {
    seasonal[i] = seasonalPattern[i % seasonLength]
  })
  
  // Calculate residuals
  const residual = data.map((val, i) => val - trend[i] - seasonal[i])
  
  // Calculate seasonal strength
  const originalVariance = calculateStatistics(data).variance
  const residualVariance = calculateStatistics(residual).variance
  const strength = originalVariance > 0 ? 1 - (residualVariance / originalVariance) : 0
  
  return { trend, seasonal, residual, strength }
}