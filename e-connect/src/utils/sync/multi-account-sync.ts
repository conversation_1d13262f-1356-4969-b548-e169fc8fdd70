import type { 
  EmailAccount, 
  AccountSyncProgress, 
  SyncStatus,
  MultiAccountSettings 
} from '../../types/multi-account'

export interface SyncManager {
  // Core sync operations
  syncAccount(accountId: string, options?: SyncOptions): Promise<SyncResult>
  syncAllAccounts(options?: BatchSyncOptions): Promise<BatchSyncResult>
  pauseSync(accountId: string): void
  resumeSync(accountId: string): void
  cancelSync(accountId: string): void
  
  // Progress tracking
  getSyncProgress(accountId: string): AccountSyncProgress | null
  getAllSyncProgress(): AccountSyncProgress[]
  onProgressUpdate(callback: (progress: AccountSyncProgress) => void): () => void
  
  // Status management
  getSyncStatus(accountId: string): SyncStatus
  getGlobalSyncStatus(): SyncStatus
  
  // Configuration
  updateSyncSettings(accountId: string, settings: Partial<AccountSyncSettings>): void
  getNextSyncTime(accountId: string): Date | null
  
  // Error handling
  getLastSyncError(accountId: string): string | null
  retryFailedSync(accountId: string): Promise<SyncResult>
  clearSyncErrors(accountId: string): void
}

export interface SyncOptions {
  force?: boolean
  fullSync?: boolean
  folders?: string[]
  priority?: 'high' | 'normal' | 'low'
  timeout?: number
}

export interface BatchSyncOptions extends SyncOptions {
  accountIds?: string[]
  maxConcurrent?: number
  stopOnError?: boolean
}

export interface SyncResult {
  success: boolean
  accountId: string
  duration: number
  itemsSynced: number
  itemsSkipped: number
  errors: SyncError[]
  stats: {
    emailsAdded: number
    emailsUpdated: number
    emailsDeleted: number
    foldersUpdated: number
    labelsUpdated: number
  }
}

export interface BatchSyncResult {
  success: boolean
  totalDuration: number
  accountResults: SyncResult[]
  globalStats: {
    totalSynced: number
    totalErrors: number
    accountsSucceeded: number
    accountsFailed: number
  }
}

export interface SyncError {
  code: string
  message: string
  details?: any
  timestamp: Date
  retryable: boolean
}

export interface AccountSyncSettings {
  enabled: boolean
  syncInterval: number
  fullSyncInterval: number
  syncFolders: string[]
  excludeFolders: string[]
  maxEmailsPerSync: number
  daysToSync: number
  backgroundSync: boolean
  wifiOnly: boolean
  lowBatteryMode: boolean
}

class MultiAccountSyncManager implements SyncManager {
  private syncProgressMap = new Map<string, AccountSyncProgress>()
  private progressCallbacks = new Set<(progress: AccountSyncProgress) => void>()
  private syncControllers = new Map<string, AbortController>()
  private syncQueues = new Map<string, Array<() => Promise<void>>>()
  private activeSyncs = new Set<string>()
  private syncIntervals = new Map<string, NodeJS.Timeout>()

  constructor(
    private accounts: EmailAccount[],
    private settings: MultiAccountSettings
  ) {
    this.initializeSync()
  }

  private initializeSync() {
    // Set up periodic sync for all accounts
    this.accounts.forEach(account => {
      if (account.sync.enabled && account.status === 'active') {
        this.scheduleSync(account.id)
      }
    })
  }

  private scheduleSync(accountId: string) {
    const account = this.accounts.find(acc => acc.id === accountId)
    if (!account || !account.sync.enabled) return

    // Clear existing interval
    const existingInterval = this.syncIntervals.get(accountId)
    if (existingInterval) {
      clearInterval(existingInterval)
    }

    // Schedule new sync
    const interval = setInterval(async () => {
      if (this.activeSyncs.has(accountId)) return
      
      try {
        await this.syncAccount(accountId, { force: false })
      } catch (error) {
        console.error(`Scheduled sync failed for ${accountId}:`, error)
      }
    }, account.sync.syncInterval * 60 * 1000) // Convert minutes to milliseconds

    this.syncIntervals.set(accountId, interval)
  }

  async syncAccount(accountId: string, options: SyncOptions = {}): Promise<SyncResult> {
    const account = this.accounts.find(acc => acc.id === accountId)
    if (!account) {
      throw new Error(`Account ${accountId} not found`)
    }

    if (!account.sync.enabled && !options.force) {
      throw new Error(`Sync is disabled for account ${accountId}`)
    }

    if (this.activeSyncs.has(accountId) && !options.force) {
      throw new Error(`Sync already in progress for account ${accountId}`)
    }

    // Create abort controller for this sync
    const controller = new AbortController()
    this.syncControllers.set(accountId, controller)
    this.activeSyncs.add(accountId)

    const startTime = Date.now()
    let progress: AccountSyncProgress = {
      accountId,
      status: 'syncing',
      progress: 0,
      currentTask: 'Initializing sync...',
      estimatedRemaining: 0,
      itemsProcessed: 0,
      totalItems: 0,
      errors: []
    }

    this.updateProgress(progress)

    try {
      // Determine what to sync
      const foldersToSync = options.folders || account.sync.syncFolders
      const isFullSync = options.fullSync || this.shouldPerformFullSync(account)

      progress.totalItems = await this.estimateTotalItems(account, foldersToSync, isFullSync)
      progress.currentTask = 'Connecting to server...'
      this.updateProgress(progress)

      // Authenticate if needed
      if (!account.auth.isValid) {
        progress.currentTask = 'Authenticating...'
        this.updateProgress(progress)
        await this.authenticateAccount(account)
      }

      const syncResult: SyncResult = {
        success: true,
        accountId,
        duration: 0,
        itemsSynced: 0,
        itemsSkipped: 0,
        errors: [],
        stats: {
          emailsAdded: 0,
          emailsUpdated: 0,
          emailsDeleted: 0,
          foldersUpdated: 0,
          labelsUpdated: 0
        }
      }

      // Sync each folder
      for (const folder of foldersToSync) {
        if (controller.signal.aborted) {
          throw new Error('Sync cancelled')
        }

        progress.currentTask = `Syncing folder: ${folder}`
        this.updateProgress(progress)

        try {
          const folderResult = await this.syncFolder(account, folder, isFullSync, controller.signal)
          
          // Update results
          syncResult.itemsSynced += folderResult.itemsSynced
          syncResult.stats.emailsAdded += folderResult.emailsAdded
          syncResult.stats.emailsUpdated += folderResult.emailsUpdated
          syncResult.stats.emailsDeleted += folderResult.emailsDeleted

          // Update progress
          progress.itemsProcessed += folderResult.itemsSynced
          progress.progress = Math.min(95, (progress.itemsProcessed / progress.totalItems) * 100)
          progress.estimatedRemaining = this.calculateEstimatedRemaining(
            startTime, 
            progress.progress
          )
          this.updateProgress(progress)

        } catch (error) {
          const syncError: SyncError = {
            code: 'FOLDER_SYNC_ERROR',
            message: `Failed to sync folder ${folder}: ${error instanceof Error ? error.message : 'Unknown error'}`,
            timestamp: new Date(),
            retryable: true
          }
          syncResult.errors.push(syncError)
          progress.errors.push(syncError.message)
        }
      }

      // Sync labels if supported
      if (account.sync.syncLabels && account.provider !== 'imap') {
        progress.currentTask = 'Syncing labels...'
        this.updateProgress(progress)

        try {
          const labelResult = await this.syncLabels(account, controller.signal)
          syncResult.stats.labelsUpdated += labelResult.labelsUpdated
        } catch (error) {
          const syncError: SyncError = {
            code: 'LABEL_SYNC_ERROR',
            message: `Failed to sync labels: ${error instanceof Error ? error.message : 'Unknown error'}`,
            timestamp: new Date(),
            retryable: true
          }
          syncResult.errors.push(syncError)
        }
      }

      // Finalize sync
      progress.currentTask = 'Finalizing...'
      progress.progress = 100
      this.updateProgress(progress)

      syncResult.duration = Date.now() - startTime
      syncResult.success = syncResult.errors.length === 0

      // Update account stats
      await this.updateAccountStats(account, syncResult)

      return syncResult

    } catch (error) {
      const syncError: SyncError = {
        code: 'SYNC_FAILED',
        message: error instanceof Error ? error.message : 'Unknown sync error',
        timestamp: new Date(),
        retryable: true
      }

      const failedResult: SyncResult = {
        success: false,
        accountId,
        duration: Date.now() - startTime,
        itemsSynced: 0,
        itemsSkipped: 0,
        errors: [syncError],
        stats: {
          emailsAdded: 0,
          emailsUpdated: 0,
          emailsDeleted: 0,
          foldersUpdated: 0,
          labelsUpdated: 0
        }
      }

      // Update progress with error
      progress.status = 'failed'
      progress.errors.push(syncError.message)
      this.updateProgress(progress)

      return failedResult

    } finally {
      // Cleanup
      this.activeSyncs.delete(accountId)
      this.syncControllers.delete(accountId)
      
      // Remove progress after delay
      setTimeout(() => {
        this.syncProgressMap.delete(accountId)
      }, 5000)
    }
  }

  async syncAllAccounts(options: BatchSyncOptions = {}): Promise<BatchSyncResult> {
    const startTime = Date.now()
    const accountsToSync = options.accountIds 
      ? this.accounts.filter(acc => options.accountIds!.includes(acc.id))
      : this.accounts.filter(acc => acc.sync.enabled && acc.status === 'active')

    const maxConcurrent = Math.min(
      options.maxConcurrent || this.settings.maxConcurrentSyncs,
      accountsToSync.length
    )

    const results: SyncResult[] = []
    const errors: SyncError[] = []

    // Process accounts in batches
    for (let i = 0; i < accountsToSync.length; i += maxConcurrent) {
      const batch = accountsToSync.slice(i, i + maxConcurrent)
      
      const batchPromises = batch.map(account => 
        this.syncAccount(account.id, options)
          .catch(error => {
            const syncError: SyncError = {
              code: 'BATCH_SYNC_ERROR',
              message: `Failed to sync account ${account.id}: ${error.message}`,
              timestamp: new Date(),
              retryable: true
            }
            errors.push(syncError)
            
            return {
              success: false,
              accountId: account.id,
              duration: 0,
              itemsSynced: 0,
              itemsSkipped: 0,
              errors: [syncError],
              stats: {
                emailsAdded: 0,
                emailsUpdated: 0,
                emailsDeleted: 0,
                foldersUpdated: 0,
                labelsUpdated: 0
              }
            } as SyncResult
          })
      )

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Stop on error if requested
      if (options.stopOnError && batchResults.some(result => !result.success)) {
        break
      }
    }

    // Calculate global stats
    const globalStats = {
      totalSynced: results.reduce((sum, result) => sum + result.itemsSynced, 0),
      totalErrors: results.reduce((sum, result) => sum + result.errors.length, 0),
      accountsSucceeded: results.filter(result => result.success).length,
      accountsFailed: results.filter(result => !result.success).length
    }

    return {
      success: globalStats.accountsFailed === 0,
      totalDuration: Date.now() - startTime,
      accountResults: results,
      globalStats
    }
  }

  pauseSync(accountId: string): void {
    const controller = this.syncControllers.get(accountId)
    if (controller) {
      controller.abort()
    }
    
    const progress = this.syncProgressMap.get(accountId)
    if (progress) {
      progress.status = 'paused'
      this.updateProgress(progress)
    }
  }

  resumeSync(accountId: string): void {
    const progress = this.syncProgressMap.get(accountId)
    if (progress && progress.status === 'paused') {
      this.syncAccount(accountId, { force: true })
    }
  }

  cancelSync(accountId: string): void {
    const controller = this.syncControllers.get(accountId)
    if (controller) {
      controller.abort()
    }
    
    this.activeSyncs.delete(accountId)
    this.syncControllers.delete(accountId)
    this.syncProgressMap.delete(accountId)
  }

  getSyncProgress(accountId: string): AccountSyncProgress | null {
    return this.syncProgressMap.get(accountId) || null
  }

  getAllSyncProgress(): AccountSyncProgress[] {
    return Array.from(this.syncProgressMap.values())
  }

  onProgressUpdate(callback: (progress: AccountSyncProgress) => void): () => void {
    this.progressCallbacks.add(callback)
    return () => this.progressCallbacks.delete(callback)
  }

  getSyncStatus(accountId: string): SyncStatus {
    const progress = this.syncProgressMap.get(accountId)
    return progress?.status || 'idle'
  }

  getGlobalSyncStatus(): SyncStatus {
    const allProgress = Array.from(this.syncProgressMap.values())
    
    if (allProgress.some(p => p.status === 'syncing')) return 'syncing'
    if (allProgress.some(p => p.status === 'failed')) return 'failed'
    if (allProgress.some(p => p.status === 'paused')) return 'paused'
    if (allProgress.some(p => p.status === 'completed')) return 'completed'
    
    return 'idle'
  }

  updateSyncSettings(accountId: string, settings: Partial<AccountSyncSettings>): void {
    const account = this.accounts.find(acc => acc.id === accountId)
    if (!account) return

    account.sync = { ...account.sync, ...settings }
    
    // Reschedule sync if interval changed
    if (settings.syncInterval !== undefined) {
      this.scheduleSync(accountId)
    }
  }

  getNextSyncTime(accountId: string): Date | null {
    const account = this.accounts.find(acc => acc.id === accountId)
    if (!account || !account.sync.enabled || !account.lastSyncAt) {
      return null
    }

    return new Date(account.lastSyncAt.getTime() + account.sync.syncInterval * 60 * 1000)
  }

  getLastSyncError(accountId: string): string | null {
    const account = this.accounts.find(acc => acc.id === accountId)
    return account?.stats.lastSyncError || null
  }

  async retryFailedSync(accountId: string): Promise<SyncResult> {
    return this.syncAccount(accountId, { force: true })
  }

  clearSyncErrors(accountId: string): void {
    const account = this.accounts.find(acc => acc.id === accountId)
    if (account) {
      account.stats.lastSyncError = undefined
      account.stats.consecutiveFailures = 0
    }
  }

  // Private helper methods
  private updateProgress(progress: AccountSyncProgress): void {
    this.syncProgressMap.set(progress.accountId, progress)
    this.progressCallbacks.forEach(callback => callback(progress))
  }

  private shouldPerformFullSync(account: EmailAccount): boolean {
    if (!account.lastSyncAt) return true
    
    const fullSyncInterval = account.sync.fullSyncInterval * 60 * 60 * 1000 // Convert hours to ms
    return Date.now() - account.lastSyncAt.getTime() > fullSyncInterval
  }

  private async estimateTotalItems(
    account: EmailAccount, 
    folders: string[], 
    isFullSync: boolean
  ): Promise<number> {
    // Estimate based on account stats and sync settings
    const averageEmailsPerFolder = Math.floor(account.stats.totalEmails / folders.length) || 100
    const syncLimit = isFullSync ? averageEmailsPerFolder : Math.min(averageEmailsPerFolder, account.sync.maxEmailsPerSync)
    
    return folders.length * syncLimit
  }

  private calculateEstimatedRemaining(startTime: number, progress: number): number {
    if (progress <= 0) return 0
    
    const elapsed = Date.now() - startTime
    const totalEstimated = elapsed / (progress / 100)
    return Math.max(0, totalEstimated - elapsed)
  }

  private async authenticateAccount(account: EmailAccount): Promise<void> {
    // Simulate authentication
    await new Promise(resolve => setTimeout(resolve, 1000))
    account.auth.isValid = true
    account.auth.lastValidated = new Date()
  }

  private async syncFolder(
    account: EmailAccount, 
    folder: string, 
    isFullSync: boolean,
    signal: AbortSignal
  ): Promise<{
    itemsSynced: number
    emailsAdded: number
    emailsUpdated: number
    emailsDeleted: number
  }> {
    // Simulate folder sync
    await new Promise(resolve => setTimeout(resolve, Math.random() * 2000 + 1000))
    
    if (signal.aborted) {
      throw new Error('Sync cancelled')
    }

    const itemsSynced = Math.floor(Math.random() * 50) + 10
    return {
      itemsSynced,
      emailsAdded: Math.floor(itemsSynced * 0.3),
      emailsUpdated: Math.floor(itemsSynced * 0.5),
      emailsDeleted: Math.floor(itemsSynced * 0.2)
    }
  }

  private async syncLabels(
    account: EmailAccount,
    signal: AbortSignal
  ): Promise<{ labelsUpdated: number }> {
    // Simulate label sync
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (signal.aborted) {
      throw new Error('Sync cancelled')
    }

    return {
      labelsUpdated: Math.floor(Math.random() * 10) + 1
    }
  }

  private async updateAccountStats(account: EmailAccount, result: SyncResult): Promise<void> {
    account.stats.lastSyncDuration = result.duration
    account.stats.averageSyncDuration = (account.stats.averageSyncDuration + result.duration) / 2
    account.stats.syncedEmails += result.itemsSynced
    
    if (result.success) {
      account.stats.consecutiveFailures = 0
      account.lastSyncAt = new Date()
    } else {
      account.stats.syncErrors += 1
      account.stats.consecutiveFailures += 1
      account.stats.lastSyncError = result.errors[0]?.message
    }
  }
}

// Factory function to create sync manager
export function createSyncManager(
  accounts: EmailAccount[], 
  settings: MultiAccountSettings
): SyncManager {
  return new MultiAccountSyncManager(accounts, settings)
}

// Sync scheduler for background operations
export class SyncScheduler {
  private scheduler: SyncManager
  private scheduledTasks = new Map<string, NodeJS.Timeout>()

  constructor(syncManager: SyncManager) {
    this.scheduler = syncManager
  }

  scheduleAccountSync(account: EmailAccount): void {
    if (!account.sync.enabled || !account.sync.backgroundSync) return

    // Clear existing schedule
    this.unscheduleAccountSync(account.id)

    // Calculate next sync time
    const nextSync = this.scheduler.getNextSyncTime(account.id)
    if (!nextSync) return

    const delay = Math.max(0, nextSync.getTime() - Date.now())
    
    const timeout = setTimeout(async () => {
      try {
        await this.scheduler.syncAccount(account.id)
        // Reschedule for next interval
        this.scheduleAccountSync(account)
      } catch (error) {
        console.error(`Scheduled sync failed for ${account.id}:`, error)
        // Reschedule with exponential backoff
        setTimeout(() => this.scheduleAccountSync(account), Math.min(300000, delay * 2))
      }
    }, delay)

    this.scheduledTasks.set(account.id, timeout)
  }

  unscheduleAccountSync(accountId: string): void {
    const timeout = this.scheduledTasks.get(accountId)
    if (timeout) {
      clearTimeout(timeout)
      this.scheduledTasks.delete(accountId)
    }
  }

  scheduleAllAccounts(accounts: EmailAccount[]): void {
    accounts.forEach(account => this.scheduleAccountSync(account))
  }

  unscheduleAllAccounts(): void {
    this.scheduledTasks.forEach(timeout => clearTimeout(timeout))
    this.scheduledTasks.clear()
  }
}