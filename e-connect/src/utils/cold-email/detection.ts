import type { 
  ColdEmailDetection, 
  SenderAnalysis, 
  ContentAnalysis,
  DetectionSignal,
  EmailFeatures
} from '../../types/cold-email';
import type { ParsedMessage } from '../../types/email';

// ML Model simulation
class ColdEmailMLModel {
  private weights: Record<string, number> = {
    // Sender features
    senderDomainAge: -0.02,
    senderReputation: -0.03,
    authenticationScore: -0.015,
    sendingFrequency: 0.025,
    recipientCount: 0.02,
    
    // Content features
    subjectLength: 0.01,
    bodyLength: 0.005,
    linkCount: 0.15,
    imageCount: 0.1,
    attachmentCount: 0.05,
    
    // Pattern features
    hasUnsubscribe: -0.2,
    hasTracking: 0.3,
    templateScore: 0.02,
    personalizedScore: -0.025,
    
    // Keyword features
    salesKeywords: 0.4,
    urgencyKeywords: 0.35,
    suspiciousKeywords: 0.45,
    
    // Behavioral features
    timeSinceLast: -0.01,
    engagementHistory: -0.03,
    reportHistory: 0.5,
  };
  
  private bias = 0.3;
  
  predict(features: EmailFeatures): number {
    let score = this.bias;
    
    // Apply weights to numeric features
    Object.entries(features).forEach(([key, value]) => {
      if (typeof value === 'number' && this.weights[key]) {
        score += value * this.weights[key];
      } else if (typeof value === 'boolean' && this.weights[key]) {
        score += (value ? 1 : 0) * this.weights[key];
      }
    });
    
    // Sigmoid activation
    return 1 / (1 + Math.exp(-score));
  }
  
  // Simulate model improvement over time
  improve(trainingData: Array<{ features: EmailFeatures; label: boolean }>) {
    // Simple gradient descent simulation
    const learningRate = 0.01;
    
    trainingData.forEach(({ features, label }) => {
      const prediction = this.predict(features);
      const error = label ? (1 - prediction) : (0 - prediction);
      
      // Update weights
      Object.entries(features).forEach(([key, value]) => {
        if (typeof value === 'number' && this.weights[key]) {
          this.weights[key] += learningRate * error * value;
        }
      });
      
      this.bias += learningRate * error;
    });
  }
}

// Pattern detection algorithms
export class ColdEmailDetector {
  private mlModel = new ColdEmailMLModel();
  
  // Sales and marketing keywords
  private salesKeywords = [
    'offer', 'discount', 'deal', 'save', 'limited time', 'exclusive',
    'special price', 'buy now', 'order now', 'click here', 'act now',
    'free trial', 'no obligation', 'risk-free', 'guaranteed',
    'increase revenue', 'boost sales', 'grow your business',
    'roi', 'conversion', 'leads', 'pipeline', 'quota'
  ];
  
  private urgencyKeywords = [
    'urgent', 'immediate', 'expires', 'last chance', 'hurry',
    'ending soon', 'final notice', 'time sensitive', 'deadline',
    'today only', 'limited spots', 'act fast', 'don\'t miss'
  ];
  
  private suspiciousKeywords = [
    'winner', 'congratulations', 'claim', 'verify account',
    'suspended', 'confirm identity', 'update payment',
    'click below', 'prize', 'selected', 'lucky'
  ];
  
  // Common cold email templates
  private templatePatterns = [
    /Hi\s+\{?\{?(?:first_?name|name)\}?\}?,?\s+I\s+(?:noticed|saw|found)/i,
    /I\s+came\s+across\s+your\s+(?:profile|company|website)/i,
    /(?:Quick|Brief)\s+(?:question|intro)/i,
    /I\'d\s+(?:love|like)\s+to\s+(?:connect|chat|talk)/i,
    /(?:Reaching|Reaching out)\s+(?:to|because)/i,
    /Are\s+you\s+the\s+right\s+person\s+to\s+(?:talk|speak)\s+(?:to|with)/i,
    /I\s+help\s+companies\s+like\s+yours/i,
    /We\'ve\s+helped\s+\d+\s+(?:companies|businesses)/i
  ];
  
  // Analyze sender reputation
  analyzeSender(email: ParsedMessage): Partial<SenderAnalysis> {
    const domain = email.from.email.split('@')[1] || 'unknown';
    
    // Check for suspicious domain patterns
    const suspiciousDomains = [
      /\d{4,}/, // Many numbers
      /^[^.]+$/, // No TLD
      /.tk$|.ml$|.ga$|.cf$/, // Free TLDs often used for spam
      /temp|disposable|trash|guerrilla/i
    ];
    
    const isSuspiciousDomain = suspiciousDomains.some(pattern => pattern.test(domain));
    
    // Simulate domain age check
    const commonDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com'];
    const isCommonDomain = commonDomains.includes(domain);
    const estimatedDomainAge = isCommonDomain ? 7300 : Math.floor(Math.random() * 1000) + 30;
    
    return {
      email: email.from.email,
      domain,
      domainInfo: {
        age: estimatedDomainAge,
        isDisposable: isSuspiciousDomain,
        isFreemail: isCommonDomain,
        isDynamic: false
      }
    };
  }
  
  // Analyze email content
  analyzeContent(email: ParsedMessage): Partial<ContentAnalysis> {
    const content = `${email.subject} ${email.body.text || email.body.plain || ''}`.toLowerCase();
    
    // Count keyword occurrences
    const salesCount = this.salesKeywords.filter(k => content.includes(k)).length;
    const urgencyCount = this.urgencyKeywords.filter(k => content.includes(k)).length;
    const suspiciousCount = this.suspiciousKeywords.filter(k => content.includes(k)).length;
    
    // Check for template patterns
    const templateMatches = this.templatePatterns.filter(p => p.test(content)).length;
    const templateScore = Math.min(100, templateMatches * 20);
    
    // Analyze personalization
    const hasRecipientName = /dear\s+[a-z]+/i.test(content) && !/dear\s+(sir|madam|customer)/i.test(content);
    const hasCompanyName = /your\s+company|your\s+business/i.test(content);
    const personalizedScore = hasRecipientName ? 70 : hasCompanyName ? 30 : 0;
    
    // Check for unsubscribe links
    const hasUnsubscribe = /unsubscribe|opt.?out|remove/i.test(content);
    
    // Count links and images (simplified)
    const linkCount = (content.match(/https?:\/\//g) || []).length;
    const imageCount = (content.match(/<img/g) || []).length;
    
    return {
      patterns: {
        isColdEmail: salesCount > 2 || templateScore > 40,
        isSalesEmail: salesCount > 3,
        isMarketingEmail: salesCount > 1 && hasUnsubscribe,
        isRecruitingEmail: /opportunity|position|role|hiring/i.test(content),
        isPhishing: suspiciousCount > 2,
        isScam: suspiciousCount > 3 && urgencyCount > 2
      },
      keywords: {
        sales: this.salesKeywords.filter(k => content.includes(k)),
        urgency: this.urgencyKeywords.filter(k => content.includes(k)),
        suspicious: this.suspiciousKeywords.filter(k => content.includes(k)),
        financial: [],
        action: ['click here', 'sign up', 'register', 'download'].filter(k => content.includes(k))
      },
      structure: {
        hasUnsubscribeLink: hasUnsubscribe,
        hasContactInfo: /\d{3}[-.\s]?\d{3}[-.\s]?\d{4}/.test(content), // Phone number
        hasCompanyInfo: /inc\.|llc|ltd|corporation/i.test(content),
        hasPrivacyPolicy: /privacy\s+policy/i.test(content),
        templateSimilarity: templateScore
      },
      mlAnalysis: {
        coldEmailProbability: 0, // Will be calculated
        spamProbability: 0,
        phishingProbability: 0,
        categoryScores: {}
      }
    };
  }
  
  // Extract features for ML model
  extractFeatures(
    email: ParsedMessage, 
    senderAnalysis: Partial<SenderAnalysis>,
    contentAnalysis: Partial<ContentAnalysis>
  ): EmailFeatures {
    const content = email.body.text || email.body.plain || '';
    
    return {
      // Sender features
      senderDomainAge: senderAnalysis.domainInfo?.age || 0,
      senderReputation: 50, // Default, would be calculated from history
      authenticationScore: 50, // Default, would check SPF/DKIM
      sendingFrequency: 1, // Default, would be calculated from history
      recipientCount: 1, // Default, would be calculated from headers
      
      // Content features
      subjectLength: email.subject.length,
      bodyLength: content.length,
      linkCount: contentAnalysis.keywords?.action?.length || 0,
      imageCount: 0, // Simplified
      attachmentCount: email.attachments.length,
      
      // Pattern features
      hasUnsubscribe: contentAnalysis.structure?.hasUnsubscribeLink || false,
      hasTracking: false, // Would check for tracking pixels
      templateScore: contentAnalysis.structure?.templateSimilarity || 0,
      personalizedScore: 50, // Default
      
      // Keyword features
      salesKeywords: contentAnalysis.keywords?.sales?.length || 0,
      urgencyKeywords: contentAnalysis.keywords?.urgency?.length || 0,
      suspiciousKeywords: contentAnalysis.keywords?.suspicious?.length || 0,
      
      // Behavioral features
      timeSinceLast: 24, // Hours, default
      engagementHistory: 0, // Would be calculated from history
      reportHistory: 0, // Would be calculated from history
    };
  }
  
  // Generate detection signals
  generateSignals(
    email: ParsedMessage,
    senderAnalysis: Partial<SenderAnalysis>,
    contentAnalysis: Partial<ContentAnalysis>,
    mlScore: number
  ): DetectionSignal[] {
    const signals: DetectionSignal[] = [];
    
    // ML confidence signal
    signals.push({
      type: 'ml_confidence',
      value: mlScore,
      weight: 0.4,
      confidence: mlScore * 100,
      description: 'Machine learning model confidence score'
    });
    
    // Sender reputation signal
    if (senderAnalysis.domainInfo?.age && senderAnalysis.domainInfo.age < 90) {
      signals.push({
        type: 'new_domain',
        value: senderAnalysis.domainInfo.age,
        weight: 0.2,
        confidence: 80,
        description: `Domain is only ${senderAnalysis.domainInfo.age} days old`
      });
    }
    
    // Content pattern signals
    if (contentAnalysis.structure?.templateSimilarity && contentAnalysis.structure.templateSimilarity > 60) {
      signals.push({
        type: 'template_match',
        value: contentAnalysis.structure.templateSimilarity,
        weight: 0.3,
        confidence: contentAnalysis.structure.templateSimilarity,
        description: 'Email matches common cold email templates'
      });
    }
    
    // Keyword signals
    const salesKeywordCount = contentAnalysis.keywords?.sales?.length || 0;
    if (salesKeywordCount > 2) {
      signals.push({
        type: 'sales_keywords',
        value: salesKeywordCount,
        weight: 0.25,
        confidence: Math.min(95, salesKeywordCount * 15),
        description: `Contains ${salesKeywordCount} sales-related keywords`
      });
    }
    
    // Mass mailing signal
    if (senderAnalysis.patterns?.massMailer) {
      signals.push({
        type: 'mass_mailing',
        value: 1,
        weight: 0.35,
        confidence: 85,
        description: 'Sender shows mass mailing patterns'
      });
    }
    
    return signals;
  }
  
  // Main detection method
  async detectColdEmail(email: ParsedMessage): Promise<Partial<ColdEmailDetection>> {
    // Analyze sender
    const senderAnalysis = this.analyzeSender(email);
    
    // Analyze content
    const contentAnalysis = this.analyzeContent(email);
    
    // Extract features for ML
    const features = this.extractFeatures(email, senderAnalysis, contentAnalysis);
    
    // Get ML prediction
    const mlScore = this.mlModel.predict(features);
    const mlProbability = Math.round(mlScore * 100);
    
    // Update content analysis with ML results
    if (contentAnalysis.mlAnalysis) {
      contentAnalysis.mlAnalysis.coldEmailProbability = mlProbability;
      contentAnalysis.mlAnalysis.spamProbability = 
        contentAnalysis.patterns?.isScam ? 80 : 
        contentAnalysis.patterns?.isPhishing ? 60 : 
        Math.round(mlProbability * 0.3);
      contentAnalysis.mlAnalysis.phishingProbability = 
        contentAnalysis.patterns?.isPhishing ? 70 + Math.random() * 30 : 
        Math.random() * 20;
    }
    
    // Generate detection signals
    const signals = this.generateSignals(email, senderAnalysis, contentAnalysis, mlScore);
    
    // Calculate overall confidence
    const confidence = Math.round(
      signals.reduce((sum, signal) => sum + (signal.confidence * signal.weight), 0) / 
      signals.reduce((sum, signal) => sum + signal.weight, 0)
    );
    
    // Determine detection methods used
    const methods = [];
    if (mlProbability > 50) methods.push('ml_model');
    if (senderAnalysis.domainInfo?.age && senderAnalysis.domainInfo.age < 90) methods.push('domain_check');
    if (contentAnalysis.structure?.templateSimilarity && contentAnalysis.structure.templateSimilarity > 60) methods.push('template_match');
    if (contentAnalysis.keywords?.sales && contentAnalysis.keywords.sales.length > 2) methods.push('keyword_match');
    
    return {
      confidence,
      detection: {
        method: methods as any,
        signals,
        mlScore: mlProbability,
        ruleMatches: [],
        explanation: this.generateExplanation(confidence, signals)
      },
      senderAnalysis: senderAnalysis as SenderAnalysis,
      contentAnalysis: contentAnalysis as ContentAnalysis
    };
  }
  
  // Generate human-readable explanation
  private generateExplanation(confidence: number, signals: DetectionSignal[]): string {
    const topSignals = signals
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 3);
    
    if (confidence > 80) {
      return `This email shows strong indicators of being a cold email. ${
        topSignals.map(s => s.description).join('. ')
      }. High confidence detection.`;
    } else if (confidence > 60) {
      return `This email has moderate cold email characteristics. ${
        topSignals.map(s => s.description).join('. ')
      }. Manual review recommended.`;
    } else if (confidence > 40) {
      return `This email shows some cold email patterns. ${
        topSignals.map(s => s.description).join('. ')
      }. Low confidence detection.`;
    } else {
      return 'This email has minimal cold email indicators but was flagged for review based on automated rules.';
    }
  }
  
  // Train the model with new data
  trainModel(trainingData: Array<{ email: ParsedMessage; isColdEmail: boolean }>) {
    const formattedData = trainingData.map(({ email, isColdEmail }) => {
      const senderAnalysis = this.analyzeSender(email);
      const contentAnalysis = this.analyzeContent(email);
      const features = this.extractFeatures(email, senderAnalysis, contentAnalysis);
      
      return {
        features,
        label: isColdEmail
      };
    });
    
    this.mlModel.improve(formattedData);
  }
}

// Singleton instance
export const coldEmailDetector = new ColdEmailDetector();