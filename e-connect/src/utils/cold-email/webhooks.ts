import type { ColdEmailDetection, ColdEmailSettings } from '../../types/cold-email';

export interface WebhookPayload {
  event: WebhookEvent;
  timestamp: Date;
  data: any;
  metadata?: Record<string, any>;
}

export type WebhookEvent = 
  | 'cold_email.detected'
  | 'cold_email.blocked'
  | 'cold_email.reviewed'
  | 'sender.blocked'
  | 'sender.whitelisted'
  | 'threshold.exceeded'
  | 'model.updated'
  | 'export.completed';

export class WebhookManager {
  private webhookUrl: string | null = null;
  private retryAttempts = 3;
  private retryDelay = 1000; // ms
  
  configure(settings: ColdEmailSettings) {
    if (settings.integration.webhooksEnabled && settings.integration.webhookUrl) {
      this.webhookUrl = settings.integration.webhookUrl;
    } else {
      this.webhookUrl = null;
    }
  }
  
  async send(event: WebhookEvent, data: any, metadata?: Record<string, any>) {
    if (!this.webhookUrl) return;
    
    const payload: WebhookPayload = {
      event,
      timestamp: new Date(),
      data,
      metadata
    };
    
    let attempts = 0;
    while (attempts < this.retryAttempts) {
      try {
        const response = await fetch(this.webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Webhook-Event': event,
            'X-Webhook-Timestamp': payload.timestamp.toISOString()
          },
          body: JSON.stringify(payload)
        });
        
        if (response.ok) {
          console.log(`Webhook sent successfully: ${event}`);
          return;
        }
        
        // Log error but don't throw
        console.error(`Webhook failed with status ${response.status}`);
      } catch (error) {
        console.error('Webhook send error:', error);
      }
      
      attempts++;
      if (attempts < this.retryAttempts) {
        await this.delay(this.retryDelay * attempts);
      }
    }
  }
  
  // Specific event methods
  async notifyDetection(detection: ColdEmailDetection) {
    await this.send('cold_email.detected', {
      detectionId: detection.id,
      emailId: detection.emailId,
      confidence: detection.confidence,
      sender: detection.email.from,
      subject: detection.email.subject,
      detectionMethods: detection.detection.method
    });
  }
  
  async notifyBlocked(detection: ColdEmailDetection) {
    await this.send('cold_email.blocked', {
      detectionId: detection.id,
      sender: detection.email.from,
      domain: detection.email.domain,
      confidence: detection.confidence,
      autoBlocked: true
    });
  }
  
  async notifyReviewed(
    detectionId: string, 
    decision: 'block' | 'allow' | 'whitelist',
    reviewer: string
  ) {
    await this.send('cold_email.reviewed', {
      detectionId,
      decision,
      reviewer,
      timestamp: new Date()
    });
  }
  
  async notifySenderBlocked(sender: string, domain: string, reason: string) {
    await this.send('sender.blocked', {
      sender,
      domain,
      reason,
      timestamp: new Date()
    });
  }
  
  async notifySenderWhitelisted(sender: string, domain: string, reason: string) {
    await this.send('sender.whitelisted', {
      sender,
      domain,
      reason,
      timestamp: new Date()
    });
  }
  
  async notifyThresholdExceeded(metric: string, value: number, threshold: number) {
    await this.send('threshold.exceeded', {
      metric,
      value,
      threshold,
      exceededBy: value - threshold,
      timestamp: new Date()
    }, {
      alert: true,
      severity: 'high'
    });
  }
  
  async notifyModelUpdated(version: string, accuracy: number, improvements: any) {
    await this.send('model.updated', {
      version,
      accuracy,
      improvements,
      timestamp: new Date()
    });
  }
  
  async notifyExportCompleted(exportId: string, format: string, recordCount: number) {
    await this.send('export.completed', {
      exportId,
      format,
      recordCount,
      timestamp: new Date()
    });
  }
  
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Singleton instance
export const webhookManager = new WebhookManager();