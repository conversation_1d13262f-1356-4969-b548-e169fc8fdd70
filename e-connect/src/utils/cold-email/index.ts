export * from './detection';
export * from './webhooks';

import { coldEmailDetector } from './detection';
import { webhookManager } from './webhooks';
import type { ParsedMessage } from '../../types/email';
import type { ColdEmailDetection, ColdEmailSettings } from '../../types/cold-email';

// High-level API for cold email detection
export async function detectAndProcessColdEmail(
  email: ParsedMessage,
  settings: ColdEmailSettings
): Promise<{
  detection: ColdEmailDetection | null;
  action: 'blocked' | 'quarantined' | 'allowed';
  reason: string;
}> {
  try {
    // Run detection
    const detectionResult = await coldEmailDetector.detectColdEmail(email);
    
    if (!detectionResult.confidence || detectionResult.confidence < 40) {
      return {
        detection: null,
        action: 'allowed',
        reason: 'Low confidence - email allowed'
      };
    }
    
    // Create full detection object
    const detection: ColdEmailDetection = {
      id: `detection-${Date.now()}`,
      emailId: email.id,
      threadId: email.threadId,
      detectedAt: new Date(),
      confidence: detectionResult.confidence,
      status: 'pending',
      detection: detectionResult.detection!,
      email: {
        from: email.from.email,
        senderName: email.from.name,
        domain: email.from.email.split('@')[1],
        subject: email.subject,
        snippet: email.snippet,
        receivedDate: email.receivedDate,
        hasAttachments: email.attachments.length > 0,
        size: email.size
      },
      senderAnalysis: detectionResult.senderAnalysis!,
      contentAnalysis: detectionResult.contentAnalysis!,
      actions: {}
    };
    
    // Send webhook notification
    await webhookManager.notifyDetection(detection);
    
    // Determine action based on settings
    let action: 'blocked' | 'quarantined' | 'allowed' = 'allowed';
    let reason = '';
    
    if (settings.actions.autoBlock && detection.confidence >= settings.actions.autoBlockThreshold) {
      action = 'blocked';
      reason = `Auto-blocked: confidence ${detection.confidence}% exceeds threshold ${settings.actions.autoBlockThreshold}%`;
      detection.status = 'blocked';
      detection.actions = { blocked: true };
      await webhookManager.notifyBlocked(detection);
    } else if (settings.actions.requireReview) {
      action = 'quarantined';
      reason = `Quarantined for review: confidence ${detection.confidence}%`;
      detection.status = 'pending';
    } else {
      action = 'allowed';
      reason = `Allowed: manual review not required, confidence ${detection.confidence}%`;
      detection.status = 'allowed';
    }
    
    return {
      detection,
      action,
      reason
    };
  } catch (error) {
    console.error('Cold email detection error:', error);
    return {
      detection: null,
      action: 'allowed',
      reason: 'Detection error - email allowed by default'
    };
  }
}

// Batch detection for multiple emails
export async function batchDetectColdEmails(
  emails: ParsedMessage[],
  settings: ColdEmailSettings,
  onProgress?: (processed: number, total: number) => void
): Promise<Array<{
  email: ParsedMessage;
  detection: ColdEmailDetection | null;
  action: 'blocked' | 'quarantined' | 'allowed';
}>> {
  const results = [];
  
  for (let i = 0; i < emails.length; i++) {
    const result = await detectAndProcessColdEmail(emails[i], settings);
    results.push({
      email: emails[i],
      detection: result.detection,
      action: result.action
    });
    
    if (onProgress) {
      onProgress(i + 1, emails.length);
    }
    
    // Small delay to avoid overwhelming the system
    if (i < emails.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 50));
    }
  }
  
  return results;
}

// Check if sender is whitelisted or blocked
export function checkSenderStatus(
  email: string,
  domain: string,
  blockedSenders: Array<{ email?: string; domain?: string; pattern?: string }>,
  whitelistedSenders: Array<{ email?: string; domain?: string; pattern?: string }>
): {
  isBlocked: boolean;
  isWhitelisted: boolean;
  matchedRule?: any;
} {
  // Check whitelist first (whitelist takes precedence)
  for (const sender of whitelistedSenders) {
    if (sender.email && sender.email === email) {
      return { isBlocked: false, isWhitelisted: true, matchedRule: sender };
    }
    if (sender.domain && domain === sender.domain) {
      return { isBlocked: false, isWhitelisted: true, matchedRule: sender };
    }
    if (sender.pattern) {
      try {
        const regex = new RegExp(sender.pattern);
        if (regex.test(email)) {
          return { isBlocked: false, isWhitelisted: true, matchedRule: sender };
        }
      } catch (e) {
        console.error('Invalid pattern:', sender.pattern);
      }
    }
  }
  
  // Check blocklist
  for (const sender of blockedSenders) {
    if (sender.email && sender.email === email) {
      return { isBlocked: true, isWhitelisted: false, matchedRule: sender };
    }
    if (sender.domain && domain === sender.domain) {
      return { isBlocked: true, isWhitelisted: false, matchedRule: sender };
    }
    if (sender.pattern) {
      try {
        const regex = new RegExp(sender.pattern);
        if (regex.test(email)) {
          return { isBlocked: true, isWhitelisted: false, matchedRule: sender };
        }
      } catch (e) {
        console.error('Invalid pattern:', sender.pattern);
      }
    }
  }
  
  return { isBlocked: false, isWhitelisted: false };
}

// Generate training data from user feedback
export function generateTrainingData(
  detection: ColdEmailDetection,
  userDecision: 'block' | 'allow' | 'whitelist',
  userFeedback?: {
    wasCorrect: boolean;
    actualType?: string;
    comments?: string;
  }
): any {
  const isColdEmail = userDecision === 'block';
  const wasCorrect = userFeedback?.wasCorrect ?? 
    ((isColdEmail && detection.confidence > 60) || 
    (!isColdEmail && detection.confidence < 40));
  
  return {
    id: `training-${Date.now()}`,
    emailId: detection.emailId,
    features: {
      senderDomainAge: detection.senderAnalysis.domainInfo?.age || 0,
      senderReputation: detection.senderAnalysis.reputation.score,
      authenticationScore: 
        detection.senderAnalysis.authentication.spf.status === 'pass' ? 100 : 0,
      sendingFrequency: 
        detection.senderAnalysis.patterns.sendingFrequency === 'high' ? 3 : 
        detection.senderAnalysis.patterns.sendingFrequency === 'medium' ? 2 : 1,
      recipientCount: detection.senderAnalysis.statistics.recipientCount,
      subjectLength: detection.email.subject.length,
      bodyLength: detection.email.snippet.length * 10, // Estimate
      linkCount: detection.contentAnalysis.links?.length || 0,
      imageCount: detection.contentAnalysis.images?.length || 0,
      attachmentCount: detection.email.hasAttachments ? 1 : 0,
      hasUnsubscribe: detection.contentAnalysis.structure.hasUnsubscribeLink,
      hasTracking: detection.contentAnalysis.links?.some(l => l.isTracking) || false,
      templateScore: detection.contentAnalysis.structure.templateSimilarity,
      personalizedScore: 50, // Default
      salesKeywords: detection.contentAnalysis.keywords.sales.length,
      urgencyKeywords: detection.contentAnalysis.keywords.urgency.length,
      suspiciousKeywords: detection.contentAnalysis.keywords.suspicious.length,
      timeSinceLast: 24,
      engagementHistory: detection.senderAnalysis.statistics.engagementRate,
      reportHistory: detection.senderAnalysis.statistics.reportCount
    },
    label: isColdEmail ? 'cold_email' : 'legitimate',
    confidence: wasCorrect ? 1 : 0,
    source: 'user_feedback',
    createdAt: new Date(),
    createdBy: '<EMAIL>',
    feedback: userFeedback
  };
}