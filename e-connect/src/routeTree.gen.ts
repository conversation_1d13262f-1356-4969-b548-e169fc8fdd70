/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as SettingsRouteImport } from './routes/settings'
import { Route as RulesRouteImport } from './routes/rules'
import { Route as ChatRouteImport } from './routes/chat'
import { Route as AboutRouteImport } from './routes/about'
import { Route as IndexRouteImport } from './routes/index'
import { Route as StatsIndexRouteImport } from './routes/stats/index'
import { Route as SettingsIndexRouteImport } from './routes/settings/index'
import { Route as MailIndexRouteImport } from './routes/mail/index'
import { Route as ColdEmailBlockerIndexRouteImport } from './routes/cold-email-blocker/index'
import { Route as CleanIndexRouteImport } from './routes/clean/index'
import { Route as BulkUnsubscribeIndexRouteImport } from './routes/bulk-unsubscribe/index'
import { Route as AutomationIndexRouteImport } from './routes/automation/index'
import { Route as AssistantIndexRouteImport } from './routes/assistant/index'
import { Route as StatsSendersRouteImport } from './routes/stats/senders'
import { Route as StatsRulesRouteImport } from './routes/stats/rules'
import { Route as StatsEmailsRouteImport } from './routes/stats/emails'
import { Route as SettingsPreferencesRouteImport } from './routes/settings/preferences'
import { Route as SettingsIntegrationsRouteImport } from './routes/settings/integrations'
import { Route as SettingsAdvancedRouteImport } from './routes/settings/advanced'
import { Route as SettingsAccountRouteImport } from './routes/settings/account'
import { Route as MailComposeRouteImport } from './routes/mail/compose'
import { Route as ColdEmailBlockerSettingsRouteImport } from './routes/cold-email-blocker/settings'
import { Route as ColdEmailBlockerBlockedRouteImport } from './routes/cold-email-blocker/blocked'
import { Route as CleanRunRouteImport } from './routes/clean/run'
import { Route as CleanOnboardingRouteImport } from './routes/clean/onboarding'
import { Route as CleanHistoryRouteImport } from './routes/clean/history'
import { Route as BulkUnsubscribeProgressRouteImport } from './routes/bulk-unsubscribe/progress'
import { Route as AutomationTestRouteImport } from './routes/automation/test'
import { Route as AutomationRulesRouteImport } from './routes/automation/rules'
import { Route as AutomationHistoryRouteImport } from './routes/automation/history'
import { Route as AutomationCreateRouteImport } from './routes/automation/create'
import { Route as AssistantOnboardingRouteImport } from './routes/assistant/onboarding'
import { Route as AssistantKnowledgeRouteImport } from './routes/assistant/knowledge'
import { Route as MailThreadThreadIdRouteImport } from './routes/mail/thread.$threadId'

const SettingsRoute = SettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRouteImport,
} as any)
const RulesRoute = RulesRouteImport.update({
  id: '/rules',
  path: '/rules',
  getParentRoute: () => rootRouteImport,
} as any)
const ChatRoute = ChatRouteImport.update({
  id: '/chat',
  path: '/chat',
  getParentRoute: () => rootRouteImport,
} as any)
const AboutRoute = AboutRouteImport.update({
  id: '/about',
  path: '/about',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const StatsIndexRoute = StatsIndexRouteImport.update({
  id: '/stats/',
  path: '/stats/',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingsIndexRoute = SettingsIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => SettingsRoute,
} as any)
const MailIndexRoute = MailIndexRouteImport.update({
  id: '/mail/',
  path: '/mail/',
  getParentRoute: () => rootRouteImport,
} as any)
const ColdEmailBlockerIndexRoute = ColdEmailBlockerIndexRouteImport.update({
  id: '/cold-email-blocker/',
  path: '/cold-email-blocker/',
  getParentRoute: () => rootRouteImport,
} as any)
const CleanIndexRoute = CleanIndexRouteImport.update({
  id: '/clean/',
  path: '/clean/',
  getParentRoute: () => rootRouteImport,
} as any)
const BulkUnsubscribeIndexRoute = BulkUnsubscribeIndexRouteImport.update({
  id: '/bulk-unsubscribe/',
  path: '/bulk-unsubscribe/',
  getParentRoute: () => rootRouteImport,
} as any)
const AutomationIndexRoute = AutomationIndexRouteImport.update({
  id: '/automation/',
  path: '/automation/',
  getParentRoute: () => rootRouteImport,
} as any)
const AssistantIndexRoute = AssistantIndexRouteImport.update({
  id: '/assistant/',
  path: '/assistant/',
  getParentRoute: () => rootRouteImport,
} as any)
const StatsSendersRoute = StatsSendersRouteImport.update({
  id: '/stats/senders',
  path: '/stats/senders',
  getParentRoute: () => rootRouteImport,
} as any)
const StatsRulesRoute = StatsRulesRouteImport.update({
  id: '/stats/rules',
  path: '/stats/rules',
  getParentRoute: () => rootRouteImport,
} as any)
const StatsEmailsRoute = StatsEmailsRouteImport.update({
  id: '/stats/emails',
  path: '/stats/emails',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingsPreferencesRoute = SettingsPreferencesRouteImport.update({
  id: '/preferences',
  path: '/preferences',
  getParentRoute: () => SettingsRoute,
} as any)
const SettingsIntegrationsRoute = SettingsIntegrationsRouteImport.update({
  id: '/integrations',
  path: '/integrations',
  getParentRoute: () => SettingsRoute,
} as any)
const SettingsAdvancedRoute = SettingsAdvancedRouteImport.update({
  id: '/advanced',
  path: '/advanced',
  getParentRoute: () => SettingsRoute,
} as any)
const SettingsAccountRoute = SettingsAccountRouteImport.update({
  id: '/account',
  path: '/account',
  getParentRoute: () => SettingsRoute,
} as any)
const MailComposeRoute = MailComposeRouteImport.update({
  id: '/mail/compose',
  path: '/mail/compose',
  getParentRoute: () => rootRouteImport,
} as any)
const ColdEmailBlockerSettingsRoute =
  ColdEmailBlockerSettingsRouteImport.update({
    id: '/cold-email-blocker/settings',
    path: '/cold-email-blocker/settings',
    getParentRoute: () => rootRouteImport,
  } as any)
const ColdEmailBlockerBlockedRoute = ColdEmailBlockerBlockedRouteImport.update({
  id: '/cold-email-blocker/blocked',
  path: '/cold-email-blocker/blocked',
  getParentRoute: () => rootRouteImport,
} as any)
const CleanRunRoute = CleanRunRouteImport.update({
  id: '/clean/run',
  path: '/clean/run',
  getParentRoute: () => rootRouteImport,
} as any)
const CleanOnboardingRoute = CleanOnboardingRouteImport.update({
  id: '/clean/onboarding',
  path: '/clean/onboarding',
  getParentRoute: () => rootRouteImport,
} as any)
const CleanHistoryRoute = CleanHistoryRouteImport.update({
  id: '/clean/history',
  path: '/clean/history',
  getParentRoute: () => rootRouteImport,
} as any)
const BulkUnsubscribeProgressRoute = BulkUnsubscribeProgressRouteImport.update({
  id: '/bulk-unsubscribe/progress',
  path: '/bulk-unsubscribe/progress',
  getParentRoute: () => rootRouteImport,
} as any)
const AutomationTestRoute = AutomationTestRouteImport.update({
  id: '/automation/test',
  path: '/automation/test',
  getParentRoute: () => rootRouteImport,
} as any)
const AutomationRulesRoute = AutomationRulesRouteImport.update({
  id: '/automation/rules',
  path: '/automation/rules',
  getParentRoute: () => rootRouteImport,
} as any)
const AutomationHistoryRoute = AutomationHistoryRouteImport.update({
  id: '/automation/history',
  path: '/automation/history',
  getParentRoute: () => rootRouteImport,
} as any)
const AutomationCreateRoute = AutomationCreateRouteImport.update({
  id: '/automation/create',
  path: '/automation/create',
  getParentRoute: () => rootRouteImport,
} as any)
const AssistantOnboardingRoute = AssistantOnboardingRouteImport.update({
  id: '/assistant/onboarding',
  path: '/assistant/onboarding',
  getParentRoute: () => rootRouteImport,
} as any)
const AssistantKnowledgeRoute = AssistantKnowledgeRouteImport.update({
  id: '/assistant/knowledge',
  path: '/assistant/knowledge',
  getParentRoute: () => rootRouteImport,
} as any)
const MailThreadThreadIdRoute = MailThreadThreadIdRouteImport.update({
  id: '/mail/thread/$threadId',
  path: '/mail/thread/$threadId',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/chat': typeof ChatRoute
  '/rules': typeof RulesRoute
  '/settings': typeof SettingsRouteWithChildren
  '/assistant/knowledge': typeof AssistantKnowledgeRoute
  '/assistant/onboarding': typeof AssistantOnboardingRoute
  '/automation/create': typeof AutomationCreateRoute
  '/automation/history': typeof AutomationHistoryRoute
  '/automation/rules': typeof AutomationRulesRoute
  '/automation/test': typeof AutomationTestRoute
  '/bulk-unsubscribe/progress': typeof BulkUnsubscribeProgressRoute
  '/clean/history': typeof CleanHistoryRoute
  '/clean/onboarding': typeof CleanOnboardingRoute
  '/clean/run': typeof CleanRunRoute
  '/cold-email-blocker/blocked': typeof ColdEmailBlockerBlockedRoute
  '/cold-email-blocker/settings': typeof ColdEmailBlockerSettingsRoute
  '/mail/compose': typeof MailComposeRoute
  '/settings/account': typeof SettingsAccountRoute
  '/settings/advanced': typeof SettingsAdvancedRoute
  '/settings/integrations': typeof SettingsIntegrationsRoute
  '/settings/preferences': typeof SettingsPreferencesRoute
  '/stats/emails': typeof StatsEmailsRoute
  '/stats/rules': typeof StatsRulesRoute
  '/stats/senders': typeof StatsSendersRoute
  '/assistant': typeof AssistantIndexRoute
  '/automation': typeof AutomationIndexRoute
  '/bulk-unsubscribe': typeof BulkUnsubscribeIndexRoute
  '/clean': typeof CleanIndexRoute
  '/cold-email-blocker': typeof ColdEmailBlockerIndexRoute
  '/mail': typeof MailIndexRoute
  '/settings/': typeof SettingsIndexRoute
  '/stats': typeof StatsIndexRoute
  '/mail/thread/$threadId': typeof MailThreadThreadIdRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/chat': typeof ChatRoute
  '/rules': typeof RulesRoute
  '/assistant/knowledge': typeof AssistantKnowledgeRoute
  '/assistant/onboarding': typeof AssistantOnboardingRoute
  '/automation/create': typeof AutomationCreateRoute
  '/automation/history': typeof AutomationHistoryRoute
  '/automation/rules': typeof AutomationRulesRoute
  '/automation/test': typeof AutomationTestRoute
  '/bulk-unsubscribe/progress': typeof BulkUnsubscribeProgressRoute
  '/clean/history': typeof CleanHistoryRoute
  '/clean/onboarding': typeof CleanOnboardingRoute
  '/clean/run': typeof CleanRunRoute
  '/cold-email-blocker/blocked': typeof ColdEmailBlockerBlockedRoute
  '/cold-email-blocker/settings': typeof ColdEmailBlockerSettingsRoute
  '/mail/compose': typeof MailComposeRoute
  '/settings/account': typeof SettingsAccountRoute
  '/settings/advanced': typeof SettingsAdvancedRoute
  '/settings/integrations': typeof SettingsIntegrationsRoute
  '/settings/preferences': typeof SettingsPreferencesRoute
  '/stats/emails': typeof StatsEmailsRoute
  '/stats/rules': typeof StatsRulesRoute
  '/stats/senders': typeof StatsSendersRoute
  '/assistant': typeof AssistantIndexRoute
  '/automation': typeof AutomationIndexRoute
  '/bulk-unsubscribe': typeof BulkUnsubscribeIndexRoute
  '/clean': typeof CleanIndexRoute
  '/cold-email-blocker': typeof ColdEmailBlockerIndexRoute
  '/mail': typeof MailIndexRoute
  '/settings': typeof SettingsIndexRoute
  '/stats': typeof StatsIndexRoute
  '/mail/thread/$threadId': typeof MailThreadThreadIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/about': typeof AboutRoute
  '/chat': typeof ChatRoute
  '/rules': typeof RulesRoute
  '/settings': typeof SettingsRouteWithChildren
  '/assistant/knowledge': typeof AssistantKnowledgeRoute
  '/assistant/onboarding': typeof AssistantOnboardingRoute
  '/automation/create': typeof AutomationCreateRoute
  '/automation/history': typeof AutomationHistoryRoute
  '/automation/rules': typeof AutomationRulesRoute
  '/automation/test': typeof AutomationTestRoute
  '/bulk-unsubscribe/progress': typeof BulkUnsubscribeProgressRoute
  '/clean/history': typeof CleanHistoryRoute
  '/clean/onboarding': typeof CleanOnboardingRoute
  '/clean/run': typeof CleanRunRoute
  '/cold-email-blocker/blocked': typeof ColdEmailBlockerBlockedRoute
  '/cold-email-blocker/settings': typeof ColdEmailBlockerSettingsRoute
  '/mail/compose': typeof MailComposeRoute
  '/settings/account': typeof SettingsAccountRoute
  '/settings/advanced': typeof SettingsAdvancedRoute
  '/settings/integrations': typeof SettingsIntegrationsRoute
  '/settings/preferences': typeof SettingsPreferencesRoute
  '/stats/emails': typeof StatsEmailsRoute
  '/stats/rules': typeof StatsRulesRoute
  '/stats/senders': typeof StatsSendersRoute
  '/assistant/': typeof AssistantIndexRoute
  '/automation/': typeof AutomationIndexRoute
  '/bulk-unsubscribe/': typeof BulkUnsubscribeIndexRoute
  '/clean/': typeof CleanIndexRoute
  '/cold-email-blocker/': typeof ColdEmailBlockerIndexRoute
  '/mail/': typeof MailIndexRoute
  '/settings/': typeof SettingsIndexRoute
  '/stats/': typeof StatsIndexRoute
  '/mail/thread/$threadId': typeof MailThreadThreadIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/about'
    | '/chat'
    | '/rules'
    | '/settings'
    | '/assistant/knowledge'
    | '/assistant/onboarding'
    | '/automation/create'
    | '/automation/history'
    | '/automation/rules'
    | '/automation/test'
    | '/bulk-unsubscribe/progress'
    | '/clean/history'
    | '/clean/onboarding'
    | '/clean/run'
    | '/cold-email-blocker/blocked'
    | '/cold-email-blocker/settings'
    | '/mail/compose'
    | '/settings/account'
    | '/settings/advanced'
    | '/settings/integrations'
    | '/settings/preferences'
    | '/stats/emails'
    | '/stats/rules'
    | '/stats/senders'
    | '/assistant'
    | '/automation'
    | '/bulk-unsubscribe'
    | '/clean'
    | '/cold-email-blocker'
    | '/mail'
    | '/settings/'
    | '/stats'
    | '/mail/thread/$threadId'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/about'
    | '/chat'
    | '/rules'
    | '/assistant/knowledge'
    | '/assistant/onboarding'
    | '/automation/create'
    | '/automation/history'
    | '/automation/rules'
    | '/automation/test'
    | '/bulk-unsubscribe/progress'
    | '/clean/history'
    | '/clean/onboarding'
    | '/clean/run'
    | '/cold-email-blocker/blocked'
    | '/cold-email-blocker/settings'
    | '/mail/compose'
    | '/settings/account'
    | '/settings/advanced'
    | '/settings/integrations'
    | '/settings/preferences'
    | '/stats/emails'
    | '/stats/rules'
    | '/stats/senders'
    | '/assistant'
    | '/automation'
    | '/bulk-unsubscribe'
    | '/clean'
    | '/cold-email-blocker'
    | '/mail'
    | '/settings'
    | '/stats'
    | '/mail/thread/$threadId'
  id:
    | '__root__'
    | '/'
    | '/about'
    | '/chat'
    | '/rules'
    | '/settings'
    | '/assistant/knowledge'
    | '/assistant/onboarding'
    | '/automation/create'
    | '/automation/history'
    | '/automation/rules'
    | '/automation/test'
    | '/bulk-unsubscribe/progress'
    | '/clean/history'
    | '/clean/onboarding'
    | '/clean/run'
    | '/cold-email-blocker/blocked'
    | '/cold-email-blocker/settings'
    | '/mail/compose'
    | '/settings/account'
    | '/settings/advanced'
    | '/settings/integrations'
    | '/settings/preferences'
    | '/stats/emails'
    | '/stats/rules'
    | '/stats/senders'
    | '/assistant/'
    | '/automation/'
    | '/bulk-unsubscribe/'
    | '/clean/'
    | '/cold-email-blocker/'
    | '/mail/'
    | '/settings/'
    | '/stats/'
    | '/mail/thread/$threadId'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AboutRoute: typeof AboutRoute
  ChatRoute: typeof ChatRoute
  RulesRoute: typeof RulesRoute
  SettingsRoute: typeof SettingsRouteWithChildren
  AssistantKnowledgeRoute: typeof AssistantKnowledgeRoute
  AssistantOnboardingRoute: typeof AssistantOnboardingRoute
  AutomationCreateRoute: typeof AutomationCreateRoute
  AutomationHistoryRoute: typeof AutomationHistoryRoute
  AutomationRulesRoute: typeof AutomationRulesRoute
  AutomationTestRoute: typeof AutomationTestRoute
  BulkUnsubscribeProgressRoute: typeof BulkUnsubscribeProgressRoute
  CleanHistoryRoute: typeof CleanHistoryRoute
  CleanOnboardingRoute: typeof CleanOnboardingRoute
  CleanRunRoute: typeof CleanRunRoute
  ColdEmailBlockerBlockedRoute: typeof ColdEmailBlockerBlockedRoute
  ColdEmailBlockerSettingsRoute: typeof ColdEmailBlockerSettingsRoute
  MailComposeRoute: typeof MailComposeRoute
  StatsEmailsRoute: typeof StatsEmailsRoute
  StatsRulesRoute: typeof StatsRulesRoute
  StatsSendersRoute: typeof StatsSendersRoute
  AssistantIndexRoute: typeof AssistantIndexRoute
  AutomationIndexRoute: typeof AutomationIndexRoute
  BulkUnsubscribeIndexRoute: typeof BulkUnsubscribeIndexRoute
  CleanIndexRoute: typeof CleanIndexRoute
  ColdEmailBlockerIndexRoute: typeof ColdEmailBlockerIndexRoute
  MailIndexRoute: typeof MailIndexRoute
  StatsIndexRoute: typeof StatsIndexRoute
  MailThreadThreadIdRoute: typeof MailThreadThreadIdRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/rules': {
      id: '/rules'
      path: '/rules'
      fullPath: '/rules'
      preLoaderRoute: typeof RulesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/chat': {
      id: '/chat'
      path: '/chat'
      fullPath: '/chat'
      preLoaderRoute: typeof ChatRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/about': {
      id: '/about'
      path: '/about'
      fullPath: '/about'
      preLoaderRoute: typeof AboutRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/stats/': {
      id: '/stats/'
      path: '/stats'
      fullPath: '/stats'
      preLoaderRoute: typeof StatsIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings/': {
      id: '/settings/'
      path: '/'
      fullPath: '/settings/'
      preLoaderRoute: typeof SettingsIndexRouteImport
      parentRoute: typeof SettingsRoute
    }
    '/mail/': {
      id: '/mail/'
      path: '/mail'
      fullPath: '/mail'
      preLoaderRoute: typeof MailIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/cold-email-blocker/': {
      id: '/cold-email-blocker/'
      path: '/cold-email-blocker'
      fullPath: '/cold-email-blocker'
      preLoaderRoute: typeof ColdEmailBlockerIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/clean/': {
      id: '/clean/'
      path: '/clean'
      fullPath: '/clean'
      preLoaderRoute: typeof CleanIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/bulk-unsubscribe/': {
      id: '/bulk-unsubscribe/'
      path: '/bulk-unsubscribe'
      fullPath: '/bulk-unsubscribe'
      preLoaderRoute: typeof BulkUnsubscribeIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/automation/': {
      id: '/automation/'
      path: '/automation'
      fullPath: '/automation'
      preLoaderRoute: typeof AutomationIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/assistant/': {
      id: '/assistant/'
      path: '/assistant'
      fullPath: '/assistant'
      preLoaderRoute: typeof AssistantIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/stats/senders': {
      id: '/stats/senders'
      path: '/stats/senders'
      fullPath: '/stats/senders'
      preLoaderRoute: typeof StatsSendersRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/stats/rules': {
      id: '/stats/rules'
      path: '/stats/rules'
      fullPath: '/stats/rules'
      preLoaderRoute: typeof StatsRulesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/stats/emails': {
      id: '/stats/emails'
      path: '/stats/emails'
      fullPath: '/stats/emails'
      preLoaderRoute: typeof StatsEmailsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings/preferences': {
      id: '/settings/preferences'
      path: '/preferences'
      fullPath: '/settings/preferences'
      preLoaderRoute: typeof SettingsPreferencesRouteImport
      parentRoute: typeof SettingsRoute
    }
    '/settings/integrations': {
      id: '/settings/integrations'
      path: '/integrations'
      fullPath: '/settings/integrations'
      preLoaderRoute: typeof SettingsIntegrationsRouteImport
      parentRoute: typeof SettingsRoute
    }
    '/settings/advanced': {
      id: '/settings/advanced'
      path: '/advanced'
      fullPath: '/settings/advanced'
      preLoaderRoute: typeof SettingsAdvancedRouteImport
      parentRoute: typeof SettingsRoute
    }
    '/settings/account': {
      id: '/settings/account'
      path: '/account'
      fullPath: '/settings/account'
      preLoaderRoute: typeof SettingsAccountRouteImport
      parentRoute: typeof SettingsRoute
    }
    '/mail/compose': {
      id: '/mail/compose'
      path: '/mail/compose'
      fullPath: '/mail/compose'
      preLoaderRoute: typeof MailComposeRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/cold-email-blocker/settings': {
      id: '/cold-email-blocker/settings'
      path: '/cold-email-blocker/settings'
      fullPath: '/cold-email-blocker/settings'
      preLoaderRoute: typeof ColdEmailBlockerSettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/cold-email-blocker/blocked': {
      id: '/cold-email-blocker/blocked'
      path: '/cold-email-blocker/blocked'
      fullPath: '/cold-email-blocker/blocked'
      preLoaderRoute: typeof ColdEmailBlockerBlockedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/clean/run': {
      id: '/clean/run'
      path: '/clean/run'
      fullPath: '/clean/run'
      preLoaderRoute: typeof CleanRunRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/clean/onboarding': {
      id: '/clean/onboarding'
      path: '/clean/onboarding'
      fullPath: '/clean/onboarding'
      preLoaderRoute: typeof CleanOnboardingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/clean/history': {
      id: '/clean/history'
      path: '/clean/history'
      fullPath: '/clean/history'
      preLoaderRoute: typeof CleanHistoryRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/bulk-unsubscribe/progress': {
      id: '/bulk-unsubscribe/progress'
      path: '/bulk-unsubscribe/progress'
      fullPath: '/bulk-unsubscribe/progress'
      preLoaderRoute: typeof BulkUnsubscribeProgressRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/automation/test': {
      id: '/automation/test'
      path: '/automation/test'
      fullPath: '/automation/test'
      preLoaderRoute: typeof AutomationTestRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/automation/rules': {
      id: '/automation/rules'
      path: '/automation/rules'
      fullPath: '/automation/rules'
      preLoaderRoute: typeof AutomationRulesRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/automation/history': {
      id: '/automation/history'
      path: '/automation/history'
      fullPath: '/automation/history'
      preLoaderRoute: typeof AutomationHistoryRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/automation/create': {
      id: '/automation/create'
      path: '/automation/create'
      fullPath: '/automation/create'
      preLoaderRoute: typeof AutomationCreateRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/assistant/onboarding': {
      id: '/assistant/onboarding'
      path: '/assistant/onboarding'
      fullPath: '/assistant/onboarding'
      preLoaderRoute: typeof AssistantOnboardingRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/assistant/knowledge': {
      id: '/assistant/knowledge'
      path: '/assistant/knowledge'
      fullPath: '/assistant/knowledge'
      preLoaderRoute: typeof AssistantKnowledgeRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/mail/thread/$threadId': {
      id: '/mail/thread/$threadId'
      path: '/mail/thread/$threadId'
      fullPath: '/mail/thread/$threadId'
      preLoaderRoute: typeof MailThreadThreadIdRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

interface SettingsRouteChildren {
  SettingsAccountRoute: typeof SettingsAccountRoute
  SettingsAdvancedRoute: typeof SettingsAdvancedRoute
  SettingsIntegrationsRoute: typeof SettingsIntegrationsRoute
  SettingsPreferencesRoute: typeof SettingsPreferencesRoute
  SettingsIndexRoute: typeof SettingsIndexRoute
}

const SettingsRouteChildren: SettingsRouteChildren = {
  SettingsAccountRoute: SettingsAccountRoute,
  SettingsAdvancedRoute: SettingsAdvancedRoute,
  SettingsIntegrationsRoute: SettingsIntegrationsRoute,
  SettingsPreferencesRoute: SettingsPreferencesRoute,
  SettingsIndexRoute: SettingsIndexRoute,
}

const SettingsRouteWithChildren = SettingsRoute._addFileChildren(
  SettingsRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AboutRoute: AboutRoute,
  ChatRoute: ChatRoute,
  RulesRoute: RulesRoute,
  SettingsRoute: SettingsRouteWithChildren,
  AssistantKnowledgeRoute: AssistantKnowledgeRoute,
  AssistantOnboardingRoute: AssistantOnboardingRoute,
  AutomationCreateRoute: AutomationCreateRoute,
  AutomationHistoryRoute: AutomationHistoryRoute,
  AutomationRulesRoute: AutomationRulesRoute,
  AutomationTestRoute: AutomationTestRoute,
  BulkUnsubscribeProgressRoute: BulkUnsubscribeProgressRoute,
  CleanHistoryRoute: CleanHistoryRoute,
  CleanOnboardingRoute: CleanOnboardingRoute,
  CleanRunRoute: CleanRunRoute,
  ColdEmailBlockerBlockedRoute: ColdEmailBlockerBlockedRoute,
  ColdEmailBlockerSettingsRoute: ColdEmailBlockerSettingsRoute,
  MailComposeRoute: MailComposeRoute,
  StatsEmailsRoute: StatsEmailsRoute,
  StatsRulesRoute: StatsRulesRoute,
  StatsSendersRoute: StatsSendersRoute,
  AssistantIndexRoute: AssistantIndexRoute,
  AutomationIndexRoute: AutomationIndexRoute,
  BulkUnsubscribeIndexRoute: BulkUnsubscribeIndexRoute,
  CleanIndexRoute: CleanIndexRoute,
  ColdEmailBlockerIndexRoute: ColdEmailBlockerIndexRoute,
  MailIndexRoute: MailIndexRoute,
  StatsIndexRoute: StatsIndexRoute,
  MailThreadThreadIdRoute: MailThreadThreadIdRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
