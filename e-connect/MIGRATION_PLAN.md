# E-Connect-2 Migration Plan: Inbox-Zero to TanStack Router + MSW

## Overview

This document outlines the comprehensive migration plan to replicate all core inbox-zero features using TanStack Router, TanStack Query, and MSW for API mocking. The goal is to create a fully functional email management application that preserves all original functionality while leveraging modern React tooling.

## Project Architecture

```mermaid
graph TB
    subgraph "Frontend (TanStack Router + React)"
        A[Root Layout] --> B[Dashboard/Inbox]
        A --> C[AI Assistant/Automation]
        A --> D[Bulk Unsubscribe]
        A --> E[Clean Inbox]
        A --> F[Cold Email Blocker]
        A --> G[Settings]
        A --> H[Stats/Analytics]
    end
    
    subgraph "MSW Mock Layer"
        I[Email API Handlers]
        J[AI/Rules Handlers]
        K[Auth Handlers]
        L[Settings Handlers]
        M[Analytics Handlers]
    end
    
    subgraph "State Management"
        N[TanStack Query]
        O[Zustand/Jotai Stores]
        P[Local Storage]
    end
    
    B --> I
    C --> J
    D --> I
    E --> I
    F --> J
    G --> L
    H --> M
    
    I --> N
    J --> N
    K --> N
    L --> N
    M --> N
```

## Phase 1: Core Infrastructure Enhancement

### 1.1 Project Structure Reorganization

```
e-connect-2/
├── src/
│   ├── components/
│   │   ├── email-list/          # Email management components
│   │   │   ├── EmailList.tsx
│   │   │   ├── EmailListItem.tsx
│   │   │   ├── EmailPanel.tsx
│   │   │   ├── ThreadView.tsx
│   │   │   └── BulkActions.tsx
│   │   ├── assistant-chat/      # AI chat interface
│   │   │   ├── Chat.tsx
│   │   │   ├── ChatMessage.tsx
│   │   │   ├── ChatInput.tsx
│   │   │   ├── SuggestedActions.tsx
│   │   │   └── RuleBuilder.tsx
│   │   ├── rules/              # Rules management
│   │   │   ├── RulesList.tsx
│   │   │   ├── RuleForm.tsx
│   │   │   ├── RuleTest.tsx
│   │   │   └── RuleHistory.tsx
│   │   ├── bulk-actions/       # Bulk operations
│   │   │   ├── BulkUnsubscribe.tsx
│   │   │   ├── CleanInbox.tsx
│   │   │   ├── ColdEmailBlocker.tsx
│   │   │   └── ProgressTracker.tsx
│   │   ├── charts/             # Analytics charts
│   │   │   ├── EmailVolumeChart.tsx
│   │   │   ├── SenderChart.tsx
│   │   │   ├── CategoryChart.tsx
│   │   │   └── RuleMetrics.tsx
│   │   └── ui/                 # Shared UI components
│   │       ├── Button.tsx
│   │       ├── Card.tsx
│   │       ├── Input.tsx
│   │       ├── Modal.tsx
│   │       ├── Table.tsx
│   │       ├── Tabs.tsx
│   │       ├── Select.tsx
│   │       ├── Checkbox.tsx
│   │       ├── Badge.tsx
│   │       ├── Alert.tsx
│   │       ├── Loading.tsx
│   │       ├── Tooltip.tsx
│   │       ├── Dropdown.tsx
│   │       ├── DatePicker.tsx
│   │       ├── Progress.tsx
│   │       └── ResizablePanels.tsx
│   ├── routes/
│   │   ├── __root.tsx
│   │   ├── index.tsx           # Dashboard
│   │   ├── mail/               # Email routes
│   │   │   ├── index.tsx       # Main inbox
│   │   │   ├── thread.$threadId.tsx
│   │   │   └── compose.tsx
│   │   ├── assistant/          # AI assistant routes
│   │   │   ├── index.tsx       # Chat interface
│   │   │   ├── onboarding.tsx
│   │   │   └── knowledge.tsx
│   │   ├── automation/         # Rules automation
│   │   │   ├── index.tsx       # Rules dashboard
│   │   │   ├── rules.tsx       # Rules management
│   │   │   ├── test.tsx        # Rule testing
│   │   │   ├── history.tsx     # Execution history
│   │   │   └── create.tsx      # Create new rule
│   │   ├── bulk-unsubscribe/   # Bulk unsubscribe
│   │   │   ├── index.tsx
│   │   │   └── progress.tsx
│   │   ├── clean/              # Clean inbox
│   │   │   ├── index.tsx
│   │   │   ├── onboarding.tsx
│   │   │   ├── run.tsx
│   │   │   └── history.tsx
│   │   ├── cold-email-blocker/ # Cold email filtering
│   │   │   ├── index.tsx
│   │   │   ├── settings.tsx
│   │   │   └── blocked.tsx
│   │   ├── settings/           # User settings
│   │   │   ├── index.tsx
│   │   │   ├── account.tsx
│   │   │   ├── preferences.tsx
│   │   │   └── integrations.tsx
│   │   └── stats/              # Analytics
│   │       ├── index.tsx
│   │       ├── emails.tsx
│   │       ├── senders.tsx
│   │       └── rules.tsx
│   ├── mocks/
│   │   ├── handlers/           # Organized MSW handlers
│   │   │   ├── auth.ts
│   │   │   ├── emails.ts
│   │   │   ├── threads.ts
│   │   │   ├── rules.ts
│   │   │   ├── ai.ts
│   │   │   ├── bulk-actions.ts
│   │   │   ├── analytics.ts
│   │   │   └── settings.ts
│   │   ├── data/              # Mock data generators
│   │   │   ├── emails.ts
│   │   │   ├── users.ts
│   │   │   ├── rules.ts
│   │   │   ├── analytics.ts
│   │   │   └── generators.ts
│   │   └── browser.ts
│   ├── stores/                 # State management
│   │   ├── auth.ts
│   │   ├── email.ts
│   │   ├── rules.ts
│   │   ├── ui.ts
│   │   └── settings.ts
│   ├── utils/
│   │   ├── ai/                # AI utilities
│   │   │   ├── categorize.ts
│   │   │   ├── rules-engine.ts
│   │   │   ├── chat.ts
│   │   │   └── mock-responses.ts
│   │   ├── email/             # Email processing
│   │   │   ├── parser.ts
│   │   │   ├── filters.ts
│   │   │   ├── actions.ts
│   │   │   └── bulk-operations.ts
│   │   ├── rules/             # Rules engine
│   │   │   ├── matcher.ts
│   │   │   ├── executor.ts
│   │   │   ├── validator.ts
│   │   │   └── conditions.ts
│   │   ├── analytics/         # Analytics helpers
│   │   │   ├── metrics.ts
│   │   │   ├── charts.ts
│   │   │   └── aggregations.ts
│   │   ├── api.ts             # API client
│   │   ├── config.ts          # Configuration
│   │   ├── constants.ts       # Constants
│   │   ├── date.ts            # Date utilities
│   │   ├── email.ts           # Email utilities
│   │   ├── formatting.ts      # Formatting helpers
│   │   └── validation.ts      # Validation schemas
│   └── types/                 # TypeScript definitions
│       ├── api.ts
│       ├── email.ts
│       ├── rules.ts
│       ├── ai.ts
│       ├── analytics.ts
│       └── index.ts
```

### 1.2 Enhanced Dependencies

Add the following packages to support all features:

```json
{
  "dependencies": {
    "@tanstack/react-query": "^5.83.0",
    "@tanstack/react-router": "^1.125.6",
    "@tanstack/router-devtools": "^1.125.6",
    "zustand": "^4.5.0",
    "jotai": "^2.12.5",
    "react-hook-form": "^7.56.4",
    "@hookform/resolvers": "^5.0.1",
    "zod": "^3.25.46",
    "date-fns": "^4.1.0",
    "lucide-react": "^0.511.0",
    "recharts": "^2.12.7",
    "react-resizable-panels": "^2.1.7",
    "react-virtual": "^3.13.9",
    "react-markdown": "^10.1.0",
    "framer-motion": "^12.15.0",
    "sonner": "^2.0.4",
    "cmdk": "^1.1.1",
    "react-day-picker": "^8.10.1",
    "@radix-ui/react-dialog": "^1.1.14",
    "@radix-ui/react-dropdown-menu": "^2.1.15",
    "@radix-ui/react-select": "^2.2.5",
    "@radix-ui/react-tabs": "^1.1.12",
    "@radix-ui/react-tooltip": "^1.2.7",
    "@radix-ui/react-checkbox": "^1.3.2",
    "@radix-ui/react-progress": "^1.1.7",
    "@radix-ui/react-alert-dialog": "^1.1.14"
  }
}
```

### 1.3 Enhanced Type System

Extend the current type system to match inbox-zero's comprehensive data structures:

```typescript
// Core email types
export interface Thread {
  id: string;
  messages: ParsedMessage[];
  snippet: string;
  historyId: string;
  category?: EmailCategory;
  plan?: ExecutionPlan;
  labels?: string[];
  unread: boolean;
  important: boolean;
  starred: boolean;
}

// AI and Rules types
export interface Rule {
  id: string;
  name: string;
  instructions: string;
  enabled: boolean;
  conditions: RuleCondition[];
  actions: RuleAction[];
  createdAt: string;
  updatedAt: string;
  stats?: RuleStats;
}

export interface RuleCondition {
  id: string;
  type: 'from' | 'to' | 'subject' | 'body' | 'label' | 'category';
  operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'regex';
  value: string;
  caseSensitive?: boolean;
}

export interface RuleAction {
  id: string;
  type: 'archive' | 'label' | 'forward' | 'reply' | 'delete' | 'markRead' | 'star';
  value?: string;
  template?: string;
}

// Analytics types
export interface EmailStats {
  totalEmails: number;
  unreadEmails: number;
  archivedEmails: number;
  deletedEmails: number;
  rulesExecuted: number;
  averageResponseTime: number;
  topSenders: SenderStats[];
  categoryBreakdown: CategoryStats[];
  volumeOverTime: VolumeData[];
}

// Bulk operation types
export interface BulkOperation {
  id: string;
  type: 'unsubscribe' | 'archive' | 'delete' | 'label';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  totalItems: number;
  processedItems: number;
  errors: string[];
  createdAt: string;
  completedAt?: string;
}
```

## Phase 2: Core Email Management

### 2.1 Enhanced Email List Component

Implement a sophisticated email list with the following features:

- **Thread-based grouping**: Group related emails into conversations
- **Virtual scrolling**: Handle large email lists efficiently
- **Advanced filtering**: Filter by read/unread, labels, categories, date ranges
- **Bulk selection**: Select multiple emails for batch operations
- **Real-time updates**: Simulate real-time email updates
- **Keyboard shortcuts**: Gmail-style keyboard navigation

### 2.2 Email Detail Panel

Create a comprehensive email detail view:

- **Resizable split-pane layout**: Adjustable email list and detail panels
- **Thread conversation view**: Show full email conversations
- **Quick actions**: Archive, delete, label, star, mark as read/unread
- **AI categorization display**: Show AI-determined categories and confidence
- **Attachment handling**: Display and download email attachments
- **Reply/Forward**: Compose replies and forwards

### 2.3 MSW Email Handlers

Implement comprehensive email API mocking:

```typescript
// Email handlers
export const emailHandlers = [
  // Get threads with pagination and filtering
  http.get('/api/google/threads', ({ request }) => {
    const url = new URL(request.url);
    const query = url.searchParams.get('q');
    const labelId = url.searchParams.get('labelId');
    const type = url.searchParams.get('type');
    const pageToken = url.searchParams.get('pageToken');
    const maxResults = parseInt(url.searchParams.get('maxResults') || '50');
    
    // Generate filtered and paginated results
    const threads = generateThreads({ query, labelId, type, pageToken, maxResults });
    
    return HttpResponse.json({
      threads: threads.items,
      nextPageToken: threads.nextPageToken,
      resultSizeEstimate: threads.totalCount
    });
  }),

  // Get individual thread details
  http.get('/api/google/threads/:threadId', ({ params }) => {
    const thread = generateThreadDetail(params.threadId as string);
    return HttpResponse.json(thread);
  }),

  // Bulk operations
  http.post('/api/google/threads/batch', async ({ request }) => {
    const body = await request.json() as BatchRequest;
    const results = await processBatchOperation(body);
    return HttpResponse.json(results);
  }),

  // Email actions
  http.post('/api/google/messages/:messageId/archive', ({ params }) => {
    return HttpResponse.json({ success: true });
  }),

  http.post('/api/google/messages/:messageId/trash', ({ params }) => {
    return HttpResponse.json({ success: true });
  }),

  http.post('/api/google/messages/:messageId/modify', async ({ request, params }) => {
    const body = await request.json();
    return HttpResponse.json({ success: true });
  })
];
```

## Phase 3: AI Assistant & Rules Engine

### 3.1 AI Chat Interface

Build a sophisticated chat interface for rule creation:

- **Conversational rule building**: Natural language rule creation
- **Chat history**: Persistent conversation history
- **Suggested actions**: AI-powered action suggestions
- **Rule preview**: Preview rules before creation
- **Example emails**: Show how rules would apply to existing emails

### 3.2 Rules Management System

Create a comprehensive rules management interface:

- **Visual rule builder**: Drag-and-drop rule construction
- **Condition builder**: Complex condition creation with AND/OR logic
- **Action configuration**: Multiple actions per rule
- **Rule testing**: Test rules against historical emails
- **Bulk rule application**: Apply rules to existing emails
- **Rule performance analytics**: Track rule effectiveness

### 3.3 AI Processing Pipeline

Implement a realistic AI processing simulation:

```mermaid
graph LR
    A[New Email] --> B[AI Categorization]
    B --> C[Rule Matching]
    C --> D[Action Execution]
    D --> E[Result Logging]
    E --> F[Analytics Update]
    F --> G[User Notification]
```

### 3.4 MSW AI/Rules Handlers

```typescript
export const aiHandlers = [
  // AI categorization
  http.post('/api/ai/categorize', async ({ request }) => {
    const { email } = await request.json();
    const category = await mockCategorizeEmail(email);
    return HttpResponse.json(category);
  }),

  // AI rule suggestions
  http.post('/api/ai/suggest-rules', async ({ request }) => {
    const { emails, prompt } = await request.json();
    const suggestions = await mockRuleSuggestions(emails, prompt);
    return HttpResponse.json(suggestions);
  }),

  // Rule testing
  http.post('/api/user/rules/test', async ({ request }) => {
    const { rule, emails } = await request.json();
    const results = await mockTestRule(rule, emails);
    return HttpResponse.json(results);
  }),

  // Rules CRUD
  http.get('/api/user/rules', () => {
    return HttpResponse.json({ rules: mockRules });
  }),

  http.post('/api/user/rules', async ({ request }) => {
    const rule = await request.json();
    const newRule = await mockCreateRule(rule);
    return HttpResponse.json(newRule);
  }),

  http.put('/api/user/rules/:ruleId', async ({ request, params }) => {
    const rule = await request.json();
    const updatedRule = await mockUpdateRule(params.ruleId as string, rule);
    return HttpResponse.json(updatedRule);
  }),

  http.delete('/api/user/rules/:ruleId', ({ params }) => {
    mockDeleteRule(params.ruleId as string);
    return HttpResponse.json({ success: true });
  })
];
```

## Phase 4: Bulk Operations

### 4.1 Bulk Unsubscribe Feature

Implement comprehensive newsletter management:

- **Newsletter detection**: Automatically identify newsletters and marketing emails
- **Sender grouping**: Group emails by sender for bulk operations
- **Unsubscribe link extraction**: Find and validate unsubscribe links
- **Bulk unsubscribe actions**: Unsubscribe from multiple newsletters at once
- **Progress tracking**: Real-time progress updates
- **Undo functionality**: Ability to reverse unsubscribe actions
- **Whitelist management**: Protect important senders from bulk operations

### 4.2 Clean Inbox Feature

Create an intelligent inbox cleaning system:

- **Smart categorization**: AI-powered email categorization
- **Bulk archiving workflows**: Archive emails by category, age, or sender
- **Preview before action**: Show what will be archived before execution
- **Cleaning progress visualization**: Real-time progress tracking
- **Undo functionality**: Reverse cleaning actions
- **Custom cleaning rules**: User-defined cleaning criteria

### 4.3 Cold Email Blocker

Implement sophisticated cold email detection:

- **Cold email detection algorithms**: ML-powered cold email identification
- **Sender reputation scoring**: Score senders based on various factors
- **Whitelist/blacklist management**: Manual sender management
- **Automatic blocking rules**: Rules-based cold email blocking
- **Review and approve interface**: Review blocked emails before permanent action
- **False positive handling**: Easy way to unblock legitimate emails

## Phase 5: Analytics & Insights

### 5.1 Email Analytics Dashboard

Build comprehensive email analytics:

- **Email volume trends**: Track email volume over time
- **Response time analytics**: Measure email response times
- **Sender analysis**: Analyze top senders and their patterns
- **Category breakdowns**: Email distribution by category
- **Rule performance metrics**: Track rule effectiveness and usage
- **Productivity insights**: Time saved through automation

### 5.2 Interactive Charts

Implement rich data visualizations:

- **Time-series charts**: Email volume, response times over time
- **Bar charts**: Top senders, categories, rule usage
- **Pie charts**: Email distribution, category breakdowns
- **Heatmaps**: Email activity patterns by time/day
- **Trend lines**: Growth/decline patterns
- **Interactive filters**: Filter charts by date range, category, etc.

## Phase 6: Advanced Features

### 6.1 Settings & Preferences

Create comprehensive user settings:

- **AI model selection**: Choose between different AI models (mocked)
- **Email signature management**: Create and manage email signatures
- **Notification preferences**: Configure email and app notifications
- **Theme customization**: Light/dark mode, color schemes
- **Keyboard shortcuts**: Customize keyboard shortcuts
- **Privacy settings**: Data handling and privacy preferences

### 6.2 Multi-Account Support

Simulate multi-account email management:

- **Account switching**: Switch between multiple email accounts
- **Per-account rules**: Different rules for different accounts
- **Unified inbox view**: Combined view of all accounts
- **Account-specific settings**: Different settings per account
- **Cross-account analytics**: Analytics across all accounts

## Implementation Strategy

```mermaid
graph TD
    A[Phase 1: Infrastructure] --> B[Phase 2: Email Management]
    B --> C[Phase 3: AI & Rules]
    C --> D[Phase 4: Bulk Operations]
    D --> E[Phase 5: Analytics]
    E --> F[Phase 6: Advanced Features]
    
    subgraph "Parallel Development"
        G[MSW Handler Development]
        H[Component Development]
        I[Type System Enhancement]
        J[State Management Setup]
    end
    
    A --> G
    A --> H
    A --> I
    A --> J
```

## Key Technical Decisions

### State Management Strategy
- **TanStack Query**: Server state management with caching, background updates, and optimistic updates
- **Zustand**: Client state for UI state, selections, and temporary data
- **Local Storage**: Persist user preferences and settings
- **Optimistic Updates**: Immediate UI updates with rollback on failure

### MSW Mock Strategy
- **Realistic Data Generation**: Create diverse, realistic email data
- **Progressive Loading**: Simulate real API loading times and pagination
- **Error Scenarios**: Test error handling with various failure modes
- **Performance Simulation**: Realistic response times and loading states
- **Data Persistence**: Maintain state across page reloads during development

### Component Architecture
- **Compound Components**: Complex components broken into smaller, reusable parts
- **Render Props**: Flexible component composition
- **Custom Hooks**: Reusable logic extraction
- **Context Providers**: Shared state and functionality
- **Error Boundaries**: Graceful error handling

### Performance Optimizations
- **Virtual Scrolling**: Handle large email lists efficiently
- **Code Splitting**: Lazy load routes and components
- **Memoization**: Prevent unnecessary re-renders
- **Debounced Search**: Optimize search performance
- **Background Sync**: Sync data in the background

## Mock Data Strategy

### Realistic Email Generation
```typescript
// Generate diverse email types
const emailTypes = [
  'newsletter',
  'personal',
  'work',
  'promotional',
  'notification',
  'social',
  'spam',
  'important'
];

// Generate realistic sender patterns
const generateSender = (type: string) => {
  switch (type) {
    case 'newsletter':
      return generateNewsletterSender();
    case 'work':
      return generateWorkSender();
    case 'personal':
      return generatePersonalSender();
    // ... other types
  }
};

// Generate realistic email content
const generateEmailContent = (type: string, sender: string) => {
  return {
    subject: generateSubject(type, sender),
    body: generateBody(type, sender),
    snippet: generateSnippet(type, sender)
  };
};
```

### AI Response Simulation
```typescript
// Simulate AI categorization with realistic confidence scores
const mockCategorizeEmail = async (email: ParsedMessage) => {
  // Simulate processing time
  await delay(500 + Math.random() * 1000);
  
  const category = determineCategory(email);
  const confidence = calculateConfidence(email, category);
  
  return {
    category,
    confidence,
    reasoning: generateReasoning(email, category)
  };
};

// Simulate rule suggestions based on email patterns
const mockRuleSuggestions = async (emails: ParsedMessage[], prompt: string) => {
  await delay(1000 + Math.random() * 2000);
  
  const patterns = analyzeEmailPatterns(emails);
  const suggestions = generateRuleSuggestions(patterns, prompt);
  
  return suggestions;
};
```

### Progressive Data Loading
```typescript
// Simulate realistic pagination
const paginateResults = <T>(items: T[], pageToken?: string, maxResults = 50) => {
  const startIndex = pageToken ? parseInt(pageToken) : 0;
  const endIndex = startIndex + maxResults;
  const pageItems = items.slice(startIndex, endIndex);
  
  return {
    items: pageItems,
    nextPageToken: endIndex < items.length ? endIndex.toString() : undefined,
    totalCount: items.length
  };
};

// Simulate network delays
const simulateNetworkDelay = () => {
  const delay = 200 + Math.random() * 800; // 200-1000ms
  return new Promise(resolve => setTimeout(resolve, delay));
};
```

## Testing Strategy

### Unit Testing
- **Component Testing**: Test individual components in isolation
- **Hook Testing**: Test custom hooks with various scenarios
- **Utility Testing**: Test utility functions and helpers
- **Mock Testing**: Verify MSW handlers work correctly

### Integration Testing
- **Route Testing**: Test route navigation and data loading
- **State Management Testing**: Test state updates and persistence
- **API Integration Testing**: Test MSW integration with components
- **User Flow Testing**: Test complete user workflows

### E2E Testing
- **Critical Path Testing**: Test main user journeys
- **Cross-browser Testing**: Ensure compatibility across browsers
- **Performance Testing**: Test with large datasets
- **Accessibility Testing**: Ensure WCAG compliance

## Deployment Considerations

### Development Environment
- **Hot Reloading**: Fast development iteration
- **MSW Integration**: Seamless API mocking
- **DevTools**: TanStack Router and Query devtools
- **Error Handling**: Comprehensive error boundaries

### Production Build
- **Code Splitting**: Optimize bundle size
- **Asset Optimization**: Compress images and assets
- **Caching Strategy**: Optimize caching headers
- **Performance Monitoring**: Track performance metrics

## Success Metrics

### Functionality Metrics
- **Feature Parity**: 100% of inbox-zero features replicated
- **Performance**: Sub-second load times for all routes
- **Reliability**: Zero critical bugs in core workflows
- **Usability**: Intuitive user experience matching inbox-zero

### Technical Metrics
- **Code Quality**: High test coverage (>80%)
- **Type Safety**: Full TypeScript coverage
- **Bundle Size**: Optimized bundle size (<500KB gzipped)
- **Accessibility**: WCAG 2.1 AA compliance

## Conclusion

This migration plan provides a comprehensive roadmap for replicating all inbox-zero features using modern React tooling. The phased approach ensures systematic development while the MSW integration provides a realistic development environment. The result will be a fully functional email management application that preserves all original functionality while leveraging the benefits of TanStack Router, TanStack Query, and comprehensive API mocking.

The plan emphasizes:
- **Comprehensive feature replication**
- **Modern React patterns and tooling**
- **Realistic API simulation with MSW**
- **Performance optimization**
- **Type safety and developer experience**
- **Scalable architecture for future enhancements**

This approach ensures that e-connect-2 will be a robust, maintainable, and feature-complete alternative to the original inbox-zero application.