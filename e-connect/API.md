# E-Connect 2 - API Documentation

## 📋 Overview

E-Connect 2 implements a comprehensive email management API that closely mirrors the Gmail API structure while extending it with AI-powered features, multi-account support, and advanced automation capabilities. The API is currently implemented using MSW (Mock Service Worker) for development and testing, but is designed to be easily replaced with real backend services.

## 🚀 API Architecture

### **Base URL & Versioning**
```
Base URL: /api
Version: v1 (implicit in current implementation)
Content-Type: application/json
```

### **Authentication**
The API supports multiple authentication methods:
- **OAuth 2.0**: For Gmail, Outlook, and other cloud providers
- **API Keys**: For service-to-service communication
- **Session Tokens**: For web client authentication

```typescript
// Authentication header format
headers: {
  'Authorization': 'Bearer <access_token>',
  'X-Account-ID': '<account_id>', // For multi-account operations
  'Content-Type': 'application/json'
}
```

## 📧 Email Management API

### **Thread Operations**

#### **List Threads**
Retrieves a list of email threads with support for Gmail-style search operators.

```typescript
GET /api/google/threads

Query Parameters:
- q?: string              // Gmail-style search query
- labelId?: string        // Filter by label ID
- type?: string          // inbox, sent, draft, spam, trash
- pageToken?: string     // Pagination token
- maxResults?: number    // Results per page (default: 50, max: 100)
- accountId?: string     // Specific account ID for multi-account

// Gmail-style search operators supported:
// from:<EMAIL>
// to:<EMAIL>
// subject:"search term"
// has:attachment
// is:unread | is:read | is:important | is:starred
// label:labelname
// category:updates | category:social | category:promotions
// after:2024/01/01 | before:2024/12/31
// newer_than:1d | older_than:1w

Response: ThreadListResponse
```

**Example Request:**
```bash
GET /api/google/threads?q=from:<EMAIL> has:attachment is:unread&maxResults=25
```

**Example Response:**
```json
{
  "threads": [
    {
      "id": "thread-001",
      "historyId": "12345",
      "snippet": "Weekly newsletter with important updates...",
      "messages": [
        {
          "id": "msg-001",
          "threadId": "thread-001",
          "labelIds": ["INBOX", "CATEGORY_UPDATES"],
          "snippet": "Weekly newsletter with important updates...",
          "payload": {
            "headers": [
              { "name": "From", "value": "<EMAIL>" },
              { "name": "Subject", "value": "Weekly Updates" },
              { "name": "Date", "value": "Wed, 15 Jan 2025 10:30:00 +0000" }
            ],
            "body": {
              "data": "base64-encoded-content"
            },
            "parts": [
              {
                "filename": "attachment.pdf",
                "mimeType": "application/pdf",
                "body": {
                  "attachmentId": "att-001",
                  "size": 1024000
                }
              }
            ]
          },
          "sizeEstimate": 1024000,
          "historyId": "12345",
          "internalDate": "*************"
        }
      ],
      "unread": true,
      "important": false,
      "starred": false,
      "category": "Newsletter",
      "labels": ["INBOX", "CATEGORY_UPDATES"],
      "accountId": "account-001",
      "accountColor": "#4285F4",
      "accountName": "<EMAIL>"
    }
  ],
  "nextPageToken": "next-page-token-123",
  "resultSizeEstimate": 150
}
```

#### **Get Thread Details**
Retrieves detailed information about a specific thread.

```typescript
GET /api/google/threads/:threadId

Query Parameters:
- format?: string        // full, minimal, metadata, raw
- metadataHeaders?: string[]  // Specific headers to include

Response: ThreadResponse
```

**Example Response:**
```json
{
  "id": "thread-001",
  "historyId": "12345",
  "messages": [
    {
      "id": "msg-001",
      "threadId": "thread-001",
      "labelIds": ["INBOX"],
      "snippet": "Email content preview...",
      "payload": {
        "headers": [
          { "name": "Message-ID", "value": "<<EMAIL>>" },
          { "name": "From", "value": "John Doe <<EMAIL>>" },
          { "name": "To", "value": "<EMAIL>" },
          { "name": "Subject", "value": "Important Project Update" },
          { "name": "Date", "value": "Wed, 15 Jan 2025 14:30:00 +0000" }
        ],
        "body": {
          "data": "SGVsbG8gV29ybGQh"  // Base64 encoded
        },
        "parts": []
      },
      "sizeEstimate": 2048,
      "historyId": "12345",
      "internalDate": "**********000"
    }
  ],
  "category": "Work",
  "aiAnalysis": {
    "sentiment": "neutral",
    "category": "Work",
    "confidence": 0.85,
    "suggestedActions": [
      {
        "type": "reply",
        "priority": "medium",
        "reasoning": "Requires acknowledgment of project update"
      }
    ],
    "extractedEntities": [
      {
        "type": "person",
        "value": "John Doe",
        "confidence": 0.95
      },
      {
        "type": "project",
        "value": "Q1 Marketing Campaign",
        "confidence": 0.88
      }
    ]
  }
}
```

#### **Modify Thread**
Updates thread properties such as labels and read status.

```typescript
POST /api/google/threads/:threadId/modify

Request Body: {
  addLabelIds?: string[]     // Labels to add
  removeLabelIds?: string[]  // Labels to remove
}

Response: ThreadResponse
```

**Example Request:**
```json
{
  "addLabelIds": ["STARRED"],
  "removeLabelIds": ["UNREAD"]
}
```

#### **Delete Thread**
Moves a thread to trash or permanently deletes it.

```typescript
DELETE /api/google/threads/:threadId

Query Parameters:
- permanent?: boolean     // true for permanent deletion

Response: { success: boolean }
```

#### **Batch Operations**
Performs bulk operations on multiple threads.

```typescript
POST /api/google/threads/batch

Request Body: {
  operation: string        // archive, delete, markRead, markUnread, addLabel, removeLabel, trash, untrash
  threadIds: string[]      // Array of thread IDs (max 100)
  labelId?: string         // For label operations
  accountId?: string       // For multi-account operations
}

Response: BatchOperationResponse
```

**Example Request:**
```json
{
  "operation": "archive",
  "threadIds": ["thread-001", "thread-002", "thread-003"]
}
```

**Example Response:**
```json
{
  "results": [
    { "threadId": "thread-001", "success": true },
    { "threadId": "thread-002", "success": true },
    { "threadId": "thread-003", "success": false, "error": "Thread not found" }
  ],
  "successCount": 2,
  "errorCount": 1,
  "totalProcessed": 3
}
```

### **Message Operations**

#### **Get Message**
Retrieves a specific message with various format options.

```typescript
GET /api/google/messages/:messageId

Query Parameters:
- format?: string        // full, minimal, metadata, raw

Response: MessageResponse
```

#### **Send Message**
Sends a new email message.

```typescript
POST /api/google/messages/send

Request Body: {
  payload: {
    headers: {
      to: string
      cc?: string
      bcc?: string
      subject: string
      replyTo?: string
    }
    body: {
      data: string         // Base64 encoded email content
    }
    attachments?: Array<{
      filename: string
      mimeType: string
      data: string         // Base64 encoded attachment
    }>
  }
  threadId?: string        // For replies
  accountId?: string       // Sending account
}

Response: MessageResponse
```

**Example Request:**
```json
{
  "payload": {
    "headers": {
      "to": "<EMAIL>",
      "subject": "Meeting Follow-up",
      "replyTo": "<EMAIL>"
    },
    "body": {
      "data": "VGhhbmsgZm9yIHRoZSBncmVhdCBtZWV0aW5nIQ=="
    }
  },
  "accountId": "account-001"
}
```

## 🤖 AI & Automation API

### **AI Categorization**

#### **Categorize Email**
Uses AI to categorize and analyze email content.

```typescript
POST /api/ai/categorize

Request Body: {
  email: ParsedMessage     // Complete email object
  options?: {
    includeConfidence?: boolean
    includeReasoning?: boolean
    includeSuggestions?: boolean
  }
}

Response: CategorizationResponse
```

**Example Response:**
```json
{
  "category": "Newsletter",
  "confidence": 0.92,
  "reasoning": "Email contains unsubscribe link, marketing content, and is from a known newsletter sender",
  "suggestedActions": [
    {
      "type": "archive",
      "priority": "low",
      "reasoning": "Newsletter content can be archived automatically"
    },
    {
      "type": "addLabel",
      "value": "NEWSLETTERS",
      "priority": "medium"
    }
  ],
  "extractedFeatures": [
    "unsubscribe_link",
    "marketing_keywords",
    "known_sender"
  ]
}
```

#### **Generate Rule Suggestions**
Creates intelligent automation rule suggestions based on email patterns.

```typescript
POST /api/ai/suggest-rules

Request Body: {
  emails: ParsedMessage[]  // Sample emails for analysis
  prompt?: string          // User description of desired automation
  context?: {
    userPreferences?: object
    existingRules?: Rule[]
  }
}

Response: RuleSuggestionsResponse
```

**Example Response:**
```json
{
  "suggestions": [
    {
      "id": "suggestion-001",
      "name": "Auto-archive newsletters",
      "description": "Automatically archive emails from newsletter senders",
      "confidence": 0.88,
      "conditions": [
        {
          "type": "from",
          "operator": "contains",
          "value": "@newsletter.com"
        },
        {
          "type": "body",
          "operator": "contains",
          "value": "unsubscribe"
        }
      ],
      "actions": [
        {
          "type": "archive",
          "priority": 1
        },
        {
          "type": "addLabel",
          "value": "NEWSLETTERS",
          "priority": 2
        }
      ],
      "estimatedMatches": 25,
      "potentialTimeSaved": "15 minutes per week"
    }
  ],
  "analysisMetadata": {
    "emailsAnalyzed": 100,
    "patternsFound": 5,
    "processingTime": 1250
  }
}
```

### **Chat Assistant API**

#### **Chat with Assistant**
Interactive chat interface for email management assistance.

```typescript
POST /api/ai/chat

Request Body: {
  message: string          // User message
  conversationId?: string  // For continuing conversations
  context?: {
    currentEmail?: string  // Current email being viewed
    selectedEmails?: string[]
    accountId?: string
  }
}

Response: ChatResponse
```

**Example Response:**
```json
{
  "response": "I can help you create a rule to automatically archive newsletters. Based on your recent emails, I found 15 newsletter-type emails that could be handled automatically. Would you like me to create a rule for that?",
  "conversationId": "conv-001",
  "suggestedActions": [
    {
      "type": "create_rule",
      "title": "Create Newsletter Archive Rule",
      "description": "Automatically archive emails from newsletter senders",
      "data": {
        "ruleTemplate": {
          "name": "Auto-archive newsletters",
          "conditions": [
            {
              "type": "body",
              "operator": "contains",
              "value": "unsubscribe"
            }
          ],
          "actions": [
            {
              "type": "archive"
            }
          ]
        }
      }
    }
  ],
  "metadata": {
    "responseTime": 850,
    "confidence": 0.91
  }
}
```

### **Rules Management API**

#### **List Rules**
Retrieves all automation rules for an account.

```typescript
GET /api/user/rules

Query Parameters:
- accountId?: string     // Filter by account
- enabled?: boolean      // Filter by enabled status
- category?: string      // Filter by rule category

Response: RulesListResponse
```

#### **Create Rule**
Creates a new automation rule.

```typescript
POST /api/user/rules

Request Body: {
  name: string
  description?: string
  enabled?: boolean
  conditions: RuleCondition[]
  actions: RuleAction[]
  schedule?: RuleSchedule
  accountId?: string
}

Response: RuleResponse
```

**Example Request:**
```json
{
  "name": "Archive Marketing Emails",
  "description": "Automatically archive emails from marketing senders",
  "enabled": true,
  "conditions": [
    {
      "type": "from",
      "operator": "contains",
      "value": "@marketing.com"
    },
    {
      "type": "subject",
      "operator": "contains",
      "value": "promotion"
    }
  ],
  "actions": [
    {
      "type": "archive"
    },
    {
      "type": "addLabel",
      "value": "MARKETING"
    }
  ],
  "schedule": {
    "type": "immediate"
  }
}
```

#### **Test Rule**
Tests a rule against existing emails to preview its effects.

```typescript
POST /api/user/rules/:ruleId/test

Request Body: {
  emails?: ParsedMessage[]  // Specific emails to test against
  limit?: number           // Max emails to test (default: 50)
}

Response: RuleTestResponse
```

**Example Response:**
```json
{
  "matches": [
    {
      "emailId": "msg-001",
      "subject": "Special Promotion - 50% Off",
      "from": "<EMAIL>",
      "matchedConditions": [
        {
          "type": "from",
          "operator": "contains",
          "value": "@marketing.com",
          "matched": true
        }
      ],
      "plannedActions": [
        {
          "type": "archive",
          "willExecute": true
        }
      ]
    }
  ],
  "totalMatches": 1,
  "totalTested": 50,
  "estimatedImpact": {
    "emailsAffected": 15,
    "timeSaved": "10 minutes per week"
  }
}
```

## 📊 Analytics API

### **Email Statistics**

#### **Get Email Stats**
Retrieves comprehensive email statistics and metrics.

```typescript
GET /api/analytics/email-stats

Query Parameters:
- timeRange?: string     // today, week, month, quarter, year
- startDate?: string     // ISO date string
- endDate?: string       // ISO date string
- accountId?: string     // Specific account or 'all'
- groupBy?: string       // day, week, month

Response: EmailStatsResponse
```

**Example Response:**
```json
{
  "summary": {
    "totalEmails": 1250,
    "unreadEmails": 85,
    "readEmails": 1165,
    "archivedEmails": 800,
    "deletedEmails": 45,
    "averageResponseTime": 3600000,
    "inboxZeroAchieved": 15,
    "inboxZeroRate": 0.75
  },
  "productivity": {
    "emailsProcessedPerDay": 45,
    "averageProcessingTime": 120000,
    "automationTimeSaved": 7200000,
    "rulesExecuted": 150,
    "unsubscribeActions": 12
  },
  "volumeData": [
    {
      "date": "2025-01-15",
      "received": 45,
      "sent": 12,
      "archived": 38,
      "deleted": 2
    }
  ],
  "categoryBreakdown": {
    "Work": { "count": 450, "percentage": 36 },
    "Newsletter": { "count": 300, "percentage": 24 },
    "Personal": { "count": 250, "percentage": 20 },
    "Marketing": { "count": 150, "percentage": 12 },
    "Other": { "count": 100, "percentage": 8 }
  },
  "timeRange": {
    "start": "2025-01-01T00:00:00Z",
    "end": "2025-01-15T23:59:59Z"
  }
}
```

#### **Get Sender Analytics**
Analyzes email senders and their patterns.

```typescript
GET /api/analytics/senders

Query Parameters:
- timeRange?: string
- limit?: number         // Max senders to return (default: 20)
- sortBy?: string        // count, responseTime, importance

Response: SenderAnalyticsResponse
```

**Example Response:**
```json
{
  "topSenders": [
    {
      "email": "<EMAIL>",
      "name": "GitHub",
      "count": 85,
      "percentage": 6.8,
      "averageResponseTime": null,
      "importance": "medium",
      "category": "Notification",
      "trend": "increasing",
      "lastEmailDate": "2025-01-15T18:30:00Z"
    }
  ],
  "insights": {
    "mostActiveSender": "<EMAIL>",
    "fastestResponseSender": "<EMAIL>",
    "totalUniqueSenders": 125,
    "newSendersThisPeriod": 8
  }
}
```

#### **Get Rule Performance**
Analyzes automation rule effectiveness and performance.

```typescript
GET /api/analytics/rules

Response: RuleAnalyticsResponse
```

**Example Response:**
```json
{
  "rulePerformance": [
    {
      "ruleId": "rule-001",
      "name": "Auto-archive newsletters",
      "executions": 45,
      "successRate": 0.98,
      "timeSaved": 1800000,
      "emailsProcessed": 44,
      "lastExecuted": "2025-01-15T16:00:00Z",
      "trend": "stable"
    }
  ],
  "summary": {
    "totalRules": 8,
    "activeRules": 6,
    "totalExecutions": 320,
    "overallSuccessRate": 0.96,
    "totalTimeSaved": ********
  }
}
```

## 🔄 Bulk Operations API

### **Bulk Unsubscribe**

#### **Scan for Newsletters**
Scans emails for newsletter subscriptions and unsubscribe opportunities.

```typescript
POST /api/bulk/scan-newsletters

Request Body: {
  timeRange?: string       // How far back to scan
  accountId?: string
  filters?: {
    includeRead?: boolean
    minFrequency?: number  // Minimum emails from sender
  }
}

Response: NewsletterScanResponse
```

**Example Response:**
```json
{
  "newsletters": [
    {
      "sender": "<EMAIL>",
      "senderName": "TechCrunch",
      "emailCount": 25,
      "frequency": "daily",
      "lastEmailDate": "2025-01-15T08:00:00Z",
      "unsubscribeMethod": "link",
      "unsubscribeUrl": "https://techcrunch.com/unsubscribe?token=xyz",
      "estimatedTimeSaved": 300000,
      "category": "Technology News"
    }
  ],
  "summary": {
    "totalNewsletters": 15,
    "totalEmails": 350,
    "estimatedTimeSaved": 4500000,
    "topCategories": ["Technology", "Marketing", "Finance"]
  }
}
```

#### **Execute Bulk Unsubscribe**
Performs bulk unsubscribe operations.

```typescript
POST /api/bulk/unsubscribe

Request Body: {
  senders: string[]        // Email addresses to unsubscribe from
  method: string          // link, reply, manual
  deleteEmails?: boolean   // Delete existing emails after unsubscribe
}

Response: BulkUnsubscribeResponse
```

### **Progress Tracking**

#### **Get Operation Progress**
Tracks progress of long-running bulk operations.

```typescript
GET /api/bulk/operations/:operationId/progress

Response: OperationProgressResponse
```

**Example Response:**
```json
{
  "operationId": "op-001",
  "type": "bulk_unsubscribe",
  "status": "running",
  "progress": {
    "total": 15,
    "completed": 8,
    "failed": 1,
    "remaining": 6,
    "percentage": 53
  },
  "results": [
    {
      "sender": "<EMAIL>",
      "status": "success",
      "method": "link",
      "timestamp": "2025-01-15T14:30:00Z"
    }
  ],
  "estimatedCompletion": "2025-01-15T14:45:00Z"
}
```

## 🔐 Multi-Account API

### **Account Management**

#### **List Accounts**
Retrieves all configured email accounts.

```typescript
GET /api/accounts

Response: AccountListResponse
```

#### **Add Account**
Adds a new email account.

```typescript
POST /api/accounts

Request Body: {
  name: string
  email: string
  provider: string         // gmail, outlook, imap, exchange
  config: AccountConfiguration
  auth: AuthenticationConfig
}

Response: AccountResponse
```

#### **Sync Account**
Triggers synchronization for a specific account.

```typescript
POST /api/accounts/:accountId/sync

Request Body: {
  force?: boolean         // Force full sync
  options?: SyncOptions
}

Response: SyncResponse
```

### **Unified Operations**

#### **Unified Search**
Searches across all accounts simultaneously.

```typescript
POST /api/unified/search

Request Body: {
  query: string
  accountIds?: string[]    // Specific accounts to search
  maxResults?: number
  timeout?: number
}

Response: UnifiedSearchResponse
```

## 🛠️ Settings API

### **User Settings**

#### **Get Settings**
Retrieves user settings and preferences.

```typescript
GET /api/user/settings

Response: UserSettingsResponse
```

#### **Update Settings**
Updates user settings and preferences.

```typescript
PUT /api/user/settings

Request Body: Partial<UserSettings>

Response: UserSettingsResponse
```

## 📡 Real-time Features

### **WebSocket Events**
The API supports real-time updates through WebSocket connections:

```typescript
// Connection
ws://localhost:5173/ws

// Event types
{
  "type": "email:new",
  "data": { "email": ParsedMessage, "accountId": string }
}

{
  "type": "email:updated", 
  "data": { "emailId": string, "changes": object }
}

{
  "type": "rule:executed",
  "data": { "ruleId": string, "result": RuleExecutionResult }
}

{
  "type": "sync:progress",
  "data": { "accountId": string, "progress": number }
}
```

## 🚨 Error Handling

### **Error Response Format**
All API errors follow a consistent format:

```typescript
{
  "error": {
    "code": string           // Error code (e.g., "INVALID_REQUEST")
    "message": string        // Human-readable error message
    "details"?: object       // Additional error context
    "timestamp": string      // ISO timestamp
    "requestId": string      // Unique request identifier
  }
}
```

### **HTTP Status Codes**
- `200 OK` - Successful request
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

### **Common Error Codes**
```typescript
// Authentication errors
"AUTH_REQUIRED"          // Authentication required
"AUTH_INVALID"           // Invalid credentials
"AUTH_EXPIRED"           // Token expired

// Request errors  
"INVALID_REQUEST"        // Malformed request
"INVALID_PARAMETER"      // Invalid parameter value
"MISSING_PARAMETER"      // Required parameter missing

// Resource errors
"RESOURCE_NOT_FOUND"     // Resource doesn't exist
"RESOURCE_CONFLICT"      // Resource conflict
"RESOURCE_LIMIT"         // Resource limit exceeded

// Rate limiting
"RATE_LIMIT_EXCEEDED"    // Too many requests
"QUOTA_EXCEEDED"         // API quota exceeded

// Server errors
"INTERNAL_ERROR"         // Internal server error
"SERVICE_UNAVAILABLE"    // Service temporarily unavailable
```

## 🔄 Rate Limiting

### **Rate Limits**
```typescript
// Default rate limits (per account)
"GET requests": "1000 per hour"
"POST requests": "500 per hour"  
"Bulk operations": "10 per hour"
"Search requests": "200 per hour"

// Headers in response
"X-RateLimit-Limit": "1000"
"X-RateLimit-Remaining": "987"
"X-RateLimit-Reset": "**********"
```

## 📝 Request/Response Examples

### **Complete Email Workflow Example**

```typescript
// 1. Search for unread emails
GET /api/google/threads?q=is:unread&maxResults=10

// 2. Get thread details
GET /api/google/threads/thread-001

// 3. AI categorization
POST /api/ai/categorize
{
  "email": { /* email object */ },
  "options": { "includeConfidence": true }
}

// 4. Create automation rule
POST /api/user/rules
{
  "name": "Auto-archive newsletters",
  "conditions": [
    { "type": "from", "operator": "contains", "value": "@newsletter.com" }
  ],
  "actions": [
    { "type": "archive" }
  ]
}

// 5. Mark as read
POST /api/google/threads/thread-001/modify
{
  "removeLabelIds": ["UNREAD"]
}
```

This comprehensive API documentation provides complete coverage of all E-Connect 2 endpoints, request/response formats, error handling, and usage examples. The API is designed to be developer-friendly while providing powerful email management capabilities.