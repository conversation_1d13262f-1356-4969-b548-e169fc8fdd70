# E-Connect 2 - Technical Architecture Documentation

## 🏗️ System Overview

E-Connect 2 is architected as a modern, scalable email management platform using a component-based React architecture with comprehensive state management, API mocking, and real-time capabilities. The system is designed for maintainability, performance, and extensibility.

## 🎯 Design Principles

### **1. Component-Driven Architecture**
- **Atomic Design**: Components organized in a hierarchical structure from atoms to organisms
- **Composition over Inheritance**: Flexible component composition patterns
- **Single Responsibility**: Each component has a focused, well-defined purpose
- **Reusability**: Shared components across feature domains

### **2. Type Safety & Developer Experience**
- **TypeScript First**: Strict type checking throughout the application
- **API-First Design**: MSW-driven development with realistic API simulation
- **Declarative State**: Predictable state management with Zustand
- **Performance by Default**: Optimistic updates and efficient rendering

### **3. Scalability & Modularity**
- **Feature-Based Organization**: Code organized by domain features
- **Lazy Loading**: Route and component-level code splitting
- **Plugin Architecture**: Extensible systems for rules, integrations, and analytics
- **Multi-Account Architecture**: Designed for managing multiple email accounts

## 🏛️ High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React Components]
        B[TanStack Router]
        C[UI State Management]
    end
    
    subgraph "State Management Layer"
        D[TanStack Query]
        E[Zustand Stores]
        F[Local Storage]
    end
    
    subgraph "API Layer"
        G[MSW Mock Handlers]
        H[Real API Adapters]
        I[Authentication Layer]
    end
    
    subgraph "Data Layer"
        J[Mock Data Generators]
        K[Email Processing]
        L[Analytics Engine]
    end
    
    subgraph "External Services"
        M[Gmail API]
        N[Outlook API]
        O[IMAP Servers]
    end
    
    A --> D
    B --> A
    C --> A
    D --> G
    D --> H
    E --> F
    G --> J
    H --> I
    H --> M
    H --> N
    H --> O
    J --> K
    K --> L
```

## 📁 Directory Structure & Organization

### **Root Structure**
```
e-connect-2/
├── src/                    # Application source code
├── public/                 # Static assets and MSW worker
├── tests/                  # Test files and utilities
├── docs/                   # Additional documentation
├── package.json           # Dependencies and scripts
├── tsconfig.json          # TypeScript configuration
├── tailwind.config.js     # Tailwind CSS configuration
├── vite.config.ts         # Vite build configuration
└── eslint.config.js       # ESLint configuration
```

### **Source Code Structure**
```
src/
├── components/             # Reusable UI components
│   ├── ui/                # Base UI primitives
│   ├── email-list/        # Email management components
│   ├── assistant-chat/    # AI assistant interface
│   ├── charts/            # Analytics visualizations
│   ├── bulk-actions/      # Bulk operation components
│   ├── rules/             # Rules management
│   ├── settings/          # Settings components
│   ├── account-management/ # Multi-account features
│   ├── account-switcher/  # Account switching UI
│   └── unified-inbox/     # Unified inbox components
├── routes/                # TanStack Router routes
│   ├── __root.tsx         # Root layout
│   ├── index.tsx          # Dashboard
│   ├── mail/              # Email routes
│   ├── assistant/         # AI assistant routes
│   ├── automation/        # Rules automation routes
│   ├── bulk-unsubscribe/  # Bulk unsubscribe routes
│   ├── clean/             # Clean inbox routes
│   ├── cold-email-blocker/ # Cold email blocker routes
│   ├── settings/          # Settings routes
│   └── stats/             # Analytics routes
├── stores/                # Zustand state stores
│   ├── authStore.ts       # Authentication state
│   ├── emailStore.ts      # Email management state
│   ├── multiAccountStore.ts # Multi-account state
│   ├── rulesStore.ts      # Rules engine state
│   ├── assistantStore.ts  # AI assistant state
│   ├── bulkStore.ts       # Bulk operations state
│   ├── coldEmailStore.ts  # Cold email detection state
│   ├── analyticsStore.ts  # Analytics state
│   ├── settingsStore.ts   # User settings state
│   └── uiStore.ts         # UI state (toasts, loading)
├── mocks/                 # MSW API mocking
│   ├── browser.ts         # MSW worker setup
│   ├── handlers.ts        # Handler index
│   ├── handlers/          # Feature-specific handlers
│   │   ├── auth.ts        # Authentication endpoints
│   │   ├── emails.ts      # Email operations
│   │   ├── ai.ts          # AI services
│   │   ├── rules.ts       # Rules management
│   │   ├── analytics.ts   # Analytics endpoints
│   │   ├── bulk.ts        # Bulk operations
│   │   ├── settings.ts    # Settings management
│   │   ├── multi-account.ts # Multi-account operations
│   │   ├── assistant.ts   # AI assistant
│   │   ├── cold-email.ts  # Cold email detection
│   │   └── progress-tracker.ts # Progress tracking
│   └── data/              # Mock data generators
│       ├── emails.ts      # Email data generation
│       ├── users.ts       # User data
│       ├── rules.ts       # Rules data
│       ├── analytics.ts   # Analytics data
│       └── senders.ts     # Sender data
├── utils/                 # Utility functions
│   ├── ai/                # AI processing utilities
│   ├── analytics/         # Analytics processing
│   ├── email/             # Email processing
│   ├── rules/             # Rules engine
│   ├── date/              # Date utilities
│   ├── validation/        # Validation schemas
│   ├── cold-email/        # Cold email detection
│   ├── sync/              # Multi-account sync
│   └── backup/            # Account backup utilities
├── types/                 # TypeScript definitions
│   ├── email.ts           # Email-related types
│   ├── rules.ts           # Rules engine types
│   ├── analytics.ts       # Analytics types
│   ├── assistant.ts       # AI assistant types
│   ├── bulk.ts            # Bulk operations types
│   ├── cold-email.ts      # Cold email types
│   ├── multi-account.ts   # Multi-account types
│   ├── settings.ts        # Settings types
│   ├── api.ts             # API types
│   ├── common.ts          # Common types
│   └── index.ts           # Type exports
├── hooks/                 # Custom React hooks
│   ├── useKeyboardShortcuts.ts # Keyboard navigation
│   └── useTheme.ts        # Theme management
├── lib/                   # Core utilities
│   └── utils.ts           # General utilities
└── assets/                # Static assets
    └── react.svg          # React logo
```

## 🧩 Component Architecture

### **Component Hierarchy**

```mermaid
graph TD
    A[__root.tsx] --> B[Navigation]
    A --> C[Main Content Area]
    
    C --> D[Dashboard Route]
    C --> E[Mail Route]
    C --> F[Assistant Route]
    C --> G[Settings Route]
    
    E --> H[EmailList]
    E --> I[EmailPanel]
    E --> J[ThreadView]
    
    H --> K[EmailListItem]
    H --> L[BulkActions]
    
    F --> M[Chat]
    F --> N[RuleBuilder]
    F --> O[SuggestedActions]
    
    subgraph "UI Primitives"
        P[Button]
        Q[Card]
        R[Input]
        S[Dialog]
        T[Tooltip]
    end
    
    K --> P
    K --> Q
    I --> R
    N --> S
    L --> T
```

### **Component Design Patterns**

#### **1. Compound Components**
Complex components broken into smaller, composable parts:

```typescript
// EmailList compound component
<EmailList>
  <EmailList.Header>
    <EmailList.SearchBox />
    <EmailList.Filters />
  </EmailList.Header>
  
  <EmailList.Body>
    <EmailList.VirtualizedList>
      {threads.map(thread => (
        <EmailList.Item key={thread.id} thread={thread}>
          <EmailList.Item.Checkbox />
          <EmailList.Item.Sender />
          <EmailList.Item.Subject />
          <EmailList.Item.Actions />
        </EmailList.Item>
      ))}
    </EmailList.VirtualizedList>
  </EmailList.Body>
  
  <EmailList.Footer>
    <EmailList.Pagination />
    <EmailList.BulkActions />
  </EmailList.Footer>
</EmailList>
```

#### **2. Render Props Pattern**
Flexible component composition for data fetching:

```typescript
<EmailProvider>
  {({ threads, isLoading, error, actions }) => (
    <div>
      {isLoading && <LoadingSpinner />}
      {error && <ErrorMessage error={error} />}
      {threads && (
        <EmailList 
          threads={threads} 
          onSelect={actions.selectThread}
          onArchive={actions.archiveThread}
        />
      )}
    </div>
  )}
</EmailProvider>
```

#### **3. Custom Hooks Pattern**
Extracting complex logic into reusable hooks:

```typescript
// Custom hook for email management
export function useEmailManagement(accountId?: string) {
  const { threads, isLoading, error } = useEmailStore()
  const { mutate: archiveThread } = useMutation({
    mutationFn: emailApi.archiveThread,
    onSuccess: () => queryClient.invalidateQueries(['threads'])
  })
  
  return {
    threads,
    isLoading,
    error,
    actions: {
      archiveThread,
      deleteThread: useCallback((threadId: string) => {
        // Implementation
      }, []),
      selectThread: useCallback((threadId: string) => {
        // Implementation
      }, [])
    }
  }
}
```

### **Component Communication Patterns**

#### **1. Props Down, Events Up**
```typescript
// Parent component
<EmailList
  threads={threads}
  selectedThreads={selectedThreads}
  onThreadSelect={handleThreadSelect}
  onBulkAction={handleBulkAction}
/>

// Child component
<EmailListItem
  thread={thread}
  isSelected={selectedThreads.includes(thread.id)}
  onSelect={() => onThreadSelect(thread.id)}
  onArchive={() => onBulkAction('archive', [thread.id])}
/>
```

#### **2. Context for Shared State**
```typescript
// Email context provider
export const EmailContext = createContext<EmailContextValue | null>(null)

export function EmailProvider({ children }: { children: React.ReactNode }) {
  const emailStore = useEmailStore()
  const multiAccountStore = useMultiAccountStore()
  
  const contextValue = useMemo(() => ({
    ...emailStore,
    ...multiAccountStore,
    isUnifiedView: multiAccountStore.activeAccountId === 'unified'
  }), [emailStore, multiAccountStore])
  
  return (
    <EmailContext.Provider value={contextValue}>
      {children}
    </EmailContext.Provider>
  )
}
```

#### **3. Event Bus for Cross-Component Communication**
```typescript
// Global event system
export const eventBus = {
  emit: (event: string, data?: any) => {
    window.dispatchEvent(new CustomEvent(event, { detail: data }))
  },
  
  on: (event: string, handler: (data: any) => void) => {
    const listener = (e: CustomEvent) => handler(e.detail)
    window.addEventListener(event, listener)
    return () => window.removeEventListener(event, listener)
  }
}

// Usage in components
useEffect(() => {
  return eventBus.on('email:new', (emailData) => {
    // Handle new email arrival
    refetchThreads()
  })
}, [])
```

## 🗃️ State Management Architecture

### **Store Structure with Zustand**

The application uses Zustand for client-side state management, organized by feature domains:

```typescript
// Store architecture pattern
interface StoreState {
  // Data
  data: DataType[]
  
  // UI State
  isLoading: boolean
  error: string | null
  selectedItems: string[]
  
  // Filters and Views
  filters: FilterConfig
  sortConfig: SortConfig
  currentView: ViewType
}

interface StoreActions {
  // Data Actions
  fetchData: () => Promise<void>
  addItem: (item: DataType) => void
  updateItem: (id: string, updates: Partial<DataType>) => void
  removeItem: (id: string) => void
  
  // UI Actions
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  toggleSelection: (id: string) => void
  
  // Filter Actions
  setFilter: (key: string, value: any) => void
  setSortConfig: (config: SortConfig) => void
  setCurrentView: (view: ViewType) => void
}

export const useFeatureStore = create<StoreState & StoreActions>((set, get) => ({
  // Implementation
}))
```

### **Multi-Account State Management**

```typescript
// Multi-account store pattern
interface MultiAccountState {
  accounts: EmailAccount[]
  activeAccountId: string | null
  isUnifiedView: boolean
  syncStatus: Record<string, SyncStatus>
  unifiedInboxSettings: UnifiedInboxSettings
}

interface MultiAccountActions {
  // Account Management
  addAccount: (account: EmailAccount) => Promise<void>
  removeAccount: (accountId: string) => Promise<void>
  updateAccount: (accountId: string, updates: Partial<EmailAccount>) => void
  
  // Account Switching
  switchToAccount: (accountId: string) => Promise<void>
  switchToUnifiedView: () => void
  
  // Synchronization
  syncAccount: (accountId: string, options?: SyncOptions) => Promise<void>
  syncAllAccounts: () => Promise<void>
  
  // Settings
  updateUnifiedInboxSettings: (settings: Partial<UnifiedInboxSettings>) => void
}
```

### **State Persistence Strategy**

```typescript
// Persistence middleware for Zustand
import { persist } from 'zustand/middleware'

export const useSettingsStore = create(
  persist<SettingsState & SettingsActions>(
    (set, get) => ({
      // Store implementation
    }),
    {
      name: 'e-connect-settings',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        // Only persist certain parts of the state
        theme: state.theme,
        keyboardShortcuts: state.keyboardShortcuts,
        emailPreferences: state.emailPreferences
      })
    }
  )
)
```

### **Integration with TanStack Query**

```typescript
// Query integration with stores
export function useEmailData(accountId?: string) {
  const { updateThreads, setLoading, setError } = useEmailStore()
  
  return useQuery({
    queryKey: ['threads', accountId],
    queryFn: () => fetchThreads(accountId),
    onSuccess: (data) => {
      updateThreads(data.threads)
      setLoading(false)
    },
    onError: (error) => {
      setError(error.message)
      setLoading(false)
    },
    onLoading: () => {
      setLoading(true)
      setError(null)
    }
  })
}
```

## 🔌 API Architecture & MSW Integration

### **MSW Handler Organization**

```typescript
// Handler structure pattern
export const emailHandlers = [
  // List threads with advanced filtering
  http.get('/api/google/threads', ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get('q')
    const labelId = url.searchParams.get('labelId')
    const accountId = url.searchParams.get('accountId')
    
    // Parse Gmail-style search query
    const searchParams = parseGmailQuery(query)
    
    // Filter and paginate results
    const filteredThreads = filterThreads(mockThreads, {
      ...searchParams,
      labelId,
      accountId
    })
    
    return HttpResponse.json({
      threads: filteredThreads,
      nextPageToken: generatePageToken(),
      resultSizeEstimate: filteredThreads.length
    })
  }),
  
  // Batch operations
  http.post('/api/google/threads/batch', async ({ request }) => {
    const { operation, threadIds } = await request.json()
    
    const results = await Promise.all(
      threadIds.map(id => performBatchOperation(operation, id))
    )
    
    return HttpResponse.json({
      results,
      successCount: results.filter(r => r.success).length,
      errorCount: results.filter(r => !r.success).length
    })
  })
]
```

### **Realistic Data Generation**

```typescript
// Email data generator with realistic patterns
export function generateEmailData() {
  const categories = [
    'Newsletter', 'Receipt', 'Marketing', 'Social', 
    'Updates', 'Personal', 'Work', 'Finance'
  ]
  
  const senders = generateSenders()
  const threads: Thread[] = []
  
  for (let i = 0; i < 150; i++) {
    const sender = selectRandomSender(senders)
    const category = selectCategoryBySender(sender)
    const conversationLength = getConversationLength(category)
    
    const thread = {
      id: `thread-${i.toString().padStart(4, '0')}`,
      messages: generateConversation(sender, conversationLength),
      snippet: generateSnippet(category),
      category,
      labels: generateLabels(category),
      unread: Math.random() < 0.2, // 20% unread rate
      important: Math.random() < 0.1, // 10% important
      starred: Math.random() < 0.05 // 5% starred
    }
    
    threads.push(thread)
  }
  
  return threads
}
```

### **AI Service Simulation**

```typescript
// AI categorization simulation
export async function mockCategorizeEmail(email: ParsedMessage) {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))
  
  const indicators = [
    { keyword: 'unsubscribe', category: 'Newsletter', weight: 0.8 },
    { keyword: 'receipt', category: 'Receipt', weight: 0.9 },
    { keyword: 'invoice', category: 'Finance', weight: 0.85 },
    { keyword: 'meeting', category: 'Work', weight: 0.7 }
  ]
  
  let bestMatch = { category: 'Other', confidence: 0.5 }
  
  for (const indicator of indicators) {
    if (email.subject.toLowerCase().includes(indicator.keyword) ||
        email.body.toLowerCase().includes(indicator.keyword)) {
      if (indicator.weight > bestMatch.confidence) {
        bestMatch = {
          category: indicator.category,
          confidence: indicator.weight
        }
      }
    }
  }
  
  return {
    category: bestMatch.category,
    confidence: bestMatch.confidence,
    reasoning: generateCategorationReasoning(email, bestMatch)
  }
}
```

## 🚦 Performance Architecture

### **Rendering Optimizations**

#### **1. Virtual Scrolling for Email Lists**
```typescript
// Virtual scrolling implementation
import { useVirtualizer } from '@tanstack/react-virtual'

export function VirtualizedEmailList({ threads }: { threads: Thread[] }) {
  const parentRef = useRef<HTMLDivElement>(null)
  
  const rowVirtualizer = useVirtualizer({
    count: threads.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 80, // Estimated row height
    overscan: 10 // Render extra items for smooth scrolling
  })
  
  return (
    <div ref={parentRef} className="h-full overflow-auto">
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {rowVirtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            <EmailListItem thread={threads[virtualItem.index]} />
          </div>
        ))}
      </div>
    </div>
  )
}
```

#### **2. Memoization Strategies**
```typescript
// Memoized components and calculations
export const EmailListItem = memo(({ thread, isSelected, onSelect }: Props) => {
  const formattedDate = useMemo(() => {
    return formatEmailDate(thread.messages[0]?.date)
  }, [thread.messages])
  
  const handleSelect = useCallback(() => {
    onSelect(thread.id)
  }, [thread.id, onSelect])
  
  return (
    <div onClick={handleSelect}>
      {/* Component content */}
    </div>
  )
})

// Memoized calculations in stores
const useEmailStore = create((set, get) => ({
  threads: [],
  
  // Memoized selectors
  getUnreadThreads: () => {
    const { threads } = get()
    return threads.filter(thread => thread.unread)
  },
  
  getThreadsByCategory: (category: string) => {
    const { threads } = get()
    return threads.filter(thread => thread.category === category)
  }
}))
```

### **Code Splitting & Lazy Loading**

#### **1. Route-based Code Splitting**
```typescript
// Lazy loaded routes
import { lazy } from 'react'

const AssistantRoute = lazy(() => import('./routes/assistant/index'))
const AnalyticsRoute = lazy(() => import('./routes/stats/index'))
const SettingsRoute = lazy(() => import('./routes/settings/index'))

// Route definitions with lazy loading
export const routeTree = rootRoute.addChildren([
  indexRoute,
  mailRoute,
  assistantRoute.addChildren([
    createRoute({
      getParentRoute: () => assistantRoute,
      path: '/',
      component: AssistantRoute
    })
  ])
])
```

#### **2. Component-level Lazy Loading**
```typescript
// Lazy loaded heavy components
const EmailAnalyticsChart = lazy(() => 
  import('./components/charts/EmailVolumeChart').then(module => ({
    default: module.EmailVolumeChart
  }))
)

// Usage with Suspense
<Suspense fallback={<ChartSkeleton />}>
  <EmailAnalyticsChart data={data} />
</Suspense>
```

### **Caching Strategies**

#### **1. TanStack Query Caching**
```typescript
// Query configuration with caching
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000)
    }
  }
})

// Background refetching
export function useEmailDataWithBackground(accountId?: string) {
  return useQuery({
    queryKey: ['threads', accountId],
    queryFn: () => fetchThreads(accountId),
    refetchInterval: 30000, // Refetch every 30 seconds
    refetchIntervalInBackground: true
  })
}
```

#### **2. Local Storage Caching**
```typescript
// Local storage cache with expiration
export class LocalStorageCache {
  private prefix = 'e-connect-cache:'
  
  set<T>(key: string, data: T, ttl: number = 3600000): void {
    const item = {
      data,
      timestamp: Date.now(),
      ttl
    }
    localStorage.setItem(this.prefix + key, JSON.stringify(item))
  }
  
  get<T>(key: string): T | null {
    const item = localStorage.getItem(this.prefix + key)
    if (!item) return null
    
    const parsed = JSON.parse(item)
    if (Date.now() - parsed.timestamp > parsed.ttl) {
      this.remove(key)
      return null
    }
    
    return parsed.data
  }
  
  remove(key: string): void {
    localStorage.removeItem(this.prefix + key)
  }
}
```

## 🔐 Security Architecture

### **Authentication & Authorization**

#### **1. OAuth Token Management**
```typescript
// Secure token storage and management
export class TokenManager {
  private encryptionKey: string
  
  constructor() {
    this.encryptionKey = this.generateEncryptionKey()
  }
  
  async storeTokens(accountId: string, tokens: OAuthTokens): Promise<void> {
    const encrypted = await this.encrypt(JSON.stringify(tokens))
    localStorage.setItem(`tokens:${accountId}`, encrypted)
  }
  
  async getTokens(accountId: string): Promise<OAuthTokens | null> {
    const encrypted = localStorage.getItem(`tokens:${accountId}`)
    if (!encrypted) return null
    
    try {
      const decrypted = await this.decrypt(encrypted)
      return JSON.parse(decrypted)
    } catch {
      return null
    }
  }
  
  private async encrypt(data: string): Promise<string> {
    // Implementation using Web Crypto API
    const encoder = new TextEncoder()
    const key = await crypto.subtle.importKey(
      'raw',
      encoder.encode(this.encryptionKey),
      { name: 'AES-GCM' },
      false,
      ['encrypt']
    )
    
    const iv = crypto.getRandomValues(new Uint8Array(12))
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      encoder.encode(data)
    )
    
    return btoa(String.fromCharCode(...new Uint8Array([...iv, ...new Uint8Array(encrypted)])))
  }
}
```

#### **2. Data Isolation Between Accounts**
```typescript
// Account-specific data containers
export class AccountDataContainer {
  private accountId: string
  private dataPrefix: string
  
  constructor(accountId: string) {
    this.accountId = accountId
    this.dataPrefix = `account:${accountId}:`
  }
  
  setData<T>(key: string, data: T): void {
    const isolated = this.isolateData(data)
    localStorage.setItem(this.dataPrefix + key, JSON.stringify(isolated))
  }
  
  getData<T>(key: string): T | null {
    const item = localStorage.getItem(this.dataPrefix + key)
    return item ? JSON.parse(item) : null
  }
  
  clearAllData(): void {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(this.dataPrefix)) {
        localStorage.removeItem(key)
      }
    })
  }
  
  private isolateData<T>(data: T): T {
    // Add account isolation metadata
    return {
      ...data,
      __accountId: this.accountId,
      __timestamp: Date.now()
    } as T
  }
}
```

### **Input Validation & Sanitization**

#### **1. Zod Schema Validation**
```typescript
// Comprehensive validation schemas
export const EmailThreadSchema = z.object({
  id: z.string().regex(/^thread-[\w-]+$/),
  messages: z.array(EmailMessageSchema).min(1),
  snippet: z.string().max(200),
  labels: z.array(z.string()).optional(),
  category: EmailCategorySchema.optional(),
  unread: z.boolean(),
  important: z.boolean(),
  starred: z.boolean()
})

export const BulkOperationSchema = z.object({
  operation: z.enum(['archive', 'delete', 'markRead', 'markUnread', 'addLabel', 'removeLabel']),
  threadIds: z.array(z.string()).min(1).max(100),
  labelId: z.string().optional(),
  accountId: z.string().optional()
})

// Runtime validation
export function validateEmailThread(data: unknown): EmailThread {
  try {
    return EmailThreadSchema.parse(data)
  } catch (error) {
    throw new ValidationError('Invalid email thread data', error)
  }
}
```

#### **2. HTML Sanitization**
```typescript
// Email content sanitization
import DOMPurify from 'dompurify'

export function sanitizeEmailContent(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'table', 'tr', 'td', 'th'
    ],
    ALLOWED_ATTR: [
      'href', 'src', 'alt', 'title', 'class', 'style'
    ],
    ALLOW_DATA_ATTR: false,
    FORBID_SCRIPT: true
  })
}
```

### **Privacy & Data Protection**

#### **1. GDPR Compliance Features**
```typescript
// Data export and deletion
export class GDPRCompliance {
  async exportUserData(userId: string): Promise<UserDataExport> {
    const userData = await this.collectUserData(userId)
    
    return {
      personalData: userData.profile,
      emailData: userData.emails,
      settingsData: userData.settings,
      rulesData: userData.rules,
      analyticsData: userData.analytics,
      exportDate: new Date().toISOString(),
      format: 'JSON'
    }
  }
  
  async deleteUserData(userId: string): Promise<DeletionReport> {
    const deletionTasks = [
      this.deleteEmails(userId),
      this.deleteSettings(userId),
      this.deleteRules(userId),
      this.deleteAnalytics(userId),
      this.deleteTokens(userId)
    ]
    
    const results = await Promise.allSettled(deletionTasks)
    
    return {
      deletedItems: results.filter(r => r.status === 'fulfilled').length,
      failedItems: results.filter(r => r.status === 'rejected').length,
      deletionDate: new Date().toISOString()
    }
  }
}
```

## 📊 Analytics Architecture

### **Real-time Analytics Engine**

```typescript
// Analytics processing pipeline
export class AnalyticsEngine {
  private aggregators: Map<string, AnalyticsAggregator> = new Map()
  private subscribers: Map<string, AnalyticsSubscriber[]> = new Map()
  
  registerAggregator(name: string, aggregator: AnalyticsAggregator): void {
    this.aggregators.set(name, aggregator)
  }
  
  subscribe(metric: string, subscriber: AnalyticsSubscriber): void {
    if (!this.subscribers.has(metric)) {
      this.subscribers.set(metric, [])
    }
    this.subscribers.get(metric)!.push(subscriber)
  }
  
  async processEvent(event: AnalyticsEvent): Promise<void> {
    // Process event through all relevant aggregators
    for (const [name, aggregator] of this.aggregators) {
      if (aggregator.canProcess(event)) {
        const metrics = await aggregator.process(event)
        
        // Notify subscribers
        for (const metric of metrics) {
          const subscribers = this.subscribers.get(metric.name) || []
          subscribers.forEach(subscriber => subscriber.onUpdate(metric))
        }
      }
    }
  }
}

// Email volume aggregator
export class EmailVolumeAggregator implements AnalyticsAggregator {
  canProcess(event: AnalyticsEvent): boolean {
    return ['email_received', 'email_sent', 'email_archived'].includes(event.type)
  }
  
  async process(event: AnalyticsEvent): Promise<AnalyticsMetric[]> {
    const timeWindow = this.getTimeWindow(event.timestamp)
    const currentVolume = await this.getCurrentVolume(timeWindow)
    
    return [{
      name: 'email_volume',
      value: currentVolume + 1,
      timestamp: event.timestamp,
      metadata: {
        timeWindow,
        eventType: event.type,
        accountId: event.accountId
      }
    }]
  }
}
```

### **Cross-Account Analytics Aggregation**

```typescript
// Multi-account analytics processing
export class MultiAccountAnalytics {
  async aggregateAccountAnalytics(
    accounts: EmailAccount[],
    analyticsData: Record<string, AnalyticsData>,
    timeRange: DateRange
  ): Promise<UnifiedAnalytics> {
    const aggregated: UnifiedAnalytics = {
      totalEmails: 0,
      unreadEmails: 0,
      averageResponseTime: 0,
      topSenders: [],
      categoryDistribution: {},
      accountBreakdown: {},
      timeRange
    }
    
    for (const account of accounts) {
      const accountData = analyticsData[account.id]
      if (!accountData) continue
      
      // Aggregate totals
      aggregated.totalEmails += accountData.totalEmails
      aggregated.unreadEmails += accountData.unreadEmails
      
      // Merge category distributions
      this.mergeCategoryDistribution(
        aggregated.categoryDistribution,
        accountData.categoryDistribution
      )
      
      // Track per-account metrics
      aggregated.accountBreakdown[account.id] = {
        name: account.name,
        totalEmails: accountData.totalEmails,
        unreadEmails: accountData.unreadEmails,
        responseTime: accountData.averageResponseTime,
        topCategory: this.getTopCategory(accountData.categoryDistribution)
      }
    }
    
    // Calculate weighted averages
    aggregated.averageResponseTime = this.calculateWeightedResponseTime(
      aggregated.accountBreakdown
    )
    
    // Merge and rank top senders
    aggregated.topSenders = this.mergeTopSenders(
      Object.values(analyticsData).map(data => data.topSenders)
    )
    
    return aggregated
  }
}
```

## 🔄 Real-time Features Architecture

### **Background Synchronization**

```typescript
// Multi-account sync coordinator
export class SyncCoordinator {
  private syncQueues: Map<string, SyncQueue> = new Map()
  private syncScheduler: SyncScheduler
  private progressTrackers: Map<string, SyncProgressTracker> = new Map()
  
  async syncAccount(accountId: string, options: SyncOptions = {}): Promise<SyncResult> {
    const account = await this.getAccount(accountId)
    if (!account) throw new Error(`Account ${accountId} not found`)
    
    const syncQueue = this.getSyncQueue(accountId)
    const progressTracker = this.getProgressTracker(accountId)
    
    progressTracker.start()
    
    try {
      const result = await this.performSync(account, options, progressTracker)
      progressTracker.complete(result)
      return result
    } catch (error) {
      progressTracker.error(error)
      throw error
    }
  }
  
  async syncAllAccounts(): Promise<Record<string, SyncResult>> {
    const accounts = await this.getActiveAccounts()
    const syncPromises = accounts.map(account => 
      this.syncAccount(account.id, { parallel: true })
    )
    
    const results = await Promise.allSettled(syncPromises)
    
    return accounts.reduce((acc, account, index) => {
      const result = results[index]
      acc[account.id] = result.status === 'fulfilled' 
        ? result.value 
        : { success: false, error: result.reason }
      return acc
    }, {} as Record<string, SyncResult>)
  }
  
  private async performSync(
    account: EmailAccount, 
    options: SyncOptions, 
    tracker: SyncProgressTracker
  ): Promise<SyncResult> {
    const provider = this.getProvider(account.provider)
    
    // Phase 1: Fetch email list changes
    tracker.updatePhase('fetching_changes')
    const changes = await provider.fetchChanges(account, options.since)
    tracker.updateProgress(25)
    
    // Phase 2: Process new emails
    tracker.updatePhase('processing_emails')
    const newEmails = await this.processNewEmails(changes.newEmails)
    tracker.updateProgress(50)
    
    // Phase 3: Apply rule automations
    tracker.updatePhase('applying_rules')
    const ruleResults = await this.applyRules(newEmails, account.id)
    tracker.updateProgress(75)
    
    // Phase 4: Update analytics
    tracker.updatePhase('updating_analytics')
    await this.updateAnalytics(account.id, newEmails, ruleResults)
    tracker.updateProgress(100)
    
    return {
      success: true,
      emailsProcessed: newEmails.length,
      rulesExecuted: ruleResults.length,
      syncDuration: Date.now() - tracker.startTime,
      lastSyncTime: new Date()
    }
  }
}
```

### **Real-time Email Simulation**

```typescript
// Real-time email arrival simulation
export class EmailSimulator {
  private intervalId: NodeJS.Timeout | null = null
  private eventBus: EventBus
  
  start(): void {
    if (this.intervalId) return
    
    this.intervalId = setInterval(() => {
      this.simulateEmailArrival()
    }, 30000 + Math.random() * 60000) // 30-90 seconds
  }
  
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
  }
  
  private async simulateEmailArrival(): Promise<void> {
    const emailType = this.selectRandomEmailType()
    const newEmail = generateEmail(emailType)
    
    // Add to mock data store
    mockThreads.unshift(newEmail)
    
    // Trigger real-time updates
    this.eventBus.emit('email:new', {
      email: newEmail,
      timestamp: new Date()
    })
    
    // Simulate AI processing
    setTimeout(async () => {
      const categorization = await mockCategorizeEmail(newEmail.messages[0])
      newEmail.category = categorization.category
      
      this.eventBus.emit('email:categorized', {
        emailId: newEmail.id,
        category: categorization.category,
        confidence: categorization.confidence
      })
    }, 1000 + Math.random() * 3000)
  }
  
  private selectRandomEmailType(): EmailCategory {
    const weights = {
      'Newsletter': 0.3,
      'Work': 0.25,
      'Personal': 0.2,
      'Marketing': 0.15,
      'Notification': 0.1
    }
    
    const random = Math.random()
    let cumulative = 0
    
    for (const [type, weight] of Object.entries(weights)) {
      cumulative += weight
      if (random <= cumulative) {
        return type as EmailCategory
      }
    }
    
    return 'Other'
  }
}
```

## 🧪 Testing Architecture

### **Testing Strategy & Patterns**

```typescript
// Testing utilities and patterns
export class TestUtils {
  static createMockThread(overrides: Partial<Thread> = {}): Thread {
    return {
      id: `thread-${Math.random().toString(36).substr(2, 9)}`,
      messages: [TestUtils.createMockMessage()],
      snippet: 'Test email snippet',
      category: 'Work',
      labels: ['INBOX'],
      unread: false,
      important: false,
      starred: false,
      ...overrides
    }
  }
  
  static createMockMessage(overrides: Partial<ParsedMessage> = {}): ParsedMessage {
    return {
      id: `message-${Math.random().toString(36).substr(2, 9)}`,
      threadId: 'thread-test',
      subject: 'Test Subject',
      from: { email: '<EMAIL>', name: 'Test Sender' },
      to: [{ email: '<EMAIL>', name: 'Test User' }],
      date: new Date().toISOString(),
      body: 'Test email body',
      snippet: 'Test snippet',
      ...overrides
    }
  }
  
  static setupMockStore<T>(initialState: T): MockStore<T> {
    const store = create<T>(() => initialState)
    return {
      ...store,
      reset: () => store.setState(initialState),
      setState: store.setState
    }
  }
}

// Component testing utilities
export function renderWithProviders(
  ui: React.ReactElement,
  options: RenderOptions = {}
) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  })
  
  function AllTheProviders({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        <MemoryRouter>
          {children}
        </MemoryRouter>
      </QueryClientProvider>
    )
  }
  
  return render(ui, { wrapper: AllTheProviders, ...options })
}
```

### **Integration Testing with MSW**

```typescript
// MSW test server setup
import { setupServer } from 'msw/node'

export const testServer = setupServer(...handlers)

// Test setup
beforeAll(() => testServer.listen())
afterEach(() => testServer.resetHandlers())
afterAll(() => testServer.close())

// Integration test example
describe('Email Management Integration', () => {
  it('should fetch and display email threads', async () => {
    const mockThreads = [
      TestUtils.createMockThread({ subject: 'Test Email 1' }),
      TestUtils.createMockThread({ subject: 'Test Email 2' })
    ]
    
    testServer.use(
      http.get('/api/google/threads', () => {
        return HttpResponse.json({ threads: mockThreads })
      })
    )
    
    renderWithProviders(<EmailList />)
    
    expect(screen.getByText('Loading...')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('Test Email 1')).toBeInTheDocument()
      expect(screen.getByText('Test Email 2')).toBeInTheDocument()
    })
  })
})
```

## 🔮 Extensibility & Plugin Architecture

### **Plugin System Design**

```typescript
// Plugin interface
export interface EmailPlugin {
  name: string
  version: string
  initialize: (context: PluginContext) => Promise<void>
  destroy: () => Promise<void>
  
  // Optional hooks
  onEmailReceived?: (email: ParsedMessage) => Promise<void>
  onRuleExecuted?: (rule: Rule, result: RuleExecutionResult) => Promise<void>
  onAnalyticsUpdate?: (metrics: AnalyticsMetric[]) => Promise<void>
}

// Plugin manager
export class PluginManager {
  private plugins: Map<string, EmailPlugin> = new Map()
  private hooks: Map<string, PluginHook[]> = new Map()
  
  async installPlugin(plugin: EmailPlugin): Promise<void> {
    if (this.plugins.has(plugin.name)) {
      throw new Error(`Plugin ${plugin.name} is already installed`)
    }
    
    const context = this.createPluginContext(plugin)
    await plugin.initialize(context)
    
    this.plugins.set(plugin.name, plugin)
    this.registerPluginHooks(plugin)
  }
  
  async executeHook(hookName: string, ...args: any[]): Promise<void> {
    const hooks = this.hooks.get(hookName) || []
    
    await Promise.all(
      hooks.map(hook => hook.execute(...args))
    )
  }
  
  private createPluginContext(plugin: EmailPlugin): PluginContext {
    return {
      pluginName: plugin.name,
      api: {
        registerCommand: (command: Command) => this.registerCommand(plugin.name, command),
        registerFilter: (filter: Filter) => this.registerFilter(plugin.name, filter),
        subscribeToEvents: (events: string[]) => this.subscribeToEvents(plugin.name, events)
      },
      storage: new PluginStorage(plugin.name),
      logger: new PluginLogger(plugin.name)
    }
  }
}
```

This comprehensive architecture documentation provides a complete technical overview of the E-Connect 2 email management platform, covering all aspects from high-level design principles to detailed implementation patterns. The architecture is designed to be scalable, maintainable, and extensible while providing excellent performance and user experience.