{"root": ["./src/env.d.ts", "./src/main.tsx", "./src/routetree.gen.ts", "./src/router.tsx", "./src/vite-env.d.ts", "./src/components/emaillist.tsx", "./src/components/account-management/accountmanagement.tsx", "./src/components/account-management/index.ts", "./src/components/account-switcher/accountswitcher.tsx", "./src/components/account-switcher/accountswitchercompact.tsx", "./src/components/account-switcher/index.ts", "./src/components/assistant-chat/chat.tsx", "./src/components/assistant-chat/chatinput.tsx", "./src/components/assistant-chat/chatmessage.tsx", "./src/components/assistant-chat/rulebuilder.tsx", "./src/components/assistant-chat/suggestedactions.tsx", "./src/components/assistant-chat/index.ts", "./src/components/bulk-actions/bulkunsubscribe.tsx", "./src/components/bulk-actions/cleaninbox.tsx", "./src/components/bulk-actions/coldemailblocker.tsx", "./src/components/bulk-actions/progresstracker.tsx", "./src/components/bulk-actions/progresstrackerdemo.tsx", "./src/components/charts/categorychart.tsx", "./src/components/charts/chartwrapper.tsx", "./src/components/charts/emailvolumechart.tsx", "./src/components/charts/rulemetrics.tsx", "./src/components/charts/senderchart.tsx", "./src/components/email-list/bulkactions.tsx", "./src/components/email-list/emaillist.tsx", "./src/components/email-list/emaillistexample.tsx", "./src/components/email-list/emaillistitem.tsx", "./src/components/email-list/emailpanel.tsx", "./src/components/email-list/threadview.tsx", "./src/components/layout/navigation.tsx", "./src/components/rules/ruleform.tsx", "./src/components/rules/rulehistory.tsx", "./src/components/rules/ruletest.tsx", "./src/components/rules/ruleslist.tsx", "./src/components/rules/index.ts", "./src/components/settings/settingscard.tsx", "./src/components/settings/settingscolorpicker.tsx", "./src/components/settings/settingsdropdownselect.tsx", "./src/components/settings/settingsgrid.tsx", "./src/components/settings/settingsitem.tsx", "./src/components/settings/settingssection.tsx", "./src/components/settings/settingsslider.tsx", "./src/components/settings/index.ts", "./src/components/ui/badge.tsx", "./src/components/ui/button.tsx", "./src/components/ui/card.tsx", "./src/components/ui/checkbox.tsx", "./src/components/ui/container.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/input.tsx", "./src/components/ui/keyboardshortcuts.tsx", "./src/components/ui/keyboardshortcutswrapper.tsx", "./src/components/ui/loading.tsx", "./src/components/ui/progress.tsx", "./src/components/ui/radiogroup.tsx", "./src/components/ui/select.tsx", "./src/components/ui/slider.tsx", "./src/components/ui/tabs.tsx", "./src/components/ui/toast.tsx", "./src/components/ui/toggle.tsx", "./src/components/ui/tooltip.tsx", "./src/components/unified-inbox/unifiedinbox.tsx", "./src/components/unified-inbox/index.ts", "./src/hooks/usekeyboardshortcuts.ts", "./src/hooks/usetheme.ts", "./src/lib/utils.ts", "./src/mocks/browser.ts", "./src/mocks/handlers.ts", "./src/mocks/data/analytics.ts", "./src/mocks/data/emails.ts", "./src/mocks/data/rules.ts", "./src/mocks/data/senders.ts", "./src/mocks/data/users.ts", "./src/mocks/handlers/ai.ts", "./src/mocks/handlers/analytics.ts", "./src/mocks/handlers/assistant.ts", "./src/mocks/handlers/auth.ts", "./src/mocks/handlers/bulk.ts", "./src/mocks/handlers/cold-email.ts", "./src/mocks/handlers/emails.ts", "./src/mocks/handlers/index.ts", "./src/mocks/handlers/multi-account.ts", "./src/mocks/handlers/progress-tracker.ts", "./src/mocks/handlers/rules.ts", "./src/mocks/handlers/settings.ts", "./src/routes/__root.tsx", "./src/routes/about.tsx", "./src/routes/chat.tsx", "./src/routes/index.tsx", "./src/routes/rules.tsx", "./src/routes/settings.tsx", "./src/routes/assistant/index.tsx", "./src/routes/assistant/knowledge.tsx", "./src/routes/assistant/onboarding.tsx", "./src/routes/automation/create.tsx", "./src/routes/automation/history.tsx", "./src/routes/automation/index.tsx", "./src/routes/automation/rules.tsx", "./src/routes/automation/test.tsx", "./src/routes/bulk-unsubscribe/index.tsx", "./src/routes/bulk-unsubscribe/progress.tsx", "./src/routes/clean/history.tsx", "./src/routes/clean/index.tsx", "./src/routes/clean/onboarding.tsx", "./src/routes/clean/run.tsx", "./src/routes/cold-email-blocker/blocked.tsx", "./src/routes/cold-email-blocker/index.tsx", "./src/routes/cold-email-blocker/settings.tsx", "./src/routes/mail/compose.tsx", "./src/routes/mail/index.tsx", "./src/routes/mail/thread.$threadid.tsx", "./src/routes/settings/account.tsx", "./src/routes/settings/advanced.tsx", "./src/routes/settings/index.tsx", "./src/routes/settings/integrations.tsx", "./src/routes/settings/preferences.tsx", "./src/routes/stats/emails.tsx", "./src/routes/stats/index.tsx", "./src/routes/stats/rules.tsx", "./src/routes/stats/senders.tsx", "./src/stores/analyticsstore.ts", "./src/stores/assistantstore.ts", "./src/stores/authstore.ts", "./src/stores/bulkstore.ts", "./src/stores/coldemailstore.ts", "./src/stores/emailstore.ts", "./src/stores/multiaccountstore.ts", "./src/stores/rulesstore.ts", "./src/stores/settingsstore.ts", "./src/stores/uistore.ts", "./src/types/analytics.ts", "./src/types/api.ts", "./src/types/assistant.ts", "./src/types/bulk.ts", "./src/types/cold-email.ts", "./src/types/common.ts", "./src/types/email.ts", "./src/types/index.ts", "./src/types/multi-account.ts", "./src/types/rules.ts", "./src/types/settings.ts", "./src/utils/api.ts", "./src/utils/config.ts", "./src/utils/date.ts", "./src/utils/email.ts", "./src/utils/index.ts", "./src/utils/types.ts", "./src/utils/ai/categorize.ts", "./src/utils/ai/chat.ts", "./src/utils/ai/index.ts", "./src/utils/ai/mock-responses.ts", "./src/utils/ai/rules-engine.ts", "./src/utils/analytics/aggregation.ts", "./src/utils/analytics/anomalies.ts", "./src/utils/analytics/calculations.ts", "./src/utils/analytics/correlations.ts", "./src/utils/analytics/exporters.ts", "./src/utils/analytics/formatters.ts", "./src/utils/analytics/index.ts", "./src/utils/analytics/metrics.ts", "./src/utils/analytics/multi-account-analytics.ts", "./src/utils/analytics/predictions.ts", "./src/utils/analytics/reporter.ts", "./src/utils/analytics/statistics.ts", "./src/utils/analytics/tracker.ts", "./src/utils/analytics/transformers.ts", "./src/utils/analytics/validators.ts", "./src/utils/backup/multi-account-backup.ts", "./src/utils/cold-email/detection.ts", "./src/utils/cold-email/index.ts", "./src/utils/cold-email/webhooks.ts", "./src/utils/date/calculator.ts", "./src/utils/date/formatter.ts", "./src/utils/date/parser.ts", "./src/utils/email/formatter.ts", "./src/utils/email/parser.ts", "./src/utils/email/threader.ts", "./src/utils/email/validator.ts", "./src/utils/rules/engine.ts", "./src/utils/rules/index.ts", "./src/utils/rules/scheduler.ts", "./src/utils/sync/multi-account-sync.ts", "./src/utils/validation/email.ts", "./src/utils/validation/form.ts", "./src/utils/validation/rule.ts", "./vite.config.ts"], "errors": true, "version": "5.8.3"}