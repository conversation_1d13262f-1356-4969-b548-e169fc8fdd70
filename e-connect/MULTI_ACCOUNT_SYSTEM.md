# Multi-Account Email Management System

## Overview

The e-connect-2 application now supports comprehensive multi-account email management, allowing users to manage multiple email accounts (Gmail, Outlook, IMAP, Exchange) from a single unified interface. This system provides account isolation, unified inbox functionality, cross-account operations, and advanced analytics.

## Features

### 1. Account Management System
- **Multiple Provider Support**: Gmail, Outlook, IMAP, Exchange, Yahoo, and custom configurations
- **Account Status Tracking**: Real-time monitoring of account connectivity and health
- **Authentication Management**: OAuth and password-based authentication with automatic token refresh
- **Account Configuration**: Provider-specific settings and customizations
- **Visual Identification**: Color-coded accounts with custom avatars and names

### 2. Unified Inbox Experience
- **Consolidated View**: View emails from all accounts in a single interface
- **Account Filtering**: Filter by specific accounts, providers, or account groups
- **Cross-Account Search**: Search across all accounts simultaneously
- **Unified Threading**: Thread management that works across account boundaries
- **Account Indicators**: Visual indicators showing which account each email belongs to

### 3. Account Switching & Navigation
- **Quick Account Switcher**: Easy switching between accounts with visual status indicators
- **Recent Accounts**: Quick access to recently used accounts
- **Keyboard Shortcuts**: Fast account switching via keyboard shortcuts
- **Account History**: Track account switching patterns and usage

### 4. Data Isolation & Security
- **Secure Token Storage**: Encrypted storage of authentication tokens per account
- **Data Separation**: Complete isolation of data between accounts
- **Account-Specific Encryption**: Individual encryption keys for each account
- **Audit Logging**: Comprehensive audit trail for multi-account operations

### 5. Synchronization System
- **Background Sync**: Automatic background synchronization for all active accounts
- **Intelligent Scheduling**: Smart sync scheduling based on account activity and settings
- **Sync Progress Tracking**: Real-time progress monitoring for each account
- **Error Recovery**: Automatic retry mechanisms with exponential backoff
- **Bandwidth Optimization**: Efficient sync strategies to minimize bandwidth usage

### 6. Cross-Account Analytics
- **Unified Analytics**: Aggregated analytics across all accounts
- **Comparative Analysis**: Compare performance and usage across accounts
- **Account Breakdown**: Detailed per-account statistics and insights
- **Trend Analysis**: Track trends and patterns across multiple accounts
- **Performance Metrics**: Sync performance and efficiency tracking

### 7. Account Migration & Backup
- **Account Migration**: Move emails and settings between accounts
- **Backup & Restore**: Comprehensive backup system for account data
- **Data Export**: Export account data in multiple formats (JSON, CSV, MBOX)
- **Import Capabilities**: Import data from various sources and formats
- **Validation & Recovery**: Data integrity validation and recovery tools

## Architecture

### Core Components

#### 1. Multi-Account Store (`src/stores/multiAccountStore.ts`)
Central state management for multi-account functionality:
- Account management (add, remove, update)
- Authentication handling
- Sync coordination
- Settings management
- Error handling and recovery

#### 2. Email Store Extensions (`src/stores/emailStore.ts`)
Enhanced email store with multi-account support:
- Account-specific thread management
- Unified inbox functionality
- Cross-account search
- Account-aware filtering and sorting

#### 3. Account Switcher Components
- **AccountSwitcher**: Full-featured account switcher with account management
- **AccountSwitcherCompact**: Compact version for headers and toolbars

#### 4. Unified Inbox Component (`src/components/unified-inbox/UnifiedInbox.tsx`)
Complete unified inbox interface:
- Multi-account email display
- Advanced filtering and search
- Account grouping and sorting
- Performance optimizations

#### 5. Account Management Interface (`src/components/account-management/AccountManagement.tsx`)
Comprehensive account management:
- Add/remove accounts
- Account configuration
- Sync settings
- Status monitoring
- Backup and restore

### Data Layer

#### 1. Type Definitions (`src/types/multi-account.ts`)
Comprehensive type system covering:
- Account configuration and authentication
- Sync settings and progress tracking
- Analytics and statistics
- Migration and backup structures
- Error handling and events

#### 2. API Handlers (`src/mocks/handlers/multi-account.ts`)
Mock API handlers for development and testing:
- Account CRUD operations
- Authentication simulation
- Sync operation mocking
- Analytics data generation
- Migration and backup simulation

#### 3. Synchronization System (`src/utils/sync/multi-account-sync.ts`)
Advanced sync management:
- Multi-account sync coordination
- Progress tracking and reporting
- Error handling and recovery
- Performance optimization
- Scheduling and throttling

#### 4. Analytics Aggregation (`src/utils/analytics/multi-account-analytics.ts`)
Cross-account analytics processing:
- Data aggregation across accounts
- Comparative analysis
- Trend detection
- Performance metrics
- Insight generation

#### 5. Backup System (`src/utils/backup/multi-account-backup.ts`)
Comprehensive backup and migration tools:
- Account data backup and restore
- Migration between accounts and providers
- Data export and import
- Validation and integrity checking
- Compression and encryption

## Usage

### Setting Up Multi-Account Support

1. **Import Required Components**:
```typescript
import { useMultiAccountStore } from './stores/multiAccountStore'
import { AccountSwitcher } from './components/account-switcher'
import { UnifiedInbox } from './components/unified-inbox'
```

2. **Add Account Switcher to Your Layout**:
```typescript
<AccountSwitcher 
  showAccountName={true}
  showUnreadCount={true}
  className="mb-4"
/>
```

3. **Implement Unified Inbox**:
```typescript
<UnifiedInbox className="flex-1" />
```

### Managing Accounts

#### Adding an Account
```typescript
const { addAccount } = useMultiAccountStore()

await addAccount({
  name: 'Work Email',
  email: '<EMAIL>',
  provider: 'outlook',
  color: '#0078D4',
  isDefault: false,
  isPrimary: false,
  displayOrder: 1,
  config: {
    provider: 'outlook',
    oauth: {
      clientId: 'your-client-id',
      scopes: ['mail.read', 'mail.send'],
      redirectUri: 'your-redirect-uri'
    }
  },
  // ... other configuration
})
```

#### Switching Accounts
```typescript
const { switchToAccount, switchToUnifiedView } = useEmailStore()

// Switch to specific account
await switchToAccount('account-id')

// Switch to unified view
switchToUnifiedView()
```

#### Account Synchronization
```typescript
const { syncAccount, syncAllAccounts } = useMultiAccountStore()

// Sync specific account
await syncAccount('account-id', { force: true })

// Sync all active accounts
await syncAllAccounts()
```

### Using Unified Features

#### Unified Search
```typescript
const { searchAllAccounts } = useMultiAccountStore()

const results = await searchAllAccounts('search query', {
  accountIds: ['account1', 'account2'],
  maxResults: 50,
  timeout: 30
})
```

#### Cross-Account Analytics
```typescript
import { createAnalyticsAggregator } from './utils/analytics/multi-account-analytics'

const aggregator = createAnalyticsAggregator()
const unifiedAnalytics = aggregator.aggregateAccountAnalytics(
  accounts,
  analyticsData,
  { start: new Date('2024-01-01'), end: new Date() }
)
```

#### Account Backup
```typescript
import { createBackupManager } from './utils/backup/multi-account-backup'

const backupManager = createBackupManager(accounts, settings)

const backup = await backupManager.createAccountBackup('account-id', {
  includeEmails: true,
  includeContacts: true,
  includeSettings: true,
  compress: true,
  encrypt: true,
  password: 'backup-password'
})
```

## Configuration

### Multi-Account Settings
```typescript
interface MultiAccountSettings {
  // UI preferences
  autoSwitchOnEmail: boolean
  showAccountInEmailList: boolean
  groupByAccount: boolean
  
  // Unified inbox
  unifiedInboxEnabled: boolean
  unifiedInboxAccounts: string[]
  unifiedSorting: 'chronological' | 'by_account' | 'by_importance'
  
  // Notifications
  consolidateNotifications: boolean
  notifyAllAccounts: boolean
  accountSpecificSounds: boolean
  
  // Search and performance
  searchAllAccountsByDefault: boolean
  searchTimeout: number
  maxSearchResults: number
  maxConcurrentSyncs: number
  
  // Security and data
  dataIsolation: boolean
  sharedContacts: boolean
  sharedRules: boolean
  encryptAccountData: boolean
}
```

### Account Configuration
```typescript
interface EmailAccount {
  id: string
  name: string
  email: string
  provider: AccountProvider
  status: AccountStatus
  isDefault: boolean
  isPrimary: boolean
  color: string
  displayOrder: number
  
  config: AccountConfiguration
  auth: AccountAuthentication
  sync: AccountSyncSettings
  stats: AccountStats
  
  createdAt: Date
  updatedAt: Date
  lastSyncAt?: Date
  lastAccessAt?: Date
}
```

## Security Considerations

### Data Isolation
- Each account's data is stored separately with unique encryption keys
- Cross-account operations require explicit user consent
- Account tokens are encrypted and stored securely
- Audit logs track all multi-account operations

### Authentication Security
- OAuth tokens are automatically refreshed before expiration
- Failed authentication attempts are tracked and rate-limited
- Two-factor authentication support for account access
- Secure credential storage with industry-standard encryption

### Privacy Protection
- User data is kept separate between accounts
- Optional data sharing controls for contacts and rules
- Granular privacy settings per account
- GDPR-compliant data handling and deletion

## Performance Optimizations

### Sync Efficiency
- Intelligent sync scheduling based on account activity
- Batch operations to minimize API calls
- Incremental sync for large mailboxes
- Background sync with priority queuing

### UI Performance
- Virtual scrolling for large unified inboxes
- Lazy loading of account data
- Optimized re-rendering with React.memo
- Efficient state management with Zustand

### Memory Management
- Account data pagination and cleanup
- Automatic cache management
- Memory usage monitoring and optimization
- Garbage collection for unused data

## Troubleshooting

### Common Issues

1. **Account Authentication Failures**
   - Check OAuth configuration and scopes
   - Verify redirect URIs are correctly configured
   - Ensure tokens haven't expired
   - Check provider-specific rate limits

2. **Sync Issues**
   - Verify account credentials and permissions
   - Check network connectivity
   - Review sync settings and folder permissions
   - Monitor API rate limits and quotas

3. **Performance Problems**
   - Reduce number of concurrent syncs
   - Adjust sync intervals for heavy accounts
   - Enable data compression and optimization
   - Clear cache and restart sync processes

### Debugging Tools

1. **Account Status Dashboard**
   - Real-time account health monitoring
   - Sync progress and error tracking
   - API usage and rate limit monitoring
   - Performance metrics and analytics

2. **Debug Logging**
   - Detailed sync operation logs
   - Authentication flow tracking
   - Error reporting and diagnostics
   - Performance profiling data

3. **Data Validation Tools**
   - Account configuration validation
   - Data integrity checking
   - Backup verification utilities
   - Migration validation reports

## API Reference

### Store Methods

#### MultiAccountStore
- `addAccount(config)` - Add new email account
- `removeAccount(accountId)` - Remove account
- `updateAccount(accountId, updates)` - Update account configuration
- `switchToAccount(accountId)` - Switch active account
- `syncAccount(accountId, options)` - Sync specific account
- `syncAllAccounts()` - Sync all active accounts
- `searchAllAccounts(query, options)` - Cross-account search

#### EmailStore Extensions
- `switchToAccount(accountId)` - Switch email view to account
- `switchToUnifiedView()` - Switch to unified inbox
- `fetchUnifiedThreads(params)` - Fetch unified thread list

### Event System

#### Account Events
- `account_added` - New account added
- `account_removed` - Account removed
- `account_switched` - Active account changed
- `account_synced` - Account sync completed
- `account_error` - Account error occurred

#### Sync Events
- `sync_started` - Sync operation started
- `sync_progress` - Sync progress update
- `sync_completed` - Sync operation completed
- `sync_failed` - Sync operation failed

## Migration Guide

### From Single Account
If you're migrating from a single-account setup:

1. **Update Store Usage**:
```typescript
// Old
const { threads, fetchThreads } = useEmailStore()

// New
const { currentAccountId, isUnifiedView } = useEmailStore()
const { accounts, activeAccountId } = useMultiAccountStore()
```

2. **Add Account Management**:
```typescript
// Add account switcher to your layout
<AccountSwitcher />

// Add account management to settings
<AccountManagement />
```

3. **Update Email Components**:
```typescript
// Update thread components to handle account context
const thread = thread as UnifiedThread
const accountColor = thread.accountColor
const accountName = thread.accountName
```

### Breaking Changes
- Email store structure now includes account context
- Thread objects may be UnifiedThread type in unified view
- Some API endpoints now require accountId parameter
- Settings structure updated with multi-account preferences

## Future Enhancements

### Planned Features
- Real-time push notifications for all accounts
- Advanced AI-powered cross-account insights
- Calendar and contact sync across providers
- Mobile app multi-account support
- Enterprise-grade account management
- Advanced automation and workflow features

### Performance Improvements
- Improved sync algorithms and caching
- Better memory management for large account sets
- Enhanced UI performance for unified inbox
- Optimized search across multiple accounts

### Security Enhancements
- Advanced threat detection across accounts
- Enhanced privacy controls and data governance
- Improved audit logging and compliance features
- Zero-trust security architecture

## Contributing

When contributing to the multi-account system:

1. **Follow Type Safety**: Use TypeScript strictly and update type definitions
2. **Maintain Data Isolation**: Ensure account data remains properly separated
3. **Test Thoroughly**: Test with multiple accounts and various scenarios
4. **Document Changes**: Update this documentation for any new features
5. **Performance Considerations**: Profile and optimize for multiple accounts

## Support

For issues, questions, or contributions related to the multi-account system:

- **Documentation**: Refer to this guide and inline code comments
- **Type Definitions**: Check `src/types/multi-account.ts` for complete API
- **Examples**: See component implementations for usage patterns
- **Testing**: Use MSW handlers for development and testing scenarios

---

*This multi-account system provides enterprise-grade email management capabilities while maintaining simplicity and performance. The modular architecture allows for easy extension and customization based on specific requirements.*